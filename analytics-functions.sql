-- Create the required analytics functions for the comprehensive API

-- Function to get customer insights for a specific company
CREATE OR REPLACE FUNCTION get_customer_insights(p_company_id UUID)
RETURNS TABLE(
    member_id UUID,
    loyalty_id TEXT,
    customer_name TEXT,
    total_visits BIGINT,
    total_spent NUMERIC,
    avg_order_value NUMERIC,
    favorite_item TEXT,
    favorite_category TEXT,
    loyalty_tier VARCHAR,
    lifetime_points INTEGER,
    last_visit_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    WITH customer_base AS (
        SELECT
            lm.id as member_id,
            lm.loyalty_id,
            lm.name as customer_name,
            lm.lifetime_points,
            lm.loyalty_tier,
            COUNT(DISTINCT r.id) as total_visits,
            COALESCE(SUM(r.total_amount), 0) as total_spent,
            COALESCE(AVG(r.total_amount), 0) as avg_order_value,
            MAX(r.created_at) as last_visit_date
        FROM loyalty_members lm
        LEFT JOIN receipts r ON lm.id = r.member_id
        WHERE lm.company_id = p_company_id
        GROUP BY lm.id, lm.loyalty_id, lm.name, lm.lifetime_points, lm.loyalty_tier
    ),
    member_preferences AS (
        SELECT
            cb.member_id,
            bi.item_name,
            bi.item_category,
            COUNT(*) as purchase_count,
            ROW_NUMBER() OVER (PARTITION BY cb.member_id ORDER BY COUNT(*) DESC) as preference_rank
        FROM customer_base cb
        JOIN receipts r ON cb.member_id = r.member_id
        JOIN receipt_items ri ON r.id = ri.receipt_id
        JOIN business_items bi ON ri.business_item_id = bi.id
        WHERE bi.company_id = p_company_id
        GROUP BY cb.member_id, bi.item_name, bi.item_category
    )
    SELECT
        cb.member_id,
        cb.loyalty_id,
        cb.customer_name,
        cb.total_visits,
        cb.total_spent,
        cb.avg_order_value,
        mp.item_name as favorite_item,
        mp.item_category as favorite_category,
        cb.loyalty_tier,
        cb.lifetime_points,
        cb.last_visit_date
    FROM customer_base cb
    LEFT JOIN member_preferences mp ON cb.member_id = mp.member_id AND mp.preference_rank = 1
    WHERE cb.total_visits > 0  -- Only customers with transactions
    ORDER BY cb.total_spent DESC, cb.total_visits DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get business performance metrics
CREATE OR REPLACE FUNCTION get_business_performance(p_company_id UUID)
RETURNS TABLE(
    item_id UUID,
    item_name TEXT,
    item_category TEXT,
    total_sales BIGINT,
    total_revenue NUMERIC,
    avg_selling_price NUMERIC,
    unique_customers BIGINT,
    popularity_score NUMERIC,
    pricing_strategy TEXT,
    revenue_share_in_category NUMERIC,
    last_sold_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    WITH item_stats AS (
        SELECT
            bi.id,
            bi.item_name,
            bi.item_category,
            bi.standard_price,
            COUNT(ri.id) as total_sales,
            SUM(ri.total_price) as total_revenue,
            AVG(ri.unit_price) as avg_selling_price,
            COUNT(DISTINCT r.member_id) as unique_customers,
            MAX(r.created_at) as last_sold_date,
            -- Calculate popularity score: 60% sales weight + 40% customer diversity
            (COUNT(ri.id)::NUMERIC * 0.6 + COUNT(DISTINCT r.member_id)::NUMERIC * 0.4) as popularity_score
        FROM business_items bi
        LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
        LEFT JOIN receipts r ON ri.receipt_id = r.id
        WHERE bi.company_id = p_company_id AND bi.is_active = true
        GROUP BY bi.id, bi.item_name, bi.item_category, bi.standard_price
    ),
    category_totals AS (
        SELECT
            item_category,
            SUM(total_revenue) as category_revenue
        FROM item_stats
        GROUP BY item_category
    )
    SELECT
        ist.id as item_id,
        ist.item_name,
        ist.item_category,
        ist.total_sales,
        ist.total_revenue,
        ist.avg_selling_price,
        ist.unique_customers,
        ist.popularity_score,
        CASE
            WHEN ist.avg_selling_price > ist.standard_price * 1.1 THEN 'Premium Pricing'
            WHEN ist.avg_selling_price < ist.standard_price * 0.9 THEN 'Discounted'
            ELSE 'Standard Pricing'
        END as pricing_strategy,
        CASE
            WHEN ct.category_revenue > 0 THEN
                ROUND((ist.total_revenue / ct.category_revenue * 100)::NUMERIC, 2)
            ELSE 0
        END as revenue_share_in_category,
        ist.last_sold_date
    FROM item_stats ist
    LEFT JOIN category_totals ct ON ist.item_category = ct.item_category
    WHERE ist.total_sales > 0  -- Only items with sales
    ORDER BY ist.popularity_score DESC, ist.total_revenue DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get template metrics
CREATE OR REPLACE FUNCTION get_template_analytics(p_company_id UUID)
RETURNS TABLE(
    template_id UUID,
    template_name TEXT,
    total_extractions INTEGER,
    success_rate_percentage NUMERIC,
    avg_confidence_score NUMERIC,
    recent_extractions BIGINT,
    recent_avg_confidence NUMERIC,
    effectiveness_score NUMERIC,
    last_updated TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        rt.id as template_id,
        rt.template_name,
        rt.total_extractions,
        CASE
            WHEN rt.total_extractions > 0 THEN
                ROUND((rt.successful_extractions::NUMERIC / rt.total_extractions * 100)::NUMERIC, 2)
            ELSE 0
        END as success_rate_percentage,
        rt.avg_confidence_score,
        COUNT(r.id) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_extractions,
        AVG(r.extraction_confidence) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_avg_confidence,
        CASE
            WHEN rt.total_extractions > 0 AND rt.avg_confidence_score IS NOT NULL THEN
                ((rt.successful_extractions::NUMERIC / rt.total_extractions * 0.7) + (rt.avg_confidence_score * 0.3))
            ELSE 0
        END as effectiveness_score,
        rt.updated_at as last_updated
    FROM receipt_templates rt
    LEFT JOIN receipts r ON rt.id = r.template_id
    WHERE rt.company_id = p_company_id AND rt.is_active = true
    GROUP BY rt.id, rt.template_name, rt.total_extractions, rt.successful_extractions,
             rt.avg_confidence_score, rt.updated_at
    ORDER BY CASE
        WHEN rt.total_extractions > 0 AND rt.avg_confidence_score IS NOT NULL THEN
            ((rt.successful_extractions::NUMERIC / rt.total_extractions * 0.7) + (rt.avg_confidence_score * 0.3))
        ELSE 0
    END DESC;
END;
$$ LANGUAGE plpgsql;
