'use client'

import { useState, useEffect } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useRole } from '@/hooks/use-role'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useTransactions, useDeleteTransaction } from '@/hooks/use-transactions'
import { useMembers } from '@/hooks/use-members'
import { Skeleton } from '@/components/ui/skeleton'
import { format } from 'date-fns'
import { Search, ArrowUpDown, ArrowUp, ArrowDown, Wand2, Calendar as CalendarIcon, X, Trash2 } from 'lucide-react'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'
import { DateRange } from 'react-day-picker'

type Transaction = {
  id: string
  member_id: string
  member_name?: string
  member_loyalty_id?: string
  points_change: number
  transaction_type: 'EARN' | 'REDEEM' | 'EXPIRE'
  description: string
  transaction_date: string
  receipt_id?: string | null
  created_at?: string
}

export default function TransactionsPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { isLoading: companyLoading } = useCompany()
  const { isOwner } = useRole()
  const [mounted, setMounted] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<string>('created_at')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined)
  const router = useRouter()

  // Use React Query hooks for data fetching with server-side date filtering
  const { data: transactionsData, isLoading: transactionsLoading } = useTransactions(
    100,
    dateRange ? { from: dateRange.from, to: dateRange.to } : undefined
  )
  const { data: membersData, isLoading: membersLoading } = useMembers()
  const deleteTransactionMutation = useDeleteTransaction()

  // Handle hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !authLoading && !user) {
      router.push('/login')
    }
  }, [mounted, authLoading, user, router])

  // Create a member lookup map for efficient member name resolution
  const memberMap = new Map()
  if (membersData?.data) {
    membersData.data.forEach((member: { id: string; name: string; loyalty_id: string }) => {
      memberMap.set(member.id, member)
    })
  }

  // Enhanced transactions with member names and loyalty IDs
  const enhancedTransactions = transactionsData?.data?.map((tx: Transaction) => {
    const member = memberMap.get(tx.member_id)
    return {
      ...tx,
      member_name: member?.name || tx.member_name || 'Unknown Member',
      member_loyalty_id: member?.loyalty_id || 'N/A',
      transaction_date: tx.transaction_date || tx.created_at || new Date().toISOString()
    }
  }) || []

  // Filter transactions based on search term only (date filtering is now server-side)
  const filteredTransactions = enhancedTransactions.filter((tx: Transaction) => {
    const searchLower = searchTerm.toLowerCase()
    return (
      tx.member_name?.toLowerCase().includes(searchLower) ||
      tx.description?.toLowerCase().includes(searchLower) ||
      tx.transaction_type?.toLowerCase().includes(searchLower) ||
      tx.points_change.toString().includes(searchTerm)
    )
  })

  // Sort transactions
  const sortedTransactions = [...filteredTransactions].sort((a, b) => {
    if (sortField === 'points_change') {
      return sortDirection === 'asc'
        ? a.points_change - b.points_change
        : b.points_change - a.points_change
    }

    if (sortField === 'transaction_date') {
      return sortDirection === 'asc'
        ? new Date(a.transaction_date).getTime() - new Date(b.transaction_date).getTime()
        : new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime()
    }

    // Default string comparison for other fields
    const aValue = a[sortField as keyof Transaction] || ''
    const bValue = b[sortField as keyof Transaction] || ''

    return sortDirection === 'asc'
      ? String(aValue).localeCompare(String(bValue))
      : String(bValue).localeCompare(String(aValue))
  })

  // Handle sort click
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Loading state
  const isLoading = !mounted || authLoading || companyLoading || transactionsLoading || membersLoading

  if (!mounted) {
    return null // Prevent hydration mismatch
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Transactions</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            View and manage all point transactions
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
            <Input
              type="search"
              placeholder="Search transactions..."
              className="pl-9 w-full sm:w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Date Range Picker */}
          <div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="date"
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !dateRange && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateRange?.from ? (
                    dateRange.to ? (
                      <>
                        {format(dateRange.from, "LLL dd, y")} -{" "}
                        {format(dateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(dateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Filter by date</span>
                  )}
                  {dateRange && (
                    <Button
                      variant="ghost"
                      className="h-6 w-6 p-0 ml-2"
                      onClick={(e) => {
                        e.stopPropagation()
                        setDateRange(undefined)
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange?.from}
                  selected={dateRange}
                  onSelect={setDateRange}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="flex gap-2">
            <Button asChild>
              <Link href="/transactions/unified">
                <Wand2 className="mr-2 h-4 w-4" />
                Smart Transaction
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Transaction History</CardTitle>
          <CardDescription>
            {filteredTransactions.length} transactions found
            {dateRange?.from && (
              <span className="ml-2 text-xs">
                {dateRange.to ? (
                  <>Filtered: {format(dateRange.from, "MMM d, yyyy")} - {format(dateRange.to, "MMM d, yyyy")}</>
                ) : (
                  <>Filtered: From {format(dateRange.from, "MMM d, yyyy")}</>
                )}
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-full" />
                </div>
              ))}
            </div>
          ) : filteredTransactions.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              {searchTerm ? 'No transactions match your search' : 'No transactions found'}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('member_name')}
                    >
                      <div className="flex items-center">
                        Member
                        {sortField === 'member_name' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'member_name' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead>Member ID</TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('transaction_type')}
                    >
                      <div className="flex items-center">
                        Type
                        {sortField === 'transaction_type' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'transaction_type' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer text-right"
                      onClick={() => handleSort('points_change')}
                    >
                      <div className="flex items-center justify-end">
                        Points
                        {sortField === 'points_change' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'points_change' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('transaction_date')}
                    >
                      <div className="flex items-center">
                        Date
                        {sortField === 'transaction_date' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'transaction_date' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.member_name}</TableCell>
                      <TableCell className="font-mono text-xs">{transaction.member_loyalty_id}</TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                          transaction.transaction_type === 'EARN' || transaction.transaction_type === 'earn'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : transaction.transaction_type === 'REDEEM' || transaction.transaction_type === 'redeem'
                            ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        }`}>
                          {transaction.transaction_type?.toUpperCase()}
                        </span>
                      </TableCell>
                      <TableCell className="text-right font-mono">
                        <span className={`${
                          transaction.transaction_type === 'EARN' || transaction.transaction_type === 'earn'
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-red-600 dark:text-red-400'
                        }`}>
                          {transaction.transaction_type === 'EARN' || transaction.transaction_type === 'earn' ? '+' : '-'}
                          {Math.abs(transaction.points_change)}
                        </span>
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate" title={transaction.description}>
                        {transaction.description || 'No description'}
                      </TableCell>
                      <TableCell>
                        {format(new Date(transaction.transaction_date), 'MMM d, yyyy')}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex gap-2 justify-end">
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                            className="h-8 px-3"
                          >
                            <Link href={`/transactions/success?id=${transaction.id}`}>
                              View Summary
                            </Link>
                          </Button>

                          {/* Owner Delete Button */}
                          {isOwner && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Transaction</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete this transaction? This action cannot be undone and will:
                                    <ul className="list-disc list-inside mt-2 space-y-1">
                                      <li>Remove {Math.abs(transaction.points_change)} points from {transaction.member_name}</li>
                                      <li>Delete any associated reward redemptions</li>
                                      <li>Update the member&apos;s point balance</li>
                                    </ul>
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => deleteTransactionMutation.mutate(transaction.id)}
                                    className="bg-red-600 hover:bg-red-700"
                                    disabled={deleteTransactionMutation.isPending}
                                  >
                                    {deleteTransactionMutation.isPending ? 'Deleting...' : 'Delete Transaction'}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
