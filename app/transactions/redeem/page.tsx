'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useMembers } from '@/hooks/use-members'
import { useRewards, type Reward } from '@/hooks/use-rewards'
import { useUploadImage, useProcessReceiptOCR } from '@/hooks/use-receipts'
import { useAIValidation, useProcessRedemption, type AIValidationResponse } from '@/hooks/use-ai-validation'
import type { ReceiptData } from '@/lib/receipt-ocr'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'


// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { MemberCombobox } from '@/components/ui/member-combobox'
import { Controller } from 'react-hook-form'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import Link from 'next/link'
import {
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Loader2,
  User,
  Calculator,
  Wand2,
  Gift,
  AlertTriangle,
  Info
} from 'lucide-react'

// Define validation schema for redemption form
const redemptionSchema = z.object({
  member_id: z.string().uuid({ message: 'Please select a member' }),
  reward_id: z.string().uuid({ message: 'Please select a reward' }),
  receipt_total: z.coerce.number().positive().optional(),
  description: z.string().min(3, {
    message: 'Description must be at least 3 characters'
  }),
  receipt_image: z.instanceof(File).optional(),
  // OCR-enhanced fields
  total_amount: z.coerce.number().optional(),
  business_name: z.string().optional(),
  financial_system_number: z.string().optional()
})

type RedemptionFormValues = z.infer<typeof redemptionSchema>

// Using the AIValidationResponse type from the use-ai-validation hook

export default function EnhancedRedemptionPage() {
  const { user, isLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [aiValidation, setAiValidation] = useState<AIValidationResponse | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  // Receipt-related state
  const [receiptFile, setReceiptFile] = useState<File | null>(null)
  const [ocrData, setOcrData] = useState<ReceiptData | null>(null)
  const [showOcrResults, setShowOcrResults] = useState(false)
  const [ocrConfidence, setOcrConfidence] = useState<number | null>(null)
  const router = useRouter()

  // Use hooks for data fetching
  const { data: membersData, isLoading: membersLoading } = useMembers()
  const { data: rewardsData, isLoading: rewardsLoading } = useRewards()

  // AI validation hooks
  const aiValidationMutation = useAIValidation()
  const processRedemptionMutation = useProcessRedemption()

  // Update AI validation state when mutation succeeds
  useEffect(() => {
    if (aiValidationMutation.isSuccess && aiValidationMutation.data) {
      setAiValidation(aiValidationMutation.data)
      setIsValidating(false)
    }
    if (aiValidationMutation.isError) {
      setIsValidating(false)
      setAiValidation(null)
    }
  }, [aiValidationMutation.isSuccess, aiValidationMutation.isError, aiValidationMutation.data])

  // Navigate on successful redemption
  useEffect(() => {
    if (processRedemptionMutation.isSuccess) {
      toast.success('Reward redeemed successfully!')
      router.push('/transactions')
    }
  }, [processRedemptionMutation.isSuccess, router])

  const members = membersData?.data || []
  const rewards = rewardsData?.data || []

  // Receipt-related mutations
  const uploadImageMutation = useUploadImage()
  const processOCRMutation = useProcessReceiptOCR()

  // Fix hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !isLoading && !user) {
      router.push('/login')
    }
  }, [mounted, isLoading, user, router])

  // Form setup
  const form = useForm<RedemptionFormValues>({
    resolver: zodResolver(redemptionSchema),
    defaultValues: {
      description: '',
      receipt_total: undefined,
      total_amount: undefined,
      business_name: '',
      financial_system_number: ''
    }
  })

  // Watch form values for real-time validation
  const watchedValues = form.watch()

  // Auto-populate description when AI validation provides one
  useEffect(() => {
    if (aiValidation?.transactionDescription) {
      const currentDescription = form.getValues('description');

      // Only auto-populate if the description field is empty or hasn't been manually edited
      if (!currentDescription || currentDescription.trim() === '') {
        form.setValue('description', aiValidation.transactionDescription);
        toast.success('Transaction description generated by AI');
      }
    }
  }, [aiValidation?.transactionDescription, form]);

  // Trigger AI validation when member, reward, or receipt total changes
  useEffect(() => {
    if (watchedValues.member_id && watchedValues.reward_id && company?.id) {
      setIsValidating(true)

      // Prepare receipt data for AI analysis - only if we have actual receipt data
      const hasReceiptData = ocrData || watchedValues.receipt_total || watchedValues.total_amount;
      const receiptData = hasReceiptData ? {
        total_amount: watchedValues.receipt_total || watchedValues.total_amount || 0,
        business_name: ocrData?.business_name || watchedValues.business_name || undefined,
        receipt_date: ocrData?.receipt_date || undefined,
        service_description: ocrData?.service_description || undefined,
        financial_system_number: ocrData?.financial_system_number || watchedValues.financial_system_number || undefined,
      } : undefined;

      aiValidationMutation.mutate({
        memberId: watchedValues.member_id,
        rewardId: watchedValues.reward_id,
        receiptTotal: watchedValues.receipt_total || watchedValues.total_amount,
        receiptData,
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedValues.member_id, watchedValues.reward_id, watchedValues.receipt_total, watchedValues.total_amount, company?.id, ocrData])

  // Handle form submission
  const onSubmit = async (data: RedemptionFormValues) => {
    if (!company || !aiValidation?.eligible) return

    await processRedemptionMutation.mutateAsync({
      member_id: data.member_id,
      reward_id: data.reward_id,
      description: data.description,
      company_id: company.id, // Add missing company_id
      calculatedOutcome: aiValidation.calculation
    })
  }

  if (!mounted || isLoading || companyLoading || membersLoading || rewardsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!user) {
    return null
  }

  const isMutating = aiValidationMutation.isPending || processRedemptionMutation.isPending ||
                    uploadImageMutation.isPending || processOCRMutation.isPending

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4 gap-2 text-muted-foreground hover:text-foreground"
          asChild
        >
          <Link href="/transactions">
            <ArrowLeft className="h-4 w-4" />
            Back to Transactions
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Redemption Form */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="border shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gift className="h-5 w-5" />
                Reward Redemption
              </CardTitle>
              <CardDescription>
                Select a member and reward to process an intelligent redemption with AI validation
              </CardDescription>
            </CardHeader>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="member_id" className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Member
                    </Label>
                    <Controller
                      name="member_id"
                      control={form.control}
                      render={({ field }) => (
                        <MemberCombobox
                          members={members}
                          value={field.value}
                          onValueChange={field.onChange}
                          placeholder="Search and select a member..."
                          disabled={false}
                        />
                      )}
                    />
                    {form.formState.errors.member_id && (
                      <p className="text-sm text-destructive">{form.formState.errors.member_id.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reward_id" className="flex items-center gap-2">
                      <Gift className="h-4 w-4" />
                      Reward
                    </Label>
                    <Controller
                      name="reward_id"
                      control={form.control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a reward" />
                          </SelectTrigger>
                          <SelectContent>
                            {rewards.map((reward: Reward) => (
                              <SelectItem key={reward.id} value={reward.id}>
                                <div className="flex items-center justify-between w-full">
                                  <span>{reward.title}</span>
                                  <Badge variant="outline" className="ml-2">
                                    {reward.points_required} pts
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {form.formState.errors.reward_id && (
                      <p className="text-sm text-destructive">{form.formState.errors.reward_id.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="receipt_total" className="flex items-center gap-2">
                    <Calculator className="h-4 w-4" />
                    Receipt Total (Birr) - Optional
                  </Label>
                  <Input
                    id="receipt_total"
                    type="number"
                    step="0.01"
                    placeholder="Enter receipt amount for percentage discounts"
                    {...form.register('receipt_total', { valueAsNumber: true })}
                  />
                  <p className="text-xs text-muted-foreground">
                    For percentage discounts, enter the total amount to calculate savings
                  </p>
                </div>

                {/* Receipt Upload Section */}
                <div className="space-y-2">
                  <Label htmlFor="receipt_image" className="flex items-center gap-2">
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Receipt Image (Optional)
                  </Label>
                  <Input
                    id="receipt_image"
                    type="file"
                    accept="image/*"
                    className="bg-card border file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                    onChange={async (e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        // Validate file size (max 10MB)
                        if (file.size > 10 * 1024 * 1024) {
                          toast.error('File size must be less than 10MB');
                          e.target.value = '';
                          return;
                        }
                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                          toast.error('Please select an image file');
                          e.target.value = '';
                          return;
                        }
                        setReceiptFile(file);
                        // Reset OCR state when new file is selected
                        setShowOcrResults(false);
                        setOcrData(null);
                        setOcrConfidence(null);

                        // Automatically process OCR when file is selected
                        toast.info('Processing receipt with AI...');
                        try {
                          const result = await processOCRMutation.mutateAsync({
                            file,
                            companyId: company?.id
                          });
                          const extractedData = result.data.raw_ocr_data;

                          setOcrData(extractedData);
                          setOcrConfidence(extractedData.confidence);

                          // Auto-populate form fields with extracted data
                          if (extractedData.total_amount) {
                            form.setValue('receipt_total', extractedData.total_amount);
                            form.setValue('total_amount', extractedData.total_amount);
                          }
                          if (extractedData.business_name) {
                            form.setValue('business_name', extractedData.business_name);
                          }
                          if (extractedData.financial_system_number) {
                            form.setValue('financial_system_number', extractedData.financial_system_number);
                          }

                          setShowOcrResults(true);
                          toast.success(`Receipt processed! Confidence: ${Math.round(extractedData.confidence * 100)}%`);
                        } catch (error) {
                          console.error('OCR processing error:', error);

                          // Check for specific error types
                          if (error instanceof Error) {
                            if (error.message.includes('overloaded')) {
                              toast.error('AI service is currently overloaded. Please try again in a few minutes or fill in manually.');
                            } else if (error.message.includes('Failed to process')) {
                              toast.error('Failed to process receipt. The AI service may be temporarily unavailable. Please fill in manually.');
                            } else {
                              toast.error('Failed to process receipt. Please fill in manually.');
                            }
                          } else {
                            toast.error('Failed to process receipt. Please fill in manually.');
                          }
                        }
                      } else {
                        setReceiptFile(null);
                        setShowOcrResults(false);
                        setOcrData(null);
                        setOcrConfidence(null);
                      }
                    }}
                  />

                  {receiptFile && (
                    <div className="space-y-3">
                      <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <svg className="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-blue-900">{receiptFile.name}</p>
                          <p className="text-xs text-blue-600">{(receiptFile.size / 1024 / 1024).toFixed(1)} MB</p>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setReceiptFile(null);
                            setShowOcrResults(false);
                            setOcrData(null);
                            setOcrConfidence(null);
                            const fileInput = document.getElementById('receipt_image') as HTMLInputElement;
                            if (fileInput) fileInput.value = '';
                          }}
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </Button>
                      </div>

                      {/* Processing indicator */}
                      {processOCRMutation.isPending && (
                        <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                          <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-blue-900">Processing with AI...</p>
                            <p className="text-xs text-blue-600">Extracting receipt information</p>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* OCR Results Display */}
                  {showOcrResults && ocrData && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <h3 className="font-semibold text-green-900">Receipt Processed Successfully</h3>
                        </div>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {Math.round((ocrConfidence || 0) * 100)}% confidence
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        {ocrData.total_amount && (
                          <div>
                            <span className="font-medium text-green-900">Amount:</span>
                            <span className="ml-2 text-green-700">{ocrData.total_amount} Birr</span>
                          </div>
                        )}
                        {ocrData.business_name && (
                          <div>
                            <span className="font-medium text-green-900">Business:</span>
                            <span className="ml-2 text-green-700">{ocrData.business_name}</span>
                          </div>
                        )}
                        {ocrData.receipt_date && (
                          <div>
                            <span className="font-medium text-green-900">Date:</span>
                            <span className="ml-2 text-green-700">{ocrData.receipt_date}</span>
                          </div>
                        )}
                        {ocrData.financial_system_number && (
                          <div>
                            <span className="font-medium text-green-900">Receipt #:</span>
                            <span className="ml-2 text-green-700">{ocrData.financial_system_number}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description" className="flex items-center gap-2">
                    Description
                    {aiValidation?.transactionDescription && (
                      <Badge variant="secondary" className="text-xs">
                        <Wand2 className="h-3 w-3 mr-1" />
                        Auto Generated
                      </Badge>
                    )}
                  </Label>
                  <Textarea
                    id="description"
                    placeholder="e.g., Redeemed 10% discount on haircut service"
                    {...form.register('description')}
                  />
                  {aiValidation?.transactionDescription && (
                    <p className="text-xs text-muted-foreground">
                      💡 Description automatically generated based on the selected reward and receipt data
                    </p>
                  )}
                  {form.formState.errors.description && (
                    <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
                  )}
                </div>
              </CardContent>

              <div className="px-6 pb-6">
                <Button
                  type="submit"
                  disabled={!aiValidation?.eligible || isMutating}
                  className="w-full"
                >
                  {isMutating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Gift className="h-4 w-4 mr-2" />
                      Redeem Reward
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Card>
        </div>

        {/* AI Validation Panel */}
        <div className="space-y-6">
          {/* Real-time Validation Status */}
          <Card className="border shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-sm">
                <Wand2 className="h-4 w-4" />
                AI Validation
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isValidating || aiValidationMutation.isPending ? (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Analyzing eligibility...</span>
                </div>
              ) : aiValidation ? (
                <div className="space-y-3">
                  {/* Eligibility Status */}
                  <div className="flex items-center gap-2">
                    {aiValidation.eligible ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-600" />
                    )}
                    <span className={`font-medium ${aiValidation.eligible ? 'text-green-700' : 'text-red-700'}`}>
                      {aiValidation.eligible ? 'Eligible' : 'Not Eligible'}
                    </span>
                    <Badge variant="secondary" className="ml-auto">
                      {Math.round(aiValidation.aiInsights.confidence * 100)}% confidence
                    </Badge>
                  </div>

                  {/* Reasoning */}
                  <div className="space-y-1">
                    {aiValidation.aiInsights.reasoning.map((reason: string, index: number) => (
                      <p key={index} className="text-xs text-muted-foreground">
                        • {reason}
                      </p>
                    ))}
                  </div>

                  {/* Calculation Preview */}
                  {aiValidation.calculation && (
                    <div className="mt-4 p-3 bg-muted rounded-lg">
                      <h4 className="text-sm font-medium mb-2">Outcome Preview</h4>
                      <div className="space-y-1 text-xs">
                        <div className="flex justify-between">
                          <span>Original Amount:</span>
                          <span>{aiValidation.calculation.originalAmount} Birr</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Discount:</span>
                          <span className="text-green-600">-{aiValidation.calculation.discountAmount} Birr</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>Final Amount:</span>
                          <span>{aiValidation.calculation.finalAmount} Birr</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Savings:</span>
                          <span className="text-green-600">{aiValidation.calculation.savingsPercentage.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Points Used:</span>
                          <span>{aiValidation.calculation.pointsUsed}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Points Remaining:</span>
                          <span>{aiValidation.calculation.pointsRemaining}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Select a member and reward to start validation
                </p>
              )}
            </CardContent>
          </Card>

          {/* Warnings and Alerts */}
          {aiValidation?.warnings && aiValidation.warnings.length > 0 && (
            <div className="space-y-2">
              {aiValidation.warnings.map((warning: {
                type: string;
                message: string;
                severity: 'error' | 'warning' | 'info';
              }, index: number) => (
                <Alert key={index} className={`border ${
                  warning.severity === 'error' ? 'border-red-200 bg-red-50' :
                  warning.severity === 'warning' ? 'border-yellow-200 bg-yellow-50' :
                  'border-blue-200 bg-blue-50'
                }`}>
                  {warning.severity === 'error' ? (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  ) : warning.severity === 'warning' ? (
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                  ) : (
                    <Info className="h-4 w-4 text-blue-600" />
                  )}
                  <AlertDescription className="text-sm">
                    {warning.message}
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          )}

          {/* Alternative Recommendations */}
          {aiValidation?.aiInsights.alternatives && aiValidation.aiInsights.alternatives.length > 0 && (
            <Card className="border shadow-sm">
              <CardHeader>
                <CardTitle className="text-sm">Alternative Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {aiValidation.aiInsights.alternatives.slice(0, 3).map((alt: {
                    rewardId: string;
                    rewardTitle: string;
                    score: number;
                    reasoning: string;
                    savings?: number;
                    urgency?: 'low' | 'medium' | 'high';
                  }) => (
                    <div key={alt.rewardId} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{alt.rewardTitle}</span>
                        <Badge variant={alt.urgency === 'high' ? 'default' : alt.urgency === 'medium' ? 'secondary' : 'outline'}>
                          {Math.round(alt.score * 100)}% match
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">{alt.reasoning}</p>
                      {alt.savings && alt.savings > 0 && (
                        <p className="text-xs text-green-600 mt-1">
                          Potential savings: {alt.savings} Birr
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
