'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useMembers } from '@/hooks/use-members'
import { useCreateTransaction } from '@/hooks/use-transactions'
import { useUploadImage, useProcessReceiptOCR } from '@/hooks/use-receipts'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import type { ReceiptData } from '@/lib/receipt-ocr'
import type { EnhancedReceiptData } from '@/lib/receipt-ocr-enhanced'

// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { MemberCombobox } from '@/components/ui/member-combobox'
import { ReceiptSummary } from '@/components/ui/receipt-summary'
import { Controller } from 'react-hook-form'
import Link from 'next/link'
import { ArrowLeft, Upload, X, Loader2, Camera, FileText, User, Calculator, Wand2 } from 'lucide-react'
import { Separator } from '@/components/ui/separator'

// Define validation schema for transaction form
const transactionSchema = z.object({
  member_id: z.string().uuid({ message: 'Please select a member' }),
  transaction_type: z.enum(['EARN', 'REDEEM'], {
    required_error: 'Please select a transaction type'
  }),
  points_change: z.coerce.number().positive({
    message: 'Points must be a positive number'
  }),
  description: z.string().min(3, {
    message: 'Description must be at least 3 characters'
  }),
  transaction_date: z.string(),
  receipt_image: z.instanceof(File).optional(),
  // OCR-enhanced fields
  total_amount: z.coerce.number().optional(),
  business_name: z.string().optional(),
  financial_system_number: z.string().optional()
})

type TransactionFormValues = z.infer<typeof transactionSchema>

export default function AddTransactionPage() {
  const { user, isLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [receiptFile, setReceiptFile] = useState<File | null>(null)
  // OCR-related state
  const [ocrData, setOcrData] = useState<ReceiptData | null>(null)
  const [enhancedOcrData, setEnhancedOcrData] = useState<EnhancedReceiptData | null>(null)
  const [showOcrResults, setShowOcrResults] = useState(false)
  const [ocrConfidence, setOcrConfidence] = useState<number | null>(null)
  const [showAiPromo, setShowAiPromo] = useState(true)
  const router = useRouter()

  // Use hooks for data fetching and mutations
  const { data: membersData, isLoading: membersLoading } = useMembers()
  const createTransactionMutation = useCreateTransaction()

  // Receipt-related mutations
  const uploadImageMutation = useUploadImage()
  const processOCRMutation = useProcessReceiptOCR()

  const members = membersData?.data || []

  // Helper to check if any mutation is in progress
  const isMutating = createTransactionMutation.isPending ||
                    uploadImageMutation.isPending ||
                    processOCRMutation.isPending

  // Fix hydration issues by ensuring client-side only rendering
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !isLoading && !user) {
      router.push('/login')
    }
  }, [mounted, isLoading, user, router])

  // Form setup
  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      transaction_type: 'EARN',
      points_change: 0,
      description: '',
      transaction_date: new Date().toISOString().split('T')[0],
      receipt_image: undefined,
      total_amount: undefined,
      business_name: '',
      financial_system_number: ''
    }
  })

  // Handle form submission
  const onSubmit = async (data: TransactionFormValues) => {
    if (!company) return;

    try {
      let receiptImageUrl: string | undefined;

      // If there's a receipt image, upload it using TanStack Query
      if (receiptFile) {
        const { url: imageUrl } = await uploadImageMutation.mutateAsync(receiptFile);
        receiptImageUrl = imageUrl;
      }

      // Create the transaction with simplified workflow (receipt number as field only)
      await createTransactionMutation.mutateAsync({
        member_id: data.member_id,
        transaction_type: data.transaction_type,
        points_change: data.points_change,
        description: data.description,
        transaction_date: data.transaction_date,
        // Include receipt data in transaction record directly
        receipt_image_url: receiptImageUrl,
        receipt_number: data.financial_system_number,
        business_name: data.business_name,
        total_amount: data.total_amount,
        // Include OCR data if available
        receipt_ocr_data: ocrData ? JSON.stringify(ocrData) : undefined,
        receipt_ocr_confidence: ocrConfidence || undefined,
        receipt_processing_status: ocrData ? 'completed' : receiptFile ? 'pending' : undefined
      });

      toast.success('Transaction added successfully');
      router.push('/transactions');
    } catch (error: unknown) {
      console.error('Error adding transaction:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add transaction';
      toast.error(errorMessage);
    }
  }

  if (!mounted || isLoading || companyLoading || membersLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in the effect
  }

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4 gap-2 text-muted-foreground hover:text-foreground"
          asChild
        >
          <Link href="/transactions">
            <ArrowLeft className="h-4 w-4" />
            Back to Transactions
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-3 gap-6">
        {/* Receipt Upload Section */}
        <Card className="border shadow-sm col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Camera className="h-5 w-5" />
              Receipt Upload (Optional)
            </CardTitle>
            <CardDescription>
              Upload a receipt to automatically extract transaction details (processing happens automatically)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <Input
                id="receipt_image"
                type="file"
                accept="image/*"
                disabled={isMutating}
                className="bg-card border-dashed border-2 h-20 text-center cursor-pointer"
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    // Validate file size (max 5MB)
                    if (file.size > 5 * 1024 * 1024) {
                      toast.error('File size must be less than 5MB');
                      e.target.value = '';
                      return;
                    }
                    // Validate file type
                    if (!file.type.startsWith('image/')) {
                      toast.error('Please select an image file');
                      e.target.value = '';
                      return;
                    }
                    setReceiptFile(file);
                    // Reset OCR state when new file is selected
                    setShowOcrResults(false);
                    setOcrData(null);
                    setEnhancedOcrData(null);
                    setOcrConfidence(null);

                    // Automatically process OCR when file is selected
                    toast.info('Processing receipt with AI...');
                    try {
                      const result = await processOCRMutation.mutateAsync({
                        file,
                        companyId: company?.id
                      });
                      const extractedData = result.data.raw_ocr_data;
                      const enhancedData = result.data.enhanced_ocr_data;
                      const transactionData = result.data.transaction_data;

                      setOcrData(extractedData);
                      setEnhancedOcrData(enhancedData || null);
                      setOcrConfidence(extractedData.confidence);

                      // Auto-populate form fields with extracted data
                      form.setValue('description', transactionData.description);
                      form.setValue('total_amount', transactionData.total_amount);
                      form.setValue('business_name', transactionData.business_name);
                      form.setValue('financial_system_number', transactionData.financial_system_number);
                      form.setValue('points_change', transactionData.suggested_points);

                      // Parse and set the date if extracted
                      if (extractedData.receipt_date) {
                        try {
                          // Convert DD/MM/YYYY to YYYY-MM-DD for input[type="date"]
                          const [day, month, year] = extractedData.receipt_date.split('/');
                          const formattedDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
                          form.setValue('transaction_date', formattedDate);
                        } catch {
                          console.warn('Failed to parse receipt date:', extractedData.receipt_date);
                        }
                      }

                      setShowOcrResults(true);
                      toast.success(`Receipt processed! Confidence: ${Math.round(extractedData.confidence * 100)}%${enhancedData?.items?.length ? ` • ${enhancedData.items.length} items detected` : ''}`);
                    } catch (error) {
                      console.error('OCR processing error:', error);
                      toast.error('Failed to process receipt. Please fill in manually.');
                    }
                  } else {
                    setReceiptFile(null);
                    setShowOcrResults(false);
                    setOcrData(null);
                    setEnhancedOcrData(null);
                    setOcrConfidence(null);
                  }
                }}
              />

              {receiptFile && (
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <Upload className="h-5 w-5 text-blue-600" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-blue-900">{receiptFile.name}</p>
                      <p className="text-xs text-blue-600">{(receiptFile.size / 1024 / 1024).toFixed(1)} MB</p>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setReceiptFile(null);
                        setShowOcrResults(false);
                        setOcrData(null);
                        setEnhancedOcrData(null);
                        setOcrConfidence(null);
                        const fileInput = document.getElementById('receipt_image') as HTMLInputElement;
                        if (fileInput) fileInput.value = '';
                      }}
                      disabled={isMutating}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Processing indicator */}
                  {processOCRMutation.isPending && (
                    <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900">Processing with AI...</p>
                        <p className="text-xs text-blue-600">Extracting receipt information</p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Enhanced OCR Results Display */}
              {showOcrResults && (enhancedOcrData || ocrData) && (
                <ReceiptSummary
                  receiptData={enhancedOcrData || {
                    // Fallback to basic data if enhanced data is not available
                    business_name: ocrData?.business_name || '',
                    financial_system_number: ocrData?.financial_system_number || '',
                    total_amount: ocrData?.total_amount || 0,
                    payment_method: ocrData?.payment_method || '',
                    receipt_date: ocrData?.receipt_date || '',
                    items: [], // No items for basic OCR
                    confidence: ocrData?.confidence || 0
                  }}
                  confidence={ocrConfidence ?? undefined}
                  className="mt-4"
                  showItems={true}
                />
              )}

              <p className="text-xs text-muted-foreground text-center">
                📸 Upload a receipt photo and AI will automatically extract and fill the transaction form
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Transaction Form */}
        <Card className="border shadow-sm col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Transaction Details
            </CardTitle>
            <CardDescription>
              {showOcrResults ? 'Review and adjust the auto-filled details' : 'Fill in the transaction information'}
            </CardDescription>
          </CardHeader>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="member_id" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Member
                </Label>
                <Controller
                  name="member_id"
                  control={form.control}
                  render={({ field }) => (
                    <MemberCombobox
                      members={members}
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder="Search and select a member..."
                      disabled={isMutating}
                    />
                  )}
                />
                {form.formState.errors.member_id && (
                  <p className="text-sm text-destructive mt-1">{form.formState.errors.member_id.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="transaction_type">Transaction Type</Label>
                  <Controller
                    name="transaction_type"
                    control={form.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isMutating}
                      >
                        <SelectTrigger className="bg-card border">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="EARN">EARN</SelectItem>
                          <SelectItem value="REDEEM">REDEEM</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {form.formState.errors.transaction_type && (
                    <p className="text-sm text-destructive mt-1">{form.formState.errors.transaction_type.message}</p>
                  )}
                </div>

                {/* AI-Enhanced Redemption Promotion */}
                {form.watch('transaction_type') === 'REDEEM' && showAiPromo && (
                  <div className="col-span-2 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 space-y-3">
                    <div className="flex flex-col gap-3">
                      <div className="bg-blue-100 rounded-full p-2 self-start">
                        <Wand2 className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="font-semibold text-blue-900">Try our Automatic Redemption Flow!</h3>
                        <p className="text-sm text-blue-700">
                          Get intelligent validation, reward recommendations, and real-time eligibility checking with our new AI-powered redemption system.
                        </p>
                        <div className="flex flex-col gap-2">
                          <Link href="/transactions/redeem">
                            <Button size="sm" variant="default" className="bg-blue-600 hover:bg-blue-700">
                              <Wand2 className="h-4 w-4 mr-2" />
                              Use Automatic Redemption
                            </Button>
                          </Link>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setShowAiPromo(false)}
                          >
                            Continue here
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="points_change" className="flex items-center gap-2">
                    <Calculator className="h-4 w-4" />
                    Points
                  </Label>
                  <Input
                    id="points_change"
                    type="number"
                    min="1"
                    disabled={isMutating}
                    className="bg-card border"
                    {...form.register('points_change', { valueAsNumber: true })}
                  />
                  {form.formState.errors.points_change && (
                    <p className="text-sm text-destructive mt-1">{form.formState.errors.points_change.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  disabled={isMutating}
                  className="bg-card border"
                  placeholder="e.g., Haircut service, Product purchase, etc."
                  {...form.register('description')}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-destructive mt-1">{form.formState.errors.description.message}</p>
                )}
              </div>

              {/* Auto-Extracted Fields (only show if OCR was successful) */}
              {showOcrResults && (
                <div className="space-y-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Wand2 className="h-4 w-4 text-blue-600" />
                    <h4 className="text-sm font-medium text-blue-800">Auto-Extracted Information</h4>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="business_name">Business Name</Label>
                      <Input
                        id="business_name"
                        disabled={isMutating}
                        className="bg-white border"
                        placeholder="Business name from receipt"
                        {...form.register('business_name')}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="total_amount">Receipt Amount</Label>
                      <Input
                        id="total_amount"
                        type="number"
                        step="0.01"
                        disabled={isMutating}
                        className="bg-white border"
                        placeholder="0.00"
                        {...form.register('total_amount', { valueAsNumber: true })}
                      />
                    </div>

                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="financial_system_number">Receipt Number (FS No)</Label>
                      <Input
                        id="financial_system_number"
                        disabled={isMutating}
                        className="bg-white border"
                        placeholder="Receipt/FS number"
                        {...form.register('financial_system_number')}
                      />
                    </div>
                  </div>

                  <p className="text-xs text-blue-600">
                    ✨ These fields were auto-filled from your receipt. You can edit them if needed.
                  </p>
                </div>
              )}

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="transaction_date">Transaction Date</Label>
                <Input
                  id="transaction_date"
                  type="date"
                  disabled={isMutating}
                  className="bg-card border"
                  {...form.register('transaction_date')}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" disabled={isMutating} className="border" asChild>
                <Link href="/transactions">Cancel</Link>
              </Button>
              <Button type="submit" disabled={isMutating} className="min-w-[140px]">
                {isMutating ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {uploadImageMutation.isPending ? 'Uploading...' :
                     processOCRMutation.isPending ? 'Processing...' :
                     'Adding Transaction...'}
                  </>
                ) : (
                  'Add Transaction'
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}
