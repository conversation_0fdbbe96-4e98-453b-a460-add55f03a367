import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Use optimized single-query function instead of 5 sequential COUNT queries
    const { data: onboardingData, error: onboardingError } = await supabase.rpc('get_onboarding_status_optimized', {
      p_user_id: userId
    })

    if (onboardingError) {
      console.error('Error fetching onboarding status:', onboardingError)
      return NextResponse.json(
        { error: `Failed to fetch onboarding status: ${onboardingError.message}` },
        { status: 500 }
      )
    }

    if (!onboardingData) {
      // Fallback to basic status if function doesn't exist yet
      return NextResponse.json({
        hasCompany: false,
        isOnboardingComplete: false,
        nextSteps: ['create_company']
      })
    }

    return NextResponse.json(onboardingData)

  } catch (error) {
    console.error('Unexpected error in onboarding status:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId, step } = await request.json()

    if (!userId || !step) {
      return NextResponse.json(
        { error: 'User ID and step are required' },
        { status: 400 }
      )
    }

    // For now, just return success since we don't have the setup_wizard_step column yet
    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Setup step update error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
