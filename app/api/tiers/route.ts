import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getServiceRoleClient } from "@/lib/supabase";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";

// Schema for tier creation/update
const tierSchema = z.object({
  tier_name: z.string().min(1, "Tier name is required"),
  minimum_points: z.coerce.number().int().min(0, "Minimum points cannot be negative"),
  benefits_description: z.string().min(1, "Benefits description is required"),
});

// GET /api/tiers - Get all tiers for the current company
export async function GET(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set: () => {},
          remove: () => {},
        },
      }
    );

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await serverSupabase.auth.getUser();

    if (!user || authError) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Use service role client for database operations
    const supabase = getServiceRoleClient();
    
    // Get companyId from query parameter or from user's company
    const { searchParams } = new URL(request.url);
    let companyId = searchParams.get('companyId');
    
    // If no companyId provided in query params, get it from the user's company
    if (!companyId) {
      // Get the user's company
      const { data: company, error: companyError } = await supabase
        .from("companies")
        .select("id")
        .eq("administrator_id", user.id)
        .single();

      if (companyError || !company) {
        return NextResponse.json(
          { error: "Company not found" },
          { status: 404 }
        );
      }

      companyId = company.id;
    }

    const { data, error } = await supabase
      .from("tier_definitions")
      .select("*")
      .eq("company_id", companyId)
      .order("minimum_points", { ascending: true });

    if (error) {
      console.error("Error fetching tiers:", error);
      return NextResponse.json(
        { error: "Failed to fetch tiers" },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/tiers - Create a new tier
export async function POST(request: NextRequest) {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies();
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set: () => {},
          remove: () => {},
        },
      }
    );

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await serverSupabase.auth.getUser();

    if (!user || authError) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log("Received tier data:", JSON.stringify(body, null, 2));

    // Use companyId from body if provided, otherwise get from user's company
    let companyId = body.companyId;
    const tierData = { ...body };
    delete tierData.companyId;

    // If no companyId provided, get it from the user's company
    if (!companyId) {
      // Use service role client for database operations
      const supabase = getServiceRoleClient();

      // Get the user's company
      const { data: company, error: companyError } = await supabase
        .from("companies")
        .select("id")
        .eq("administrator_id", user.id)
        .single();

      if (companyError || !company) {
        return NextResponse.json(
          { error: "Company not found" },
          { status: 404 }
        );
      }

      companyId = company.id;
    }

    // Check if companyId is available
    if (!companyId) {
      console.log("Unable to determine company ID");
      return NextResponse.json(
        { error: "Company ID is required" },
        { status: 400 }
      );
    }

    console.log("Tier data for validation:", JSON.stringify(tierData, null, 2));

    // Validate tier data
    const validationResult = tierSchema.safeParse(tierData);
    if (!validationResult.success) {
      console.log("Validation failed:", JSON.stringify(validationResult.error.format(), null, 2));
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Use service role client for database operations
    const supabase = getServiceRoleClient();
    const finalTierData = {
      ...validationResult.data,
      company_id: companyId,
    };

    const { data, error } = await supabase
      .from("tier_definitions")
      .insert(finalTierData)
      .select()
      .single();

    if (error) {
      console.error("Error creating tier:", error);
      return NextResponse.json(
        { error: "Failed to create tier" },
        { status: 500 }
      );
    }

    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
