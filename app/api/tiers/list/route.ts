import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

export async function GET(request: Request) {
  try {
    // Parse URL to get query parameters
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Get tiers and member counts with a single optimized query
    const { data: result, error } = await supabase.rpc('get_tiers_with_member_counts', {
      company_id_param: companyId
    });

    if (error) {
      console.error('RPC function not available, falling back to manual calculation:', error);

      // Fallback: Get tiers first, then calculate counts manually
      const { data: tiers, error: tiersError } = await supabase
        .from('tier_definitions')
        .select('*')
        .eq('company_id', companyId)
        .order('minimum_points', { ascending: true });

      if (tiersError) {
        console.error('Error fetching tiers:', tiersError);
        return NextResponse.json({ error: tiersError.message }, { status: 500 });
      }

      if (!tiers || tiers.length === 0) {
        return NextResponse.json({ data: [] });
      }

      // Get all members for this company
      const { data: members, error: membersError } = await supabase
        .from('loyalty_members')
        .select('lifetime_points')
        .eq('company_id', companyId);

      if (membersError) {
        console.error('Error fetching members:', membersError);
        return NextResponse.json({ error: membersError.message }, { status: 500 });
      }

      // Calculate member counts manually
      const tiersWithCounts = tiers.map((tier, index) => {
        const currentMinPoints = tier.minimum_points;
        const nextTier = tiers[index + 1];
        const nextMinPoints = nextTier ? nextTier.minimum_points : Number.MAX_SAFE_INTEGER;

        const memberCount = members?.filter(member =>
          member.lifetime_points >= currentMinPoints &&
          member.lifetime_points < nextMinPoints
        ).length || 0;

        console.log(`Tier ${tier.tier_name} (${currentMinPoints}-${nextMinPoints}): ${memberCount} members`);

        return { ...tier, member_count: memberCount };
      });

      console.log('Final tiers with counts:', tiersWithCounts.map(t => ({ name: t.tier_name, count: t.member_count })));

      return NextResponse.json({ data: tiersWithCounts });
    }

    return NextResponse.json({ data: result || [] });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
