import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getServiceRoleClient } from "@/lib/supabase";
import { getCompanyIdFromSession } from "@/lib/auth";

// Schema for tier update
const tierUpdateSchema = z.object({
  tier_name: z.string().min(1, "Tier name is required").optional(),
  minimum_points: z.number().int().min(0, "Minimum points cannot be negative").optional(),
  benefits_description: z.string().min(1, "Benefits description is required").optional(),
  company_id: z.string().uuid().optional(),
  companyId: z.string().uuid().optional(), // Accept both formats
});

// GET /api/tiers/[id] - Get a specific tier
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const tierId = (await params).id;
    const supabase = getServiceRoleClient();

    // Get companyId from query param or from session
    const { searchParams } = new URL(request.url);
    const queryCompanyId = searchParams.get('companyId');

    // First try to get it from session, then fallback to query param
    let companyId = await getCompanyIdFromSession(supabase);

    // If no company ID from session but we have it in query params, use that
    if (!companyId && queryCompanyId) {
      console.log(`Using companyId from query: ${queryCompanyId}`);
      companyId = queryCompanyId;
    }

    if (!companyId) {
      return NextResponse.json(
        { error: "Unauthorized - No company ID available" },
        { status: 401 }
      );
    }

    const { data, error } = await supabase
      .from("tier_definitions")
      .select("*")
      .eq("id", tierId)
      .eq("company_id", companyId)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        return NextResponse.json(
          { error: "Tier not found" },
          { status: 404 }
        );
      }
      console.error("Error fetching tier:", error);
      return NextResponse.json(
        { error: "Failed to fetch tier" },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in tier GET:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

// PATCH /api/tiers/[id] - Update a tier
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const tierId = (await params).id;
    const supabase = getServiceRoleClient();
    const companyId = await getCompanyIdFromSession(supabase);

    if (!companyId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if tier exists and belongs to the company
    const { error: fetchError } = await supabase
      .from("tier_definitions")
      .select("*")
      .eq("id", tierId)
      .eq("company_id", companyId)
      .single();

    if (fetchError) {
      if (fetchError.code === "PGRST116") {
        return NextResponse.json(
          { error: "Tier not found" },
          { status: 404 }
        );
      }
      console.error("Error fetching tier:", fetchError);
      return NextResponse.json(
        { error: "Failed to fetch tier" },
        { status: 500 }
      );
    }

    // Parse and validate update data
    const tierData = await request.json();
    const validationResult = tierUpdateSchema.safeParse(tierData);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid tier data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Update tier
    const { data, error } = await supabase
      .from("tier_definitions")
      .update(validationResult.data)
      .eq("id", tierId)
      .eq("company_id", companyId)
      .select()
      .single();

    if (error) {
      console.error("Error updating tier:", error);
      return NextResponse.json(
        { error: "Failed to update tier" },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in tier PATCH:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

// PUT /api/tiers/[id] - Update a tier (alias for PATCH)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const tierId = (await params).id;

    // Use service role client to bypass RLS since we're doing explicit company_id checks
    const supabase = getServiceRoleClient();

    const body = await request.json();
    console.log('=== Tier Update API Called ===');
    console.log('Tier ID:', tierId);
    console.log('Request body:', JSON.stringify(body, null, 2));

    // Get companyId from multiple sources in order of preference:
    // 1. Request body (explicit)
    // 2. Query parameter
    // 3. Session (fallback)
    const { searchParams } = new URL(request.url);
    const queryCompanyId = searchParams.get('companyId');

    let companyId = body.company_id || body.companyId || queryCompanyId;
    console.log('Company ID determination:');
    console.log('- body.company_id:', body.company_id);
    console.log('- body.companyId:', body.companyId);
    console.log('- queryCompanyId:', queryCompanyId);
    console.log('- Final companyId before session check:', companyId);

    if (!companyId) {
      console.log('No company ID found, trying session...');
      try {
        // For session fallback, use regular client
        const sessionClient = getServiceRoleClient();
        companyId = await getCompanyIdFromSession(sessionClient);
        console.log('Session company ID:', companyId);
      } catch (error) {
        console.log('Session company ID retrieval failed:', error);
      }
    } else {
      console.log('Using company ID from request:', companyId);
    }

    if (!companyId) {
      return NextResponse.json(
        { error: "Unauthorized - No company ID available" },
        { status: 401 }
      );
    }

    // Parse and validate update data
    const validationResult = tierUpdateSchema.safeParse(body);

    if (!validationResult.success) {
      console.log('Validation failed:', JSON.stringify(validationResult.error.format(), null, 2));
      return NextResponse.json(
        { error: "Invalid tier data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    console.log('Final update data:', JSON.stringify(validationResult.data, null, 2));

    // Remove company_id fields from update data as they're not part of the table
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { company_id: _, companyId: __, ...updateData } = validationResult.data;

    // Update tier (if tier doesn't exist or doesn't belong to company, this will fail safely)
    const { data, error } = await supabase
      .from("tier_definitions")
      .update(updateData)
      .eq("id", tierId)
      .eq("company_id", companyId)
      .select()
      .single();

    if (error) {
      console.error("Error updating tier:", error);
      if (error.code === "PGRST116") {
        return NextResponse.json(
          { error: "Tier not found or access denied" },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: "Failed to update tier" },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in tier PUT:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

// DELETE /api/tiers/[id] - Delete a tier
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const tierId = (await params).id;
    const supabase = getServiceRoleClient();
    const companyId = await getCompanyIdFromSession(supabase);

    if (!companyId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check if tier exists and belongs to the company
    const { error: fetchError } = await supabase
      .from("tier_definitions")
      .select("*")
      .eq("id", tierId)
      .eq("company_id", companyId)
      .single();

    if (fetchError) {
      if (fetchError.code === "PGRST116") {
        return NextResponse.json(
          { error: "Tier not found" },
          { status: 404 }
        );
      }
      console.error("Error fetching tier:", fetchError);
      return NextResponse.json(
        { error: "Failed to check tier existence" },
        { status: 500 }
      );
    }

    const { error } = await supabase
      .from("tier_definitions")
      .delete()
      .eq("id", tierId)
      .eq("company_id", companyId);

    if (error) {
      console.error("Error deleting tier:", error);
      return NextResponse.json(
        { error: "Failed to delete tier" },
        { status: 500 }
      );
    }

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error in tier DELETE:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
