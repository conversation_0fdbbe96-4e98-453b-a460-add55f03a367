import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    const supabase = getServiceRoleClient()

    // Get company data to access points earning ratio
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('points_earning_ratio')
      .eq('id', companyId)
      .single()

    if (companyError) {
      console.error('Error fetching company data:', companyError)
      return NextResponse.json(
        { error: 'Failed to fetch company data' },
        { status: 500 }
      )
    }

    // Get time ranges for analysis
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const sixtyDaysAgo = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)

    // Member Acquisition Rate (last 30 days vs previous 30 days)
    const { data: memberAcquisition, error: memberError } = await supabase
      .from('loyalty_members')
      .select('registration_date')
      .eq('company_id', companyId)
      .gte('registration_date', sixtyDaysAgo.toISOString())

    if (memberError) {
      console.error('Error fetching member acquisition:', memberError)
    }

    const last30DaysMembers = memberAcquisition?.filter(m =>
      new Date(m.registration_date) >= thirtyDaysAgo
    ).length || 0

    const previous30DaysMembers = memberAcquisition?.filter(m =>
      new Date(m.registration_date) < thirtyDaysAgo && new Date(m.registration_date) >= sixtyDaysAgo
    ).length || 0

    const acquisitionGrowthRate = previous30DaysMembers > 0
      ? ((last30DaysMembers - previous30DaysMembers) / previous30DaysMembers) * 100
      : last30DaysMembers > 0 ? 100 : 0

    // Member Retention Analysis (members who registered 30+ days ago and are still active)
    // First get members who registered more than 30 days ago
    const { data: eligibleMembers, error: eligibleError } = await supabase
      .from('loyalty_members')
      .select('id')
      .eq('company_id', companyId)
      .lt('registration_date', thirtyDaysAgo.toISOString())

    if (eligibleError) {
      console.error('Error fetching eligible members:', eligibleError)
    }

    // Then get which of those have been active in the last 30 days
    const eligibleMemberIds = eligibleMembers?.map(m => m.id) || []
    let activeEligibleCount = 0

    if (eligibleMemberIds.length > 0) {
      const { data: activeMembers, error: activeError } = await supabase
        .from('points_transactions')
        .select('member_id')
        .eq('company_id', companyId)
        .gte('transaction_date', thirtyDaysAgo.toISOString())
        .in('member_id', eligibleMemberIds)

      if (activeError) {
        console.error('Error fetching active members:', activeError)
      }

      activeEligibleCount = new Set(activeMembers?.map(t => t.member_id)).size || 0
    }

    const eligibleCount = eligibleMembers?.length || 0
    const retentionRate = eligibleCount > 0 ? (activeEligibleCount / eligibleCount) * 100 : 0

    // Average Transaction Value (last 30 days) - include all EARN transactions
    const { data: transactionValues, error: transactionError } = await supabase
      .from('points_transactions')
      .select('total_amount, points_change')
      .eq('company_id', companyId)
      .eq('transaction_type', 'EARN')
      .gte('transaction_date', thirtyDaysAgo.toISOString())

    if (transactionError) {
      console.error('Error fetching transaction values:', transactionError)
    }    // Calculate average transaction value using actual amounts or estimated from points
    // Use company's points earning ratio (e.g., 1.00 means 1 point = 1 ETB)
    const pointsEarningRatio = Number(companyData.points_earning_ratio) || 1.0
    const ETB_PER_POINT = 1 / pointsEarningRatio // If ratio is 1.0, then 1 point = 1 ETB

    let totalTransactionValue = 0
    let transactionCount = 0

    if (transactionValues && transactionValues.length > 0) {
      transactionValues.forEach(t => {
        if (t.total_amount !== null) {
          // Use actual transaction amount
          totalTransactionValue += Number(t.total_amount)
        } else if (t.points_change && t.points_change > 0) {
          // Estimate transaction value from points earned using company's ratio
          totalTransactionValue += t.points_change * ETB_PER_POINT
        }
        transactionCount++
      })
    }

    const avgTransactionValue = transactionCount > 0 ? totalTransactionValue / transactionCount : 0

    // Customer Lifetime Value (simplified calculation - average lifetime points for this company)
    const { data: memberLifetimeData, error: lifetimeError } = await supabase
      .from('loyalty_members')
      .select('lifetime_points, registration_date')
      .eq('company_id', companyId)
      .gt('lifetime_points', 0)

    if (lifetimeError) {
      console.error('Error fetching lifetime data:', lifetimeError)
    }

    const avgCustomerLifetimeValue = memberLifetimeData && memberLifetimeData.length > 0
      ? memberLifetimeData.reduce((sum, m) => sum + (m.lifetime_points || 0), 0) / memberLifetimeData.length
      : 0

    // Program ROI Calculation (enhanced: total customer value vs. total rewards given)
    const { data: rewardCosts, error: rewardError } = await supabase
      .from('reward_redemptions')
      .select('applied_value, points_used')
      .eq('company_id', companyId)

    if (rewardError) {
      console.error('Error fetching reward costs:', rewardError)
    }

    // Calculate total reward cost, estimating value for zero-cost redemptions
    let totalRewardCost = 0
    let estimatedRewardCost = 0

    if (rewardCosts && rewardCosts.length > 0) {
      // Sum actual applied values
      totalRewardCost = rewardCosts.reduce((sum, r) => sum + (Number(r.applied_value) || 0), 0)

      // For redemptions with zero applied_value, estimate cost as points used * ETB_PER_POINT
      const zeroValueRedemptions = rewardCosts.filter(r => Number(r.applied_value) === 0)
      estimatedRewardCost = zeroValueRedemptions.reduce((sum, r) => sum + (r.points_used * ETB_PER_POINT || 0), 0)
    }

    const totalEstimatedRewardCost = totalRewardCost + estimatedRewardCost
    const totalCustomerValue = memberLifetimeData?.reduce((sum, m) => sum + (m.lifetime_points || 0), 0) || 0
    const totalCustomerValueETB = totalCustomerValue * ETB_PER_POINT

    // Calculate ROI with more accurate cost estimation
    const programROI = totalEstimatedRewardCost > 0
      ? ((totalCustomerValueETB - totalEstimatedRewardCost) / totalEstimatedRewardCost) * 100
      : totalCustomerValueETB > 0 ? 100 : 0

    // Member Engagement Score (transactions per active member in last 30 days)
    const { data: recentTransactions, error: engagementError } = await supabase
      .from('points_transactions')
      .select('member_id')
      .eq('company_id', companyId)
      .gte('transaction_date', thirtyDaysAgo.toISOString())

    if (engagementError) {
      console.error('Error fetching engagement data:', engagementError)
    }

    const uniqueActiveMembers = new Set(recentTransactions?.map(t => t.member_id)).size || 0
    const totalRecentTransactions = recentTransactions?.length || 0
    const engagementScore = uniqueActiveMembers > 0
      ? totalRecentTransactions / uniqueActiveMembers
      : 0

    // Reward Effectiveness (enhanced with more detailed metrics)
    const { data: rewardStats, error: effectivenessError } = await supabase
      .from('rewards')
      .select(`
        id,
        title,
        points_required,
        description,
        created_at,
        reward_redemptions(id, points_used, created_at)
      `)
      .eq('company_id', companyId)
      .eq('is_active', true)

    if (effectivenessError) {
      console.error('Error fetching reward effectiveness:', effectivenessError)
    }

    const totalRedemptions = rewardStats?.reduce((sum, reward) => sum + (reward.reward_redemptions?.length || 0), 0) || 0

    const rewardEffectiveness = rewardStats?.map(reward => {
      const redemptions = reward.reward_redemptions?.length || 0
      const recentRedemptions = reward.reward_redemptions?.filter(r =>
        new Date(r.created_at) >= thirtyDaysAgo
      ).length || 0

      // Calculate market share
      const marketShare = totalRedemptions > 0 ? (redemptions / totalRedemptions) * 100 : 0

      // Calculate value efficiency (redemptions per 100 points required)
      const valueEfficiency = reward.points_required > 0 ? (redemptions / reward.points_required) * 100 : 0

      // Determine popularity category
      const popularityLevel = redemptions >= 5 ? 'high' : redemptions >= 2 ? 'medium' : redemptions === 0 ? 'unused' : 'low'

      return {
        id: reward.id,
        title: reward.title,
        pointsRequired: reward.points_required,
        redemptions,
        recentRedemptions,
        marketShare: Math.round(marketShare * 10) / 10,
        valueEfficiency: Math.round(valueEfficiency * 10) / 10,
        popularityLevel,
        effectivenessScore: reward.points_required > 0
          ? (redemptions / reward.points_required) * 1000
          : 0
      }
    }).sort((a, b) => b.redemptions - a.redemptions) || []

    return NextResponse.json({
      data: {
        // Acquisition & Growth
        memberAcquisitionRate: {
          last30Days: last30DaysMembers,
          previous30Days: previous30DaysMembers,
          growthRate: Math.round(acquisitionGrowthRate * 10) / 10,
          trend: acquisitionGrowthRate >= 0 ? 'up' : 'down'
        },

        // Retention
        memberRetention: {
          retentionRate: Math.round(retentionRate * 10) / 10,
          benchmark: 60, // Industry benchmark for loyalty programs
          status: retentionRate >= 60 ? 'good' : retentionRate >= 40 ? 'fair' : 'poor'
        },

        // Transaction Analytics
        averageTransactionValue: {
          value: Math.round(avgTransactionValue * 100) / 100,
          currency: 'ETB',
          sampleSize: transactionCount
        },

        // Customer Value
        customerLifetimeValue: {
          avgPoints: Math.round(avgCustomerLifetimeValue),
          estimatedValue: Math.round(avgCustomerLifetimeValue * ETB_PER_POINT * 100) / 100, // Using company's points earning ratio
          currency: 'ETB'
        },

        // Program Performance
        programROI: {
          roi: Math.round(programROI * 10) / 10,
          totalValue: Math.round(totalCustomerValueETB),
          totalCost: Math.round(totalEstimatedRewardCost * 100) / 100,
          actualCost: Math.round(totalRewardCost * 100) / 100,
          estimatedCost: Math.round(estimatedRewardCost * 100) / 100,
          status: programROI >= 200 ? 'excellent' : programROI >= 100 ? 'good' : programROI >= 0 ? 'break-even' : 'loss'
        },

        // Engagement
        memberEngagement: {
          score: Math.round(engagementScore * 10) / 10,
          activeMembers: uniqueActiveMembers,
          totalTransactions: totalRecentTransactions,
          frequency: engagementScore >= 3 ? 'high' : engagementScore >= 1.5 ? 'medium' : 'low'
        },

        // Reward Performance (enhanced)
        rewardEffectiveness: {
          rewards: rewardEffectiveness,
          topPerforming: rewardEffectiveness[0] || null, // Already sorted by redemptions
          totalRewards: rewardEffectiveness.length,
          totalRedemptions,
          highPerformingCount: rewardEffectiveness.filter(r => r.redemptions >= 3).length,
          unusedRewardsCount: rewardEffectiveness.filter(r => r.redemptions === 0).length,
          averageRedemptions: rewardEffectiveness.length > 0
            ? Math.round((totalRedemptions / rewardEffectiveness.length) * 10) / 10
            : 0,
          mostEfficientReward: rewardEffectiveness.sort((a, b) => b.valueEfficiency - a.valueEfficiency)[0] || null
        },

        // Meta
        calculatedAt: new Date().toISOString(),
        timeRange: '30 days'
      }
    })

  } catch (error) {
    console.error('Unexpected error in business metrics:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
