import { NextRequest, NextResponse } from 'next/server'
import { getServerSession, getUserRole } from '@/lib/auth-server'

/**
 * Optimized endpoint that returns the user's role for a specific company
 * Uses standardized server authentication with Supabase SSR
 */
export async function GET(request: NextRequest) {
  try {
    // Get company ID from query string
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Use the standardized server session approach
    const { session } = await getServerSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }
    
    const userId = session.user.id

    // Get user role using the centralized function
    const role = await getUserRole(userId, companyId)
    
    return NextResponse.json({ role })
  } catch (error) {
    console.error('Unexpected error in user-role API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
