import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Validation schema for receipt submission
const receiptSchema = z.object({
  loyalty_id: z.string().min(1, { message: 'Loyalty ID is required' }),
  company_id: z.string().uuid({ message: 'Valid company ID is required' }),
  amount: z.number().positive({ message: 'Amount must be positive' }),
  receipt_date: z.string().optional(),
  receipt_number: z.string().optional(),
  notes: z.string().optional(),
})

// Used for type checking the request body
// eslint-disable-next-line @typescript-eslint/no-unused-vars
type ReceiptData = z.infer<typeof receiptSchema>

/**
 * POST /api/receipts - Submit a receipt and earn points
 * Creates a points transaction based on receipt amount
 */
export async function POST(request: NextRequest) {
  try {
    // Get auth session from cookies
    // Use synchronous cookies() call to avoid Promise<ReadonlyRequestCookies> type error
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            // Use synchronous get method
            return cookieStore.get(name)?.value
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          set(_name: string, _value: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes as we can't set cookies in the response directly
            // We're just satisfying the type requirements
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          remove(_name: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes as we can't remove cookies in the response directly
            // We're just satisfying the type requirements
          },
        },
      }
    )

    // Check authentication using getUser() for server-side security
    const { data: { user }, error: authError } = await serverSupabase.auth.getUser()
    if (!user || authError) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = receiptSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const receiptData = validationResult.data

    // Initialize server-side Supabase client
    const supabase = getServiceRoleClient() // Use server-side client

    // Find the member by loyalty_id
    const { data: member, error: memberError } = await supabase
      .from('loyalty_members')
      .select('id, loyalty_tier')
      .eq('loyalty_id', receiptData.loyalty_id)
      .eq('company_id', receiptData.company_id)
      .single()

    if (memberError || !member) {
      return NextResponse.json(
        { error: 'Member not found with the provided loyalty ID' },
        { status: 404 }
      )
    }

    // Get company settings for points calculation
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('points_earning_ratio, points_expiration_days')
      .eq('id', receiptData.company_id)
      .single()

    if (companyError || !company) {
      return NextResponse.json(
        { error: 'Company not found or error retrieving company settings' },
        { status: 500 }
      )
    }

    // Calculate points based on amount and company ratio
    const pointsEarningRatio = company.points_earning_ratio || 1.0
    const pointsEarned = Math.floor(receiptData.amount * pointsEarningRatio)

    // Apply tier multiplier if applicable
    let finalPoints = pointsEarned
    if (member.loyalty_tier) {
      const { data: tierData } = await supabase
        .from('tier_definitions')
        .select('multiplier')
        .eq('company_id', receiptData.company_id)
        .eq('tier_name', member.loyalty_tier)
        .maybeSingle()

      if (tierData && tierData.multiplier && tierData.multiplier > 1) {
        finalPoints = Math.floor(pointsEarned * tierData.multiplier)
      }
    }

    // Calculate expiration date
    const expirationDays = company.points_expiration_days || 365
    const now = new Date()
    const expirationDate = new Date(now)
    expirationDate.setDate(expirationDate.getDate() + expirationDays)

    // Check for duplicate receipt number if provided
    if (receiptData.receipt_number) {
      const { data: existingReceipt, error: receiptCheckError } = await supabase
        .from('points_transactions')
        .select('id, receipt_number')
        .eq('company_id', receiptData.company_id)
        .eq('receipt_number', receiptData.receipt_number)
        .maybeSingle()

      if (existingReceipt) {
        return NextResponse.json(
          { error: 'Duplicate receipt', message: `A receipt with FS No. ${receiptData.receipt_number} has already been submitted.` },
          { status: 409 } // 409 Conflict status code
        )
      }

      if (receiptCheckError) {
        console.error('Error checking for duplicate receipt:', receiptCheckError)
      }
    }

    // Create points transaction
    const { data: transaction, error: transactionError } = await supabase
      .from('points_transactions')
      .insert({
        member_id: member.id,
        transaction_type: 'earn',
        points: finalPoints,
        transaction_date: receiptData.receipt_date || now.toISOString(),
        receipt_amount: receiptData.amount,
        receipt_number: receiptData.receipt_number,
        description: `Points earned from receipt${receiptData.receipt_number ? ` #${receiptData.receipt_number}` : ''}`,
        notes: receiptData.notes,
        expiration_date: expirationDate.toISOString(),
      })
      .select()
      .single()

    if (transactionError) {
      return NextResponse.json(
        { error: 'Failed to create points transaction', details: transactionError.message },
        { status: 500 }
      )
    }

    // Get updated member data with new points balance
    const { data: updatedMember } = await supabase
      .from('loyalty_members')
      .select('id, name, loyalty_id, available_points, lifetime_points')
      .eq('id', member.id)
      .single()

    return NextResponse.json({
      success: true,
      transaction,
      member: updatedMember,
      pointsEarned: finalPoints,
    })
  } catch (error: unknown) {
    console.error('Error in /api/receipts:', error)
    const errorMessage = error instanceof Error ? error.message : 'Internal server error'
    return NextResponse.json({ error: errorMessage }, { status: 500 })
  }
}
