import { NextRequest, NextResponse } from 'next/server';
import { extractReceiptData, convertReceiptToTransactionData } from '@/lib/receipt-ocr';
import { extractReceiptDataEnhanced, convertToLegacyFormat, type MatchedItem } from '@/lib/receipt-ocr-enhanced';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const companyId = formData.get('company_id') as string;

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: 'Image file too large (max 10MB)' }, { status: 400 });
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    let extractedData;
    let enhancedData = null;
    let matchedItems: MatchedItem[] = [];

    // Try enhanced processing if company ID is provided
    if (companyId) {
      const supabase = await createClient();

      // Check if company has templates for enhanced processing
      const { data: templateExists } = await supabase
        .from('receipt_templates')
        .select('id')
        .eq('company_id', companyId)
        .eq('is_active', true)
        .limit(1);

      if (templateExists && templateExists.length > 0) {
        try {
          console.log('Using enhanced OCR processing with template support...');
          enhancedData = await extractReceiptDataEnhanced(buffer, companyId);
          extractedData = convertToLegacyFormat(enhancedData);
          matchedItems = enhancedData.matched_items || [];
        } catch (enhancedError) {
          console.warn('Enhanced OCR failed, falling back to basic OCR:', enhancedError);
          extractedData = await extractReceiptData(buffer);
        }
      } else {
        // Use enhanced processing without template
        try {
          console.log('Using enhanced OCR processing without template...');
          enhancedData = await extractReceiptDataEnhanced(buffer, companyId);
          extractedData = convertToLegacyFormat(enhancedData);
        } catch (enhancedError) {
          console.warn('Enhanced OCR failed, falling back to basic OCR:', enhancedError);
          extractedData = await extractReceiptData(buffer);
        }
      }
    } else {
      // Fallback to basic OCR if no company ID
      console.log('Using basic OCR processing...');
      extractedData = await extractReceiptData(buffer);
    }

    // Convert to transaction-friendly format
    const transactionData = convertReceiptToTransactionData(extractedData);

    // Return enhanced response with items if available
    const response = {
      success: true,
      data: {
        raw_ocr_data: extractedData,
        enhanced_ocr_data: enhancedData, // Include the enhanced data with items
        transaction_data: transactionData,
        matched_items: matchedItems,
        confidence: extractedData.confidence,
        processing_status: 'completed'
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('OCR processing error:', error);

    return NextResponse.json(
      {
        error: 'Failed to process receipt image',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
