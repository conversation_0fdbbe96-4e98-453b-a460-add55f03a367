import { google } from '@ai-sdk/google'
import { generateText } from 'ai'
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { 
      message, 
      targetAudience, 
      businessType, 
      campaignGoal,
      memberInsights 
    } = await request.json()

    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const prompt = `
    You are a marketing optimization expert. Analyze this marketing campaign message and provide actionable improvements.

    Current Message:
    "${message}"

    Campaign Context:
    - Target Audience: ${targetAudience}
    - Business Type: ${businessType}
    - Campaign Goal: ${campaignGoal}
    - Member Insights: ${JSON.stringify(memberInsights)}

    Provide optimization suggestions in the following JSON format:
    {
      "overallScore": number (1-10),
      "improvements": [
        {
          "category": "string (e.g., 'Personalization', 'Call-to-Action', 'Tone')",
          "suggestion": "string",
          "impact": "string (High/Medium/Low)",
          "reason": "string"
        }
      ],
      "optimizedMessage": "string (improved version of the message)",
      "keyStrengths": ["string"],
      "potentialIssues": ["string"]
    }

    Focus on:
    1. Personalization opportunities
    2. Call-to-action effectiveness
    3. Message clarity and engagement
    4. Loyalty program best practices
    5. Audience-specific language
    `

    const { text } = await generateText({
      model: google('gemini-1.5-flash'),
      prompt,
      temperature: 0.3,
      maxTokens: 1000,
    })

    let analysis
    try {
      analysis = JSON.parse(text)
    } catch {
      // Fallback if JSON parsing fails
      analysis = {
        overallScore: 7,
        improvements: [
          {
            category: "Personalization",
            suggestion: "Add member name and tier-specific benefits",
            impact: "High",
            reason: "Personalized messages increase engagement by 26%"
          }
        ],
        optimizedMessage: message,
        keyStrengths: ["Clear messaging"],
        potentialIssues: ["Could be more personalized"]
      }
    }

    return NextResponse.json(analysis)

  } catch (error) {
    console.error('Error optimizing campaign:', error)
    return NextResponse.json(
      { error: 'Failed to optimize campaign' },
      { status: 500 }
    )
  }
}
