import { google } from '@ai-sdk/google'
import { generateText } from 'ai'
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { 
      campaignType, 
      targetAudience, 
      businessName, 
      businessType, 
      tone, 
      keyMessage, 
      callToAction,
      memberData 
    } = await request.json()

    // Get user's company context
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Build context for AI
    const audienceContext = memberData ? `
    Target audience insights:
    - Total members: ${memberData.totalMembers}
    - Average tier: ${memberData.averageTier}
    - Top spending segment: ${memberData.topSpenders}
    - Engagement rate: ${memberData.engagementRate}%
    ` : ''

    const prompt = `
    You are an expert marketing copywriter specializing in loyalty program communications. 
    Create a compelling marketing message for a ${campaignType} campaign.

    Business Context:
    - Business Name: ${businessName}
    - Business Type: ${businessType}
    - Target Audience: ${targetAudience}
    - Tone: ${tone}
    - Key Message: ${keyMessage}
    - Call to Action: ${callToAction}
    ${audienceContext}

    Requirements:
    1. Keep the message concise and engaging (150-300 words)
    2. Use the specified tone consistently
    3. Include personalization elements where appropriate
    4. Make the call-to-action clear and compelling
    5. Ensure the message aligns with loyalty program best practices
    6. Include emojis sparingly but effectively
    7. Make it feel personal and valuable to the recipient

    Generate a marketing message that will drive engagement and action from loyalty program members.
    `

    const { text } = await generateText({
      model: google('gemini-1.5-flash'),
      prompt,
      temperature: 0.7,
      maxTokens: 500,
    })

    // Also generate subject line suggestions
    const subjectPrompt = `
    Based on this marketing message: "${text}"
    
    Generate 3 compelling email subject lines that would work well for this ${campaignType} campaign.
    Make them attention-grabbing but not spammy. Each should be under 50 characters.
    Return them as a JSON array of strings.
    `

    const { text: subjectLines } = await generateText({
      model: google('gemini-1.5-flash'),
      prompt: subjectPrompt,
      temperature: 0.8,
      maxTokens: 200,
    })

    let parsedSubjectLines = []
    try {
      parsedSubjectLines = JSON.parse(subjectLines)
    } catch {
      parsedSubjectLines = [
        `${businessName}: Special Offer Inside! 🎁`,
        `Your loyalty rewards await! ⭐`,
        `Exclusive member benefits 💎`
      ]
    }

    return NextResponse.json({
      message: text,
      subjectLines: parsedSubjectLines,
      metadata: {
        campaignType,
        targetAudience,
        tone,
        generatedAt: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error generating marketing message:', error)
    return NextResponse.json(
      { error: 'Failed to generate marketing message' },
      { status: 500 }
    )
  }
}
