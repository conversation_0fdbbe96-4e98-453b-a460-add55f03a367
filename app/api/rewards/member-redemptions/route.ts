import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { z } from 'zod'

const memberRedemptionsSchema = z.object({
  member_id: z.string().uuid(),
  company_id: z.string().uuid(),
})

export async function POST(request: NextRequest) {
  try {
    const supabase = getServiceRoleClient()
    const requestData = await request.json()
    
    const validatedData = memberRedemptionsSchema.parse(requestData)

    // Get all rewards that this member has already redeemed
    const { data: redeemedRewards, error } = await supabase
      .from('reward_redemptions')
      .select('reward_id')
      .eq('member_id', validatedData.member_id)
      .eq('company_id', validatedData.company_id)

    if (error) {
      console.error('Error fetching member redemptions:', error)
      return NextResponse.json({ error: 'Failed to fetch redemptions' }, { status: 500 })
    }

    const redeemedRewardIds = redeemedRewards?.map(r => r.reward_id) || []

    return NextResponse.json({
      success: true,
      data: {
        redeemed_reward_ids: redeemedRewardIds
      }
    })

  } catch (error) {
    console.error('Member redemptions API error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 })
  }
}
