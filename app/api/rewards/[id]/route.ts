import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'

// Schema for reward update
const rewardUpdateSchema = z.object({
  reward_code: z.string().optional(),
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().min(1, "Description is required").optional(),
  reward_type: z.enum(['SEASONAL', 'HOLIDAY', 'GENERAL']).optional(),
  reward_value_type: z.enum(['PERCENTAGE', 'FIXED_AMOUNT']).optional(),
  reward_value: z.number().min(0, "Reward value must be positive").optional(),
  additional_details: z.record(z.any()).optional(),
  start_date: z.string().optional(),
  expiration_date: z.string().optional(),
  points_required: z.number().min(0, "Points required must be non-negative").optional(),
  is_active: z.boolean().optional(),
  // Note: reward_image_url is handled by dedicated upload endpoint
  code: z.string().length(4, "Code must be 4 characters").optional(),
  company_id: z.string().uuid().optional(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()

    console.log('=== Reward Update API Called ===')
    console.log('Reward ID:', id)
    console.log('Request body:', JSON.stringify(body, null, 2))

    if (!id) {
      return NextResponse.json({ error: 'Reward ID is required' }, { status: 400 })
    }

    // Validate request body
    const validationResult = rewardUpdateSchema.safeParse(body)
    if (!validationResult.success) {
      console.log('Validation failed:', JSON.stringify(validationResult.error.format(), null, 2))
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    // Prepare update data
    const updateData: Record<string, unknown> = {}

    // Only include fields that are provided
    const validatedData = validationResult.data
    Object.keys(validatedData).forEach(key => {
      if (validatedData[key as keyof typeof validatedData] !== undefined) {
        updateData[key] = validatedData[key as keyof typeof validatedData]
      }
    })

    // Always update the updated_at timestamp
    updateData.updated_at = new Date().toISOString()

    console.log('Final update data:', JSON.stringify(updateData, null, 2))

    const supabase = getServiceRoleClient()
    const { data, error } = await supabase
      .from('rewards')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating reward:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Unexpected error in reward update:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const { searchParams } = new URL(request.url)
  const companyId = searchParams.get('companyId')

  if (!id) {
    return NextResponse.json({ error: 'Reward ID is required' }, { status: 400 })
  }

  if (!companyId) {
    return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
  }

  const supabase = getServiceRoleClient()
  const { data, error } = await supabase
    .from('rewards')
    .select('*')
    .eq('id', id)
    .eq('company_id', companyId)
    .single()

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json(data)
}

/**
 * DELETE /api/rewards/[id] - Delete a reward
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')
    const forceDelete = searchParams.get('force') === 'true'

    if (!id) {
      return NextResponse.json({ error: 'Reward ID is required' }, { status: 400 })
    }

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    console.log('=== Reward Delete API Called ===');
    console.log('Reward ID:', id);
    console.log('Company ID:', companyId);
    console.log('Force Delete:', forceDelete);

    const supabase = getServiceRoleClient()

    // First check if the reward exists and belongs to the company
    const { error: checkError } = await supabase
      .from('rewards')
      .select('id')
      .eq('id', id)
      .eq('company_id', companyId)
      .single()

    if (checkError) {
      return NextResponse.json({ 
        error: 'Reward not found or does not belong to this company' 
      }, { status: 404 })
    }

    // Check if there are any redemptions for this reward
    const { data: redemptions, error: redemptionsError } = await supabase
      .from('reward_redemptions')
      .select('id')
      .eq('reward_id', id)
      .limit(1)

    if (redemptionsError) {
      console.error('Error checking for redemptions:', redemptionsError)
      return NextResponse.json({ error: 'Failed to check for existing redemptions' }, { status: 500 })
    }

    // If there are redemptions and force delete is not enabled, return an error
    if (redemptions && redemptions.length > 0 && !forceDelete) {
      return NextResponse.json({ 
        error: 'This reward has redemptions and cannot be deleted',
        code: 'REWARD_HAS_REDEMPTIONS',
        message: 'This reward has been redeemed by members and cannot be deleted. You can deactivate it instead.'
      }, { status: 409 }) // 409 Conflict
    }

    // If force delete is enabled, first delete all redemptions
    if (forceDelete && redemptions && redemptions.length > 0) {
      console.log('Force deleting reward and its redemptions');
      
      // Get all redemptions for this reward
      const { data: allRedemptions, error: allRedemptionsError } = await supabase
        .from('reward_redemptions')
        .select('id')
        .eq('reward_id', id)
      
      if (allRedemptionsError) {
        console.error('Error fetching all redemptions:', allRedemptionsError)
        return NextResponse.json({ error: 'Failed to fetch redemptions for deletion' }, { status: 500 })
      }
      
      if (allRedemptions && allRedemptions.length > 0) {
        // Delete all redemptions for this reward
        const { error: deleteRedemptionsError } = await supabase
          .from('reward_redemptions')
          .delete()
          .eq('reward_id', id)
        
        if (deleteRedemptionsError) {
          console.error('Error deleting redemptions:', deleteRedemptionsError)
          return NextResponse.json({ error: 'Failed to delete associated redemptions' }, { status: 500 })
        }
        
        console.log(`Deleted ${allRedemptions.length} redemptions for reward ${id}`);
      }
    }

    // Now delete the reward
    const { error: deleteError } = await supabase
      .from('rewards')
      .delete()
      .eq('id', id)
      .eq('company_id', companyId)

    if (deleteError) {
      console.error('Error deleting reward:', deleteError)
      
      // Check if it's a foreign key constraint error
      if (deleteError.code === '23503') {
        return NextResponse.json({ 
          error: 'This reward has redemptions and cannot be deleted',
          code: 'REWARD_HAS_REDEMPTIONS',
          message: 'This reward has been redeemed by members and cannot be deleted. You can deactivate it instead.'
        }, { status: 409 }) // 409 Conflict
      }
      
      return NextResponse.json({ error: deleteError.message }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'Reward deleted successfully' })
  } catch (error) {
    console.error('Unexpected error in reward deletion:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
