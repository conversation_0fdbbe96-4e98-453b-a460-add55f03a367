import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const companyId = searchParams.get('companyId')
  
  if (!companyId) {
    return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
  }

  const supabase = getServiceRoleClient()
  const query = supabase
    .from('rewards')
    .select('id, title, description, points_required, reward_value_type, reward_value, is_active, expiration_date, created_at, code, reward_image_url')
    .eq('company_id', companyId)

  // Show all rewards including expired ones - they will be marked as expired in the UI

  const { data, error } = await query.order('created_at', { ascending: false })

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  // Add expiration status to each reward
  const enrichedData = data?.map(reward => ({
    ...reward,
    is_expired: new Date(reward.expiration_date) <= new Date(),
    expires_soon: new Date(reward.expiration_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
  })) || []

  return NextResponse.json({ data: enrichedData })
}

export async function POST(request: NextRequest) {
  const { companyId, ...rewardData } = await request.json()

  if (!companyId) {
    return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
  }

  const supabase = getServiceRoleClient()
  const { data, error } = await supabase
    .from('rewards')
    .insert({
      ...rewardData,
      company_id: companyId,
    })
    .select()
    .single()

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json(data)
}
