import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const code = searchParams.get('code')
  const companyId = searchParams.get('companyId')

  if (!code) {
    return NextResponse.json({ error: 'Code is required' }, { status: 400 })
  }

  if (!companyId) {
    return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
  }

  try {
    const supabase = getServiceRoleClient()
    const { data, error } = await supabase
      .from('rewards')
      .select('code')
      .eq('code', code)
      .eq('company_id', companyId)
      .maybeSingle()

    if (error) {
      console.error('Error checking reward code:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ exists: !!data })
  } catch (error) {
    console.error('Unexpected error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
