import { NextRequest, NextResponse } from 'next/server';

// Define types for API metrics data
type ApiEndpointMetric = {
  endpoint: string;
  method: string;
  total_calls: number;
  avg_duration: number;
  last_called: string;
};

export async function GET(request: NextRequest) {
  try {
    // Get the time period from the query string
    const searchParams = request.nextUrl.searchParams;
    const timePeriod = searchParams.get('timePeriod') || '24h';

    // Valid time periods
    const validPeriods = ['1h', '6h', '24h', '7d', '30d'];
    if (!validPeriods.includes(timePeriod)) {
      return NextResponse.json(
        { error: 'Invalid time period. Use 1h, 6h, 24h, 7d, or 30d' },
        { status: 400 }
      );
    }

    // Return mock data for now since the database function doesn't exist
    // TODO: Implement actual API metrics tracking
    const mockEndpoints: ApiEndpointMetric[] = [
      {
        endpoint: '/api/members',
        method: 'GET',
        total_calls: 25,
        avg_duration: 150,
        last_called: new Date().toISOString()
      },
      {
        endpoint: '/api/dashboard-metrics',
        method: 'GET',
        total_calls: 15,
        avg_duration: 200,
        last_called: new Date().toISOString()
      },
      {
        endpoint: '/api/transactions',
        method: 'POST',
        total_calls: 10,
        avg_duration: 300,
        last_called: new Date().toISOString()
      }
    ];

    // Calculate overall summary from mock data
    const totalCalls = mockEndpoints.reduce((sum: number, endpoint: ApiEndpointMetric) => sum + endpoint.total_calls, 0);
    const uniqueEndpoints = mockEndpoints.length;
    let totalDuration = 0;

    mockEndpoints.forEach((endpoint: ApiEndpointMetric) => {
      totalDuration += endpoint.total_calls * endpoint.avg_duration;
    });

    const avgDuration = totalCalls > 0 ? totalDuration / totalCalls : 0;

    // Format the response
    const callsByEndpoint: Record<string, number> = {};
    mockEndpoints.forEach((endpoint: ApiEndpointMetric) => {
      callsByEndpoint[`${endpoint.method} ${endpoint.endpoint}`] = endpoint.total_calls;
    });

    // Get slowest endpoints
    const slowestEndpoints = [...mockEndpoints]
      .sort((a, b) => b.avg_duration - a.avg_duration)
      .slice(0, 5)
      .map((endpoint: ApiEndpointMetric) => ({
        endpoint: `${endpoint.method} ${endpoint.endpoint}`,
        duration: endpoint.avg_duration
      }));

    return NextResponse.json({
      summary: {
        totalCalls,
        uniqueEndpoints,
        totalResponseSize: 0, // Mock value
        averageDuration: avgDuration,
        callsByEndpoint,
        slowestEndpoints
      },
      endpoints: mockEndpoints
    });

  } catch (error) {
    console.error('Unexpected error in API metrics endpoint:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}