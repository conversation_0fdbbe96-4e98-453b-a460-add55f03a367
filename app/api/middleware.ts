import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

// An object to temporarily store API metrics in memory
// This is just for throttling database writes
const apiMetricsBuffer: {
  [key: string]: {
    count: number;
    totalDuration: number;
    lastFlushed: number;
  };
} = {};

// How often to flush metrics to the database (in ms)
const FLUSH_INTERVAL = 60000; // 1 minute

/**
 * API middleware to track API usage
 */
export async function apiMiddleware(req: NextRequest) {
  const start = performance.now();
  const path = req.nextUrl.pathname;
  const method = req.method;

  // Continue to the API route
  const response = NextResponse.next();

  // Track timing
  const duration = performance.now() - start;

  // Update the buffer
  const key = `${method} ${path}`;
  if (!apiMetricsBuffer[key]) {
    apiMetricsBuffer[key] = {
      count: 0,
      totalDuration: 0,
      lastFlushed: Date.now()
    };
  }

  apiMetricsBuffer[key].count++;
  apiMetricsBuffer[key].totalDuration += duration;

  // Check if it's time to flush this endpoint's metrics
  if (Date.now() - apiMetricsBuffer[key].lastFlushed > FLUSH_INTERVAL) {
    try {
      const supabase = getServiceRoleClient();

      // Check if the api_metrics table exists, if not create it
      const { error: tableCheckError } = await supabase.rpc('check_api_metrics_table_exists');

      if (tableCheckError) {
        // Table doesn't exist, create it
        const createTableQuery = `
          CREATE TABLE IF NOT EXISTS api_metrics (
            id SERIAL PRIMARY KEY,
            endpoint TEXT NOT NULL,
            method TEXT NOT NULL,
            count INTEGER NOT NULL,
            total_duration NUMERIC NOT NULL,
            avg_duration NUMERIC NOT NULL,
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `;

        await supabase.rpc('execute_sql', { sql: createTableQuery });
      }

      // Insert the metrics
      await supabase
        .from('api_metrics')
        .insert({
          endpoint: path,
          method: method,
          count: apiMetricsBuffer[key].count,
          total_duration: apiMetricsBuffer[key].totalDuration,
          avg_duration: apiMetricsBuffer[key].totalDuration / apiMetricsBuffer[key].count
        });

      // Reset the buffer for this endpoint
      apiMetricsBuffer[key].count = 0;
      apiMetricsBuffer[key].totalDuration = 0;
      apiMetricsBuffer[key].lastFlushed = Date.now();

    } catch (error) {
      // Don't let metrics logging failures affect the API response
      console.error('Error logging API metrics:', error);
    }
  }

  return response;
}