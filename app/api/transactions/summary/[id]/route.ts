import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { calculateRewardDiscount } from '@/lib/reward-calculations'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const transactionId = (await params).id

    // Use service role client to bypass RLS (same pattern as other working APIs)
    const supabase = getServiceRoleClient()

    // First, get the transaction to find the company_id
    const { data: transactionData, error: transactionError } = await supabase
      .from('points_transactions')
      .select('company_id, member_id')
      .eq('id', transactionId)
      .single()

    if (transactionError || !transactionData) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 })
    }

    // Fetch the primary transaction
    const { data: primaryTransaction, error: primaryError } = await supabase
      .from('points_transactions')
      .select(`
        *,
        receipt_ocr_data,
        receipt_ocr_confidence,
        loyalty_members!points_transactions_member_id_fkey (
          id,
          name,
          email,
          phone_number,
          loyalty_id,
          profile_image_url,
          company_id
        )
      `)
      .eq('id', transactionId)
      .eq('loyalty_members.company_id', transactionData.company_id)
      .single()

    if (primaryError || !primaryTransaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 })
    }

    // For individual transaction summaries, we'll be conservative but not too restrictive
    // This avoids mixing up separate transaction sessions while ensuring related transactions are found
    const transactionCreatedAt = new Date(primaryTransaction.created_at)
    const timeBuffer = 30 * 1000 // 30 seconds in milliseconds - reasonable window for related transactions
    const startTime = new Date(transactionCreatedAt.getTime() - timeBuffer)
    const endTime = new Date(transactionCreatedAt.getTime() + timeBuffer)

    const { data: relatedTransactions, error: relatedError } = await supabase
      .from('points_transactions')
      .select(`*, receipt_ocr_data, receipt_ocr_confidence`)
      .eq('member_id', primaryTransaction.member_id)
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString())
      .order('created_at', { ascending: true })

    if (relatedError) {
      return NextResponse.json({ error: 'Failed to fetch transaction details' }, { status: 500 })
    }

    // Get member's current points using the new clean view
    const { data: memberPointsData } = await supabase
      .from('member_points_live')
      .select('available_points, lifetime_points, redeemed_points, expired_points')
      .eq('loyalty_id', primaryTransaction.loyalty_members.loyalty_id)
      .single()

    // Get company information
    const { data: companyData } = await supabase
      .from('companies')
      .select('name, business_type')
      .eq('id', transactionData.company_id)
      .single()

    // Calculate summary statistics
    let pointsEarned = 0
    let pointsUsed = 0
    let rewardsApplied = 0
    let netChange = 0

    relatedTransactions?.forEach(transaction => {
      if (transaction.transaction_type === 'EARN') {
        pointsEarned += transaction.points_change
      } else if (transaction.transaction_type === 'REDEEM') {
        pointsUsed += Math.abs(transaction.points_change)
        rewardsApplied += 1
      }
      netChange += transaction.points_change
    })

    // Also check for double points rewards that were applied (they create redemption records with points_used = 0)
    const { data: doublePointsRedemptions } = await supabase
      .from('reward_redemptions')
      .select('id, reward_id, points_used, applied_value')
      .eq('member_id', primaryTransaction.member_id)
      .eq('points_used', 0) // Double points rewards don't consume points
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString())

    // Add double points rewards to the count (they don't create REDEEM transactions)
    if (doublePointsRedemptions && doublePointsRedemptions.length > 0) {
      rewardsApplied += doublePointsRedemptions.length
    }

    const summary = {
      total_transactions: relatedTransactions?.length || 0,
      points_earned: pointsEarned,
      total_points_used: pointsUsed,
      rewards_applied: rewardsApplied,
      net_points_change: netChange
    }

    // Format response data - ensure requested transaction is first in the array
    // Lookup reward data for primary transaction if it's a REDEEM
    let primaryRewardData = null
    if (primaryTransaction.transaction_type === 'REDEEM') {
      const { data: redemptionData } = await supabase
        .from('reward_redemptions')
        .select(`
          points_used,
          applied_value,
          reward_id,
          rewards!reward_redemptions_reward_id_fkey (
            id,
            title,
            description,
            points_required,
            reward_value,
            reward_value_type
          )
        `)
        .eq('member_id', primaryTransaction.member_id)
        .eq('points_used', Math.abs(primaryTransaction.points_change))
        .gte('created_at', new Date(primaryTransaction.created_at).toISOString())
        .lte('created_at', new Date(new Date(primaryTransaction.created_at).getTime() + 10000).toISOString()) // Within 10 seconds
        .single()

      if (redemptionData?.rewards) {
        // Handle the case where Supabase returns an array for the join
        const rewardInfo = Array.isArray(redemptionData.rewards)
          ? redemptionData.rewards[0]
          : redemptionData.rewards

        if (rewardInfo) {
          primaryRewardData = {
            id: rewardInfo.id,
            title: rewardInfo.title,
            description: rewardInfo.description,
            points_required: rewardInfo.points_required,
            reward_value: rewardInfo.reward_value,
            reward_value_type: rewardInfo.reward_value_type,
            applied_value: redemptionData.applied_value
          }
        }
      }
    }    // Calculate amount due after reward (for transactions with receipts)
    let amountDue = null
    let receiptAmount = primaryTransaction.total_amount
    let receiptBusinessName = primaryTransaction.business_name
    let receiptNumber = primaryTransaction.receipt_number

    // If this is a REDEEM transaction without receipt details, look for them in related EARN transactions
    // Also ensure receipt image URL is inherited
    if (primaryTransaction.transaction_type === 'REDEEM' && 
        (!receiptAmount || !primaryTransaction.receipt_image_url)) {
      // Find the most complete EARN transaction with receipt details
      const earnTransaction = relatedTransactions?.find(t =>
        t.transaction_type === 'EARN' &&
        t.total_amount &&
        t.business_name &&
        t.receipt_image_url
      ) || relatedTransactions?.find(t =>
        t.transaction_type === 'EARN' &&
        (t.total_amount || t.business_name)
      )

      if (earnTransaction) {
        // Only override if the primary transaction is missing these values
        if (!receiptAmount) receiptAmount = earnTransaction.total_amount
        if (!receiptBusinessName) receiptBusinessName = earnTransaction.business_name
        if (!receiptNumber) receiptNumber = earnTransaction.receipt_number
        if (!primaryTransaction.receipt_image_url) primaryTransaction.receipt_image_url = earnTransaction.receipt_image_url
        if (!primaryTransaction.receipt_ocr_data) primaryTransaction.receipt_ocr_data = earnTransaction.receipt_ocr_data
        if (!primaryTransaction.receipt_ocr_confidence) primaryTransaction.receipt_ocr_confidence = earnTransaction.receipt_ocr_confidence
      }
    }

    if (receiptAmount && primaryRewardData?.applied_value) {
      // Get subtotal from OCR data if available
      let subtotal: number | undefined
      if (primaryTransaction.receipt_ocr_data) {
        try {
          const ocrData = JSON.parse(primaryTransaction.receipt_ocr_data)
          subtotal = ocrData.subtotal
        } catch (e) {
          console.warn('Failed to parse OCR data for subtotal:', e)
        }
      }

      const calculation = calculateRewardDiscount({
        total_amount: parseFloat(receiptAmount),
        subtotal: subtotal,
        reward_value: parseFloat(primaryRewardData.applied_value),
        reward_value_type: primaryRewardData.reward_value_type as 'PERCENTAGE' | 'FIXED' | 'DOUBLE_POINTS'
      })
      
      amountDue = calculation.amount_due
    }

    const primaryTransactionData = {
      id: primaryTransaction.id,
      type: primaryTransaction.transaction_type,
      points_change: primaryTransaction.points_change,
      description: primaryTransaction.description,
      transaction_date: primaryTransaction.transaction_date,
      // Receipt details (may be inherited from related EARN transaction for REDEEM)
      total_amount: receiptAmount,
      business_name: receiptBusinessName,
      receipt_number: receiptNumber,
      receipt_image_url: primaryTransaction.receipt_image_url,
      // Enhanced OCR data
      receipt_ocr_data: primaryTransaction.receipt_ocr_data,
      receipt_ocr_confidence: primaryTransaction.receipt_ocr_confidence,
      // Reward and savings information
      reward: primaryRewardData,
      amount_due: amountDue
    }    // Get other related transactions (excluding the primary one) and lookup reward data for REDEEM transactions
    const otherTransactions = await Promise.all(
      relatedTransactions?.filter(t => t.id !== primaryTransaction.id)?.map(async transaction => {
        let rewardData = null

        // For REDEEM transactions, lookup the actual reward information
        if (transaction.transaction_type === 'REDEEM') {
          const { data: redemptionData } = await supabase
            .from('reward_redemptions')
            .select(`
              points_used,
              applied_value,
              reward_id,
              rewards!reward_redemptions_reward_id_fkey (
                id,
                title,
                description,
                points_required,
                reward_value,
                reward_value_type
              )
            `)
            .eq('member_id', transaction.member_id)
            .eq('points_used', Math.abs(transaction.points_change))
            .gte('created_at', new Date(transaction.created_at).toISOString())
            .lte('created_at', new Date(new Date(transaction.created_at).getTime() + 10000).toISOString()) // Within 10 seconds
            .single()

          if (redemptionData?.rewards) {
            // Handle the case where Supabase returns an array for the join
            const rewardInfo = Array.isArray(redemptionData.rewards)
              ? redemptionData.rewards[0]
              : redemptionData.rewards

            if (rewardInfo) {
              rewardData = {
                id: rewardInfo.id,
                title: rewardInfo.title,
                description: rewardInfo.description,
                points_required: rewardInfo.points_required,
                reward_value: rewardInfo.reward_value,
                reward_value_type: rewardInfo.reward_value_type,
                applied_value: redemptionData.applied_value
              }
            }
          }
        }

        // For unified transactions, find the EARN transaction with receipt details to calculate amount_due
        let transactionAmountDue = null
        let receiptAmount = transaction.total_amount
        let receiptBusinessName = transaction.business_name
        let receiptNumber = transaction.receipt_number

        // If this REDEEM transaction doesn't have receipt details, look for them in related EARN transactions
        // Also ensure receipt image URL is inherited
        if (transaction.transaction_type === 'REDEEM' && 
            (!receiptAmount || !transaction.receipt_image_url)) {
          // Find the most complete EARN transaction with receipt details
          const earnTransaction = relatedTransactions?.find(t =>
            t.transaction_type === 'EARN' &&
            t.total_amount &&
            t.business_name &&
            t.receipt_image_url
          ) || relatedTransactions?.find(t =>
            t.transaction_type === 'EARN' &&
            (t.total_amount || t.business_name)
          )

          if (earnTransaction) {
            // Only override if the transaction is missing these values
            if (!receiptAmount) receiptAmount = earnTransaction.total_amount
            if (!receiptBusinessName) receiptBusinessName = earnTransaction.business_name
            if (!receiptNumber) receiptNumber = earnTransaction.receipt_number
            if (!transaction.receipt_image_url) transaction.receipt_image_url = earnTransaction.receipt_image_url
            if (!transaction.receipt_ocr_data) transaction.receipt_ocr_data = earnTransaction.receipt_ocr_data
            if (!transaction.receipt_ocr_confidence) transaction.receipt_ocr_confidence = earnTransaction.receipt_ocr_confidence
          }
        }

        // Calculate amount due after reward for transactions with receipt details
        if (receiptAmount && rewardData?.applied_value) {
          const originalAmount = parseFloat(receiptAmount)
          const appliedValue = parseFloat(rewardData.applied_value)

          if (rewardData.reward_value_type === 'PERCENTAGE') {
            // Percentage discount
            transactionAmountDue = originalAmount - (originalAmount * appliedValue / 100)
          } else if (rewardData.reward_value_type === 'FIXED') {
            // Fixed amount discount
            transactionAmountDue = Math.max(0, originalAmount - appliedValue)
          }
        }

        return {
          id: transaction.id,
          type: transaction.transaction_type,
          points_change: transaction.points_change,
          description: transaction.description,
          transaction_date: transaction.transaction_date,
          // Receipt details (may be inherited from related EARN transaction for REDEEM)
          total_amount: receiptAmount,
          business_name: receiptBusinessName,
          receipt_number: receiptNumber,
          receipt_image_url: transaction.receipt_image_url,
          // Enhanced OCR data
          receipt_ocr_data: transaction.receipt_ocr_data,
          receipt_ocr_confidence: transaction.receipt_ocr_confidence,
          // Reward and savings information
          reward: rewardData,
          amount_due: transactionAmountDue
        }
      }) || []
    )

    // Add double points rewards as virtual transactions for display
    const doublePointsTransactions = []
    if (doublePointsRedemptions && doublePointsRedemptions.length > 0) {
      for (const redemption of doublePointsRedemptions) {
        const { data: rewardData } = await supabase
          .from('reward_redemptions')
          .select(`
            points_used,
            applied_value,
            reward_id,
            notes,
            rewards!reward_redemptions_reward_id_fkey (
              id,
              title,
              description,
              points_required,
              reward_value,
              reward_value_type
            )
          `)
          .eq('id', redemption.id)
          .single()

        if (rewardData?.rewards) {
          const rewardInfo = Array.isArray(rewardData.rewards)
            ? rewardData.rewards[0]
            : rewardData.rewards

          if (rewardInfo) {
            // Calculate the bonus points for display
            const bonusPoints = relatedTransactions?.find(t =>
              t.transaction_type === 'EARN' &&
              t.description?.includes('bonus points') &&
              t.description?.includes(rewardInfo.title)
            )?.points_change || 0

            doublePointsTransactions.push({
              id: `double-points-${redemption.id}`,
              type: 'DOUBLE_POINTS' as const,
              points_change: bonusPoints, // Show the actual bonus points earned
              description: rewardData.notes || `Applied ${rewardInfo.title} (+${bonusPoints} bonus points)`,
              transaction_date: primaryTransaction.transaction_date,
              total_amount: null,
              business_name: null,
              receipt_number: null,
              reward: {
                id: rewardInfo.id,
                title: rewardInfo.title,
                description: rewardInfo.description,
                points_required: rewardInfo.points_required,
                reward_value: rewardInfo.reward_value,
                reward_value_type: rewardInfo.reward_value_type,
                applied_value: rewardData.applied_value
              },
              amount_due: null
            })
          }
        }
      }
    }

    const responseData = {
      transactions: [primaryTransactionData, ...otherTransactions, ...doublePointsTransactions],
      member: {
        id: primaryTransaction.loyalty_members.id,
        name: primaryTransaction.loyalty_members.name,
        email: primaryTransaction.loyalty_members.email,
        phone: primaryTransaction.loyalty_members.phone_number,
        profile_image_url: primaryTransaction.loyalty_members.profile_image_url,
        loyalty_id: primaryTransaction.loyalty_members.loyalty_id,
        available_points: memberPointsData?.available_points || 0,
        lifetime_points: memberPointsData?.lifetime_points || 0,
        redeemed_points: memberPointsData?.redeemed_points || 0,
        expired_points: memberPointsData?.expired_points || 0,
        current_tier: null // TODO: Implement tier lookup
      },
      summary,
      company: {
        name: companyData?.name || 'Unknown Company',
        business_type: companyData?.business_type
      }
    }

    return NextResponse.json(responseData)

  } catch {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
