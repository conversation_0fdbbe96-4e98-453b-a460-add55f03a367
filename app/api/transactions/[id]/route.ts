import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const transactionId = (await params).id

    // Create server client to check authentication
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set: () => {},
          remove: () => {},
        },
      }
    )

    // Get authenticated user first
    const { data: { user }, error: authError } = await serverSupabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Use service role client for admin operations
    const supabase = getServiceRoleClient()    // First check if the transaction exists
    const { data: existingTransaction, error: fetchError } = await supabase
      .from('points_transactions')
      .select('id, member_id, points_change, transaction_type, company_id')
      .eq('id', transactionId)
      .single()

    if (fetchError || !existingTransaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 })
    }

    // Check if user has owner permissions for this company (only owners can delete transactions)
    const { data: userRole } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('administrator_id', user.id)
      .eq('company_id', existingTransaction.company_id)
      .single()

    if (!userRole || userRole.role !== 'OWNER') {
      return NextResponse.json({ error: 'Owner permissions required' }, { status: 403 })
    }

    // Start transaction to ensure data consistency
    // 1. Delete any reward redemptions associated with this transaction
    const { error: redemptionDeleteError } = await supabase
      .from('reward_redemptions')
      .delete()
      .eq('transaction_id', transactionId)

    if (redemptionDeleteError && redemptionDeleteError.code !== 'PGRST116') {
      console.error('Error deleting reward redemptions:', redemptionDeleteError)
      return NextResponse.json({ error: 'Failed to delete associated rewards' }, { status: 500 })
    }

    // 2. Delete the transaction itself
    const { error: deleteError } = await supabase
      .from('points_transactions')
      .delete()
      .eq('id', transactionId)

    if (deleteError) {
      console.error('Error deleting transaction:', deleteError)
      return NextResponse.json({ error: 'Failed to delete transaction' }, { status: 500 })
    }

    // 3. Update member's point balance if needed
    // For safety, we'll trigger a points recalculation for this member
    const { error: recalcError } = await supabase.rpc('recalculate_member_points', {
      p_member_id: existingTransaction.member_id
    })

    // Don't fail if recalculation doesn't exist - it's a nice-to-have
    if (recalcError) {
      console.warn('Points recalculation failed, but transaction was deleted:', recalcError)
    }

    return NextResponse.json({
      success: true,
      message: 'Transaction deleted successfully',
      deleted_transaction: {
        id: transactionId,
        member_id: existingTransaction.member_id,
        points_change: existingTransaction.points_change,
        transaction_type: existingTransaction.transaction_type
      }
    })

  } catch (error) {
    console.error('Delete transaction error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
