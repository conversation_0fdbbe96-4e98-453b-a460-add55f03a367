import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(request: Request) {
  const url = new URL(request.url)
  const memberId = url.searchParams.get('memberId')
  const companyId = url.searchParams.get('companyId')
  const limit = parseInt(url.searchParams.get('limit') || '50', 10)

  if (!memberId) {
    return NextResponse.json({ error: 'Member ID is required' }, { status: 400 })
  }
  if (!companyId) {
    return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
  }

  const supabase = getServiceRoleClient()
  const { data, error } = await supabase
    .from('points_transactions')
    .select('id, member_id, created_at, transaction_date, points_change, transaction_type, description')
    .eq('member_id', memberId)
    .eq('company_id', companyId)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (error) {
    console.error('Error fetching transactions:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }

  return NextResponse.json({ data })
}