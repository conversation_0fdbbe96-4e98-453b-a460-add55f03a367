import { NextRequest, NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const memberId = (await params).id;
    // Parse URL for query params
    const searchParams = request.nextUrl.searchParams;
    const companyId = searchParams.get('companyId');
    const limit = parseInt(searchParams.get('limit') || '50', 10);

    if (!memberId) {
      return NextResponse.json(
        { error: 'Member ID is required' },
        { status: 400 }
      );
    }

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Get transactions for the specific member
    const { data, error } = await supabase
      .from('points_transactions')
      .select(`
        id,
        member_id,
        created_at,
        transaction_date,
        points_change,
        transaction_type,
        description
      `)
      .eq('member_id', memberId)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching member transactions:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}