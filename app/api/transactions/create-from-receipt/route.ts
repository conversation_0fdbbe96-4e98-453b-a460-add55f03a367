import { NextRequest, NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';
import { extractReceiptData, convertReceiptToTransactionData } from '@/lib/receipt-ocr';
import { extractReceiptDataEnhanced, saveEnhancedReceiptData, convertToLegacyFormat } from '@/lib/receipt-ocr-enhanced';
import type { MatchedItem } from '@/lib/receipt-ocr-enhanced';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const memberIdRaw = formData.get('member_id') as string;
    const companyIdRaw = formData.get('company_id') as string;
    const transactionType = formData.get('transaction_type') as string || 'EARN';

    if (!file) {
      return NextResponse.json({ error: 'No image file provided' }, { status: 400 });
    }

    if (!memberIdRaw || !companyIdRaw) {
      return NextResponse.json({ error: 'Member ID and Company ID are required' }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({ error: 'Image file too large (max 10MB)' }, { status: 400 });
    }

    const supabase = getServiceRoleClient(); // Use service role

    // Step 1: Upload image to storage
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${timestamp}_${randomString}.${fileExtension}`;
    const filePath = `receipts/${fileName}`;

    const buffer = Buffer.from(await file.arrayBuffer());

    const { error: uploadError } = await supabase.storage
      .from('fufis')
      .upload(filePath, buffer, {
        cacheControl: '3600',
        upsert: false,
        contentType: file.type
      });

    if (uploadError) {
      console.error('Storage upload error:', uploadError);
      return NextResponse.json({ error: `Upload failed: ${uploadError.message}` }, { status: 500 });
    }

    // Get public URL
    const { data: publicUrlData } = supabase.storage
      .from('fufis')
      .getPublicUrl(filePath);

    if (!publicUrlData?.publicUrl) {
      return NextResponse.json({ error: 'Failed to get public URL' }, { status: 500 });
    }

    // Step 2: Check if company has template support and process accordingly
    let extractedData;
    let matchedItems: MatchedItem[] = [];
    let receiptId: string | undefined;

    try {
      // Check if company has active templates for enhanced processing
      const { data: templateExists } = await supabase
        .from('receipt_templates')
        .select('id')
        .eq('company_id', companyIdRaw)
        .eq('is_active', true)
        .limit(1);

      if (templateExists && templateExists.length > 0) {
        // Use enhanced OCR with template support
        console.log('Using enhanced OCR with template support');
        const enhancedResult = await extractReceiptDataEnhanced(buffer, companyIdRaw);
        extractedData = convertToLegacyFormat(enhancedResult);
        matchedItems = enhancedResult.matched_items || [];

        // Create receipt record first for item linking
        const { data: receipt, error: receiptError } = await supabase
          .from('receipts')
          .insert({
            receipt_number: enhancedResult.financial_system_number,
            member_id: memberIdRaw,
            uploader_telegram_id: 'api_upload',
            purchase_date: new Date().toISOString(),
            total_amount: enhancedResult.total_amount,
            subtotal: enhancedResult.subtotal || enhancedResult.total_amount,
            tax_amount: enhancedResult.tax_amount || 0,
            service_description: enhancedResult.items.map((item: { description: string }) => item.description).join(', '),
            points_awarded: Math.floor(enhancedResult.total_amount * 0.1),
            payment_method: enhancedResult.payment_method,
            receipt_image_url: publicUrlData.publicUrl,
            business_name: enhancedResult.business_name,
            business_tin: enhancedResult.business_tin,
            business_location: enhancedResult.business_location,
            company_id: companyIdRaw,
            template_id: templateExists[0].id,
            extraction_confidence: enhancedResult.confidence,
            pos_system: enhancedResult.pos_system
          })
          .select()
          .single();

        if (receiptError) {
          throw new Error(`Receipt creation failed: ${receiptError.message}`);
        }

        receiptId = receipt.id;

        // Save enhanced item data
        if (matchedItems && matchedItems.length > 0 && receiptId) {
          await saveEnhancedReceiptData(enhancedResult, matchedItems, receiptId, templateExists[0].id);
        }

      } else {
        // Use legacy OCR for backward compatibility
        console.log('Using legacy OCR (no templates available)');
        extractedData = await extractReceiptData(buffer);
      }
    } catch (ocrError) {
      console.error('OCR processing failed, falling back to legacy:', ocrError);
      extractedData = await extractReceiptData(buffer);
    }

    const transactionData = convertReceiptToTransactionData(extractedData);

    // Step 3: Create transaction record with all data
    const transactionInsertData: Record<string, unknown> = {
      member_id: memberIdRaw,
      company_id: companyIdRaw,
      transaction_type: transactionType,
      points_change: transactionData.suggested_points,
      description: transactionData.description,
      transaction_date: new Date().toISOString(),
      expiration_date: new Date().toISOString(), // Required field
      // Receipt and OCR data
      receipt_image_url: publicUrlData.publicUrl,
      receipt_number: transactionData.financial_system_number,
      business_name: transactionData.business_name,
      total_amount: transactionData.total_amount,
      receipt_ocr_data: JSON.stringify(extractedData),
      receipt_ocr_confidence: extractedData.confidence,
      receipt_processing_status: 'completed'
    };

    // Add receipt_id if we created one for enhanced processing
    if (receiptId) {
      transactionInsertData.receipt_id = receiptId;
      transactionInsertData.item_count = matchedItems?.length || 0;
    }

    const { data: transaction, error: transactionError } = await supabase
      .from('points_transactions')
      .insert(transactionInsertData)
      .select()
      .single();    if (transactionError) {
      console.error('Transaction creation error:', transactionError);
      // Clean up uploaded file if transaction fails
      await supabase.storage.from('fufis').remove([filePath]);
      return NextResponse.json({ error: `Transaction creation failed: ${transactionError.message}` }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: {
        transaction,
        ocr_data: extractedData,
        suggested_form_data: {
          description: transactionData.description,
          total_amount: transactionData.total_amount,
          business_name: transactionData.business_name,
          financial_system_number: transactionData.financial_system_number,
          points_change: transactionData.suggested_points,
          receipt_date: extractedData.receipt_date
        },
        // Enhanced data when available
        ...(matchedItems.length > 0 && {
          items: matchedItems,
          receipt_id: receiptId,
          enhanced_processing: true,
          item_count: matchedItems.length
        }),
        image_url: publicUrlData.publicUrl,
        confidence: extractedData.confidence
      }
    });

  } catch (error) {
    console.error('OCR + Transaction creation error:', error);
    return NextResponse.json(
      {
        error: 'Failed to process receipt and create transaction',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
