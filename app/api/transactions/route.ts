import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { getServiceRoleClient } from '@/lib/supabase';

// Schema for transaction POST
const transactionSchema = z.object({
  member_id: z.string().uuid(),
  points_change: z.coerce.number(),
  transaction_type: z.enum(['EARN', 'REDEEM', 'EXPIRE']),
  description: z.string().optional(),
  // allow arbitrary receipt IDs or null
  receipt_id: z.string().nullable().optional(),
  transaction_date: z.string().optional(),
  // Map to the table's expiration_date (required by NOT NULL constraint)
  expiration_date: z.string().optional(),
  company_id: z.string().uuid(),
  // OCR-related fields
  receipt_ocr_data: z.string().optional(), // JSON string
  receipt_ocr_confidence: z.number().min(0).max(1).optional(),
  receipt_processing_status: z.enum(['pending', 'processing', 'completed', 'failed']).optional(),
  // Receipt image and extracted data fields
  receipt_image_url: z.string().optional(),
  receipt_number: z.string().optional(),
  business_name: z.string().optional(),
  total_amount: z.number().optional(),
});

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const companyId = searchParams.get('companyId');
  const limit = parseInt(searchParams.get('limit') || '100', 10);
  const startDate = searchParams.get('startDate');
  const endDate = searchParams.get('endDate');

  if (!companyId) {
    return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
  }

  const supabase = getServiceRoleClient();
  let query = supabase
    .from('points_transactions')
    .select('id, member_id, points_change, transaction_type, description, transaction_date, receipt_id, created_at, receipt_ocr_data, receipt_ocr_confidence, receipt_processing_status, receipt_image_url, receipt_number, business_name, total_amount')
    .eq('company_id', companyId);

  // Apply date filters if provided
  if (startDate) {
    query = query.gte('transaction_date', startDate);
  }
  
  if (endDate) {
    // Add one day to make the end date inclusive
    const nextDay = new Date(endDate);
    nextDay.setDate(nextDay.getDate() + 1);
    query = query.lt('transaction_date', nextDay.toISOString());
  }

  const { data, error } = await query
    .order('created_at', { ascending: false })
    .limit(limit);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  
  return NextResponse.json({ data });
}

export async function POST(request: NextRequest) {
  const body = await request.json();
  console.error('POST /api/transactions received body:', body);
  const parse = transactionSchema.safeParse(body);
  if (!parse.success) {
    console.error('POST /api/transactions validation failed:', parse.error.format());
    return NextResponse.json({ error: 'Validation failed', details: parse.error.format(), received: body }, { status: 400 });
  }
  // Ensure expiration_date is set (fallback to transaction_date)
  const txData = {
    ...parse.data,
    expiration_date: parse.data.expiration_date ?? parse.data.transaction_date
  };

  const supabase = getServiceRoleClient();
  
  // Check for duplicate receipt number if provided
  if (txData.receipt_number) {
    const { data: existingReceipt, error: receiptCheckError } = await supabase
      .from('points_transactions')
      .select('id, receipt_number')
      .eq('company_id', txData.company_id)
      .eq('receipt_number', txData.receipt_number)
      .maybeSingle();

    if (existingReceipt) {
      return NextResponse.json(
        { error: 'Duplicate receipt', message: `A receipt with FS No. ${txData.receipt_number} has already been submitted.` },
        { status: 409 } // 409 Conflict status code
      );
    }

    if (receiptCheckError) {
      console.error('Error checking for duplicate receipt:', receiptCheckError);
    }
  }

  const { data, error } = await supabase
    .from('points_transactions')
    .insert(txData)
    .select()
    .single();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  return NextResponse.json({ data });
}
