import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

export async function GET(request: Request) {
  try {
    // Parse URL to get query parameters
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');
    const timeRange = searchParams.get('timeRange') || '30d';

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Calculate date range based on timeRange
    let days: number;
    switch (timeRange) {
      case '7d':
        days = 7;
        break;
      case '30d':
        days = 30;
        break;
      case '90d':
        days = 90;
        break;
      case '1y':
        days = 365;
        break;
      default:
        days = 30;
    }

    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - days);

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Optimized query - relies on composite index: idx_points_transactions_company_date_type
    // This index covers: (company_id, created_at, transaction_type) for optimal performance
    const { data, error } = await supabase
      .from('points_transactions')
      .select('created_at, points_change, transaction_type')
      .eq('company_id', companyId)
      .gte('created_at', daysAgo.toISOString())
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching points data:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
