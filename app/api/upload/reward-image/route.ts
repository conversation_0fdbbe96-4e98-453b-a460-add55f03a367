import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File
    const rewardId = formData.get('rewardId') as string
    const companyId = formData.get('companyId') as string

    console.log('=== Reward Image Upload API Called ===')
    console.log('Reward ID:', rewardId)
    console.log('Company ID:', companyId)
    console.log('File:', file?.name, file?.size, file?.type)

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!rewardId) {
      return NextResponse.json({ error: 'Reward ID is required' }, { status: 400 })
    }

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      }, { status: 400 })
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 5MB.'
      }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Generate unique filename
    const fileExtension = file.name.split('.').pop()
    const fileName = `reward-${rewardId}-${Date.now()}.${fileExtension}`
    const filePath = `reward-images/${fileName}`

    // Convert File to ArrayBuffer
    const arrayBuffer = await file.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('fufis')
      .upload(filePath, uint8Array, {
        contentType: file.type,
        upsert: false // Don't overwrite existing files
      })

    if (uploadError) {
      console.error('Storage upload error:', uploadError)
      return NextResponse.json({
        error: 'Failed to upload image',
        details: uploadError.message
      }, { status: 500 })
    }

    // Get public URL
    const { data: publicUrlData } = supabase.storage
      .from('fufis')
      .getPublicUrl(filePath)

    const imageUrl = publicUrlData.publicUrl

    console.log('Image uploaded successfully:', imageUrl)

    // Update reward record with new image URL
    const { data: rewardData, error: updateError } = await supabase
      .from('rewards')
      .update({
        reward_image_url: imageUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', rewardId)
      .eq('company_id', companyId)
      .select()
      .single()

    if (updateError) {
      console.error('Database update error:', updateError)
      // Try to clean up uploaded file if database update fails
      await supabase.storage.from('fufis').remove([filePath])
      return NextResponse.json({
        error: 'Failed to update reward with image URL',
        details: updateError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Image uploaded successfully',
      imageUrl,
      reward: rewardData
    })

  } catch (error) {
    console.error('Unexpected error in reward image upload:', error)
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const rewardId = searchParams.get('rewardId')
    const companyId = searchParams.get('companyId')

    console.log('=== Reward Image Delete API Called ===')
    console.log('Reward ID:', rewardId)
    console.log('Company ID:', companyId)

    if (!rewardId) {
      return NextResponse.json({ error: 'Reward ID is required' }, { status: 400 })
    }

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Get current reward to find the image URL
    const { data: reward, error: fetchError } = await supabase
      .from('rewards')
      .select('reward_image_url')
      .eq('id', rewardId)
      .eq('company_id', companyId)
      .single()

    if (fetchError) {
      console.error('Error fetching reward:', fetchError)
      return NextResponse.json({
        error: 'Reward not found',
        details: fetchError.message
      }, { status: 404 })
    }

    if (reward.reward_image_url) {
      // Extract file path from URL
      const url = new URL(reward.reward_image_url)
      const pathParts = url.pathname.split('/')
      const filePath = pathParts.slice(-2).join('/') // Get 'reward-images/filename.ext'

      // Delete from storage
      const { error: deleteError } = await supabase.storage
        .from('fufis')
        .remove([filePath])

      if (deleteError) {
        console.error('Storage delete error:', deleteError)
        // Continue with database update even if storage delete fails
      }
    }

    // Remove image URL from reward record
    const { data: rewardData, error: updateError } = await supabase
      .from('rewards')
      .update({
        reward_image_url: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', rewardId)
      .eq('company_id', companyId)
      .select()
      .single()

    if (updateError) {
      console.error('Database update error:', updateError)
      return NextResponse.json({
        error: 'Failed to remove image URL from reward',
        details: updateError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Image removed successfully',
      reward: rewardData
    })

  } catch (error) {
    console.error('Unexpected error in reward image delete:', error)
    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 })
  }
}
