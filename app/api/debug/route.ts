import { NextResponse } from 'next/server';

export async function GET() {
  // Get the environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'not set';
  // Only show first few chars of sensitive keys
  const anonKeyPrefix = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY 
    ? process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 10) + '...' 
    : 'not set';
  const serviceKeyPrefix = process.env.SUPABASE_SERVICE_ROLE_KEY 
    ? process.env.SUPABASE_SERVICE_ROLE_KEY.substring(0, 10) + '...' 
    : 'not set';
  const appUrl = process.env.NEXT_PUBLIC_APP_URL || 'not set';
  
  return NextResponse.json({
    environment: process.env.NODE_ENV,
    supabaseUrl,
    anonKeyPrefix,
    serviceKeyPrefix,
    appUrl,
    vercelUrl: process.env.NEXT_PUBLIC_VERCEL_URL || 'not set',
    timestamp: new Date().toISOString(),
  });
}
