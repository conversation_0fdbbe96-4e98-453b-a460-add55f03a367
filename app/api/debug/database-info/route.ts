import { NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

/**
 * GET /api/debug/database-info
 * Debug endpoint to get database table information using service role
 */
export async function GET() {
  try {
    console.log('Getting database info with service role client');
    const supabase = createServiceRoleClient();

    // Get all loyalty_members
    const { data: members, error: membersError } = await supabase
      .from('loyalty_members')
      .select('*')
      .limit(5);

    // Get table columns info
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 'loyalty_members')
      .eq('table_schema', 'public')
      .limit(20);

    // Get rewards sample
    const { data: rewards, error: rewardsError } = await supabase
      .from('rewards')
      .select('*')
      .limit(3);

    return NextResponse.json({
      members: {
        data: members,
        error: membersError?.message,
        count: members?.length || 0,
        sampleFields: members?.[0] ? Object.keys(members[0]) : []
      },
      columns: {
        data: columns,
        error: columnsError?.message
      },
      rewards: {
        data: rewards,
        error: rewardsError?.message,
        count: rewards?.length || 0,
        sampleFields: rewards?.[0] ? Object.keys(rewards[0]) : []
      }
    });

  } catch (error) {
    console.error('Database info error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
