import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

/**
 * GET /api/debug/lookup-member?id=<memberId>
 * Debug endpoint to look up member information
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const memberId = searchParams.get('id');
    const showSchema = searchParams.get('schema') === 'true';

    if (!memberId && !showSchema) {
      return NextResponse.json(
        { error: 'Member ID is required or schema=true parameter must be present' },
        { status: 400 }
      );
    }

    console.log('DEBUG: Looking up member with ID:', memberId);
    const supabase = createServiceRoleClient();

    // Check loyalty_members table only (members table doesn't exist)
    const tables = ['loyalty_members'];
    let memberData = null;
    const errors: Array<{ table: string, error: string }> = [];
    const tableSchemas: Record<string, unknown> = {};

    // Get table schemas if requested
    if (showSchema) {
      for (const table of tables) {
        // Get table definition
        const { data: columnInfo, error: schemaError } = await supabase
          .rpc('get_table_definition', { table_name: table });

        if (columnInfo) {
          tableSchemas[table] = columnInfo;
        } else if (schemaError) {
          errors.push({ table, error: `Schema error: ${schemaError.message}` });
        }
      }

      return NextResponse.json({
        schemas: tableSchemas,
        errors: errors.length > 0 ? errors : undefined
      });
    }

    // Try each table
    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .eq('id', memberId)
        .single();

      if (data) {
        memberData = { table, data };
        break;
      }

      if (error) {
        errors.push({ table, error: error.message });
      }
    }

    // List all tables if not found
    if (!memberData) {
      // Get available tables
      const { data: tableList, error: tablesError } = await supabase
        .rpc('get_tables');

      const availableTables = tablesError ? [] : tableList;

      return NextResponse.json({
        found: false,
        checkedTables: tables,
        errors,
        availableTables,
        memberId
      });
    }

    return NextResponse.json({
      found: true,
      memberData,
      errors
    });

  } catch (error) {
    console.error('DEBUG lookup member error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
