import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('[DEBUG] Request body received:', JSON.stringify(body, null, 2));

    return NextResponse.json({
      received: body,
      hasUserId: !!body.userId,
      keys: Object.keys(body),
    });
  } catch (error) {
    console.error('[DEBUG] Error parsing request body:', error);
    return NextResponse.json({ error: 'Failed to parse body' }, { status: 400 });
  }
}
