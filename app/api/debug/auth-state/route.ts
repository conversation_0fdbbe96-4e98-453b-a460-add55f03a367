import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET() {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: sessionData, error } = await supabase.auth.getSession()

    return NextResponse.json({
      hasSession: !!sessionData.session,
      sessionError: error?.message,
      userId: sessionData.session?.user?.id,
      userEmail: sessionData.session?.user?.email,
      cookieCount: cookieStore.getAll().length
    })
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      hasSession: false
    })
  }
}
