/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// Ensure this API runs in Node.js so SECRET env vars are accessible
export const runtime = 'nodejs';

/**
 * GET /api/debug/receipts - Debug endpoint for receipt diagnostic
 * This endpoint helps diagnose issues with receipt creation and points awarding
 */
export async function GET(request: NextRequest) {
  // Use service role key on server for RLS bypass
  const supabase = getServiceRoleClient()

  // Get query parameters
  const receipt_id = request.nextUrl.searchParams.get('receipt_id')
  const member_id = request.nextUrl.searchParams.get('member_id')
  const loyalty_id = request.nextUrl.searchParams.get('loyalty_id')

  // Prepare results object
  const results: Record<string, any> = {
    timestamp: new Date().toISOString(),
    database_connection: true,
    query_params: {
      receipt_id,
      member_id,
      loyalty_id
    },
    data: {}
  }

  try {
    // Test database connection
    const { error: testError } = await supabase
      .from('loyalty_members')
      .select('id')
      .limit(1)

    if (testError) {
      results.database_connection = false
      results.connection_error = testError.message
      return NextResponse.json(results, { status: 500 })
    }

    // Fetch receipt if ID provided
    if (receipt_id) {
      const { data: receipt, error: receiptError } = await supabase
        .from('receipts')
        .select('*')
        .eq('id', receipt_id)
        .single()

      results.data.receipt = receipt || null
      results.data.receipt_error = receiptError?.message || null
      results.data.receipt_found = !!receipt
    }

    // Fetch member if ID provided
    if (member_id) {
      const { data: member, error: memberError } = await supabase
        .from('loyalty_members')
        .select('id, name, loyalty_id, lifetime_points, redeemed_points, expired_points')
        .eq('id', member_id)
        .single()

      results.data.member = member || null
      results.data.member_error = memberError?.message || null
      results.data.member_found = !!member
    }

    // Fetch member by loyalty ID if provided
    if (loyalty_id) {
      const { data: memberByLoyaltyId, error: loyaltyIdError } = await supabase
        .from('loyalty_members')
        .select('id, name, loyalty_id, lifetime_points, redeemed_points, expired_points')
        .eq('loyalty_id', loyalty_id)
        .single()

      results.data.member_by_loyalty_id = memberByLoyaltyId || null
      results.data.loyalty_id_error = loyaltyIdError?.message || null
      results.data.loyalty_id_found = !!memberByLoyaltyId
    }

    // Fetch recent points transactions for this member
    if (member_id || (results.data.member_by_loyalty_id && results.data.member_by_loyalty_id.id)) {
      const id = member_id || results.data.member_by_loyalty_id.id

      const { data: transactions, error: transactionError } = await supabase
        .from('points_transactions')
        .select('*')
        .eq('member_id', id)
        .order('created_at', { ascending: false })
        .limit(5)

      results.data.recent_transactions = transactions || []
      results.data.transaction_error = transactionError?.message || null
    }

    // Return diagnostic information
    return NextResponse.json(results)
  } catch (error: any) {
    console.error('[ReceiptsDebugAPI] Unexpected error:', error)
    results.error = error.message
    return NextResponse.json(results, { status: 500 })
  }
}