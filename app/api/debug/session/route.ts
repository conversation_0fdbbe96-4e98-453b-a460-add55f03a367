import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { getServiceRoleClient } from '@/lib/supabase'

// GET /api/debug/session - Debug current session and company state
export async function GET() {
  try {
    const cookieStore = await cookies()

    // Get all cookies for debugging
    const allCookies = cookieStore.getAll()
    const supabaseCookies = allCookies.filter(cookie =>
      cookie.name.includes('sb-') || cookie.name.includes('supabase')
    )

    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set: () => {},
          remove: () => {},
        },
      }
    )

    // Get session
    const { data: { session }, error: sessionError } = await serverSupabase.auth.getSession()

    if (sessionError) {
      return NextResponse.json({
        error: 'Session error',
        details: sessionError.message,
        cookies: {
          total: allCookies.length,
          supabase: supabaseCookies.map(c => ({ name: c.name, hasValue: !!c.value }))
        }
      })
    }

    if (!session) {
      return NextResponse.json({
        authenticated: false,
        message: 'No session found',
        debug: {
          totalCookies: allCookies.length,
          supabaseCookies: supabaseCookies.map(c => ({
            name: c.name,
            hasValue: !!c.value,
            valueLength: c.value?.length || 0
          })),
          env: {
            hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
            hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
          }
        }
      })
    }

    const userId = session.user.id

    // Use service role to check companies
    const supabase = getServiceRoleClient()

    // Check if user has any companies
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('*')
      .eq('administrator_id', userId)

    return NextResponse.json({
      authenticated: true,
      session: {
        userId: userId,
        email: session.user.email,
        expires_at: session.expires_at
      },
      companies: companies || [],
      companiesError: companiesError?.message || null,
      companyCount: companies?.length || 0
    })

  } catch (error) {
    return NextResponse.json({
      error: 'Debug API error',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
