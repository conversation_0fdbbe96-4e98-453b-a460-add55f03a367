import { NextRequest, NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

/**
 * API route for serving user avatars
 * @route GET /api/avatar
 */
export async function GET(request: NextRequest) {
  // Get the email from the query parameters
  const { searchParams } = new URL(request.url);
  const email = searchParams.get('email');
  const size = searchParams.get('size') ? parseInt(searchParams.get('size') as string, 10) : 80;

  // Return 400 if email is not provided
  if (!email) {
    return NextResponse.json({ error: 'Email is required' }, { status: 400 });
  }

  try {
    // Initialize Supabase client with service role to bypass RLS
    const supabase = getServiceRoleClient();

    // Check if the user has a profile image in the database
    const { data: member } = await supabase
      .from('members')
      .select('profile_image_url, name')
      .eq('email', email)
      .maybeSingle();

    // If there's a profile image in the database, redirect to it
    if (member?.profile_image_url) {
      return NextResponse.redirect(member.profile_image_url);
    }

    // If no profile image or member not found, generate a UI Avatar based on name or email
    const displayName = member?.name || (email ? email.split('@')[0] : 'user');
    
    // Generate a UI Avatar (text-based avatar) that matches the style used in the Members page
    // This creates an avatar with the person's initials on a colored background
    const uiAvatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=random&size=${size}`;
    
    return NextResponse.redirect(uiAvatarUrl);
  } catch (error) {
    console.error('Error fetching avatar:', error);
    
    // Fallback to UI Avatars in case of any error
    const fallbackName = email ? email.split('@')[0] : 'user';
    const fallbackUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(fallbackName)}&background=random&size=${size}`;
    return NextResponse.redirect(fallbackUrl);
  }
}
