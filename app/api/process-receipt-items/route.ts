import { NextRequest, NextResponse } from 'next/server'
import { processExistingReceipts, processReceiptItems } from '@/lib/receipt-items-processor'
import { createClient } from '@/lib/supabase/server'

/**
 * POST /api/process-receipt-items - Process receipt items for analytics
 * This endpoint handles:
 * - Processing existing receipts that don't have receipt_items
 * - Processing individual receipts
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (!user || authError) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, company_id, receipt_id } = body

    if (action === 'process_existing') {
      // Process all existing receipts for a company
      if (!company_id) {
        return NextResponse.json({ error: 'company_id is required for process_existing action' }, { status: 400 })
      }

      const result = await processExistingReceipts(company_id)

      return NextResponse.json({
        success: result.success,
        message: `Processed ${result.receipts_processed} receipts, created ${result.items_created} receipt items`,
        details: {
          receipts_processed: result.receipts_processed,
          items_created: result.items_created,
          errors: result.errors
        }
      })
    }

    if (action === 'process_receipt') {
      // Process a specific receipt
      if (!receipt_id) {
        return NextResponse.json({ error: 'receipt_id is required for process_receipt action' }, { status: 400 })
      }

      // Get receipt details
      const { data: receipt, error: receiptError } = await supabase
        .from('receipts')
        .select('id, company_id, service_description, total_amount, subtotal')
        .eq('id', receipt_id)
        .single()

      if (receiptError || !receipt) {
        return NextResponse.json({ error: 'Receipt not found' }, { status: 404 })
      }

      const result = await processReceiptItems(
        receipt.id,
        receipt.company_id,
        receipt.service_description || '',
        receipt.total_amount,
        receipt.subtotal
      )

      if (result.success) {
        return NextResponse.json({
          success: true,
          message: `Created ${result.items_created} receipt items`,
          receipt_id: receipt.id,
          items_created: result.items_created
        })
      } else {
        return NextResponse.json({
          success: false,
          error: result.error,
          receipt_id: receipt.id
        }, { status: 500 })
      }
    }

    return NextResponse.json({ error: 'Invalid action. Use "process_existing" or "process_receipt"' }, { status: 400 })

  } catch (error) {
    console.error('Error in process-receipt-items API:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

/**
 * GET /api/process-receipt-items - Get status of receipt items processing
 */
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (!user || authError) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const companyId = request.nextUrl.searchParams.get('company_id')

    // Get statistics
    let receiptsQuery = supabase
      .from('receipts')
      .select('id, company_id, created_at')

    const receiptItemsQuery = supabase
      .from('receipt_items')
      .select('id, receipt_id, created_at')

    if (companyId) {
      receiptsQuery = receiptsQuery.eq('company_id', companyId)
      // For receipt_items with company filter, we'll filter after fetching
    }

    const [receiptsResult, receiptItemsResult] = await Promise.all([
      receiptsQuery,
      receiptItemsQuery
    ])

    if (receiptsResult.error || receiptItemsResult.error) {
      return NextResponse.json({
        error: 'Error fetching statistics',
        details: receiptsResult.error?.message || receiptItemsResult.error?.message
      }, { status: 500 })
    }

    const totalReceipts = receiptsResult.data?.length || 0
    const totalReceiptItems = receiptItemsResult.data?.length || 0

    // Calculate receipts without items
    const receiptIds = receiptsResult.data?.map(r => r.id) || []
    const receiptIdsWithItems = receiptItemsResult.data?.map(ri => ri.receipt_id) || []
    const receiptsWithoutItems = receiptIds.filter(id => !receiptIdsWithItems.includes(id))

    return NextResponse.json({
      success: true,
      statistics: {
        total_receipts: totalReceipts,
        total_receipt_items: totalReceiptItems,
        receipts_without_items: receiptsWithoutItems.length,
        receipts_with_items: totalReceipts - receiptsWithoutItems.length,
        company_id: companyId || 'all'
      },
      receipts_needing_processing: receiptsWithoutItems
    })

  } catch (error) {
    console.error('Error in process-receipt-items GET:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
