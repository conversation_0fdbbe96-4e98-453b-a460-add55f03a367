import { NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { createServiceRoleClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // Get user from session using both approaches
    const cookieStore = await cookies();

    // First try with anon key client
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set: () => {},
          remove: () => {},
        },
      }
    );

    const { data: { user }, error: userError } = await supabase.auth.getUser();

    console.log('[API] Admin status check - User auth result:', {
      user: user ? { id: user.id, email: user.email } : null,
      userError: userError?.message,
      cookies: Object.fromEntries(cookieStore.getAll().map(c => [c.name, c.value.substring(0, 20) + '...']))
    });

    if (userError || !user) {
      console.log('[API] Admin status check - Not authenticated');
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Create service role client to bypass RLS
    const serviceSupabase = createServiceRoleClient();

    // Use the SECURITY DEFINER function to bypass RLS policies
    console.log('[API] Using SECURITY DEFINER function to check admin status for user:', user.id);
    
    // Add retry logic for the RPC call
    let retries = 0;
    const maxRetries = 2;
    let adminStatusData;
    let adminStatusError;
    
    while (retries <= maxRetries) {
      const { data, error } = await serviceSupabase
        .rpc('check_user_admin_status_secure', { user_id: user.id });
      
      adminStatusData = data;
      adminStatusError = error;
      
      if (!error) break;
      
      console.log(`[API] Retry ${retries + 1}/${maxRetries} for admin status check:`, error.message);
      retries++;
      
      // Small delay before retry
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('[API] Admin status function result:', {
      adminStatusData,
      adminStatusError: adminStatusError?.message
    });

    if (adminStatusError) {
      console.error('[API] Error calling admin status function:', adminStatusError);
      return NextResponse.json(
        { error: 'Error checking admin status' },
        { status: 500 }
      );
    }

    if (adminStatusData && adminStatusData.length > 0) {
      const adminInfo = adminStatusData[0];

      if (adminInfo.is_admin) {
        console.log('[API] User has admin access:', adminInfo);
        
        // Add cache control headers to prevent stale responses
        const headers = new Headers();
        headers.append('Cache-Control', 'no-cache, no-store, must-revalidate');
        headers.append('Pragma', 'no-cache');
        headers.append('Expires', '0');
        
        return NextResponse.json({
          isAdmin: true,
          adminData: {
            company_id: adminInfo.company_id,
            administrator_id: user.id,
            role: adminInfo.role_name,
            created_at: new Date().toISOString()
          },
          companyId: adminInfo.company_id,
          companyName: adminInfo.company_name
        }, { headers });
      }
    }
    
    // Try a direct check on the companies table as fallback
    // This handles the case where the user is a direct owner but not in company_administrators
    try {
      const { data: companyData, error: companyError } = await serviceSupabase
        .from('companies')
        .select('id, name')
        .eq('owner_id', user.id)
        .maybeSingle();
      
      if (companyData && !companyError) {
        console.log('[API] User is company owner:', companyData);
        
        const headers = new Headers();
        headers.append('Cache-Control', 'no-cache, no-store, must-revalidate');
        
        return NextResponse.json({
          isAdmin: true,
          adminData: {
            company_id: companyData.id,
            administrator_id: user.id,
            role: 'OWNER',
            created_at: new Date().toISOString()
          },
          companyId: companyData.id,
          companyName: companyData.name
        }, { headers });
      }
    } catch (companyCheckError) {
      console.error('[API] Error checking company ownership:', companyCheckError);
    }

    // User is not an admin
    console.log('[API] User is not an admin');
    
    const headers = new Headers();
    headers.append('Cache-Control', 'no-cache, no-store, must-revalidate');
    
    return NextResponse.json({
      isAdmin: false,
      adminData: null,
      companyId: null,
      companyName: null
    }, { headers });

  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
