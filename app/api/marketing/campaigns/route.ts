import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { authenticateMarketingRequest, validateCampaignData } from '../auth';

/**
 * GET /api/marketing/campaigns
 * List all campaigns for the authenticated user's company
 */
export async function GET() {
  try {
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const supabase = await createClient();

    const { data: campaigns, error } = await supabase
      .from('marketing_campaigns')
      .select(`
        id,
        name,
        description,
        status,
        target_type,
        total_recipients,
        successful_sends,
        failed_sends,
        scheduled_at,
        sent_at,
        created_at,
        updated_at
      `)
      .eq('company_id', auth.companyId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching campaigns:', error);
      return NextResponse.json({ error: 'Failed to fetch campaigns' }, { status: 500 });
    }

    // Calculate delivery rates
    const campaignsWithMetrics = campaigns.map(campaign => ({
      ...campaign,
      delivery_rate: campaign.total_recipients > 0
        ? Math.round((campaign.successful_sends / campaign.total_recipients) * 100)
        : 0
    }));

    return NextResponse.json({
      campaigns: campaignsWithMetrics,
      total: campaigns.length
    });

  } catch (error) {
    console.error('Campaign list error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/marketing/campaigns
 * Create a new campaign
 */
export async function POST(req: NextRequest) {
  try {
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const body = await req.json();
    const validation = validateCampaignData(body);

    if (!validation.isValid) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validation.errors
      }, { status: 400 });
    }

    const supabase = await createClient();

    // Create the campaign
    const { data: campaign, error } = await supabase
      .from('marketing_campaigns')
      .insert({
        ...validation.cleanData,
        company_id: auth.companyId,
        created_by: auth.user?.id,
        status: 'draft'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating campaign:', error);
      return NextResponse.json({ error: 'Failed to create campaign' }, { status: 500 });
    }

    return NextResponse.json({
      campaign,
      message: 'Campaign created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Campaign creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
