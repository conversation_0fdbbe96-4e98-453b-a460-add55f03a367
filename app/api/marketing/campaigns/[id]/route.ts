import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { authenticateMarketingRequest, validateCampaignData } from '../../auth';

/**
 * GET /api/marketing/campaigns/[id]
 * Get a specific campaign with detailed information
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const supabase = await createClient();

    const { data: campaign, error } = await supabase
      .from('marketing_campaigns')
      .select(`
        *,
        campaign_recipients (
          id,
          member_id,
          status,
          sent_at,
          error_message,
          loyalty_members (
            name,
            email,
            telegram_chat_id
          )
        )
      `)
      .eq('id', params.id)
      .eq('company_id', auth.companyId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
      }
      console.error('Error fetching campaign:', error);
      return NextResponse.json({ error: 'Failed to fetch campaign' }, { status: 500 });
    }

    return NextResponse.json({ campaign });

  } catch (error) {
    console.error('Campaign fetch error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PUT /api/marketing/campaigns/[id]
 * Update a campaign (only allowed for draft campaigns)
 */
export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const body = await req.json();
    const validation = validateCampaignData(body);

    if (!validation.isValid) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validation.errors
      }, { status: 400 });
    }

    const supabase = await createClient();

    // First check if campaign exists and is editable
    const { data: existingCampaign, error: fetchError } = await supabase
      .from('marketing_campaigns')
      .select('id, status')
      .eq('id', params.id)
      .eq('company_id', auth.companyId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
      }
      console.error('Error fetching campaign:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch campaign' }, { status: 500 });
    }

    // Only allow editing draft campaigns
    if (existingCampaign.status !== 'draft') {
      return NextResponse.json({
        error: 'Cannot edit campaign that is not in draft status'
      }, { status: 400 });
    }

    // Update the campaign
    const { data: campaign, error } = await supabase
      .from('marketing_campaigns')
      .update(validation.cleanData)
      .eq('id', params.id)
      .eq('company_id', auth.companyId)
      .select()
      .single();

    if (error) {
      console.error('Error updating campaign:', error);
      return NextResponse.json({ error: 'Failed to update campaign' }, { status: 500 });
    }

    return NextResponse.json({
      campaign,
      message: 'Campaign updated successfully'
    });

  } catch (error) {
    console.error('Campaign update error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * DELETE /api/marketing/campaigns/[id]
 * Delete a campaign (only allowed for draft campaigns)
 */
export async function DELETE(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const supabase = await createClient();

    // First check if campaign exists and is deletable
    const { data: existingCampaign, error: fetchError } = await supabase
      .from('marketing_campaigns')
      .select('id, status')
      .eq('id', params.id)
      .eq('company_id', auth.companyId)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
      }
      console.error('Error fetching campaign:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch campaign' }, { status: 500 });
    }

    // Only allow deleting draft campaigns
    if (existingCampaign.status !== 'draft') {
      return NextResponse.json({
        error: 'Cannot delete campaign that is not in draft status'
      }, { status: 400 });
    }

    // Delete the campaign (campaign_recipients will be deleted automatically due to CASCADE)
    const { error } = await supabase
      .from('marketing_campaigns')
      .delete()
      .eq('id', params.id)
      .eq('company_id', auth.companyId);

    if (error) {
      console.error('Error deleting campaign:', error);
      return NextResponse.json({ error: 'Failed to delete campaign' }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Campaign deleted successfully'
    });

  } catch (error) {
    console.error('Campaign deletion error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
