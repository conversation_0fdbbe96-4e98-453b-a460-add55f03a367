import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { authenticateMarketingRequest } from '../../../auth';
import { SupabaseClient } from '@supabase/supabase-js';

interface MemberData {
  member_name: string;
  loyalty_tier: string;
  available_points: number;
}

interface Recipient {
  member_id: string;
  member_name: string;
  telegram_chat_id: number;
  loyalty_tier: string;
  available_points: number;
}

/**
 * Interpolate variables in message content
 */
function interpolateMessage(template: string, memberData: MemberData): string {
  return template
    .replace(/\{\{name\}\}/g, memberData.member_name || 'Member')
    .replace(/\{\{tier\}\}/g, memberData.loyalty_tier || 'Bronze')
    .replace(/\{\{points\}\}/g, memberData.available_points?.toString() || '0')
    .replace(/\{\{company\}\}/g, 'Your Business'); // TODO: Get from company table
}

/**
 * Send Telegram notification (reusing existing function)
 */
async function sendTelegramNotification(
  chatId: string,
  message: string,
  supabase: SupabaseClient,
  memberId: string,
  campaignId: string
) {
  try {
    // Insert notification record
    const { data: notification, error: notificationError } = await supabase
      .from('telegram_notifications')
      .insert({
        member_id: memberId,
        chat_id: chatId,
        message,
        notification_type: 'marketing_campaign',
        campaign_id: campaignId,
        sent_at: new Date().toISOString()
      })
      .select()
      .single();

    if (notificationError) {
      console.error('Error creating notification record:', notificationError);
      return { success: false, error: notificationError.message };
    }

    // Send via Telegram API (placeholder - implement actual sending)
    // For now, we'll simulate sending and mark as successful
    const telegramResponse = await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId,
        text: message,
        parse_mode: 'HTML'
      })
    });

    if (!telegramResponse.ok) {
      const errorData = await telegramResponse.json();
      console.error('Telegram API error:', errorData);
      return { success: false, error: errorData.description || 'Telegram send failed' };
    }

    return { success: true, notificationId: notification.id };

  } catch (error) {
    console.error('Send notification error:', error);
    return { success: false, error: 'Send failed' };
  }
}

/**
 * POST /api/marketing/campaigns/[id]/send
 * Execute a campaign by sending messages to all eligible recipients
 */
export async function POST(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const params = await context.params

  try {
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const body = await req.json();
    const { confirm } = body;

    if (!confirm) {
      return NextResponse.json({
        error: 'Campaign sending must be confirmed with confirm: true'
      }, { status: 400 });
    }

    const supabase = await createClient();

    // Get campaign details
    const { data: campaign, error: campaignError } = await supabase
      .from('marketing_campaigns')
      .select('*')
      .eq('id', params.id)
      .eq('company_id', auth.companyId)
      .single();

    if (campaignError) {
      if (campaignError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Campaign not found' }, { status: 404 });
      }
      console.error('Error fetching campaign:', campaignError);
      return NextResponse.json({ error: 'Failed to fetch campaign' }, { status: 500 });
    }

    // Check campaign status
    if (campaign.status !== 'draft') {
      return NextResponse.json({
        error: 'Campaign must be in draft status to send'
      }, { status: 400 });
    }

    // Get eligible recipients using direct query (same logic as preview API)
    let query = supabase
      .from('loyalty_members')
      .select(`
        id,
        name,
        loyalty_tier,
        telegram_chat_id,
        lifetime_points,
        redeemed_points,
        registration_date
      `)
      .eq('company_id', auth.companyId);

    // Filter based on target type and criteria
    if (campaign.target_type === 'tier' && campaign.target_criteria?.tier_names) {
      const tierNames = Array.isArray(campaign.target_criteria.tier_names)
        ? campaign.target_criteria.tier_names
        : [campaign.target_criteria.tier_names];
      query = query.in('loyalty_tier', tierNames);
    } else if (campaign.target_type === 'individual' && campaign.target_criteria?.member_ids) {
      const memberIds = Array.isArray(campaign.target_criteria.member_ids)
        ? campaign.target_criteria.member_ids
        : [campaign.target_criteria.member_ids];
      query = query.in('id', memberIds);
    } else if (campaign.target_type === 'custom') {
      if (campaign.target_criteria?.min_points) {
        query = query.gte('lifetime_points', campaign.target_criteria.min_points);
      }
      if (campaign.target_criteria?.max_points) {
        query = query.lte('lifetime_points', campaign.target_criteria.max_points);
      }
      if (campaign.target_criteria?.tier_names) {
        const tierNames = Array.isArray(campaign.target_criteria.tier_names)
          ? campaign.target_criteria.tier_names
          : [campaign.target_criteria.tier_names];
        query = query.in('loyalty_tier', tierNames);
      }
    }

    const { data: allMembers, error: recipientsError } = await query;

    if (recipientsError) {
      console.error('Error getting recipients:', recipientsError);
      return NextResponse.json({ error: 'Failed to get campaign recipients' }, { status: 500 });
    }

    // Filter for members with Telegram (only they can receive messages) and format for sending
    const recipients = (allMembers || [])
      .filter(member => member.telegram_chat_id)
      .map(member => ({
        member_id: member.id,
        member_name: member.name,
        telegram_chat_id: parseInt(member.telegram_chat_id),
        loyalty_tier: member.loyalty_tier || 'Basic',
        available_points: (member.lifetime_points || 0) - (member.redeemed_points || 0)
      }));

    if (!recipients || recipients.length === 0) {
      return NextResponse.json({
        error: 'No eligible recipients found for this campaign'
      }, { status: 400 });
    }

    // Update campaign status to sending
    await supabase
      .from('marketing_campaigns')
      .update({
        status: 'sending',
        total_recipients: recipients.length,
        sent_at: new Date().toISOString()
      })
      .eq('id', params.id);

    let successfulSends = 0;
    let failedSends = 0;

    // Create campaign recipients records and send messages
    const recipientPromises = recipients.map(async (recipient: Recipient) => {
      try {
        // Create recipient record
        const { data: recipientRecord, error: recipientError } = await supabase
          .from('campaign_recipients')
          .insert({
            campaign_id: params.id,
            member_id: recipient.member_id,
            status: 'pending'
          })
          .select()
          .single();

        if (recipientError) {
          console.error('Error creating recipient record:', recipientError);
          failedSends++;
          return;
        }

        // Interpolate message
        const personalizedMessage = interpolateMessage(campaign.message_content, recipient);
        const fullMessage = campaign.message_title
          ? `*${campaign.message_title}*\n\n${personalizedMessage}`
          : personalizedMessage;

        // Send Telegram message
        const sendResult = await sendTelegramNotification(
          recipient.telegram_chat_id.toString(),
          fullMessage,
          supabase,
          recipient.member_id,
          params.id
        );

        // Update recipient status
        if (sendResult.success) {
          await supabase
            .from('campaign_recipients')
            .update({
              status: 'sent',
              sent_at: new Date().toISOString(),
              notification_id: sendResult.notificationId
            })
            .eq('id', recipientRecord.id);

          successfulSends++;
        } else {
          await supabase
            .from('campaign_recipients')
            .update({
              status: 'failed',
              error_message: sendResult.error
            })
            .eq('id', recipientRecord.id);

          failedSends++;
        }

        // Rate limiting: wait 100ms between sends to respect Telegram limits
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error('Error processing recipient:', error);
        failedSends++;
      }
    });

    // Execute all sends
    await Promise.all(recipientPromises);

    // Update final campaign status
    const finalStatus = failedSends === 0 ? 'sent' : (successfulSends > 0 ? 'sent' : 'failed');

    const { data: finalCampaign } = await supabase
      .from('marketing_campaigns')
      .update({
        status: finalStatus,
        successful_sends: successfulSends,
        failed_sends: failedSends
      })
      .eq('id', params.id)
      .select()
      .single();

    return NextResponse.json({
      campaign: finalCampaign,
      results: {
        total_recipients: recipients.length,
        successful_sends: successfulSends,
        failed_sends: failedSends,
        delivery_rate: recipients.length > 0 ? Math.round((successfulSends / recipients.length) * 100) : 0
      },
      message: 'Campaign sent successfully'
    });

  } catch (error) {
    console.error('Campaign sending error:', error);

    // Update campaign status to failed
    try {
      const supabase = await createClient();
      await supabase
        .from('marketing_campaigns')
        .update({ status: 'failed' })
        .eq('id', params.id);
    } catch (updateError) {
      console.error('Error updating campaign status to failed:', updateError);
    }

    return NextResponse.json({ error: 'Campaign sending failed' }, { status: 500 });
  }
}
