import { createClient } from '@/lib/supabase/server';
import { getCompanyIdFromSession } from '@/lib/auth';
import { User } from '@supabase/supabase-js';

export interface MarketingAuthResult {
  isAuthorized: boolean;
  user: User | null;
  companyId: string | null;
  error?: string;
}

/**
 * Authenticate and authorize marketing API requests
 * Ensures user is logged in and has admin access to their company
 */
export async function authenticateMarketingRequest(): Promise<MarketingAuthResult> {
  try {
    const supabase = await createClient();

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return {
        isAuthorized: false,
        user: null,
        companyId: null,
        error: 'Authentication required'
      };
    }

    // Get the company ID for this user
    const companyId = await getCompanyIdFromSession(supabase);

    if (!companyId) {
      return {
        isAuthorized: false,
        user,
        companyId: null,
        error: 'No company access found'
      };
    }

    // Verify user is an admin for their company
    const { error: adminError } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('administrator_id', user.id)
      .eq('company_id', companyId)
      .single();

    // If not in company_administrators, check if they're the company owner
    if (adminError) {
      const { data: ownerCheck, error: ownerError } = await supabase
        .from('companies')
        .select('administrator_id')
        .eq('id', companyId)
        .eq('administrator_id', user.id)
        .single();

      if (ownerError || !ownerCheck) {
        return {
          isAuthorized: false,
          user,
          companyId,
          error: 'Admin access required for marketing features'
        };
      }
    }

    return {
      isAuthorized: true,
      user,
      companyId
    };

  } catch (error) {
    console.error('Marketing authentication error:', error);
    return {
      isAuthorized: false,
      user: null,
      companyId: null,
      error: 'Authentication error'
    };
  }
}

/**
 * Validate campaign creation/update data
 */
export interface TargetCriteria {
  tier_names?: string[];
  member_ids?: string[];
  balance_segments?: string[];
  member_age_segments?: string[];
  min_points?: number;
  max_points?: number;
}

export interface CampaignData {
  name: string;
  description?: string;
  message_title?: string;
  message_content: string;
  target_type: 'all' | 'tier' | 'individual' | 'custom';
  target_criteria?: TargetCriteria;
  scheduled_at?: string;
}

export function validateCampaignData(data: unknown): { isValid: boolean; errors: string[]; cleanData?: CampaignData } {
  const errors: string[] = [];

  // Type guard
  if (!data || typeof data !== 'object') {
    return { isValid: false, errors: ['Invalid data format'] };
  }

  const inputData = data as Record<string, unknown>;

  // Required fields
  if (!inputData.name || typeof inputData.name !== 'string' || inputData.name.trim().length === 0) {
    errors.push('Campaign name is required');
  }

  if (!inputData.message_content || typeof inputData.message_content !== 'string' || inputData.message_content.trim().length === 0) {
    errors.push('Message content is required');
  }

  if (!inputData.target_type || !['all', 'tier', 'individual', 'custom'].includes(inputData.target_type as string)) {
    errors.push('Valid target type is required (all, tier, individual, custom)');
  }

  // Message length validation
  if (inputData.message_content && typeof inputData.message_content === 'string' && inputData.message_content.length > 4096) {
    errors.push('Message content cannot exceed 4096 characters');
  }

  // Campaign name length validation
  if (inputData.name && typeof inputData.name === 'string' && inputData.name.length > 255) {
    errors.push('Campaign name cannot exceed 255 characters');
  }

  // Target criteria validation
  const targetCriteria = inputData.target_criteria as TargetCriteria | undefined;
  if (inputData.target_type === 'tier' && (!targetCriteria?.tier_names || !Array.isArray(targetCriteria.tier_names))) {
    errors.push('Tier names are required for tier targeting');
  }

  if (inputData.target_type === 'individual' && (!targetCriteria?.member_ids || !Array.isArray(targetCriteria.member_ids))) {
    errors.push('Member IDs are required for individual targeting');
  }

  // Date validation
  if (inputData.scheduled_at) {
    const scheduledDate = new Date(inputData.scheduled_at as string);
    if (isNaN(scheduledDate.getTime())) {
      errors.push('Invalid scheduled date format');
    } else if (scheduledDate <= new Date()) {
      errors.push('Scheduled date must be in the future');
    }
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  // Return clean data
  const cleanData: CampaignData = {
    name: (inputData.name as string).trim(),
    description: inputData.description ? (inputData.description as string).trim() : undefined,
    message_title: inputData.message_title ? (inputData.message_title as string).trim() : undefined,
    message_content: (inputData.message_content as string).trim(),
    target_type: inputData.target_type as 'all' | 'tier' | 'individual' | 'custom',
    target_criteria: targetCriteria || {},
    scheduled_at: inputData.scheduled_at as string | undefined
  };

  return { isValid: true, errors: [], cleanData };
}
