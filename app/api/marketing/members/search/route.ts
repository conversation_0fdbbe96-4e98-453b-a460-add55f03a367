import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { authenticateMarketingRequest } from '../../auth';

/**
 * GET /api/marketing/members/search
 * Search members for individual targeting
 */
export async function GET(req: NextRequest) {
  try {
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const query = searchParams.get('q') || '';
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const telegramOnly = searchParams.get('telegram_only') === 'true';

    const supabase = await createClient();

    let dbQuery = supabase
      .from('loyalty_members')
      .select('id, name, email, telegram_chat_id, loyalty_tier, lifetime_points, redeemed_points')
      .eq('company_id', auth.companyId)
      .limit(limit);

    // Add telegram filter if requested
    if (telegramOnly) {
      dbQuery = dbQuery.not('telegram_chat_id', 'is', null);
    }

    // Add search filter if query provided
    if (query.trim()) {
      dbQuery = dbQuery.or(`name.ilike.%${query}%,email.ilike.%${query}%`);
    }

    // Order by name
    dbQuery = dbQuery.order('name');

    const { data: members, error } = await dbQuery;

    if (error) {
      console.error('Error searching members:', error);
      return NextResponse.json({ error: 'Failed to search members' }, { status: 500 });
    }

    return NextResponse.json({
      members: members || [],
      total: (members || []).length,
      query,
      telegram_only: telegramOnly
    });

  } catch (error) {
    console.error('Member search error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
