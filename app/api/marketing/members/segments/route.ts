import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { authenticateMarketingRequest } from '../../auth';

/**
 * GET /api/marketing/members/segments
 * Get available member segments and statistics
 */
export async function GET() {
  try {
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const supabase = await createClient();

    // Get member data to generate segments dynamically
    const { data: members, error } = await supabase
      .from('loyalty_members')
      .select('id, loyalty_tier, lifetime_points, redeemed_points, telegram_chat_id, registration_date')
      .eq('company_id', auth.companyId);

    if (error) {
      console.error('Error fetching member data:', error);
      return NextResponse.json({ error: 'Failed to fetch member data' }, { status: 500 });
    }

    // Get loyalty tiers
    const { data: tiers, error: tiersError } = await supabase
      .from('loyalty_tiers')
      .select('tier_name')
      .eq('company_id', auth.companyId)
      .order('tier_name');

    if (tiersError) {
      console.error('Error fetching loyalty tiers:', tiersError);
    }

    // Calculate statistics from members data
    const totalMembers = members?.length || 0;
    const telegramMembers = members?.filter(m => m.telegram_chat_id).length || 0;

    // Helper function to categorize members by balance
    const getBalanceSegment = (points: number) => {
      if (points >= 1000) return 'high_balance';
      if (points >= 500) return 'medium_balance';
      return 'low_balance';
    };

    // Helper function to categorize members by registration age
    const getMemberAgeSegment = (registrationDate: string) => {
      const regDate = new Date(registrationDate);
      const now = new Date();
      const daysDiff = Math.floor((now.getTime() - regDate.getTime()) / (1000 * 3600 * 24));

      if (daysDiff <= 30) return 'new';
      if (daysDiff <= 90) return 'recent';
      return 'established';
    };

    // Tier distribution
    const tierDistribution = (tiers || []).map(tier => {
      const tierMembers = members?.filter(m => m.loyalty_tier === tier.tier_name) || [];
      const tierTelegramMembers = tierMembers.filter(m => m.telegram_chat_id);
      return {
        tier_name: tier.tier_name,
        total_members: tierMembers.length,
        telegram_members: tierTelegramMembers.length
      };
    });

    // Balance segment distribution
    const balanceSegments = {
      high_balance: members?.filter(m => {
        const availablePoints = (m.lifetime_points || 0) - (m.redeemed_points || 0);
        return m.telegram_chat_id && getBalanceSegment(availablePoints) === 'high_balance';
      }).length || 0,
      medium_balance: members?.filter(m => {
        const availablePoints = (m.lifetime_points || 0) - (m.redeemed_points || 0);
        return m.telegram_chat_id && getBalanceSegment(availablePoints) === 'medium_balance';
      }).length || 0,
      low_balance: members?.filter(m => {
        const availablePoints = (m.lifetime_points || 0) - (m.redeemed_points || 0);
        return m.telegram_chat_id && getBalanceSegment(availablePoints) === 'low_balance';
      }).length || 0
    };

    // Member age distribution
    const memberAgeSegments = {
      new: members?.filter(m => m.telegram_chat_id && m.registration_date && getMemberAgeSegment(m.registration_date) === 'new').length || 0,
      recent: members?.filter(m => m.telegram_chat_id && m.registration_date && getMemberAgeSegment(m.registration_date) === 'recent').length || 0,
      established: members?.filter(m => m.telegram_chat_id && m.registration_date && getMemberAgeSegment(m.registration_date) === 'established').length || 0
    };

    return NextResponse.json({
      statistics: {
        total_members: totalMembers,
        telegram_members: telegramMembers,
        telegram_percentage: totalMembers > 0 ? Math.round((telegramMembers / totalMembers) * 100) : 0
      },
      segments: {
        loyalty_tiers: tierDistribution,
        balance_segments: balanceSegments,
        member_age_segments: memberAgeSegments
      },
      available_tiers: (tiers || []).map(t => t.tier_name),
      available_balance_segments: ['high_balance', 'medium_balance', 'low_balance'],
      available_age_segments: ['new', 'recent', 'established']
    });

  } catch (error) {
    console.error('Member segments error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
