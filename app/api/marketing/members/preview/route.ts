import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { authenticateMarketingRequest } from '../../auth';

/**
 * POST /api/marketing/members/preview
 * Preview recipients for a campaign based on targeting criteria
 */
export async function POST(req: NextRequest) {
  try {
    const auth = await authenticateMarketingRequest();

    if (!auth.isAuthorized) {
      return NextResponse.json({ error: auth.error }, { status: 401 });
    }

    const body = await req.json();
    const { target_type, target_criteria } = body;

    // Debug logging
    console.log('📧 Marketing preview request:', { target_type, target_criteria });

    if (!target_type || !['all', 'tier', 'individual', 'custom'].includes(target_type)) {
      return NextResponse.json({
        error: 'Valid target_type is required (all, tier, individual, custom)'
      }, { status: 400 });
    }

    const supabase = await createClient();

    // Build query based on target type using existing tables
    let query = supabase
      .from('loyalty_members')
      .select(`
        id,
        name,
        loyalty_tier,
        telegram_chat_id,
        lifetime_points,
        registration_date
      `)
      .eq('company_id', auth.companyId);

    // Filter based on target type and criteria
    if (target_type === 'tier' && target_criteria?.tier_names) {
      const tierNames = Array.isArray(target_criteria.tier_names)
        ? target_criteria.tier_names
        : [target_criteria.tier_names];
      query = query.in('loyalty_tier', tierNames);
    } else if (target_type === 'individual' && target_criteria?.member_ids) {
      const memberIds = Array.isArray(target_criteria.member_ids)
        ? target_criteria.member_ids
        : [target_criteria.member_ids];
      query = query.in('id', memberIds);
    } else if (target_type === 'custom') {
      if (target_criteria?.min_points) {
        query = query.gte('lifetime_points', target_criteria.min_points);
      }
      if (target_criteria?.max_points) {
        query = query.lte('lifetime_points', target_criteria.max_points);
      }
      if (target_criteria?.tier_names) {
        const tierNames = Array.isArray(target_criteria.tier_names)
          ? target_criteria.tier_names
          : [target_criteria.tier_names];
        query = query.in('loyalty_tier', tierNames);
      }
    }

    const { data: allMembers, error } = await query;

    if (error) {
      console.error('Error getting eligible members:', error);
      return NextResponse.json({ error: 'Failed to get eligible members' }, { status: 500 });
    }

    console.log('📊 Found members before Telegram filter:', allMembers?.length || 0);

    // Filter for members with Telegram (only they can receive messages)
    const eligibleMembers = (allMembers || []).filter(member => member.telegram_chat_id);

    console.log('📱 Eligible members with Telegram:', eligibleMembers.length);
    console.log('📋 Eligible member details:', eligibleMembers.map(m => ({ id: m.id, name: m.name, telegram_chat_id: m.telegram_chat_id })));

    // Get total counts
    const { data: totalMembersData, error: totalError } = await supabase
      .from('loyalty_members')
      .select('id, telegram_chat_id')
      .eq('company_id', auth.companyId);

    if (totalError) {
      console.error('Error getting total member stats:', totalError);
    }

    const totalMembers = totalMembersData?.length || 0;
    const telegramEnabledMembers = totalMembersData?.filter(m => m.telegram_chat_id).length || 0;

    return NextResponse.json({
      recipients: eligibleMembers.map(member => ({
        id: member.id,
        name: member.name,
        tier: member.loyalty_tier || 'Basic',
        telegram_chat_id: member.telegram_chat_id
      })),
      total_count: eligibleMembers.length,
      telegram_enabled_count: telegramEnabledMembers,
      total_members: totalMembers,
      targeting: {
        type: target_type,
        criteria: target_criteria
      }
    });

  } catch (error) {
    console.error('Preview recipients error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
