import { NextRequest, NextResponse } from 'next/server'

/**
 * Simple test endpoint that doesn't interact with the database
 * This helps diagnose if the issue is with database permissions or something else
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: 'Test endpoint is working',
    timestamp: new Date().toISOString()
  })
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()
    
    return NextResponse.json({
      status: 'success',
      message: 'Test endpoint received data successfully',
      receivedData: data,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      status: 'error',
      message: 'Failed to parse request body',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 400 })
  }
}
