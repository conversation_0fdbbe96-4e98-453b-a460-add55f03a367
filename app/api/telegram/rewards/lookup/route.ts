import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// API key for Telegram integration security
// In a production environment, store this in an environment variable
const TELEGRAM_API_KEY = 'telegram-loyal-integration-key'

/**
 * GET /api/telegram/rewards/lookup - Look up a reward by code
 * This endpoint does not require auth but uses an API key for security
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const apiKey = searchParams.get('api_key')
    const companyId = searchParams.get('company_id')
    const code = searchParams.get('code')

    // Check API key
    if (apiKey !== TELEGRAM_API_KEY) {
      return NextResponse.json({
        error: 'Unauthorized',
        details: 'Invalid or missing API key'
      }, { status: 401 })
    }

    // Check required parameters
    if (!companyId || !code) {
      return NextResponse.json({
        error: 'Bad request',
        details: 'Company ID and reward code are required'
      }, { status: 400 })
    }

    // Look up reward by code (case insensitive)
    const supabase = getServiceRoleClient() // Use server-side client
    const { data, error } = await supabase
      .from('rewards')
      .select('*')
      .eq('company_id', companyId)
      .ilike('code', code)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({
          error: 'Not found',
          details: 'Reward not found with the provided code'
        }, { status: 404 })
      }

      return NextResponse.json({
        error: 'Database error',
        details: error.message
      }, { status: 500 })
    }

    // Check if reward is active
    if (!data.is_active) {
      return NextResponse.json({
        error: 'Inactive reward',
        details: 'This reward is not currently active'
      }, { status: 403 })
    }

    // Check if reward is expired
    if (data.expiration_date && new Date(data.expiration_date) < new Date()) {
      return NextResponse.json({
        error: 'Expired reward',
        details: 'This reward has expired'
      }, { status: 403 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error looking up reward:', error)
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
