import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function POST(request: Request) {
  try {
    const { memberId } = await request.json()

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // First, get the current member data to verify they have a Telegram connection
    const { data: currentMember, error: fetchError } = await supabase
      .from('loyalty_members')
      .select('name, phone_number, telegram_chat_id, telegram_username')
      .eq('id', memberId)
      .single()

    if (fetchError || !currentMember) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    if (!currentMember.telegram_chat_id) {
      return NextResponse.json({
        error: 'Member is not linked to Telegram',
        member: currentMember
      }, { status: 400 })
    }

    // Clear Telegram connection data for the member
    const { data, error } = await supabase
      .from('loyalty_members')
      .update({
        telegram_chat_id: null,
        telegram_username: null,
        linking_token: null,
        linked_at: null
      })
      .eq('id', memberId)
      .select('name, phone_number')
      .single()

    if (error) {
      console.error('Unlink error:', error)
      return NextResponse.json({ error: 'Failed to unlink member' }, { status: 500 })
    }

    console.log(`✅ Successfully unlinked member ${currentMember.name} (${memberId}) from Telegram chat ${currentMember.telegram_chat_id}`)

    return NextResponse.json({
      success: true,
      message: `Member ${currentMember.name} successfully unlinked from Telegram`,
      member: data,
      previousConnection: {
        chatId: currentMember.telegram_chat_id,
        username: currentMember.telegram_username
      }
    })

  } catch (error) {
    console.error('Unlink error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
