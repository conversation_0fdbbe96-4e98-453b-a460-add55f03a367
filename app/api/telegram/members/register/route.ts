import { NextRequest, NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';
import { z } from 'zod';

// Define validation schema for request body
const registrationSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  phone_number: z.string().min(10, { message: "Phone number must be at least 10 characters" }),
  email: z.string().email().nullable().optional(),
  birthday: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, { message: "Birthday must be in YYYY-MM-DD format" }),
  telegram_chat_id: z.string().min(1, { message: "Telegram ID is required" })
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();

    const validatedData = registrationSchema.parse(body);

    // Get Supabase client
    const supabase = getServiceRoleClient();

    // Check if a member with this phone number or telegram ID already exists
    const { data: existingMember, error: searchError } = await supabase
      .from('loyalty_members')
      .select('id, phone_number, telegram_chat_id')
      .or(`phone_number.eq.${validatedData.phone_number},telegram_chat_id.eq.${validatedData.telegram_chat_id}`);

    if (searchError) {
      console.error('Error checking existing member:', searchError);
      return NextResponse.json(
        { message: 'Error checking membership status' },
        { status: 500 }
      );
    }

    if (existingMember && existingMember.length > 0) {
      // Check if this is the same telegram user but different phone, or same phone but different telegram ID
      const samePhone = existingMember.some(m => m.phone_number === validatedData.phone_number);
      const sameTelegram = existingMember.some(m => m.telegram_chat_id === validatedData.telegram_chat_id);

      if (samePhone && !sameTelegram) {
        // Update existing member with Telegram ID
        const { error: updateError } = await supabase
          .from('loyalty_members')
          .update({
            telegram_chat_id: validatedData.telegram_chat_id,
            // Update email and birthday if they were empty before
            email: validatedData.email || undefined,
            birthday: validatedData.birthday || undefined
          })
          .eq('phone_number', validatedData.phone_number);

        if (updateError) {
          console.error('Error updating member:', updateError);
          return NextResponse.json(
            { message: 'Error updating your membership information' },
            { status: 500 }
          );
        }

        return NextResponse.json({
          message: 'Your account has been linked to Telegram!',
          status: 'linked'
        });
      }

      // Already registered
      return NextResponse.json({
        message: 'You are already registered in our loyalty program!',
        status: 'existing'
      });
    }

    // Create new loyalty member
    const { data: newMember, error: insertError } = await supabase
      .from('loyalty_members')
      .insert({
        name: validatedData.name,
        phone_number: validatedData.phone_number,
        email: validatedData.email || null,
        birthday: validatedData.birthday,
        telegram_chat_id: validatedData.telegram_chat_id,
        registration_date: new Date().toISOString(),
        lifetime_points: 0,
        redeemed_points: 0,
        expired_points: 0,
        loyalty_tier: null // Will be set by DB trigger based on points
      })
      .select('id')
      .single();

    if (insertError) {
      console.error('Error creating new member:', insertError);
      return NextResponse.json(
        { message: 'Error registering your account' },
        { status: 500 }
      );
    }

    // Send success response with new member ID
    return NextResponse.json({
      message: 'Welcome to our loyalty program!',
      status: 'created',
      memberId: newMember.id
    });

  } catch (error) {
    console.error('Registration error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.errors.map(err => `${err.path}: ${err.message}`).join(', ');
      return NextResponse.json(
        { message: `Validation error: ${errorMessages}` },
        { status: 400 }
      );
    }

    // Handle other errors
    return NextResponse.json(
      { message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}