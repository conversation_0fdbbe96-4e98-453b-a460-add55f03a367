import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createClient } from '@supabase/supabase-js'

// API key for Telegram integration security
const TELEGRAM_API_KEY = 'telegram-loyal-integration-key'

// These environment variables need to be set
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

// Create a service role client that bypasses RLS
const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Validation schema for member upsert
const memberUpsertSchema = z.object({
  api_key: z.string().refine(val => val === TELEGRAM_API_KEY, {
    message: 'Invalid API key'
  }),
  company_id: z.string().uuid({ message: 'Valid company ID is required' }),
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  phone_number: z.string().min(10, { message: 'Phone number must be at least 10 digits' }),
  email: z.string().email({ message: 'Please enter a valid email' }).optional().nullable(),
  birthday: z.string().optional().nullable(),
  telegram_chat_id: z.union([z.string(), z.number()]).transform(val => String(val)),
  loyalty_id: z.string().optional().nullable(),
  loyalty_tier: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  initial_points: z.number().nonnegative().default(0),
})

// Create a modified type that allows null for loyalty_id
type MemberDataWithNullableId = {
  company_id: string;
  name: string;
  phone_number: string;
  email: string | null;
  birthday: string | null;
  telegram_chat_id: string;
  loyalty_tier: string | null;
  loyalty_id: string | null;
  lifetime_points: number;
  redeemed_points: number;
  expired_points: number;
  registration_date: string;
};

/**
 * POST /api/telegram/members - Create or update a member via Telegram integration
 * Upserts a member by telegram_chat_id or loyalty_id
 * This endpoint does not require auth but uses an API key for security
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Received request to /api/telegram/members')

    let data;
    try {
      data = await request.json();
      console.log('Request data:', JSON.stringify(data, null, 2));
    } catch (error) {
      console.error('Error parsing JSON from request:', error);
      return NextResponse.json({
        error: 'Invalid JSON',
        details: 'Could not parse the request body as JSON'
      }, { status: 400 });
    }

    // Validate request data
    const validationResult = memberUpsertSchema.safeParse(data)

    if (!validationResult.success) {
      console.error('Validation error:', validationResult.error.format());
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.format()
      }, { status: 400 })
    }

    // Extract validated data (without API key)
    const {
      company_id,
      name,
      phone_number,
      email,
      telegram_chat_id,
      loyalty_id,
      loyalty_tier,
      initial_points,
    } = validationResult.data

    console.log('Creating new member with service role client');

    // Check if member exists by telegram_chat_id
    let existingMember = null
    if (telegram_chat_id) {
      console.log('Searching for member by telegram_chat_id:', telegram_chat_id)

      const { data: memberByTelegramId, error: telegramSearchError } = await supabaseAdmin
        .from('loyalty_members')
        .select('*')
        .eq('company_id', company_id)
        .eq('telegram_chat_id', telegram_chat_id)
        .single()

      if (telegramSearchError && telegramSearchError.code !== 'PGRST116') {
        console.error('Error searching by telegram_chat_id:', telegramSearchError)
      } else if (memberByTelegramId) {
        existingMember = memberByTelegramId
        console.log('Found existing member by telegram_chat_id:', existingMember.id)
      }
    }

    // If not found and loyalty_id provided, check by loyalty_id
    if (!existingMember && loyalty_id) {
      console.log('Searching for member by loyalty_id:', loyalty_id)
      const { data: memberByLoyaltyId, error: loyaltySearchError } = await supabaseAdmin
        .from('loyalty_members')
        .select('*')
        .eq('company_id', company_id)
        .eq('loyalty_id', loyalty_id)
        .single()

      if (loyaltySearchError && loyaltySearchError.code !== 'PGRST116') {
        console.error('Error searching by loyalty_id:', loyaltySearchError)
      } else if (memberByLoyaltyId) {
        existingMember = memberByLoyaltyId
        console.log('Found existing member by loyalty_id:', existingMember.id)
      }
    }

    // Set a default birthday if not provided (since it's a NOT NULL field)
    const defaultBirthday = '1900-01-01'; // Default date for unknown birthdays

    let result;

    if (existingMember) {
      // Update existing member
      const memberData = {
        name,
        phone_number,
        email: email || null,
        birthday: validationResult.data.birthday || existingMember.birthday || defaultBirthday, // Keep existing or use default
        telegram_chat_id: telegram_chat_id,
        loyalty_tier: loyalty_tier || null
      };

      console.log('Updating existing member:', existingMember.id);
      console.log('Member data to update:', JSON.stringify(memberData, null, 2));

      const { data: updatedMember, error: updateError } = await supabaseAdmin
        .from('loyalty_members')
        .update(memberData)
        .eq('id', existingMember.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating member:', updateError);
        throw updateError;
      }

      console.log('Successfully updated member:', updatedMember);

      result = {
        data: updatedMember,
        isNew: false
      };
    } else {
      // Generate a unique loyalty ID if not provided
      // Format: 'F' followed by 7 digits padded with zeros (e.g., ********)
      let generatedLoyaltyId = loyalty_id;

      if (!generatedLoyaltyId) {
        // Match the exact format from the database default:
        // concat('F', lpad((floor((random() * (10000000)::double precision)))::text, 7, '0'::text))
        const randomNum = Math.floor(Math.random() * 10000000);
        generatedLoyaltyId = 'F' + randomNum.toString().padStart(7, '0');
        console.log('Generated loyalty ID:', generatedLoyaltyId);
      }

      // Create new member
      const newMemberData: MemberDataWithNullableId = {
        company_id,
        name,
        phone_number,
        email: email || null,
        birthday: validationResult.data.birthday || defaultBirthday, // Use default birthday if not provided
        telegram_chat_id,
        loyalty_tier: loyalty_tier || null,
        loyalty_id: generatedLoyaltyId,
        lifetime_points: initial_points,
        redeemed_points: 0,
        expired_points: 0,
        registration_date: new Date().toISOString(),
      };

      console.log('Creating new member with data:', JSON.stringify(newMemberData, null, 2));

      // Try to insert with the correct format
      const { data: newMember, error: insertError } = await supabaseAdmin
        .from('loyalty_members')
        .insert(newMemberData)
        .select()
        .single();

      if (insertError) {
        console.error('Error inserting member:', insertError);

        // If it's a duplicate key issue, try again with a different random number
        if (insertError.message?.includes('duplicate key value')) {
          console.log('Trying with a different random loyalty ID');

          // Generate a new random ID
          const newRandomNum = Math.floor(Math.random() * 10000000);
          newMemberData.loyalty_id = 'F' + newRandomNum.toString().padStart(7, '0');

          const { data: retryMember, error: retryError } = await supabaseAdmin
            .from('loyalty_members')
            .insert(newMemberData)
            .select()
            .single();

          if (retryError) {
            console.error('Retry with new random ID also failed:', retryError);

            // If that fails too, try with null loyalty_id to let the database use its default
            console.log('Trying with null loyalty_id to use database default');

            newMemberData.loyalty_id = null;

            const { data: finalMember, error: finalError } = await supabaseAdmin
              .from('loyalty_members')
              .insert(newMemberData)
              .select()
              .single();

            if (finalError) {
              console.error('All attempts failed:', finalError);
              throw finalError;
            }

            console.log('Successfully created new member with database default ID:', finalMember);

            // Add initial points if specified
            if (initial_points > 0 && finalMember) {
              try {
                await addInitialPoints(finalMember.id, initial_points);
              } catch (pointsError) {
                console.error('Failed to add initial points:', pointsError);
              }
            }

            result = {
              data: finalMember,
              isNew: true
            };

            return NextResponse.json(result);
          }

          console.log('Successfully created new member with new random ID:', retryMember);

          // Add initial points if specified
          if (initial_points > 0 && retryMember) {
            try {
              await addInitialPoints(retryMember.id, initial_points);
            } catch (pointsError) {
              console.error('Failed to add initial points:', pointsError);
            }
          }

          result = {
            data: retryMember,
            isNew: true
          };

          return NextResponse.json(result);
        }

        // If it's a format issue, try with null loyalty_id to use the database default
        if (insertError.message?.includes('check_loyalty_id_format')) {
          console.log('Format issue - trying with null loyalty_id to use database default');

          newMemberData.loyalty_id = null;

          const { data: defaultMember, error: defaultError } = await supabaseAdmin
            .from('loyalty_members')
            .insert(newMemberData)
            .select()
            .single();

          if (defaultError) {
            console.error('Attempt with database default also failed:', defaultError);
            throw defaultError;
          }

          console.log('Successfully created new member with database default ID:', defaultMember);

          // Add initial points if specified
          if (initial_points > 0 && defaultMember) {
            try {
              await addInitialPoints(defaultMember.id, initial_points);
            } catch (pointsError) {
              console.error('Failed to add initial points:', pointsError);
            }
          }

          result = {
            data: defaultMember,
            isNew: true
          };

          return NextResponse.json(result);
        }

        throw insertError;
      }

      console.log('Successfully created new member:', newMember);

      // Add initial points if specified
      if (initial_points > 0 && newMember) {
        try {
          await addInitialPoints(newMember.id, initial_points);
          console.log(`Added ${initial_points} initial points to member ${newMember.id}`);
        } catch (pointsError) {
          console.error('Failed to add initial points:', pointsError);
          // Continue despite points error
        }
      }

      result = {
        data: newMember,
        isNew: true
      };
    }

    return NextResponse.json(result);
  } catch {
    console.error('Error in member upsert');

    // Dump raw error for debugging
    let errorDetails: string;
    try {
      errorDetails = JSON.stringify({}, null, 2);
    } catch {
      errorDetails = '';
    }

    return NextResponse.json({
      error: 'Failed to upsert member',
      details: errorDetails
    }, { status: 500 });
  }
}

/**
 * Helper function to add initial points to a member
 */
async function addInitialPoints(memberId: string, points: number) {
  const now = new Date()
  const expirationDate = new Date()
  expirationDate.setFullYear(expirationDate.getFullYear() + 1) // Default 1 year expiration

  await supabaseAdmin
    .from('points_transactions')
    .insert({
      member_id: memberId,
      transaction_type: 'earn',
      points: points,
      transaction_date: now.toISOString(),
      description: 'Initial points',
      expiration_date: expirationDate.toISOString(),
    })
}
