import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// API key for Telegram integration security
// In a production environment, store this in an environment variable
const TELEGRAM_API_KEY = 'telegram-loyal-integration-key'

/**
 * GET /api/telegram/members/lookup - Look up a member by loyalty_id
 * This endpoint does not require auth but uses an API key for security
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const apiKey = searchParams.get('api_key')
    const companyId = searchParams.get('company_id')
    const loyaltyId = searchParams.get('loyalty_id')

    // Check API key
    if (apiKey !== TELEGRAM_API_KEY) {
      return NextResponse.json({
        error: 'Unauthorized',
        details: 'Invalid or missing API key'
      }, { status: 401 })
    }

    // Check required parameters
    if (!companyId || !loyaltyId) {
      return NextResponse.json({
        error: 'Bad request',
        details: 'Company ID and loyalty ID are required'
      }, { status: 400 })
    }

    // Look up member by loyalty ID
    const supabase = getServiceRoleClient() // Use server-side client
    const { data, error } = await supabase
      .from('members')
      .select('*')
      .eq('company_id', companyId)
      .eq('loyalty_id', loyaltyId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({
          error: 'Not found',
          details: 'Member not found with the provided loyalty ID'
        }, { status: 404 })
      }

      return NextResponse.json({
        error: 'Database error',
        details: error.message
      }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error looking up member:', error)
    return NextResponse.json({
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
