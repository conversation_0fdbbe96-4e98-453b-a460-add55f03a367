import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ memberId: string }> }
) {
  try {
    const { memberId } = await params

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Fetch current Telegram status for the member
    const { data: member, error } = await supabase
      .from('loyalty_members')
      .select('telegram_chat_id, telegram_username, linked_at, linking_token')
      .eq('id', memberId)
      .single()

    if (error || !member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    return NextResponse.json({
      isLinked: !!member.telegram_chat_id,
      telegramChatId: member.telegram_chat_id,
      telegramUsername: member.telegram_username,
      linkedAt: member.linked_at,
      linkingToken: member.linking_token,
    })

  } catch (error) {
    console.error('Telegram status error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
