import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  // Create a debug response with all request information
  try {
    // Clone the request to read it multiple ways
    const clonedRequest = request.clone()

    // Get headers
    const headers: Record<string, string> = {}
    request.headers.forEach((value, key) => {
      headers[key] = value
    })

    // Try to get body as text
    let bodyText = ''
    try {
      bodyText = await clonedRequest.text()
    } catch (e) {
      bodyText = 'Could not read body as text: ' + String(e)
    }

    // Try to parse as JSON
    let bodyJson = null
    try {
      bodyJson = JSON.parse(bodyText)
    } catch (e) {
      bodyJson = { error: 'Could not parse body as JSON: ' + String(e) }
    }

    // Get query params
    const url = new URL(request.url)
    const queryParams: Record<string, string> = {}
    url.searchParams.forEach((value, key) => {
      queryParams[key] = value
    })

    // Check environment variables
    const envVars = {
      NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY_EXISTS: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      TELEGRAM_LOYAL_API_KEY_EXISTS: !!process.env.TELEGRAM_LOYAL_API_KEY,
    }

    // Return all debug info
    return NextResponse.json({
      debug: true,
      timestamp: new Date().toISOString(),
      request: {
        method: request.method,
        url: request.url,
        headers,
        queryParams,
        bodyText: bodyText.length > 1000 ? bodyText.substring(0, 1000) + '...' : bodyText,
        bodyJson,
      },
      environment: envVars
    })
  } catch (error) {
    return NextResponse.json({
      error: 'Debug endpoint error',
      message: String(error)
    }, { status: 500 })
  }
}

// Use a different approach without unused parameters
export const GET = async () => {
  try {
    // Initialize Supabase client with service role
    const supabase = getServiceRoleClient()

    // Create a test member if it doesn't exist
    const testMemberId = 'F9999999'
    const companyId = 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6'

    // Check if test member exists
    const { data: existingMember } = await supabase
      .from('loyalty_members')
      .select('*')
      .eq('company_id', companyId)
      .eq('loyalty_id', testMemberId)
      .single()

    if (!existingMember) {
      // Create test member
      const { data: newMember, error: createError } = await supabase
        .from('loyalty_members')
        .insert({
          company_id: companyId,
          loyalty_id: testMemberId,
          name: 'Test Member',
          email: '<EMAIL>',
          phone_number: '+1234567890',
          loyalty_tier: null, // Will be set by tier calculation trigger
          lifetime_points: 0,
          redeemed_points: 0,
          expired_points: 0,
          registration_date: new Date().toISOString()
        })
        .select()
        .single()

      if (createError) {
        return NextResponse.json({
          error: 'Failed to create test member',
          details: createError.message
        }, { status: 500 })
      }

      return NextResponse.json({
        message: 'Test member created successfully',
        testMember: {
          company_id: companyId,
          loyalty_id: testMemberId
        },
        details: newMember,
        instructions: `
          Use these values in your n8n workflow:

          company_id: ${companyId}
          loyalty_id: ${testMemberId}
          api_key: telegram-loyal-integration-key
          amount: 100

          POST to: https://loyal-et.vercel.app/api/telegram/receipts
        `
      })
    } else {
      // Member already exists
      return NextResponse.json({
        message: 'Test member already exists',
        testMember: {
          company_id: companyId,
          loyalty_id: testMemberId
        },
        details: existingMember,
        instructions: `
          Use these values in your n8n workflow:

          company_id: ${companyId}
          loyalty_id: ${testMemberId}
          api_key: telegram-loyal-integration-key
          amount: 100

          POST to: https://loyal-et.vercel.app/api/telegram/receipts
        `
      })
    }
  } catch (error) {
    return NextResponse.json({
      error: 'Debug endpoint error',
      message: String(error)
    }, { status: 500 })
  }
}
