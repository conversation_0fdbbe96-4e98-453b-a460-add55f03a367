/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'

// Ensure this API runs in Node.js so SECRET env vars (service role) are accessible
export const runtime = 'nodejs';

// API key for Telegram integration security
// In a production environment, store this in an environment variable
const TELEGRAM_API_KEY = 'telegram-loyal-integration-key'

// Validation schema for receipt submission
const receiptSchema = z.object({
  api_key: z.string().refine(val => val === TELEGRAM_API_KEY, {
    message: 'Invalid API key'
  }),
  company_id: z.string().uuid({ message: 'Valid company ID is required' }),
  loyalty_id: z.string().min(1, { message: 'Loyalty ID is required' }),
  amount: z.number().positive({ message: 'Amount must be positive' }),
  receipt_number: z.string().optional(),
  receipt_date: z.string().optional(),
  telegram_chat_id: z.union([z.string(), z.number()]).transform(val => String(val)).optional(),
  notes: z.string().optional().nullable(),
  // Additional fields
  uploader_telegram_id: z.string().optional(),
  subtotal: z.number().positive().optional(),
  service_description: z.string().optional(),
  payment_method: z.string().optional()
})

/**
 * POST /api/telegram/receipts - Submit a receipt via Telegram integration
 * Creates a receipt and awards points to the member
 * This endpoint does not require auth but uses an API key for security
 */
export async function POST(request: NextRequest) {
  console.log('[ReceiptsAPI] Received POST request')

  // Use service role key on server for RLS bypass
  const supabase = getServiceRoleClient()

  // STEP 1: Extract the payload - specifically handle n8n's format
  let payload: Record<string, any> = {}

  try {
    // Parse the request body
    const jsonData = await request.json()
    console.log('[ReceiptsAPI] Request body (raw):', JSON.stringify(jsonData))

    // n8n sends data in a nested "body" property
    if (jsonData.body && typeof jsonData.body === 'object') {
      console.log('[ReceiptsAPI] Found n8n body format')
      payload = jsonData.body
    } else {
      // Direct JSON format
      payload = jsonData
    }

    console.log('[ReceiptsAPI] Processed payload:', JSON.stringify(payload))
    console.log('[ReceiptsAPI] Receipt number in payload:', payload.receipt_number)
  } catch (error) {
    console.error('[ReceiptsAPI] Error parsing request:', error)
    return NextResponse.json({
      error: 'Invalid request format',
      message: 'Could not parse request body as JSON'
    }, { status: 400 })
  }

  // STEP 2: Validate payload
  const validationResult = receiptSchema.safeParse(payload)

  if (!validationResult.success) {
    console.error('[ReceiptsAPI] Validation failed:', validationResult.error.format())
    return NextResponse.json({
      error: 'Validation failed',
      details: validationResult.error.format(),
      receivedPayload: payload
    }, { status: 400 })
  }

  // STEP 3: Extract validated data
  const { api_key, company_id, loyalty_id, amount } = validationResult.data

  // STEP 4: Verify API key
  if (api_key !== TELEGRAM_API_KEY) {
    console.error('[ReceiptsAPI] Invalid API key')
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // STEP 5: Look up member
  console.log('[ReceiptsAPI] Looking up member:', { company_id, loyalty_id })

  const { data: members, error: memberError } = await supabase
    .from('loyalty_members')
    .select('*')
    .eq('company_id', company_id)
    .eq('loyalty_id', loyalty_id)

  if (memberError) {
    console.error('[ReceiptsAPI] Database error:', memberError)
    return NextResponse.json({
      error: 'Database error',
      message: memberError.message
    }, { status: 500 })
  }

  if (!members || members.length === 0) {
    console.error('[ReceiptsAPI] Member not found:', { company_id, loyalty_id })
    return NextResponse.json({
      error: 'Member not found',
      details: 'The loyalty ID provided does not match any member in this company',
      debug: { company_id, loyalty_id }
    }, { status: 404 })
  }

  const member = members[0]
  console.log('[ReceiptsAPI] Member found:', member)

  // STEP 6: Get company settings
  const { data: company, error: companyError } = await supabase
    .from('companies')
    .select('points_earning_ratio, points_expiration_days')
    .eq('id', company_id)
    .single()

  if (companyError || !company) {
    console.error('[ReceiptsAPI] Company error:', companyError)
    return NextResponse.json({
      error: 'Company not found',
      message: companyError?.message || 'Could not find company settings'
    }, { status: 500 })
  }

  // STEP 7: Calculate points
  const pointsRate = company.points_earning_ratio || 1
  const expirationDays = company.points_expiration_days || 0
  const pointsEarned = Math.floor(amount * pointsRate)

  // Generate required values if not provided
  const timestamp = new Date().getTime().toString().slice(-6);
  const random4digits = Math.floor(1000 + Math.random() * 9000);
  const generatedReceiptNumber = `TG-${timestamp}-${random4digits}`;
  const currentDate = new Date().toISOString();
  // Compute expiration date for points
  const expirationDate = new Date(Date.now() + expirationDays * 24 * 60 * 60 * 1000).toISOString();

  // STEP 8: Create receipt
  const receiptData = {
    company_id,
    member_id: member.id,
    loyalty_id: loyalty_id,
    uploader_telegram_id: validationResult.data.uploader_telegram_id || `tg-${validationResult.data.telegram_chat_id || 'unknown'}`,
    receipt_number: validationResult.data.receipt_number || generatedReceiptNumber,
    purchase_date: validationResult.data.receipt_date || currentDate,
    total_amount: amount,
    subtotal: validationResult.data.subtotal || amount, // Default to amount if subtotal not provided
    service_description: validationResult.data.service_description || 'Purchase via Telegram',
    payment_method: validationResult.data.payment_method || 'CASH',
    points_awarded: pointsEarned,
    created_at: currentDate
  };

  // STEP 7.5: Check for duplicate receipt
  try {
    const { data: duplicateReceipts, error: duplicateError } = await supabase
      .from('receipts')
      .select('id, receipt_number, member_id, created_at')
      .eq('receipt_number', receiptData.receipt_number)
      .eq('member_id', member.id)
      .order('created_at', { ascending: false })
      .limit(1);

    if (duplicateError) {
      console.error('[ReceiptsAPI] Error checking for duplicate receipt:', duplicateError, { receipt_number: receiptData.receipt_number, member_id: member.id });
    } else if (duplicateReceipts && duplicateReceipts.length > 0) {
      console.error('[ReceiptsAPI] Duplicate receipt detected:', { receipt_number: receiptData.receipt_number, member_id: member.id, duplicateId: duplicateReceipts[0].id });
      return NextResponse.json({
        error: 'Duplicate receipt',
        message: 'A receipt with this number already exists for this member.',
        duplicateReceipt: duplicateReceipts[0]
      }, { status: 409 });
    }
  } catch (dupException) {
    console.error('[ReceiptsAPI] Exception during duplicate receipt check:', dupException, { receipt_number: receiptData.receipt_number, member_id: member.id });
  }

  console.log('[ReceiptsAPI] About to create receipt with data:', JSON.stringify(receiptData));

  let receipt;
  try {
    const { data: receiptResult, error: receiptError } = await supabase
      .from('receipts')
      .insert(receiptData)
      .select()
      .single();

    if (receiptError) {
      console.error('[ReceiptsAPI] Receipt creation error:', receiptError);
      throw receiptError;
    }

    if (!receiptResult) {
      throw new Error('Receipt created but no data returned');
    }

    receipt = receiptResult;
    console.log('[ReceiptsAPI] Receipt created successfully:', receipt.id);

    // STEP 8.5: Create receipt_items for analytics using the proper processor
    try {
      console.log('[ReceiptsAPI] Processing receipt items for analytics...');

      const { processReceiptItems } = await import('@/lib/receipt-items-processor');

      const result = await processReceiptItems(
        receipt.id,
        company_id,
        validationResult.data.service_description || 'General Purchase',
        amount,
        validationResult.data.subtotal || amount
      );

      if (result.success) {
        console.log(`[ReceiptsAPI] Created ${result.items_created} receipt items successfully`);
      } else {
        console.error('[ReceiptsAPI] Failed to process receipt items:', result.error);
        // Don't fail the whole request, just log the error
      }
    } catch (receiptItemException) {
      console.error('[ReceiptsAPI] Exception processing receipt items:', receiptItemException);
      // Don't fail the whole request, just log the error
    }
  } catch (error) {
    console.error('[ReceiptsAPI] Receipt error details:', error);
    return NextResponse.json({
      error: 'Failed to create receipt',
      message: error instanceof Error ? error.message : 'Unknown error',
      receiptData
    }, { status: 500 });
  }

  // STEP 9: Create a points transaction linked to this receipt
  try {
    const { data: transaction, error: txError } = await supabase
      .from('points_transactions')
      .insert({
        member_id: member.id,
        company_id,
        loyalty_id: member.loyalty_id,
        receipt_id: receipt.id,
        points_change: pointsEarned,
        description: `Points for receipt ${receipt.receipt_number}`,
        transaction_type: 'EARN',
        expiration_date: expirationDate
      })
      .select()
      .single();

    if (txError) {
      console.error('[ReceiptsAPI] Failed to insert points transaction:', txError);
    } else {
      console.log('[ReceiptsAPI] Points transaction created:', transaction.id);
    }
  } catch (txException) {
    console.error('[ReceiptsAPI] Exception inserting points transaction:', txException);
  }

  // STEP 10: Verify transaction was recorded by fetching the latest points transaction
  try {
    const { data: transaction, error: transactionError } = await supabase
      .from('points_transactions')
      .select('*')
      .eq('member_id', member.id)
      .order('created_at', { ascending: false })
      .limit(1);

    if (transactionError) {
      console.error('[ReceiptsAPI] Transaction verification error:', transactionError);
    } else if (!transaction || transaction.length === 0) {
      console.warn('[ReceiptsAPI] No transaction found after points award');
    } else {
      console.log('[ReceiptsAPI] Transaction verified:', transaction[0].id);
    }
  } catch (error) {
    console.error('[ReceiptsAPI] Transaction verification exception:', error);
  }

  // STEP 10: Return success
  return NextResponse.json({
    success: true,
    message: 'Receipt processed successfully',
    receipt_id: receipt.id,
    points_earned: pointsEarned,
    member: {
      id: member.id,
      loyalty_id: member.loyalty_id,
      name: member.name
    }
  }, { status: 201 })
}

// Special debug GET handler - no validation, just lookup members
export async function GET(request: NextRequest) {
  const supabase = getServiceRoleClient()

  // Extract company_id and loyalty_id from query params
  const company_id = request.nextUrl.searchParams.get('company_id')
  const loyalty_id = request.nextUrl.searchParams.get('loyalty_id')

  // Look up the member
  const lookupRes = company_id && loyalty_id ? await supabase
    .from('loyalty_members')
    .select('*')
    .eq('company_id', company_id)
    .eq('loyalty_id', loyalty_id)
    .single() : null

  // Return simple diagnosis
  return NextResponse.json({
    debug: true,
    timestamp: new Date().toISOString(),
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
    serviceRoleKeyLength: process.env.SUPABASE_SERVICE_ROLE_KEY?.length,
    request: {
      url: request.url,
      company_id,
      loyalty_id
    },
    member: lookupRes?.data || null,
    error: lookupRes?.error || null,
    requestHeaders: Object.fromEntries(request.headers),
    env: {
      NODE_ENV: process.env.NODE_ENV,
      VERCEL_ENV: process.env.VERCEL_ENV
    }
  })
}
