import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { randomBytes } from 'crypto'

export async function POST(request: Request) {
  try {
    const { memberId } = await request.json()

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Generate unique linking token
    const linkingToken = randomBytes(16).toString('hex')

    // Update member with linking token
    const { data, error } = await supabase
      .from('loyalty_members')
      .update({ linking_token: linkingToken })
      .eq('id', memberId)
      .select('phone_number, name')
      .single()

    if (error) {
      console.error('Token generation error:', error)
      return NextResponse.json({ error: 'Failed to generate token' }, { status: 500 })
    }

    // Return deep link for Telegram
    const botUsername = process.env.TELEGRAM_BOT_USERNAME
    const telegramLink = `https://t.me/${botUsername}?start=${linkingToken}`

    return NextResponse.json({
      linkingToken,
      link: telegramLink, // Changed from telegramLink to link
      telegramLink, // Keep this for backward compatibility
      instructions: `Send this link to member: ${telegramLink}`,
      member: data
    })

  } catch (error) {
    console.error('Token generation error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const memberId = searchParams.get('memberId')

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Check if member already has an active linking token
    const { data: member, error } = await supabase
      .from('loyalty_members')
      .select('linking_token, name, phone_number, telegram_chat_id')
      .eq('id', memberId)
      .single()

    if (error) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 })
    }

    return NextResponse.json({
      hasToken: !!member.linking_token,
      isLinked: !!member.telegram_chat_id,
      member: {
        name: member.name,
        phone_number: member.phone_number
      }
    })

  } catch (error) {
    console.error('Token check error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
