import { NextResponse } from 'next/server'
import { setTelegramWebhook, deleteTelegramWebhook } from '@/lib/telegram'

export async function POST(request: Request) {
  try {
    const { action, webhookUrl } = await request.json()

    if (action === 'set') {
      if (!webhookUrl) {
        return NextResponse.json({ error: 'Webhook URL required' }, { status: 400 })
      }

      const success = await setTelegramWebhook(webhookUrl)

      if (success) {
        return NextResponse.json({
          message: 'Webhook set successfully',
          webhookUrl
        })
      } else {
        return NextResponse.json({ error: 'Failed to set webhook' }, { status: 500 })
      }
    } else if (action === 'delete') {
      const success = await deleteTelegramWebhook()

      if (success) {
        return NextResponse.json({ message: 'Webhook deleted successfully' })
      } else {
        return NextResponse.json({ error: 'Failed to delete webhook' }, { status: 500 })
      }
    } else {
      return NextResponse.json({ error: 'Invalid action. Use "set" or "delete"' }, { status: 400 })
    }

  } catch (error) {
    console.error('Webhook setup error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
