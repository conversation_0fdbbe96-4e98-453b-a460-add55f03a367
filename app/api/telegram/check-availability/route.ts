import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

/**
 * Check if a Telegram chat ID is available for linking
 * Used to validate uniqueness before attempting to link
 */
export async function POST(request: Request) {
  try {
    const { chatId } = await request.json()

    if (!chatId) {
      return NextResponse.json({ error: 'Chat ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Check if chat_id is already linked to any member
    const { data: existingLink, error } = await supabase
      .from('loyalty_members')
      .select('id, name, loyalty_id, telegram_username, linked_at')
      .eq('telegram_chat_id', chatId.toString())
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Telegram availability check error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    const isAvailable = !existingLink

    return NextResponse.json({
      available: isAvailable,
      chatId: chatId.toString(),
      ...(existingLink && {
        linkedTo: {
          memberId: existingLink.id,
          memberName: existingLink.name,
          loyaltyId: existingLink.loyalty_id,
          telegramUsername: existingLink.telegram_username,
          linkedAt: existingLink.linked_at
        }
      })
    })

  } catch (error) {
    console.error('Telegram availability check error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
