import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'

// Ensure this API runs in Node.js so SECRET env vars (service role) are accessible
export const runtime = 'nodejs';

// API key for Telegram integration security
// In a production environment, store this in an environment variable
const TELEGRAM_API_KEY = 'telegram-loyal-integration-key'

// Enhanced validation schema for redemption requests that includes receipt details
const redemptionSchema = z.object({
  api_key: z.string().refine(val => val === TELEGRAM_API_KEY, {
    message: 'Invalid API key'
  }),
  company_id: z.string().uuid({ message: 'Valid company ID is required' }),
  loyalty_id: z.string().min(1, { message: 'Loyalty ID is required' }),
  reward_code: z.string().min(1, { message: 'Reward code is required' }),
  telegram_chat_id: z.union([z.string(), z.number()]).transform(val => String(val)).optional(),
  notes: z.string().optional().nullable(),
  // New receipt details fields
  receipt_total: z.number().positive({ message: 'Receipt total amount is required' }).optional(),
  receipt_number: z.string().optional(),
  receipt_id: z.string().uuid().optional(),
})

/**
 * POST /api/telegram/redemptions - Process a redemption via Telegram integration
 * Redeems a reward for a member and deducts points
 * This endpoint does not require auth but uses an API key for security
 */
export async function POST(request: NextRequest) {
  console.log('[RedemptionsAPI] Received POST request');

  // Use service role key on server for RLS bypass
  const supabase = getServiceRoleClient();

  try {
    // Extract the payload - specifically handle n8n's format
    let payload = {};

    try {
      // Parse the request body
      const jsonData = await request.json();
      console.log('[RedemptionsAPI] Request body:', jsonData);

      // n8n sends data in a nested "body" property
      if (jsonData.body && typeof jsonData.body === 'object') {
        console.log('[RedemptionsAPI] Found n8n body format');
        payload = jsonData.body;
      } else {
        // Direct JSON format
        payload = jsonData;
      }
    } catch (error) {
      console.error('[RedemptionsAPI] Error parsing request:', error);
      return NextResponse.json({
        error: 'Invalid request format',
        message: 'Could not parse request body as JSON'
      }, { status: 400 });
    }

    // Validate request data
    const validationResult = redemptionSchema.safeParse(payload);

    if (!validationResult.success) {
      console.error('[RedemptionsAPI] Validation failed:', validationResult.error.format());
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.format()
      }, { status: 400 });
    }

    // Extract validated data
    const {
      company_id,
      loyalty_id,
      reward_code,
      notes,
      receipt_total,
      receipt_number,
      receipt_id,
    } = validationResult.data;

    console.log(`[RedemptionsAPI] Processing redemption for loyalty_id: ${loyalty_id}, reward_code: ${reward_code}`);

    // Look up member by loyalty ID
    const { data: members, error: memberError } = await supabase
      .from('loyalty_members')
      .select('*')
      .eq('company_id', company_id)
      .eq('loyalty_id', loyalty_id);

    if (memberError) {
      console.error('[RedemptionsAPI] Member lookup error:', memberError);
      return NextResponse.json({
        error: 'Database error',
        message: memberError.message
      }, { status: 500 });
    }

    if (!members || members.length === 0) {
      console.error('[RedemptionsAPI] Member not found:', { company_id, loyalty_id });
      return NextResponse.json({
        error: 'Member not found',
        details: 'The loyalty ID provided does not match any member in this company',
        debug: { company_id, loyalty_id }
      }, { status: 404 });
    }

    const member = members[0];
    console.log('[RedemptionsAPI] Member found:', member.name);

    // Look up reward by code
    const { data: reward, error: rewardError } = await supabase
      .from('rewards')
      .select('*')
      .eq('company_id', company_id)
      .eq('code', reward_code.toUpperCase())
      .eq('is_active', true)
      .single();

    if (rewardError || !reward) {
      console.error('[RedemptionsAPI] Reward lookup error:', rewardError);
      return NextResponse.json({
        error: 'Reward not found',
        details: 'The reward code provided does not match any active reward in this company',
        debug: { reward_code }
      }, { status: 404 });
    }

    console.log('[RedemptionsAPI] Reward found:', reward.title);

    // Check if reward is expired
    if (reward.expiration_date && new Date(reward.expiration_date) < new Date()) {
      console.error('[RedemptionsAPI] Reward expired:', reward.expiration_date);
      return NextResponse.json({
        error: 'Reward expired',
        details: `This reward has expired on ${reward.expiration_date} and can no longer be redeemed`
      }, { status: 400 });
    }

    // Calculate available points
    const availablePoints = (member.lifetime_points || 0) -
                           (member.redeemed_points || 0) -
                           (member.expired_points || 0);

    // Check if member has enough points
    if (availablePoints < reward.points_required) {
      console.error('[RedemptionsAPI] Insufficient points:',
        { required: reward.points_required, available: availablePoints });
      return NextResponse.json({
        error: 'Insufficient points',
        details: `This reward requires ${reward.points_required} points, but member only has ${availablePoints} available`
      }, { status: 400 });
    }

    // Calculate the actual discount value and final amount
    let discountValue = null;
    let finalAmount = receipt_total;
    let discountAmount = 0;

    if (receipt_total) {
      if (reward.reward_value_type === 'PERCENTAGE') {
        // For percentage discounts, calculate the actual amount
        discountAmount = (receipt_total * reward.reward_value) / 100;
        discountValue = `${reward.reward_value}%`;
        finalAmount = receipt_total - discountAmount;
      } else if (reward.reward_value_type === 'FIXED_AMOUNT') {
        // For fixed discounts, use the value directly
        discountAmount = reward.reward_value;
        discountValue = `${reward.reward_value} Birr`;
        finalAmount = receipt_total - discountAmount;

        // Ensure final amount is not negative
        finalAmount = Math.max(0, finalAmount);
      } else {
        // For FREE_SERVICE, POINTS_BONUS, PRODUCT_GIFT
        discountValue = reward.description;
        // No discount calculation for non-monetary rewards
      }
    } else {
      // If receipt_total is not provided, just format the discount value
      if (reward.reward_value_type === 'PERCENTAGE') {
        discountValue = `${reward.reward_value}%`;
      } else if (reward.reward_value_type === 'FIXED_AMOUNT') {
        discountValue = `${reward.reward_value} Birr`;
      } else {
        discountValue = reward.description;
      }
    }

    console.log('[RedemptionsAPI] Calculated discount:',
      { type: reward.reward_value_type, value: discountValue, amount: discountAmount });

    // Deduct points using the stored procedure
    try {
      console.log('[RedemptionsAPI] Deducting points:', reward.points_required);
      const { error: deductError } = await supabase.rpc('deduct_points_transaction', {
        p_member_id: member.id,
        p_company_id: company_id,
        p_points: reward.points_required,
        p_description: `Redeemed ${reward.title} (${discountValue})`,
        p_transaction_type: 'REDEEM'
      });

      if (deductError) {
        console.error('[RedemptionsAPI] Points deduction error:', deductError);
        return NextResponse.json({
          error: 'Failed to deduct points',
          message: deductError.message
        }, { status: 500 });
      }
    } catch (error) {
      console.error('[RedemptionsAPI] Points deduction exception:', error);
      return NextResponse.json({
        error: 'Failed to deduct points',
        message: error instanceof Error ? error.message : 'Unknown error'
      }, { status: 500 });
    }

    // Create redemption record
    const redemptionData = {
      member_id: member.id,
      reward_id: reward.id,
      company_id: company_id,
      points_used: reward.points_required,
      // Store discount amount in the existing applied_value column
      applied_value: discountAmount,
      status: 'REDEEMED',
      // Store receipt details in the notes field as JSON string
      notes: JSON.stringify({
        receipt_total: receipt_total,
        receipt_number: receipt_number,
        receipt_id: receipt_id,
        discount_value: discountValue,
        final_amount: finalAmount,
        message: notes || `Redeemed ${reward.title} (${discountValue})`,
        created_via: 'telegram_bot'
      })
    };

    console.log('[RedemptionsAPI] Creating redemption record:', redemptionData);

    const { data: redemptionRecord, error: redemptionError } = await supabase
      .from('reward_redemptions')
      .insert(redemptionData)
      .select()
      .single();

    if (redemptionError) {
      console.error('[RedemptionsAPI] Redemption record error:', redemptionError);
      return NextResponse.json({
        error: 'Failed to create redemption record',
        message: redemptionError.message
      }, { status: 500 });
    }

    console.log('[RedemptionsAPI] Redemption successful:', redemptionRecord.id);

    // Get updated member data with latest points
    const { data: updatedMember, error: updatedMemberError } = await supabase
      .from('loyalty_members')
      .select('id, name, loyalty_id, lifetime_points, redeemed_points, expired_points')
      .eq('id', member.id)
      .single();

    if (updatedMemberError) {
      console.error('[RedemptionsAPI] Updated member fetch error:', updatedMemberError);
      // Continue anyway since the redemption was successful
    }

    // Format the response with payment details
    const updatedAvailablePoints = updatedMember ?
      (updatedMember.lifetime_points || 0) -
      (updatedMember.redeemed_points || 0) -
      (updatedMember.expired_points || 0) :
      availablePoints - reward.points_required;

    const response = {
      success: true,
      message: 'Reward redeemed successfully',
      redemption_id: redemptionRecord.id,
      reward: {
        title: reward.title,
        code: reward.code,
        type: reward.reward_type,
        discount_value: discountValue
      },
      payment: receipt_total ? {
        original_amount: receipt_total,
        discount_amount: discountAmount,
        final_amount: finalAmount,
        currency: 'Birr'
      } : null,
      member: {
        id: member.id,
        name: member.name,
        loyalty_id: member.loyalty_id,
        points_used: reward.points_required,
        remaining_points: updatedAvailablePoints
      }
    };

    console.log('[RedemptionsAPI] Response:', response);
    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error('[RedemptionsAPI] Unhandled error:', error);
    return NextResponse.json({
      error: 'Failed to process redemption',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
