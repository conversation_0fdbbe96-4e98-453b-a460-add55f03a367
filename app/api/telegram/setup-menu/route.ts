import { NextResponse } from 'next/server'
import { setTelegramBotMenu } from '@/lib/telegram'

export async function POST(request: Request) {
  try {
    // Allow setup from admin interface without auth token for easier deployment
    const body = await request.json().catch(() => ({}))
    const { adminSetup } = body

    if (!adminSetup) {
      // Only require auth for non-admin setup calls
      const authHeader = request.headers.get('authorization')
      if (!authHeader || !authHeader.includes('setup-token')) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }
    }

    const success = await setTelegramBotMenu()

    if (success) {
      return NextResponse.json({
        success: true,
        message: 'Bot menu configured successfully. All users (new and existing) will now see the menu when they open the bot chat.',
        instructions: {
          newUsers: 'Will see the menu immediately when starting the bot',
          existingUsers: 'Will see the menu when they next open the chat or restart Telegram',
          fallback: 'Users can always type "/" or /help to see all commands'
        }
      })
    } else {
      return NextResponse.json({
        error: 'Failed to configure bot menu'
      }, { status: 500 })
    }
  } catch (error) {
    console.error('Menu setup error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Telegram Bot Menu Setup',
    commands: [
      '/start - Initialize bot and link account',
      '/link - Get account linking instructions',
      '/balance - Check your points balance',
      '/rewards - Browse available rewards',
      '/history - View transaction history',
      '/profile - View your profile information',
      '/settings - Bot preferences',
      '/help - Show help message',
      '/unlink - Unlink your account'
    ]
  })
}
