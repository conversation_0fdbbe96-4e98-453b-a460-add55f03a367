import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Use service role client to bypass RLS
    const supabase = getServiceRoleClient()

    // Get data from our new live dashboard metrics view
    const { data: viewData, error: viewError } = await supabase
      .from('dashboard_metrics_live')
      .select('*')
      .eq('company_id', companyId)
      .single()

    if (!viewError && viewData) {
      // Get reward statistics separately since they're not in the view
      const { data: rewardStats, error: rewardError } = await supabase
        .from('rewards')
        .select('id, is_active, expiration_date')
        .eq('company_id', companyId)

      if (rewardError) {
        console.error('Error fetching reward stats:', rewardError)
      }

      const totalRewards = rewardStats?.length || 0
      const activeRewards = rewardStats?.filter(r =>
        r.is_active && new Date(r.expiration_date) > new Date()
      ).length || 0

      // Use materialized view data + reward statistics
      return NextResponse.json({
        data: {
          totalMembers: viewData.total_members,
          activeMembers: viewData.active_members_30d,
          totalRewards,
          activeRewards,
          totalPoints: viewData.total_lifetime_points,
          redeemedPoints: viewData.total_redeemed_points,
          availablePoints: viewData.total_available_points,
          redemptionRate: viewData.redemption_rate_percentage,
          lastUpdated: viewData.last_updated
        }
      })
    }

    // Fallback to direct queries if view doesn't exist
    console.log('Dashboard view not found, using transaction-based calculations')

    // Get member count
    const { data: memberStats, error: memberError } = await supabase
      .from('loyalty_members')
      .select('id')
      .eq('company_id', companyId)

    if (memberError) {
      console.error('Error fetching member stats:', memberError)
      return NextResponse.json(
        { error: 'Failed to fetch member statistics' },
        { status: 500 }
      )
    }

    const totalMembers = memberStats?.length || 0

    // Calculate points from actual transactions (source of truth)
    const { data: transactionStats, error: transactionError } = await supabase
      .from('points_transactions')
      .select('transaction_type, points_change')
      .eq('company_id', companyId)

    if (transactionError) {
      console.error('Error fetching transaction stats:', transactionError)
      return NextResponse.json(
        { error: 'Failed to fetch transaction statistics' },
        { status: 500 }
      )
    }

    // Calculate totals from transaction data (source of truth)
    const totalEarnedPoints = transactionStats
      ?.filter(t => t.transaction_type === 'EARN')
      .reduce((sum, t) => sum + (t.points_change || 0), 0) || 0

    const redeemedPoints = Math.abs(transactionStats
      ?.filter(t => t.transaction_type === 'REDEEM')
      .reduce((sum, t) => sum + (t.points_change || 0), 0) || 0)

    const expiredPoints = Math.abs(transactionStats
      ?.filter(t => t.transaction_type === 'EXPIRE')
      .reduce((sum, t) => sum + (t.points_change || 0), 0) || 0)

    const totalPoints = totalEarnedPoints // This is lifetime points earned
    const availablePoints = totalPoints - redeemedPoints - expiredPoints

    // Get active members (with transactions in last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const { data: activeTransactions } = await supabase
      .from('points_transactions')
      .select('member_id')
      .eq('company_id', companyId)
      .gte('created_at', thirtyDaysAgo.toISOString())

    const activeMembers = activeTransactions
      ? [...new Set(activeTransactions.map(t => t.member_id))].length
      : 0

    // Get reward statistics
    const { data: rewardStats, error: rewardError } = await supabase
      .from('rewards')
      .select('id, is_active, expiration_date')
      .eq('company_id', companyId)

    if (rewardError) {
      console.error('Error fetching reward stats:', rewardError)
    }

    const totalRewards = rewardStats?.length || 0
    const activeRewards = rewardStats?.filter(r =>
      r.is_active && new Date(r.expiration_date) > new Date()
    ).length || 0

    // Calculate redemption rate
    const redemptionRate = totalPoints > 0 ? (redeemedPoints / totalPoints) * 100 : 0

    return NextResponse.json({
      data: {
        totalMembers,
        activeMembers,
        totalRewards,
        activeRewards,
        totalPoints,
        redeemedPoints,
        availablePoints,
        redemptionRate: Math.round(redemptionRate * 10) / 10, // Round to 1 decimal
        lastUpdated: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Unexpected error in dashboard metrics:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
