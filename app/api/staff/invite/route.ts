import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { emails } = body

    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      return NextResponse.json({ error: 'Email addresses are required' }, { status: 400 })
    }

    // Get the company for this user (must be administrator)
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .eq('administrator_id', user.id)
      .single()

    if (companyError) {
      console.error('Error fetching company:', companyError)
      return NextResponse.json({ error: 'Company not found or unauthorized' }, { status: 404 })
    }

    // Filter out empty emails and validate format
    const validEmails = emails
      .filter((email: string) => email && email.trim().length > 0)
      .map((email: string) => email.trim().toLowerCase())
      .filter((email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        return emailRegex.test(email)
      })

    if (validEmails.length === 0) {
      return NextResponse.json({ error: 'No valid email addresses provided' }, { status: 400 })
    }

    // For MVP, we'll store staff emails in a simple array field in the companies table
    // In the future, this could be expanded to a proper staff/users table with roles
    const { error: updateError } = await supabase
      .from('companies')
      .update({
        staff_emails: validEmails,
        updated_at: new Date().toISOString()
      })
      .eq('id', company.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating company with staff emails:', updateError)
      return NextResponse.json({ error: 'Failed to save staff emails' }, { status: 500 })
    }

    // TODO: In a production system, you would:
    // 1. Send invitation emails to the staff members
    // 2. Create temporary invitation tokens
    // 3. Set up a proper user registration flow for staff
    // 4. Implement role-based access control

    // For now, we'll just return success with the saved emails
    return NextResponse.json({
      data: {
        companyId: company.id,
        companyName: company.name,
        staffEmails: validEmails,
        message: `Successfully saved ${validEmails.length} staff email(s). Invitation system will be implemented in the next iteration.`
      }
    })

  } catch (error) {
    console.error('Error in staff invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const supabase = await createClient()

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the company staff emails for this user
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('id, name, staff_emails')
      .eq('administrator_id', user.id)
      .single()

    if (companyError) {
      console.error('Error fetching company:', companyError)
      return NextResponse.json({ error: 'Company not found or unauthorized' }, { status: 404 })
    }

    return NextResponse.json({
      data: {
        companyId: company.id,
        companyName: company.name,
        staffEmails: company.staff_emails || []
      }
    })

  } catch (error) {
    console.error('Error fetching staff emails:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
