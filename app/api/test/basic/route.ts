import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Enhanced OCR Test API is working',
    timestamp: new Date().toISOString(),
    status: 'healthy'
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Simple test response without dependencies
    return NextResponse.json({
      message: 'Enhanced OCR Test API received POST request',
      receivedData: {
        hasReceiptText: !!body.receiptText,
        hasCompanyId: !!body.companyId,
        dataLength: body.receiptText?.length || 0
      },
      status: 'success',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    return NextResponse.json(
      {
        error: 'Failed to parse request',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 400 }
    );
  }
}
