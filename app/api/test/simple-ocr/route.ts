import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Simple Enhanced OCR Test API is working',
    timestamp: new Date().toISOString(),
    status: 'healthy'
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Mock enhanced OCR response for testing
    if (body.receiptText) {
      const mockResult = {
        message: 'Enhanced OCR processing complete',
        status: 'success',
        input: {
          receiptText: body.receiptText.substring(0, 100) + '...',
          companyId: body.companyId || 'no-company-id',
          hasTemplate: !!body.templateId
        },
        mockExtraction: {
          business_name: 'Mock Business',
          total_amount: 100.50,
          items: [
            { name: 'Mock Item 1', price: 50.25, quantity: 1 },
            { name: 'Mock Item 2', price: 50.25, quantity: 1 }
          ],
          confidence: 0.95
        },
        features: {
          enhanced_ocr: true,
          business_item_matching: true,
          template_support: true,
          ethiopian_format_support: true
        },
        timestamp: new Date().toISOString()
      };

      return NextResponse.json(mockResult);
    } else {
      return NextResponse.json({
        error: 'No receiptText provided in request body',
        expected_format: {
          receiptText: 'string',
          companyId: 'string (optional)',
          templateId: 'string (optional)'
        }
      }, { status: 400 });
    }

  } catch (error) {
    return NextResponse.json({
      error: 'Failed to process request',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
