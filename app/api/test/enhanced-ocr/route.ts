import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

interface TestRequestData {
  receiptText?: string;
  companyId?: string;
  templateId?: string | null;
  action?: string;
  testMode?: boolean;
  memberId?: string;
}

export async function GET() {
  return NextResponse.json({
    message: 'Enhanced OCR Test API is working',
    timestamp: new Date().toISOString(),
    status: 'healthy',
    endpoints: ['POST for receipt testing']
  });
}

export async function POST(request: NextRequest) {
  try {
    const requestData = await request.json() as TestRequestData;

    // Mock enhanced OCR response for testing
    if (requestData.receiptText) {
      const mockResult = {
        message: 'Enhanced OCR processing complete',
        status: 'success',
        input: {
          receiptText: requestData.receiptText.substring(0, 100) + '...',
          companyId: requestData.companyId || 'no-company-id',
          hasTemplate: !!requestData.templateId
        },
        mockExtraction: {
          business_name: 'ADDIS BEAUTY SALON',
          financial_system_number: 'FS87654321',
          total_amount: 2530.00,
          vat_amount: 330.00,
          subtotal: 2200.00,
          items: [
            {
              name: 'Refill gel with shellac',
              price: 1200.00,
              quantity: 1,
              matched_business_item: 'refill-gel-shellac-001'
            },
            {
              name: 'Beard Shaving',
              price: 450.00,
              quantity: 1,
              matched_business_item: 'beard-shaving-001'
            },
            {
              name: 'Shellac Polish',
              price: 550.00,
              quantity: 1,
              matched_business_item: 'shellac-polish-001'
            }
          ],
          confidence: 0.95
        },
        businessItemMatching: {
          total_items: 3,
          matched_items: 3,
          fuzzy_matches: 0,
          exact_matches: 3,
          matching_accuracy: 100
        },
        databaseTest: await testDatabaseConnection(requestData.companyId),
        features: {
          enhanced_ocr: true,
          business_item_matching: true,
          template_support: true,
          ethiopian_format_support: true,
          fuzzy_matching: true
        },
        timestamp: new Date().toISOString()
      };

      return NextResponse.json(mockResult);
    }

    // Database test action
    if (requestData.action === 'test_database') {
      const dbTest = await testDatabaseConnection(requestData.companyId);
      return NextResponse.json({
        message: 'Database connectivity test',
        status: 'success',
        databaseTest: dbTest,
        timestamp: new Date().toISOString()
      });
    }

    // Default response for missing data
    return NextResponse.json({
      error: 'No receiptText or valid action provided in request body',
      expected_format: {
        receiptText: 'string',
        companyId: 'string (optional)',
        templateId: 'string (optional)',
        action: 'string (optional): "test_database"'
      }
    }, { status: 400 });

  } catch (error) {
    return NextResponse.json({
      error: 'Failed to process request',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function testDatabaseConnection(companyId?: string) {
  try {
    const supabase = await createClient();

    // Test basic connectivity
    const { data: testQuery, error: testError } = await supabase
      .from('business_items')
      .select('id, item_name')
      .limit(5);

    if (testError) {
      return {
        status: 'error',
        message: 'Database connection failed',
        error: testError.message
      };
    }

    // Test company-specific data if companyId provided
    let companyData = null;
    if (companyId) {
      const { data: companyItems } = await supabase
        .from('business_items')
        .select('id, item_name, item_category')
        .eq('company_id', companyId)
        .limit(10);

      companyData = companyItems;
    }

    return {
      status: 'success',
      tables_accessible: true,
      sample_business_items: testQuery?.length || 0,
      company_specific_items: companyData?.length || 0,
      database_schema: {
        business_items: true,
        receipt_templates: true,
        receipt_items: true,
        item_matching_suggestions: true
      }
    };

  } catch (error) {
    return {
      status: 'error',
      message: 'Database test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
