import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Define types for our data structures
interface Member {
  id: string;
  name: string;
  registration_date: string;
  loyalty_tier: string;
  loyalty_id: string;
  phone_number: string;
  email: string | null;
  available_points?: number;
  total_points?: number;
  lifetime_points?: number;
  redeemed_points?: number;
  expired_points?: number;
  profile_image_url?: string | null;
}

// Used for type checking the request body
type MemberUpsertData = z.infer<typeof memberUpsertSchema>

// Validation schema for member upsert
const memberUpsertSchema = z.object({
  company_id: z.string().uuid({ message: 'Valid company ID is required' }),
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  phone_number: z.string().min(10, { message: 'Phone number is required and must be at least 10 digits' }),
  email: z.string().email({ message: 'Please enter a valid email' }).optional().nullable(),
  birthday: z.string().min(1, { message: 'Date of birth is required' }),
  telegram_chat_id: z.string().optional().nullable(),
  loyalty_id: z.string().optional().nullable(),
  loyalty_tier: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  initial_points: z.number().nonnegative().default(0),
  profile_image_url: z.string().url().optional().nullable(),
})

/**
 * GET /api/members - Get members for a company
 */
export async function GET(request: NextRequest) {
  try {
    // Parse URL to get query parameters
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');
    const limitParam = searchParams.get('limit');
    // Default to no limit if not specified
    const limit = limitParam ? parseInt(limitParam, 10) : 1000;

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Use optimized query with member_points_live view to get calculated points
    // This eliminates the N+1 pattern and manual calculations
    const { data, error } = await supabase
      .from('member_points_live')
      .select(`
        id,
        name,
        loyalty_id,
        available_points,
        lifetime_points,
        redeemed_points,
        expired_points
      `)
      .eq('company_id', companyId)
      .order('name', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching members from member_points_live view:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    // Get additional member details from loyalty_members table
    const memberIds = data?.map(m => m.id) || [];
    let memberDetails: Array<{
      id: string;
      registration_date: string;
      loyalty_tier: string;
      phone_number: string;
      email: string | null;
      profile_image_url: string | null;
    }> = [];

    if (memberIds.length > 0) {
      const { data: detailsData, error: detailsError } = await supabase
        .from('loyalty_members')
        .select(`
          id,
          registration_date,
          loyalty_tier,
          phone_number,
          email,
          profile_image_url
        `)
        .in('id', memberIds);

      if (detailsError) {
        console.error('Error fetching member details:', detailsError);
        // Continue with just the points data if details fail
        memberDetails = [];
      } else {
        memberDetails = detailsData || [];
      }
    }

    // Combine points data with member details
    const transformedData = data?.map((member) => {
      const details = memberDetails.find(d => d.id === member.id);
      return {
        ...member,
        registration_date: details?.registration_date || null,
        loyalty_tier: details?.loyalty_tier || 'Bronze',
        phone_number: details?.phone_number || '',
        email: details?.email || null,
        profile_image_url: details?.profile_image_url || null
      };
    }) || [];

    return NextResponse.json({ data: transformedData });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/members - Create or update a member
 * Upserts a member by telegram_chat_id or loyalty_id
 */
export async function POST(request: NextRequest) {
  try {
    // Get auth session from cookies
    // Use synchronous cookies() call to avoid Promise<ReadonlyRequestCookies> type error
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            // Use synchronous get method
            return cookieStore.get(name)?.value
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          set(_name: string, _value: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes as we can't set cookies in the response directly
            // We're just satisfying the type requirements
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          remove(_name: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes as we can't remove cookies in the response directly
            // We're just satisfying the type requirements
          },
        },
      }
    )

    // Verify user is authenticated using getUser() for server-side security
    const {
      data: { user },
      error: authError,
    } = await serverSupabase.auth.getUser()

    if (!user || authError) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()

    // Validate request body against schema
    const validationResult = memberUpsertSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const memberData: MemberUpsertData = validationResult.data

    // Use service role client to bypass RLS
    const supabase = getServiceRoleClient()

    // Check if member exists by telegram_chat_id, loyalty_id, or phone_number
    let existingMember: Member | null = null

    if (memberData.telegram_chat_id) {
      const { data: memberByTelegram } = await supabase
        .from('loyalty_members')
        .select('*')
        .eq('telegram_chat_id', memberData.telegram_chat_id)
        .eq('company_id', memberData.company_id)
        .maybeSingle()

      if (memberByTelegram) {
        existingMember = memberByTelegram
      }
    }

    if (!existingMember && memberData.loyalty_id) {
      const { data: memberByLoyaltyId } = await supabase
        .from('loyalty_members')
        .select('*')
        .eq('loyalty_id', memberData.loyalty_id)
        .eq('company_id', memberData.company_id)
        .maybeSingle()

      if (memberByLoyaltyId) {
        existingMember = memberByLoyaltyId
      }
    }

    // Check if member exists by phone number
    if (!existingMember && memberData.phone_number) {
      const { data: memberByPhone } = await supabase
        .from('loyalty_members')
        .select('*')
        .eq('phone_number', memberData.phone_number)
        .eq('company_id', memberData.company_id)
        .maybeSingle()

      if (memberByPhone) {
        // Don't update existing member, just return error for duplicate phone
        return NextResponse.json(
          { error: 'A member with this phone number already exists for this company' },
          { status: 409 }
        )
      }
    }

    // Generate loyalty_id if not provided
    if (!memberData.loyalty_id) {
      memberData.loyalty_id = generateLoyaltyId()
    }

    // Handle upsert based on whether member exists
    let result: Member

    if (existingMember) {
      // Update existing member
      const { data: updatedMember, error: updateError } = await supabase
        .from('loyalty_members')
        .update({
          name: memberData.name,
          phone_number: memberData.phone_number,
          email: memberData.email,
          birthday: memberData.birthday,
          telegram_chat_id: memberData.telegram_chat_id,
          loyalty_id: memberData.loyalty_id,
          loyalty_tier: memberData.loyalty_tier,
          notes: memberData.notes,
        })
        .eq('id', existingMember.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating member:', updateError)
        return NextResponse.json(
          { error: 'Failed to update member', details: updateError },
          { status: 500 }
        )
      }

      result = updatedMember

      // Add initial points if specified and member has no previous transactions
      if (memberData.initial_points > 0) {
        const { data: transactionsData } = await supabase
          .from('points_transactions')
          .select('id')
          .eq('member_id', existingMember.id)
          .limit(1)

        if (!transactionsData || transactionsData.length === 0) {
          // No existing transactions, add initial points
          await addInitialPoints(existingMember.id, memberData.initial_points)
        }
      }
    } else {
      // Create new member
      const { data: newMember, error: insertError } = await supabase
        .from('loyalty_members')
        .insert({
          company_id: memberData.company_id,
          name: memberData.name,
          phone_number: memberData.phone_number,
          email: memberData.email,
          birthday: memberData.birthday,
          telegram_chat_id: memberData.telegram_chat_id,
          loyalty_id: memberData.loyalty_id,
          loyalty_tier: memberData.loyalty_tier,
          notes: memberData.notes,
          profile_image_url: memberData.profile_image_url,
          lifetime_points: 0,
          redeemed_points: 0,
          expired_points: 0,
          registration_date: new Date().toISOString(),
        })
        .select()
        .single()

      if (insertError) {
        console.error('Error creating member:', insertError)

        // Handle specific error types
        if (insertError.code === '23505' && insertError.message?.includes('unique_phone_per_company')) {
          return NextResponse.json(
            { error: 'A member with this phone number already exists for this company' },
            { status: 409 }
          )
        }

        return NextResponse.json(
          { error: 'Failed to create member', details: insertError },
          { status: 500 }
        )
      }

      result = newMember

      // Add initial points if specified
      if (memberData.initial_points > 0) {
        await addInitialPoints(newMember.id, memberData.initial_points)
      }
    }

    return NextResponse.json({ data: result })
  } catch (error) {
    console.error('Error in member API:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

// Helper function to add initial points to a member
async function addInitialPoints(memberId: string, points: number) {
  try {
    // Use service role client to bypass RLS
    const supabase = getServiceRoleClient()

    await supabase.from('points_transactions').insert({
      member_id: memberId,
      points_change: points,
      transaction_type: 'earn',
      description: 'Initial points',
    })
  } catch (error) {
    console.error(`Error adding initial points to member ${memberId}:`, error)
  }
}

// Generate a unique loyalty ID (6 alphanumeric characters)
function generateLoyaltyId(): string {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789' // Removed similar-looking characters
  let result = ''
  for (let i = 0; i < 6; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  return result
}
