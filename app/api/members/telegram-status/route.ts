import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID required' }, { status: 400 })
    }

    const supabase = getServiceRoleClient()

    // Fetch all members with their Telegram status using the updated view
    const { data: members, error } = await supabase
      .from('member_points_live')
      .select(`
        id,
        name,
        email,
        phone_number,
        telegram_chat_id,
        telegram_username,
        linked_at,
        linking_token,
        available_points,
        lifetime_points,
        loyalty_tier
      `)
      .eq('company_id', companyId)
      .order('name')

    if (error) {
      console.error('Error fetching members with Telegram status:', error)
      return NextResponse.json({ error: 'Failed to fetch members' }, { status: 500 })
    }

    // Transform data to include computed Telegram status
    const membersWithStatus = members?.map(member => ({
      ...member,
      telegramStatus: {
        isLinked: !!member.telegram_chat_id,
        telegramChatId: member.telegram_chat_id,
        telegramUsername: member.telegram_username,
        linkedAt: member.linked_at,
        hasLinkingToken: !!member.linking_token
      }
    })) || []

    // Calculate summary statistics
    const totalMembers = membersWithStatus.length
    const connectedMembers = membersWithStatus.filter(m => m.telegramStatus.isLinked).length
    const pendingConnections = membersWithStatus.filter(m => m.telegramStatus.hasLinkingToken && !m.telegramStatus.isLinked).length

    return NextResponse.json({
      members: membersWithStatus,
      summary: {
        totalMembers,
        connectedMembers,
        pendingConnections,
        connectionRate: totalMembers > 0 ? Math.round((connectedMembers / totalMembers) * 100) : 0
      }
    })

  } catch (error) {
    console.error('Members Telegram status error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
