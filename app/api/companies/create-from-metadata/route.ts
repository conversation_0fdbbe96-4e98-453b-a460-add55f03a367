import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// POST /api/companies/create-from-metadata - Create company from user metadata
export async function POST() {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated using getUser() for server-side security
    const {
      data: { user },
      error: authError,
    } = await serverSupabase.auth.getUser()

    if (!user || authError) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const companyName = user.user_metadata?.company_name
    const businessType = user.user_metadata?.business_type

    if (!companyName) {
      return NextResponse.json(
        { error: 'No company name found in user metadata' },
        { status: 400 }
      )
    }

    // Call the auto-create API
    const autoCreateResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/companies/auto-create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: user.id,
        companyName,
        businessType: businessType || 'other',
      }),
    })

    if (!autoCreateResponse.ok) {
      const errorText = await autoCreateResponse.text()
      console.error('Auto-create failed:', errorText)
      return NextResponse.json(
        { error: 'Failed to create company' },
        { status: 500 }
      )
    }

    const result = await autoCreateResponse.json()
    return NextResponse.json(result)

  } catch (error) {
    console.error('Error in create-from-metadata:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
