import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const supabase = getServiceRoleClient() // Use service role

    const { companyId } = await request.json()

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Check if tiers already exist
    const { data: existingTiers, error: checkError } = await supabase
      .from('tier_definitions')
      .select('id')
      .eq('company_id', companyId)

    if (checkError) {
      console.error('Error checking existing tiers:', checkError)
      return NextResponse.json(
        { error: 'Failed to check existing tiers' },
        { status: 500 }
      )
    }

    if (existingTiers && existingTiers.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'Tiers already exist',
        existingCount: existingTiers.length
      })
    }

    // Create default tier definitions
    const defaultTiers = [
      {
        company_id: companyId,
        tier_name: 'Bronze',
        minimum_points: 0,
        benefits_description: 'Welcome tier with basic benefits: 1x points earning, standard support'
      },
      {
        company_id: companyId,
        tier_name: 'Silver',
        minimum_points: 250,
        benefits_description: 'Enhanced benefits for loyal customers: 1.2x points earning, priority support, 5% bonus on special offers'
      },
      {
        company_id: companyId,
        tier_name: 'Gold',
        minimum_points: 500,
        benefits_description: 'Premium benefits for top customers: 1.5x points earning, VIP support, 10% bonus on special offers, early access to new products'
      }
    ]

    const { data: tierDefinitions, error: tiersError } = await supabase
      .from('tier_definitions')
      .insert(defaultTiers)
      .select()

    if (tiersError) {
      console.error('Error creating tier definitions:', tiersError)
      return NextResponse.json(
        { error: 'Failed to create tier definitions', details: tiersError.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      tiers: tierDefinitions,
      message: 'Default tiers created successfully'
    })

  } catch (error) {
    console.error('Error in create-default-tiers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
