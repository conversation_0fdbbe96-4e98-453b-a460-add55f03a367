import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function POST() {
  try {
    // Create server client to check authentication
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          set(_name: string, _value: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          remove(_name: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes
          },
        },
      }
    )

    // Verify user is authenticated using getUser() for server-side security
    const {
      data: { user },
      error: authError,
    } = await serverSupabase.auth.getUser()

    if (!user || authError) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const userId = user.id

    // Use service role client for database operations
    const supabase = getServiceRoleClient()

    // Get the user's company
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('*')
      .eq('administrator_id', userId)
      .single()

    if (companyError || !company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    // Check if company has the required data for onboarding completion
    const [membersResult, rewardsResult, tiersResult] = await Promise.all([
      supabase
        .from('loyalty_members')
        .select('id')
        .eq('company_id', company.id)
        .limit(1),
      supabase
        .from('rewards')
        .select('id')
        .eq('company_id', company.id)
        .limit(1),
      supabase
        .from('tier_definitions')
        .select('id')
        .eq('company_id', company.id)
        .limit(1)
    ])

    const hasMembers = (membersResult.data?.length || 0) > 0
    const hasRewards = (rewardsResult.data?.length || 0) > 0
    const hasTiers = (tiersResult.data?.length || 0) > 0

    if (!hasMembers || !hasRewards || !hasTiers) {
      return NextResponse.json({
        error: 'Company setup incomplete',
        missing: {
          members: !hasMembers,
          rewards: !hasRewards,
          tiers: !hasTiers
        }
      }, { status: 400 })
    }

    // Mark onboarding as complete
    const { data: updatedCompany, error: updateError } = await supabase
      .from('companies')
      .update({ onboarding_completed: true })
      .eq('id', company.id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating onboarding status:', updateError)
      return NextResponse.json(
        { error: 'Failed to update onboarding status' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      company: updatedCompany
    })

  } catch (error) {
    console.error('Error in complete onboarding API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
