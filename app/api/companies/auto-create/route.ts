import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { v4 as uuidv4 } from 'uuid'
import { SupabaseClient } from '@supabase/supabase-js'

// Generate slug from business name
function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special chars
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Remove consecutive hyphens
    .trim()
}

// POST /api/companies/auto-create - Auto-create company during signup
export async function POST(request: NextRequest) {
  try {
    console.log('Starting /api/companies/auto-create request')

    // Log environment variables for debugging
    console.log('Environment Variables:', {
      SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
      SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY
    })

    const supabase = getServiceRoleClient()
    console.log('Supabase client initialized')

    // Handle potential empty request body
    let requestData
    try {
      const body = await request.text()
      if (!body) {
        console.log('Request body is empty')
        return NextResponse.json(
          { error: 'Request body is empty' },
          { status: 400 }
        )
      }
      requestData = JSON.parse(body)
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      )
    }

    console.log('Request data:', requestData)

    const { userId, companyName, businessType } = requestData

    if (!userId || !companyName) {
      console.log('Missing userId or companyName in request data')
      return NextResponse.json(
        { error: 'User ID and company name are required' },
        { status: 400 }
      )
    }

    // First check if the user already has a company
    const { data: existingCompany, error: checkError } = await supabase
      .from('companies')
      .select('*')
      .eq('administrator_id', userId)
      .single()

    console.log('Existing company check result:', {
      hasCompany: !!existingCompany,
      checkError: checkError?.message || 'none'
    })

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking for existing company:', checkError)
      return NextResponse.json(
        { error: 'Failed to check for existing company' },
        { status: 500 }
      )
    }

    if (existingCompany) {
      console.log('Company already exists for user:', existingCompany.id)
      return NextResponse.json({
        success: true,
        company: existingCompany,
        message: 'Company already exists'
      })
    }

    // Generate company data
    const companyId = uuidv4()
    const slug = generateSlug(companyName)

    const companyData = {
      id: companyId,
      name: companyName,
      slug: slug,
      administrator_id: userId,
      business_type: businessType || null,
      points_earning_ratio: 1.0,
      points_expiration_days: 365,
    }

    console.log('Company data to insert:', companyData)

    // Create the company
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .insert(companyData)
      .select()
      .single()

    console.log('Company creation result:', {
      companyId: company?.id || 'none',
      companyError: companyError?.message || 'none'
    })

    if (companyError) {
      console.error('Error creating company:', companyError)
      return NextResponse.json(
        { error: 'Failed to create company', details: companyError.message },
        { status: 500 }
      )
    }

    // Create sample data for the new company
    await createSampleData(supabase, companyId)

    return NextResponse.json({
      success: true,
      company,
      message: 'Company created successfully with sample data'
    })

  } catch (error) {
    console.error('Unexpected error in /api/companies/auto-create:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// Helper function to create sample data for new companies
async function createSampleData(supabase: SupabaseClient, companyId: string) {
  try {
    // Create default tier definitions first
    const defaultTiers = [
      {
        company_id: companyId,
        tier_name: 'Bronze',
        minimum_points: 0,
        benefits_description: 'Welcome tier with basic benefits: 1x points earning, standard support'
      },
      {
        company_id: companyId,
        tier_name: 'Silver',
        minimum_points: 250,
        benefits_description: 'Enhanced benefits for loyal customers: 1.2x points earning, priority support, 5% bonus on special offers'
      },
      {
        company_id: companyId,
        tier_name: 'Gold',
        minimum_points: 500,
        benefits_description: 'Premium benefits for top customers: 1.5x points earning, VIP support, 10% bonus on special offers, early access to new products'
      }
    ]

    const { data: tierDefinitions, error: tiersError } = await supabase
      .from('tier_definitions')
      .insert(defaultTiers)
      .select()

    if (tiersError) {
      console.error('Error creating tier definitions:', tiersError)
      throw new Error(`Tier definitions creation failed: ${tiersError.message}`)
    }

    console.log('Created tier definitions:', tierDefinitions.map(t => t.tier_name))

    // Generate unique loyalty IDs based on timestamp
    const timestamp = Date.now().toString().slice(-6) // Last 6 digits of timestamp
    const loyaltyIdBase = `F${timestamp.padStart(7, '0')}`

    // Create sample members
    const sampleMembers = [
      {
        company_id: companyId,
        name: 'Sarah Johnson',
        phone_number: '+251911123456',
        email: '<EMAIL>',
        loyalty_id: `${loyaltyIdBase.slice(0, -1)}1`, // F000001
        lifetime_points: 250,
        redeemed_points: 100,
        expired_points: 0,
        loyalty_tier: 'Silver',
        registration_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
        birthday: '1990-05-15'
      },
      {
        company_id: companyId,
        name: 'Ahmed Hassan',
        phone_number: '+251922654321',
        email: '<EMAIL>',
        loyalty_id: `${loyaltyIdBase.slice(0, -1)}2`, // F000002
        lifetime_points: 450,
        redeemed_points: 100,
        expired_points: 0,
        loyalty_tier: 'Gold',
        registration_date: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(), // 45 days ago
        birthday: '1985-12-03'
      },
      {
        company_id: companyId,
        name: 'Meron Tadesse',
        phone_number: '+251933987654',
        email: '<EMAIL>',
        loyalty_id: `${loyaltyIdBase.slice(0, -1)}3`, // F000003
        lifetime_points: 80,
        redeemed_points: 0,
        expired_points: 0,
        loyalty_tier: 'Bronze',
        registration_date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        birthday: '1995-08-20'
      }
    ]

    // Insert sample members
    const { data: members, error: membersError } = await supabase
      .from('loyalty_members')
      .insert(sampleMembers)
      .select()

    if (membersError) {
      console.error('Error creating sample members:', membersError)
      return
    }

    // Create sample transactions for members
    const sampleTransactions = [
      // Sarah's transactions
      {
        member_id: members[0].id,
        company_id: companyId,
        points_change: 150,
        transaction_type: 'EARN',
        description: 'Purchase - 150 ETB',
        transaction_date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        expiration_date: new Date(Date.now() + 360 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        member_id: members[0].id,
        company_id: companyId,
        points_change: -100,
        transaction_type: 'REDEEM',
        description: 'Redeemed - 10% discount',
        transaction_date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        expiration_date: new Date('2099-12-31').toISOString()
      },
      // Ahmed's transactions
      {
        member_id: members[1].id,
        company_id: companyId,
        points_change: 200,
        transaction_type: 'EARN',
        description: 'Purchase - 200 ETB',
        transaction_date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
        expiration_date: new Date(Date.now() + 355 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        member_id: members[1].id,
        company_id: companyId,
        points_change: 250,
        transaction_type: 'EARN',
        description: 'Purchase - 250 ETB',
        transaction_date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(),
        expiration_date: new Date(Date.now() + 345 * 24 * 60 * 60 * 1000).toISOString()
      }
    ]

    await supabase
      .from('points_transactions')
      .insert(sampleTransactions)

    // Create sample reward
    const rewardTimestamp = Date.now().toString().slice(-4) // Last 4 digits
    const rewardCode = `WL${rewardTimestamp.slice(-2)}` // e.g., "WL36" (2 letters + 2 digits)
    const rewardCodeField = `WEL${rewardTimestamp.slice(-2)}` // e.g., "WEL36" (unique reward_code)

    const sampleReward = {
      company_id: companyId,
      reward_code: rewardCodeField,
      title: '10% Off Next Purchase',
      description: 'Get 10% discount on your next purchase. Valid for 30 days.',
      reward_type: 'GENERAL',
      reward_value_type: 'PERCENTAGE',
      reward_value: 10,
      code: rewardCode, // Generate unique code
      points_required: 100,
      start_date: new Date().toISOString(),
      expiration_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { error: rewardError } = await supabase
      .from('rewards')
      .insert(sampleReward)

    if (rewardError) {
      throw new Error(`Reward creation failed: ${rewardError.message}`)
    }

  } catch (error) {
    console.error('Error creating sample data:', error)
    throw new Error(`Sample data creation failed: ${error}`)
  }
}
