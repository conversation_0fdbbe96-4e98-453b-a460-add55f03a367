import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getServiceRoleClient } from "@/lib/supabase";
import { getCompanyIdFromSession } from "@/lib/auth";
import { v4 as uuidv4 } from 'uuid';

// Schema for company update
const companyUpdateSchema = z.object({
  name: z.string().min(2, "Company name must be at least 2 characters"),
  logo_url: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  primary_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Please enter a valid hex color code").optional().or(z.literal("")),
  points_expiration_days: z.number().int().positive("Must be a positive number"),
  points_earning_ratio: z.number().positive("Must be a positive number"),
});

// PUT /api/companies - Update company
export async function PUT(request: NextRequest) {
  try {
    console.log('[PUT /api/companies] Starting request');

    const body = await request.json();
    console.log('[PUT /api/companies] Request body:', JSON.stringify(body, null, 2));
    console.log('[PUT /api/companies] Body keys:', Object.keys(body));

    // Check if userId is provided in the request body (for debugging)
    const { userId, ...companyUpdateData } = body;

    console.log('[PUT /api/companies] Extracted userId:', userId);
    console.log('[PUT /api/companies] Company update data:', JSON.stringify(companyUpdateData, null, 2));

    if (!userId) {
      console.error('[PUT /api/companies] CRITICAL: No userId provided in request body');
      console.error('[PUT /api/companies] Full request body was:', JSON.stringify(body, null, 2));
      return NextResponse.json(
        { error: "User ID required in request body", receivedKeys: Object.keys(body) },
        { status: 400 }
      );
    }

    // Validate request body
    const validationResult = companyUpdateSchema.safeParse(companyUpdateData);
    if (!validationResult.success) {
      console.log('[PUT /api/companies] Validation failed:', validationResult.error.format());
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Use service role client for database operations
    const serviceClient = getServiceRoleClient();

    // Get company ID for this user
    const { data: companyData, error: companyError } = await serviceClient
      .from('companies')
      .select('id')
      .eq('administrator_id', userId)
      .single();

    if (companyError || !companyData) {
      console.error("[PUT /api/companies] Error getting company:", companyError);
      return NextResponse.json(
        { error: "No company found for user" },
        { status: 404 }
      );
    }

    const companyId = companyData.id;
    console.log('[PUT /api/companies] Found company:', companyId);

    // Validate request body (already parsed above)
    const validationResult2 = companyUpdateSchema.safeParse(companyUpdateData);
    if (!validationResult2.success) {
      console.log('[PUT /api/companies] Validation failed:', validationResult2.error.format());
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult2.error.format() },
        { status: 400 }
      );
    }

    const updateData = {
      ...validationResult2.data,
      logo_url: validationResult2.data.logo_url || null,
      primary_color: validationResult2.data.primary_color || null,
    };

    const { data, error } = await serviceClient
      .from("companies")
      .update(updateData)
      .eq("id", companyId)
      .select()
      .single();

    if (error) {
      console.error("Error updating company:", error);
      return NextResponse.json(
        { error: "Failed to update company" },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Schema for company creation
const companyCreateSchema = z.object({
  name: z.string().min(2, "Company name must be at least 2 characters"),
  slug: z.string().min(2, "Slug must be at least 2 characters"),
  logo_url: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  primary_color: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "Please enter a valid hex color code").optional().or(z.literal("")),
  points_expiration_days: z.number().int().positive("Must be a positive number"),
  points_earning_ratio: z.number().positive("Must be a positive number"),
});

// POST /api/companies - Create company
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();    // For now, let's get the user ID from the request body to debug the issue
    // Later we can add proper session-based authentication
    const { userId, ...companyData } = body;

    console.log('[API] Received data:', { userId, companyData });

    if (!userId) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Validate request body
    const validationResult = companyCreateSchema.safeParse(companyData);
    if (!validationResult.success) {
      console.log('[API] Validation failed:', validationResult.error.format());
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Generate a UUID for the company
    const companyId = uuidv4();

    const createData = {
      id: companyId,
      ...validationResult.data,
      administrator_id: userId,
      logo_url: validationResult.data.logo_url || null,
      primary_color: validationResult.data.primary_color || null,
    };

    // Use service role client for database operations requiring elevated permissions
    const serviceRoleSupabase = getServiceRoleClient();

    const { data, error } = await serviceRoleSupabase
      .from("companies")
      .insert(createData)
      .select()
      .single();

    if (error) {
      console.error("Error creating company:", error);
      return NextResponse.json(
        { error: "Failed to create company" },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET /api/companies - Get current user's company (fallback endpoint)
export async function GET() {
  try {
    const supabase = getServiceRoleClient();
    const companyId = await getCompanyIdFromSession(supabase);

    if (!companyId) {
      return NextResponse.json(
        { error: "Unauthorized - No company associated with user" },
        { status: 401 }
      );
    }

    const { data: company, error } = await supabase
      .from("companies")
      .select("*")
      .eq("id", companyId)
      .single();

    if (error) {
      console.error("Error fetching company:", error);
      return NextResponse.json(
        { error: "Failed to fetch company" },
        { status: 500 }
      );
    }

    if (!company) {
      return NextResponse.json(
        { error: "Company not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(company);
  } catch (error) {
    console.error("Unexpected error in GET /api/companies:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
