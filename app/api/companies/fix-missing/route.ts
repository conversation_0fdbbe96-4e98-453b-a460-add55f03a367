import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// POST /api/companies/fix-missing - Fix users who signed up but don't have companies
export async function POST() {
  try {
    const supabase = getServiceRoleClient() // Use service role

    // Get all users who have company metadata but no company record
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers()

    if (usersError) {
      console.error('Error fetching users:', usersError)
      return NextResponse.json(
        { error: 'Failed to fetch users' },
        { status: 500 }
      )
    }

    const fixedUsers = []
    const errors = []

    for (const user of users.users) {
      const companyName = user.user_metadata?.company_name
      const businessType = user.user_metadata?.business_type

      if (companyName) {
        // Check if this user already has a company
        const { data: existingCompany } = await supabase
          .from('companies')
          .select('id')
          .eq('administrator_id', user.id)
          .single()

        if (!existingCompany) {
          // User needs a company created
          try {
            const createResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/companies/auto-create`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                userId: user.id,
                companyName,
                businessType: businessType || 'other',
              }),
            })

            if (createResponse.ok) {
              const result = await createResponse.json()
              fixedUsers.push({
                userId: user.id,
                email: user.email,
                companyName,
                companyId: result.company?.id
              })
            } else {
              const errorText = await createResponse.text()
              errors.push({
                userId: user.id,
                email: user.email,
                error: errorText
              })
            }
          } catch (error) {
            errors.push({
              userId: user.id,
              email: user.email,
              error: error instanceof Error ? error.message : 'Unknown error'
            })
          }
        }
      }
    }

    return NextResponse.json({
      success: true,
      fixedUsers,
      errors,
      message: `Fixed ${fixedUsers.length} users, ${errors.length} errors`
    })

  } catch (error) {
    console.error('Error in fix-missing companies:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
