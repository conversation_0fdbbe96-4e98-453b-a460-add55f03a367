import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseKey)

export async function POST(request: NextRequest) {
  try {
    const { userId } = await request.json()

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Check if user has a company
    const { data: companies, error } = await supabase
      .from('companies')
      .select('id')
      .eq('administrator_id', userId)
      .limit(1)

    if (error) {
      console.error('Error checking company:', error)
      return NextResponse.json(
        { error: 'Failed to check company' },
        { status: 500 }
      )
    }

    const hasCompany = companies && companies.length > 0
    const companyId = hasCompany ? companies[0].id : null

    return NextResponse.json({
      hasCompany,
      companyId
    })

  } catch (error) {
    console.error('Error in company check:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
