import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// GET /api/companies/current - Get current user's company
export async function GET() {
  try {
    console.log('Starting /api/companies/current request')

    // Log environment variables for debugging
    console.log('Environment Variables:', {
      SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
      SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY
    })

    // Create server client to check authentication
    const cookieStore = await cookies()
    console.log('Cookie store initialized')

    // Log available cookies for debugging
    const allCookies = cookieStore.getAll()
    console.log('Available cookies:', allCookies.map(c => ({ name: c.name })))

    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            const cookie = cookieStore.get(name)
            console.log(`Getting cookie ${name}:`, cookie ? 'found' : 'not found')
            return cookie?.value
          },
          set: () => {},
          remove: () => {},
        },
      }
    )
    console.log('Supabase server client created')

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await serverSupabase.auth.getUser()

    console.log('Auth check result:', {
      hasUser: !!user,
      userId: user?.id || 'none',
      authError: authError?.message || 'none'
    })

    if (!user || authError) {
      console.log('Authentication failed:', { error: authError?.message })
      return NextResponse.json(
        { error: 'Not authenticated', details: authError?.message },
        { status: 401 }
      )
    }

    // Use service role client for database operations
    const supabase = getServiceRoleClient()
    console.log('Created service role client')

    // Get the user's company with detailed error logging
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('*')
      .eq('administrator_id', user.id)
      .single()

    console.log('Company query result:', {
      hasCompany: !!company,
      companyError: companyError?.message || 'none',
      companyId: company?.id || 'none'
    })

    if (companyError) {
      console.error('Company fetch error:', companyError)
      return NextResponse.json(
        { error: 'Failed to fetch company', details: companyError.message },
        { status: 500 }
      )
    }

    if (!company) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(company)
  } catch (error) {
    console.error('Unexpected error in /api/companies/current:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
