import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('company_id');

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    const supabase = await createClient();

    // Get comprehensive analytics data using optimized queries

    // 1. Customer insights with detailed preferences (using new function)
    const { data: customerInsights, error: customerError } = await supabase
      .rpc('get_detailed_customer_preferences', { p_company_id: companyId });

    if (customerError) {
      console.error('Error fetching customer insights:', customerError);
    }

    // 2. Business performance metrics
    const { data: businessPerformance, error: businessError } = await supabase
      .rpc('get_business_performance', { input_company_id: companyId });

    if (businessError) {
      console.error('Error fetching business performance:', businessError);
    }

    // 3. Template analytics
    const { data: templateAnalytics, error: templateError } = await supabase
      .rpc('get_template_analytics', { input_company_id: companyId });

    if (templateError) {
      console.error('Error fetching template analytics:', templateError);
    }

    // 4. Dashboard summary
    const { data: dashboardSummary, error: summaryError } = await supabase
      .from('analytics_summary_dashboard')
      .select('*')
      .eq('company_id', companyId)
      .single();

    if (summaryError) {
      console.error('Error fetching dashboard summary:', summaryError);
    }

    // 5. Category breakdown
    const { data: categoryBreakdown, error: categoryError } = await supabase
      .from('analytics_business_performance')
      .select(`
        item_category,
        total_revenue,
        total_sales,
        unique_customers
      `)
      .eq('company_id', companyId);

    let categoryData = [];
    if (!categoryError && categoryBreakdown) {
      // Aggregate by category
      const categoryMap = new Map();
      categoryBreakdown.forEach(item => {
        const category = item.item_category || 'Other';
        if (categoryMap.has(category)) {
          const existing = categoryMap.get(category);
          existing.revenue += Number(item.total_revenue || 0);
          existing.sales += Number(item.total_sales || 0);
          existing.customers += Number(item.unique_customers || 0);
        } else {
          categoryMap.set(category, {
            category,
            revenue: Number(item.total_revenue || 0),
            sales: Number(item.total_sales || 0),
            customers: Number(item.unique_customers || 0)
          });
        }
      });
      categoryData = Array.from(categoryMap.values());
    }

    // 6. Recent activity metrics using fixed function
    const { data: recentActivity, error: activityError } = await supabase
      .rpc('get_recent_activity', {
        input_company_id: companyId,
        limit_count: 10
      });

    if (activityError) {
      console.error('Error fetching recent activity:', activityError);
    }

    return NextResponse.json({
      success: true,
      data: {
        summary: dashboardSummary || {
          total_customers: 0,
          total_receipts: 0,
          total_revenue: 0,
          active_business_items: 0,
          avg_transaction_value: 0,
          template_usage_percentage: 0,
          avg_extraction_confidence: 0
        },
        customerInsights: customerInsights || [],
        businessPerformance: businessPerformance || [],
        templateAnalytics: templateAnalytics || [],
        categoryBreakdown: categoryData,
        recentActivity: recentActivity || [],
        metadata: {
          fetched_at: new Date().toISOString(),
          company_id: companyId,
          data_points: {
            customers: customerInsights?.length || 0,
            business_items: businessPerformance?.length || 0,
            templates: templateAnalytics?.length || 0,
            categories: categoryData.length
          }
        }
      }
    });

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json({
      error: 'Failed to fetch analytics data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
