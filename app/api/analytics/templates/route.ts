import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('company_id');
    const templateId = searchParams.get('template_id');
    const daysBack = parseInt(searchParams.get('days_back') || '30');

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    const supabase = await createClient();

    // Get template performance metrics
    let templateQuery = supabase
      .from('template_performance_metrics')
      .select('*')
      .eq('company_name',
        supabase
          .from('companies')
          .select('name')
          .eq('id', companyId)
      );

    if (templateId) {
      templateQuery = templateQuery.eq('template_id', templateId);
    }

    const { data: templatePerformance, error: templateError } = await templateQuery
      .order('effectiveness_score', { ascending: false });

    if (templateError) {
      console.error('Error fetching template performance:', templateError);
      return NextResponse.json({ error: 'Failed to fetch template performance' }, { status: 500 });
    }

    // Get error analysis for templates if specific template is requested
    let errorAnalysis = null;
    if (templateId) {
      const { data: errors, error: errorError } = await supabase
        .rpc('analyze_template_errors', {
          p_template_id: templateId,
          p_days_back: daysBack
        });

      if (errorError) {
        console.error('Error fetching error analysis:', errorError);
      } else {
        errorAnalysis = errors;
      }
    }

    // Get system-wide template analytics
    const { data: systemAnalytics, error: systemError } = await supabase
      .from('system_analytics_summary')
      .select('*')
      .limit(1);

    if (systemError) {
      console.error('Error fetching system analytics:', systemError);
    }

    // Calculate template summary stats
    const totalTemplates = templatePerformance?.length || 0;
    const activeTemplates = templatePerformance?.filter(t => t.recent_extractions > 0).length || 0;
    const avgSuccessRate = templatePerformance?.length > 0
      ? templatePerformance.reduce((sum, t) => sum + (t.success_rate_percentage || 0), 0) / templatePerformance.length
      : 0;
    const avgConfidence = templatePerformance?.length > 0
      ? templatePerformance.reduce((sum, t) => sum + (t.avg_confidence_score || 0), 0) / templatePerformance.length
      : 0;

    // Identify top performing and problematic templates
    const topPerformers = templatePerformance?.filter(t =>
      (t.effectiveness_score || 0) > 0.8 && (t.success_rate_percentage || 0) > 90
    ) || [];

    const problematicTemplates = templatePerformance?.filter(t =>
      (t.effectiveness_score || 0) < 0.6 || (t.success_rate_percentage || 0) < 70
    ) || [];

    // Template usage trends (last 30 days)
    const recentUsage = templatePerformance?.map(t => ({
      template_id: t.template_id,
      template_name: t.template_name,
      recent_extractions: t.recent_extractions || 0,
      recent_confidence: t.recent_avg_confidence || 0,
      effectiveness_score: t.effectiveness_score || 0
    })).sort((a, b) => (b.recent_extractions || 0) - (a.recent_extractions || 0)) || [];

    // Performance insights and recommendations
    interface InsightsType {
      overall_health: string;
      confidence_level: string;
      template_adoption: string;
      recommendations: string[];
    }

    const insights: InsightsType = {
      overall_health: avgSuccessRate > 85 ? 'Excellent' : avgSuccessRate > 70 ? 'Good' : 'Needs Improvement',
      confidence_level: avgConfidence > 0.85 ? 'High' : avgConfidence > 0.7 ? 'Moderate' : 'Low',
      template_adoption: totalTemplates > 0 ? `${activeTemplates}/${totalTemplates} templates actively used` : 'No templates active',
      recommendations: []
    };

    // Generate specific recommendations
    const recommendations: string[] = [];

    if (problematicTemplates.length > 0) {
      recommendations.push(`Review ${problematicTemplates.length} underperforming templates`);
    }

    if (avgConfidence < 0.8) {
      recommendations.push('Consider updating AI prompts to improve extraction confidence');
    }

    if (activeTemplates < totalTemplates / 2) {
      recommendations.push('Promote template usage - many templates are underutilized');
    }

    if (topPerformers.length > 0) {
      recommendations.push(`Use ${topPerformers[0].template_name} as template for other businesses`);
    }

    insights.recommendations = recommendations;

    return NextResponse.json({
      summary: {
        total_templates: totalTemplates,
        active_templates: activeTemplates,
        avg_success_rate: avgSuccessRate,
        avg_confidence: avgConfidence,
        analysis_period_days: daysBack
      },
      performance_metrics: templatePerformance || [],
      top_performers: topPerformers,
      problematic_templates: problematicTemplates,
      recent_usage: recentUsage,
      error_analysis: errorAnalysis,
      system_analytics: systemAnalytics?.[0] || null,
      insights: insights,
      ...(templateId && {
        template_specific: {
          template_id: templateId,
          error_analysis: errorAnalysis,
          performance_detail: templatePerformance?.find(t => t.template_id === templateId) || null
        }
      })
    });

  } catch (error) {
    console.error('Template performance API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
