import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('company_id');
    const memberId = searchParams.get('member_id');
    const monthsBack = parseInt(searchParams.get('months_back') || '12');

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    const supabase = await createClient();

    // If specific member requested, get their preferences
    if (memberId) {
      const { data: memberPreferences, error: memberError } = await supabase
        .rpc('get_customer_seasonal_preferences', {
          p_member_id: memberId,
          p_months_back: monthsBack
        });

      if (memberError) {
        console.error('Error fetching member preferences:', memberError);
        return NextResponse.json({ error: 'Failed to fetch member preferences' }, { status: 500 });
      }

      return NextResponse.json({
        member_preferences: memberPreferences || [],
        type: 'individual'
      });
    }

    // Get overall customer analytics for the company
    // First get member IDs for this company
    const { data: companyMembers, error: membersError } = await supabase
      .from('receipts')
      .select('member_id')
      .eq('company_id', companyId);

    if (membersError) {
      console.error('Error fetching company members:', membersError);
      return NextResponse.json({ error: 'Failed to fetch customer analytics' }, { status: 500 });
    }

    const memberIds = companyMembers?.map(m => m.member_id) || [];

    const { data: customerPreferences, error: prefError } = await supabase
      .from('customer_item_preferences')
      .select('*')
      .in('member_id', memberIds)
      .order('preference_score', { ascending: false })
      .limit(100);

    if (prefError) {
      console.error('Error fetching customer preferences:', prefError);
      return NextResponse.json({ error: 'Failed to fetch customer analytics' }, { status: 500 });
    }

    // Get customer favorites summary
    const { data: customerFavorites, error: favError } = await supabase
      .from('customer_favorite_items')
      .select('*')
      .in('member_id', memberIds)
      .order('preference_score', { ascending: false })
      .limit(50);

    if (favError) {
      console.error('Error fetching customer favorites:', favError);
      return NextResponse.json({ error: 'Failed to fetch customer favorites' }, { status: 500 });
    }

    // Calculate summary statistics
    const totalCustomers = new Set(customerPreferences?.map(p => p.member_id) || []).size;
    const totalItems = new Set(customerPreferences?.map(p => p.item_name) || []).size;
    const avgPreferenceScore = customerPreferences?.length > 0
      ? customerPreferences.reduce((sum, p) => sum + (p.preference_score || 0), 0) / customerPreferences.length
      : 0;

    // Group by item category for insights
    interface CategoryInsight {
      category: string;
      unique_customers: Set<string>;
      total_purchases: number;
      total_revenue: number;
      items: Set<string>;
    }

    const categoryInsights = customerPreferences?.reduce((acc: Record<string, CategoryInsight>, pref) => {
      const category = pref.item_category || 'Uncategorized';
      if (!acc[category]) {
        acc[category] = {
          category: category,
          unique_customers: new Set(),
          total_purchases: 0,
          total_revenue: 0,
          items: new Set()
        };
      }

      acc[category].unique_customers.add(pref.member_id);
      acc[category].total_purchases += pref.purchase_count || 0;
      acc[category].total_revenue += pref.total_spent || 0;
      acc[category].items.add(pref.item_name);

      return acc;
    }, {});

    // Convert sets to counts and calculate averages
    const categoryStats = Object.values(categoryInsights || {}).map((cat: CategoryInsight) => ({
      category: cat.category,
      unique_customers: cat.unique_customers.size,
      total_purchases: cat.total_purchases,
      total_revenue: cat.total_revenue,
      unique_items: cat.items.size,
      avg_revenue_per_customer: cat.unique_customers.size > 0 ? cat.total_revenue / cat.unique_customers.size : 0
    }));

    return NextResponse.json({
      summary: {
        total_customers: totalCustomers,
        total_items: totalItems,
        avg_preference_score: avgPreferenceScore,
        analysis_period_months: monthsBack
      },
      customer_preferences: customerPreferences || [],
      customer_favorites: customerFavorites || [],
      category_insights: categoryStats,
      type: 'company_overview'
    });

  } catch (error) {
    console.error('Customer analytics API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
