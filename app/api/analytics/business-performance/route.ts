import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('company_id');

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 });
    }

    const supabase = await createClient();

    // Get business item performance data
    const { data: itemPerformance, error: itemError } = await supabase
      .from('business_item_performance')
      .select('*')
      .eq('company_id', companyId)
      .order('popularity_score', { ascending: false });

    if (itemError) {
      console.error('Error fetching item performance:', itemError);
      return NextResponse.json({ error: 'Failed to fetch item performance data' }, { status: 500 });
    }

    // Get category revenue analysis
    const { data: categoryAnalysis, error: categoryError } = await supabase
      .from('category_revenue_analysis')
      .select('*')
      .eq('company_id', companyId)
      .order('total_revenue', { ascending: false });

    if (categoryError) {
      console.error('Error fetching category analysis:', categoryError);
      return NextResponse.json({ error: 'Failed to fetch category analysis' }, { status: 500 });
    }

    // Get service bundling patterns
    const { data: bundlingPatterns, error: bundlingError } = await supabase
      .rpc('get_service_bundling_patterns', {
        p_company_id: companyId,
        p_min_occurrences: 2
      });

    if (bundlingError) {
      console.error('Error fetching bundling patterns:', bundlingError);
      return NextResponse.json({ error: 'Failed to fetch bundling patterns' }, { status: 500 });
    }

    // Calculate summary metrics
    const totalRevenue = itemPerformance?.reduce((sum, item) => sum + (item.total_revenue || 0), 0) || 0;
    const totalSales = itemPerformance?.reduce((sum, item) => sum + (item.total_sales || 0), 0) || 0;
    const uniqueCustomers = itemPerformance?.reduce((sum, item) => sum + (item.unique_customers || 0), 0) || 0;
    const averageOrderValue = totalSales > 0 ? totalRevenue / totalSales : 0;

    // Top performing items (top 10)
    const topItems = itemPerformance?.slice(0, 10) || [];

    // Items needing attention (low performance)
    const lowPerformingItems = itemPerformance?.filter(item =>
      (item.total_sales || 0) < 5 || (item.popularity_score || 0) < 2
    ).slice(0, 10) || [];

    // Price optimization insights
    const pricingInsights = itemPerformance?.reduce((insights, item) => {
      if (item.pricing_strategy === 'Premium Pricing') {
        insights.premium_items++;
      } else if (item.pricing_strategy === 'Discounted') {
        insights.discounted_items++;
      } else {
        insights.standard_pricing++;
      }
      return insights;
    }, { premium_items: 0, discounted_items: 0, standard_pricing: 0 }) ||
    { premium_items: 0, discounted_items: 0, standard_pricing: 0 };

    // Category trends analysis
    const categoryTrends = categoryAnalysis?.reduce((trends, category) => {
      if (category.trend === 'Growing') {
        trends.growing++;
      } else if (category.trend === 'Declining') {
        trends.declining++;
      } else {
        trends.stable++;
      }
      return trends;
    }, { growing: 0, declining: 0, stable: 0 }) ||
    { growing: 0, declining: 0, stable: 0 };

    // Revenue distribution by category
    const categoryRevenue = categoryAnalysis?.map(cat => ({
      category: cat.item_category,
      revenue: cat.total_revenue || 0,
      percentage: cat.revenue_percentage || 0,
      items: cat.items_in_category || 0,
      customers: cat.unique_customers || 0
    })) || [];

    // Service bundle recommendations (top 5 bundles)
    const topBundles = bundlingPatterns?.slice(0, 5) || [];

    return NextResponse.json({
      summary: {
        total_revenue: totalRevenue,
        total_sales: totalSales,
        unique_customers: uniqueCustomers,
        average_order_value: averageOrderValue,
        total_items: itemPerformance?.length || 0,
        active_categories: categoryAnalysis?.length || 0
      },
      item_performance: {
        all_items: itemPerformance || [],
        top_performers: topItems,
        low_performers: lowPerformingItems,
        pricing_insights: pricingInsights
      },
      category_analysis: {
        revenue_by_category: categoryRevenue,
        trend_analysis: categoryTrends,
        detailed_analysis: categoryAnalysis || []
      },
      bundling_insights: {
        top_bundles: topBundles,
        total_patterns: bundlingPatterns?.length || 0,
        bundle_opportunities: bundlingPatterns?.filter((b: { bundle_frequency: number }) => b.bundle_frequency >= 5) || []
      },
      recommendations: {
        pricing: pricingInsights.discounted_items > pricingInsights.premium_items
          ? 'Consider premium pricing for popular items'
          : 'Pricing strategy appears balanced',
        categories: categoryTrends.declining > categoryTrends.growing
          ? 'Focus on declining categories for improvement'
          : 'Category performance is generally positive',
        bundles: topBundles.length > 0
          ? `Promote ${topBundles[0]?.item1_name} + ${topBundles[0]?.item2_name} bundle`
          : 'Insufficient data for bundle recommendations'
      }
    });

  } catch (error) {
    console.error('Business performance API error:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
