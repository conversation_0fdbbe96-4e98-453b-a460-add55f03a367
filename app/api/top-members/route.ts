import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export const dynamic = 'force-dynamic'

interface TopMemberData {
  id: string
  name: string
  email: string
  loyalty_tier: string
  lifetime_points: number
  available_points: number
  redeemed_points: number
  profile_image_url?: string | null
  value: number
  metric: string
}

interface RedemptionTransaction {
  member_id: string
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const metric = searchParams.get('metric') || 'lifetime_points'
    const limit = parseInt(searchParams.get('limit') || '5', 10)
    const paramCompanyId = searchParams.get('companyId')

    // Validate metric parameter
    const validMetrics = ['lifetime_points', 'available_points', 'redeemed_points', 'redemption_count']
    if (!validMetrics.includes(metric)) {
      return NextResponse.json(
        { error: `Invalid metric. Valid options are: ${validMetrics.join(', ')}` },
        { status: 400 }
      )
    }

    // Validate limit parameter
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Limit must be a number between 1 and 100' },
        { status: 400 }
      )
    }

    // Use service role client to bypass RLS policies
    const supabase = getServiceRoleClient()

    // Get the company ID from the parameter
    const companyId = paramCompanyId

    // If no company ID was provided in the parameters, return an error
    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    let data: TopMemberData[] = []

    // Try to use the optimized database function first, fall back to direct queries
    try {
      const { data: functionResult, error: functionError } = await supabase
        .rpc('get_top_members_live', {
          p_company_id: companyId,
          p_metric: metric,
          p_limit: limit
        })

      if (!functionError && functionResult) {
        // Function returned valid data
        data = Array.isArray(functionResult) ? functionResult : []
      } else {
        // Function failed or doesn't exist, use direct queries
        throw new Error('Function not available, using fallback')
      }
    } catch {
      console.log('Using fallback query method for top members')

      // Fallback: Use the new member_points_live view for consistent, accurate data
      if (metric === 'redemption_count') {
        // Special handling for redemption count - calculate from transactions
        const { data: membersData, error } = await supabase
          .from('member_points_live')
          .select(`
            id,
            name,
            email,
            loyalty_tier,
            lifetime_points,
            available_points,
            redeemed_points,
            profile_image_url
          `)
          .eq('company_id', companyId)
          .gt('redeemed_points', 0)
          .order('redeemed_points', { ascending: false })
          .limit(limit)

        if (error) throw error

        // Get actual redemption counts from transactions
        const memberIds = (membersData || []).map(m => m.id)
        if (memberIds.length > 0) {
          const { data: redemptionCounts, error: countError } = await supabase
            .from('points_transactions')
            .select('member_id')
            .in('member_id', memberIds)
            .eq('transaction_type', 'REDEEM')
            .eq('company_id', companyId)

          if (countError) throw countError

          // Count redemptions per member
          const countsByMember = (redemptionCounts || []).reduce((acc: Record<string, number>, tx: RedemptionTransaction) => {
            acc[tx.member_id] = (acc[tx.member_id] || 0) + 1
            return acc
          }, {})

          // Add redemption counts to member data
          data = (membersData || []).map(member => ({
            ...member,
            value: countsByMember[member.id] || 0,
            metric: 'redemption_count'
          })).sort((a, b) => b.value - a.value)
        }
      } else {
        // Handle points-based metrics using member_points_live view
        const orderBy = metric === 'lifetime_points' ? 'lifetime_points' :
                       metric === 'available_points' ? 'available_points' :
                       metric === 'redeemed_points' ? 'redeemed_points' : 'lifetime_points'

        const { data: membersData, error } = await supabase
          .from('member_points_live')
          .select(`
            id,
            name,
            email,
            loyalty_tier,
            lifetime_points,
            available_points,
            redeemed_points,
            profile_image_url
          `)
          .eq('company_id', companyId)
          .order(orderBy, { ascending: false })
          .limit(limit)

        if (error) throw error

        // Transform data to include the metric value
        data = (membersData || []).map(member => ({
          ...member,
          value: member[orderBy as keyof typeof member] as number,
          metric
        }))
      }
    }

    // Calculate correct tier based on lifetime points for each member
    const processedData = await Promise.all((data || []).map(async (member: TopMemberData) => {
      let tierName = member.loyalty_tier

      // Calculate correct tier based on lifetime points
      if (member.lifetime_points && member.lifetime_points > 0) {
        try {
          // Get all tier definitions to calculate the correct tier
          const { data: tierDefinitions } = await supabase
            .from('tier_definitions')
            .select('id, tier_name, minimum_points')
            .eq('company_id', companyId)
            .order('minimum_points', { ascending: true })

          if (tierDefinitions && tierDefinitions.length > 0) {
            // Find the appropriate tier based on lifetime points
            let appropriateTier = tierDefinitions[0] // Default to lowest tier
            for (const tier of tierDefinitions) {
              if (member.lifetime_points >= tier.minimum_points) {
                appropriateTier = tier
              } else {
                break
              }
            }
            tierName = appropriateTier.tier_name
          }
        } catch (tierError) {
          console.error('Error calculating tier for member:', member.id, tierError)
          // Keep the existing tier as fallback
        }
      }

      return {
        ...member,
        loyalty_tier: tierName
      }
    }))

    return NextResponse.json({ data: processedData })
  } catch (error) {
    console.error('Unexpected error in top-members route:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
