import { NextRequest, NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';
import { isCompanyAdminByEmail } from '@/lib/auth';

export async function GET(req: NextRequest) {
  const searchParams = req.nextUrl.searchParams;
  const email = searchParams.get('email');
  const companyId = searchParams.get('companyId');

  if (!email || !companyId) {
    return NextResponse.json(
      { error: 'Email and companyId parameters are required' },
      { status: 400 }
    );
  }

  try {
    // Create Supabase client with service role to bypass RLS
    const supabase = getServiceRoleClient();

    // Check if the user exists
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('id, email')
      .eq('email', email)
      .single();

    if (userError) {
      console.error(`User not found with email ${email}:`, userError);
      return NextResponse.json(
        { error: `User not found with email ${email}` },
        { status: 404 }
      );
    }

    // Check if the company exists
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .eq('id', companyId)
      .single();

    if (companyError) {
      console.error(`Company not found with ID ${companyId}:`, companyError);
      return NextResponse.json(
        { error: `Company not found with ID ${companyId}` },
        { status: 404 }
      );
    }

    // Check if the user is an admin for the company
    const isAdmin = await isCompanyAdminByEmail(supabase, email, companyId);

    // If not an admin, let's check if we need to add them
    if (!isAdmin && email === '<EMAIL>') {
      console.log(`Adding ${email} as admin for company ${companyId} (${companyData.name})`);
      
      // First check if they're already in the company_administrators table
      const { data: existingAdmin } = await supabase
        .from('company_administrators')
        .select('id')
        .eq('administrator_id', userData.id)
        .eq('company_id', companyId)
        .single();
      
      if (!existingAdmin) {
        // Add the user to the company_administrators table
        const { error: insertError } = await supabase
          .from('company_administrators')
          .insert({
            administrator_id: userData.id,
            company_id: companyId,
            created_at: new Date().toISOString(),
          });
        
        if (insertError) {
          console.error('Error adding admin:', insertError);
          return NextResponse.json(
            { 
              error: 'Failed to add user as admin',
              user: userData,
              company: companyData,
              isAdmin: false,
              addAttempted: true,
              addError: insertError.message
            },
            { status: 500 }
          );
        }
        
        // Verify the addition worked
        const isAdminNow = await isCompanyAdminByEmail(supabase, email, companyId);
        
        return NextResponse.json({
          user: userData,
          company: companyData,
          isAdmin: isAdminNow,
          wasAdded: true
        });
      }
    }

    return NextResponse.json({
      user: userData,
      company: companyData,
      isAdmin
    });
  } catch (error) {
    console.error('Error checking admin status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
