-- Function to add points to a member
CREATE OR REPLACE FUNCTION add_points_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_points INTEGER,
  p_description TEXT,
  p_transaction_type TEXT DEFAULT 'EARN'
) RETURNS VOID AS $$
DECLARE
  v_member_exists BOOLEAN;
  v_expiration_date DATE;
BEGIN
  -- Check if member exists and belongs to the company
  SELECT EXISTS (
    SELECT 1 FROM loyalty_members
    WHERE id = p_member_id AND company_id = p_company_id
  ) INTO v_member_exists;

  IF NOT v_member_exists THEN
    RAISE EXCEPTION 'Member not found or does not belong to this company';
  END IF;

  -- Set expiration date - 1 year from now for EARN transactions, far future for others
  IF p_transaction_type = 'EARN' THEN
    v_expiration_date := CURRENT_DATE + INTERVAL '1 year';
  ELSE
    v_expiration_date := '2099-12-31'::DATE;
  END IF;

  -- Create the transaction record
  INSERT INTO points_transactions (
    member_id,
    company_id,
    points_change,
    description,
    transaction_type,
    expiration_date,
    transaction_date
  ) VALUES (
    p_member_id,
    p_company_id,
    p_points,
    p_description,
    p_transaction_type,
    v_expiration_date,
    CURRENT_TIMESTAMP
  );

  -- Update the member's lifetime points
  UPDATE loyalty_members
  SET lifetime_points = COALESCE(lifetime_points, 0) + p_points
  WHERE id = p_member_id AND company_id = p_company_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to deduct points from a member
CREATE OR REPLACE FUNCTION deduct_points_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_points INTEGER,
  p_description TEXT,
  p_transaction_type TEXT DEFAULT 'REDEEM'
) RETURNS VOID AS $$
DECLARE
  v_member_exists BOOLEAN;
  v_available_points INTEGER;
BEGIN
  -- Check if member exists and belongs to the company
  SELECT EXISTS (
    SELECT 1 FROM loyalty_members
    WHERE id = p_member_id AND company_id = p_company_id
  ) INTO v_member_exists;

  IF NOT v_member_exists THEN
    RAISE EXCEPTION 'Member not found or does not belong to this company';
  END IF;

  -- Calculate available points
  SELECT
    COALESCE(lifetime_points, 0) - COALESCE(redeemed_points, 0) - COALESCE(expired_points, 0)
  INTO v_available_points
  FROM loyalty_members
  WHERE id = p_member_id AND company_id = p_company_id;

  -- Check if member has enough points
  IF v_available_points < p_points THEN
    RAISE EXCEPTION 'Insufficient points. Member only has % points available.', v_available_points;
  END IF;

  -- Create the transaction record with negative points value
  INSERT INTO points_transactions (
    member_id,
    company_id,
    points_change,
    description,
    transaction_type,
    expiration_date,
    transaction_date
  ) VALUES (
    p_member_id,
    p_company_id,
    -p_points, -- Negative value for deductions
    p_description,
    p_transaction_type,
    '2099-12-31'::DATE, -- Far future date for REDEEM/EXPIRE transactions
    CURRENT_TIMESTAMP
  );

  -- Update the member's redeemed or expired points based on transaction type
  IF p_transaction_type = 'REDEEM' THEN
    UPDATE loyalty_members
    SET redeemed_points = COALESCE(redeemed_points, 0) + p_points
    WHERE id = p_member_id AND company_id = p_company_id;
  ELSIF p_transaction_type = 'EXPIRE' THEN
    UPDATE loyalty_members
    SET expired_points = COALESCE(expired_points, 0) + p_points
    WHERE id = p_member_id AND company_id = p_company_id;
  END IF;

  -- For ADJUST type, we don't update redeemed or expired points, just record the transaction
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
