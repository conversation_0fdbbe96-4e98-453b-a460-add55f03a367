import { NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase/server'

export async function POST() {
  try {
    const supabase = createServiceRoleClient()

    // Fix profile image URLs that are pointing to receipts folder
    const { data, error } = await supabase
      .from('loyalty_members')
      .select('id, name, profile_image_url')
      .like('profile_image_url', '%/receipts/%')

    if (error) {
      console.error('Error fetching members with wrong URLs:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('Found members with wrong URLs:', data)

    // Update each member's profile_image_url
    for (const member of data || []) {
      if (member.profile_image_url) {
        const correctedUrl = member.profile_image_url.replace('/receipts/', '/profile-images/')

        const { error: updateError } = await supabase
          .from('loyalty_members')
          .update({ profile_image_url: correctedUrl })
          .eq('id', member.id)

        if (updateError) {
          console.error(`Error updating member ${member.id}:`, updateError)
        } else {
          console.log(`Updated ${member.name}: ${correctedUrl}`)
        }
      }
    }

    return NextResponse.json({
      message: 'Profile image URLs fixed',
      updated_count: data?.length || 0
    })
  } catch (error) {
    console.error('Error fixing profile URLs:', error)
    return NextResponse.json(
      { error: 'Failed to fix profile URLs' },
      { status: 500 }
    )
  }
}
