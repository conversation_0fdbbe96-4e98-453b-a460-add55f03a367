import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

// Types for business report data
// Generic type for Supabase RPC responses
type RpcResponse<T> = { data: T | null; error: unknown };
type RevenueCorrelationRecord = { revenue: number; [key: string]: unknown };
type CustomerValueByTierRecord = { member_count: number; [key: string]: unknown };
type RoiMetricsRecord = { program_roi: number; retention_value: number; [key: string]: unknown };
type PromotionalImpactRecord = unknown;
type LifetimeValueRecord = unknown;

interface BusinessReportSummary {
  totalRevenue: number;
  revenuePerMember: number;
  programROI: number;
  retentionValue: number;
}

interface ComparisonData {
  revenueCorrelation: RevenueCorrelationRecord[];
  customerValueByTier: CustomerValueByTierRecord[];
  roi: RoiMetricsRecord;
  totalRevenue: number;
  revenuePerMember: number;
  programROI: number;
  retentionValue: number;
  revenueChange: number;
  revenuePerMemberChange: number;
  roiChange: number;
}

interface BusinessReportResponse {
  revenueCorrelation: RevenueCorrelationRecord[];
  customerValueByTier: CustomerValueByTierRecord[];
  roi: RoiMetricsRecord;
  promotionalImpact: PromotionalImpactRecord[];
  lifetimeValue: LifetimeValueRecord[];
  summary: BusinessReportSummary;
  comparison?: ComparisonData;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    const prevStartDate = searchParams.get('prevStartDate') || '';
    const prevEndDate = searchParams.get('prevEndDate') || '';
    const showComparison = searchParams.get('showComparison') === 'true';

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Fetch revenue correlation data
    const { data: revenueCorrelationData } = (await supabase.rpc('get_revenue_loyalty_correlation', {
      p_start_date: startDate,
      p_end_date: endDate,
    }) as RpcResponse<RevenueCorrelationRecord[]>);
    const revenueCorrelation: RevenueCorrelationRecord[] = revenueCorrelationData ?? [];

    // Fetch customer value by tier
    const { data: customerValueData } = (await supabase.rpc('get_customer_value_by_tier', {
      p_start_date: startDate,
      p_end_date: endDate,
    }) as RpcResponse<CustomerValueByTierRecord[]>);
    const customerValueByTier: CustomerValueByTierRecord[] = customerValueData ?? [];

    // Fetch ROI metrics
    const { data: roiMetricsData } = (await supabase.rpc('get_loyalty_program_roi', {
      p_start_date: startDate,
      p_end_date: endDate,
    }) as RpcResponse<RoiMetricsRecord>);
    const roiMetrics: RoiMetricsRecord = roiMetricsData ?? { program_roi: 0, retention_value: 0 };

    // Fetch promotional impact data
    const { data: promotionalImpactData } = (await supabase.rpc('get_promotional_impact', {
      p_start_date: startDate,
      p_end_date: endDate,
    }) as RpcResponse<PromotionalImpactRecord[]>);
    const promotionalImpact: PromotionalImpactRecord[] = promotionalImpactData ?? [];

    // Fetch lifetime value analysis
    const { data: lifetimeValueData } = (await supabase.rpc('get_member_lifetime_value', {
      p_start_date: startDate,
      p_end_date: endDate,
    }) as RpcResponse<LifetimeValueRecord[]>);
    const lifetimeValue: LifetimeValueRecord[] = lifetimeValueData ?? [];

    // Initialize response data
    const responseData: BusinessReportResponse = {
      revenueCorrelation,
      customerValueByTier,
      roi: roiMetrics,
      promotionalImpact,
      lifetimeValue,
      summary: {
        totalRevenue: 0,
        revenuePerMember: 0,
        programROI: 0,
        retentionValue: 0,
      },
    };

    // Calculate summary values
    if (responseData.revenueCorrelation.length > 0) {
      // Sum up total revenue from all entries
      responseData.summary.totalRevenue = responseData.revenueCorrelation.reduce(
        (sum: number, item: RevenueCorrelationRecord) => sum + (item.revenue as number),
        0
      );
    }

    // Calculate revenue per member
    if (responseData.customerValueByTier.length > 0) {
      const totalMembers = responseData.customerValueByTier.reduce(
        (sum: number, item: CustomerValueByTierRecord) => sum + (item.member_count as number),
        0
      );

      responseData.summary.revenuePerMember =
        totalMembers > 0
          ? Math.round(responseData.summary.totalRevenue / totalMembers)
          : 0;
    }

    // Get ROI and retention values from roi metrics
    if (responseData.roi && typeof responseData.roi === 'object') {
      responseData.summary.programROI = responseData.roi.program_roi || 0;
      responseData.summary.retentionValue = responseData.roi.retention_value || 0;
    }

    // If comparison is requested, get previous period metrics
    if (showComparison && prevStartDate && prevEndDate) {
      const [
        prevRevenueCorrelationResult,
        prevCustomerValueResult,
        prevRoiMetricsResult,
      ] = await Promise.all([
        supabase.rpc('get_revenue_loyalty_correlation', {
          p_start_date: prevStartDate,
          p_end_date: prevEndDate,
        }),

        supabase.rpc('get_customer_value_by_tier', {
          p_start_date: prevStartDate,
          p_end_date: prevEndDate,
        }),

        supabase.rpc('get_loyalty_program_roi', {
          p_start_date: prevStartDate,
          p_end_date: prevEndDate,
        }),
      ]);

      let prevTotalRevenue = 0;
      if (prevRevenueCorrelationResult.data && prevRevenueCorrelationResult.data.length > 0) {
        prevTotalRevenue = prevRevenueCorrelationResult.data.reduce(
          (sum: number, item: RevenueCorrelationRecord) => sum + (item.revenue as number),
          0
        );
      }

      let prevRevenuePerMember = 0;
      if (prevCustomerValueResult.data && prevCustomerValueResult.data.length > 0) {
        const prevTotalMembers = prevCustomerValueResult.data.reduce(
          (sum: number, item: CustomerValueByTierRecord) => sum + (item.member_count as number),
          0
        );

        prevRevenuePerMember =
          prevTotalMembers > 0
            ? Math.round(prevTotalRevenue / prevTotalMembers)
            : 0;
      }

      let prevProgramROI = 0;
      let prevRetentionValue = 0;
      if (prevRoiMetricsResult.data && typeof prevRoiMetricsResult.data === 'object') {
        prevProgramROI = prevRoiMetricsResult.data.program_roi || 0;
        prevRetentionValue = prevRoiMetricsResult.data.retention_value || 0;
      }

      responseData.comparison = {
        revenueCorrelation: prevRevenueCorrelationResult.data || [],
        customerValueByTier: prevCustomerValueResult.data || [],
        roi: prevRoiMetricsResult.data || { program_roi: 0, retention_value: 0 },
        totalRevenue: prevTotalRevenue,
        revenuePerMember: prevRevenuePerMember,
        programROI: prevProgramROI,
        retentionValue: prevRetentionValue,
        revenueChange:
          prevTotalRevenue > 0
            ? Math.round(((responseData.summary.totalRevenue - prevTotalRevenue) / prevTotalRevenue) * 100)
            : 0,
        revenuePerMemberChange:
          prevRevenuePerMember > 0
            ? Math.round(((responseData.summary.revenuePerMember - prevRevenuePerMember) / prevRevenuePerMember) * 100)
            : 0,
        roiChange:
          prevProgramROI > 0
            ? Math.round(((responseData.summary.programROI - prevProgramROI) / prevProgramROI) * 100)
            : 0,
      };
    }

    // Return the response
    return NextResponse.json(responseData);

  } catch (error) {
    console.error("Error in business impact API:", error);
    return NextResponse.json(
      { error: "Failed to fetch business impact data" },
      { status: 500 }
    );
  }
}