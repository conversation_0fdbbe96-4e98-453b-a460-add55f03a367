import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';
// Type for redemption trends RPC results
type RedemptionTrendRecord = { count?: number; points_redeemed?: number; [key: string]: unknown };
// Type for popular rewards RPC results
type PopularRewardRecord = { reward_name?: string; [key: string]: unknown };
// Type for comparison data
type RewardsComparisonData = {
  redemptionTrends: RedemptionTrendRecord[];
  popularRewards: PopularRewardRecord[];
  totalRedemptions: number;
  totalPointsRedeemed: number;
  redemptionRate: number;
  redemptionsChange: number;
  pointsRedeemedChange: number;
  redemptionRateChange: number;
};
// Overall report response type
interface RewardsReportResponse {
  redemptionTrends: RedemptionTrendRecord[];
  popularRewards: PopularRewardRecord[];
  rewardEfficiency: unknown[];
  categoryPerformance: unknown[];
  inventoryStatus: unknown[];
  recentRedemptions: unknown[];
  summary: {
    totalRedemptions: number;
    totalPointsRedeemed: number;
    redemptionRate: number;
    mostPopularReward: string;
  };
  comparison?: RewardsComparisonData;
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    const prevStartDate = searchParams.get('prevStartDate') || '';
    const prevEndDate = searchParams.get('prevEndDate') || '';
    const showComparison = searchParams.get('showComparison') === 'true';

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Fetch redemption trends over time
    const redemptionTrendsResult = await supabase.rpc('get_redemption_trends', {
      p_start_date: startDate,
      p_end_date: endDate,
    });
    // Cast to typed array for safer reduce usage
    const redemptionTrends: RedemptionTrendRecord[] = redemptionTrendsResult.data || [];

    // Fetch most popular rewards
    const popularRewardsResult = await supabase.rpc('get_popular_rewards', {
      p_start_date: startDate,
      p_end_date: endDate,
      p_limit: 10,
    });

    // Fetch reward efficiency metrics (points cost vs perceived value)
    const rewardEfficiencyResult = await supabase.rpc('get_reward_efficiency', {
      p_start_date: startDate,
      p_end_date: endDate,
    });

    // Fetch reward category analysis
    const rewardCategoryResult = await supabase.rpc('get_category_performance', {
      p_start_date: startDate,
      p_end_date: endDate,
    });

    // Fetch inventory status for rewards
    const inventoryStatusResult = await supabase
      .from('rewards')
      .select('id, name, code, points_cost, is_active, redemption_count')
      .order('redemption_count', { ascending: false });

    // Fetch recent redemptions for table display
    const recentRedemptionsResult = await supabase
      .from('reward_redemptions')
      .select(`
        id,
        redeemed_at,
        points_used,
        member_id,
        reward_id,
        rewards (
          id,
          name,
          code
        ),
        loyalty_members (
          id,
          name,
          loyalty_id
        )
      `)
      .order('redeemed_at', { ascending: false })
      .limit(20);

    // Initialize response data
    const responseData: RewardsReportResponse = {
      redemptionTrends: redemptionTrendsResult.data || [],
      popularRewards: popularRewardsResult.data || [],
      rewardEfficiency: rewardEfficiencyResult.data || [],
      categoryPerformance: rewardCategoryResult.data || [],
      inventoryStatus: inventoryStatusResult.data || [],
      recentRedemptions: recentRedemptionsResult.data || [],
      summary: {
        totalRedemptions: 0,
        totalPointsRedeemed: 0,
        redemptionRate: 0,
        mostPopularReward: '',
      },
    };

    // Calculate total redemptions and points redeemed
    if (redemptionTrends.length > 0) {
      responseData.summary.totalRedemptions = redemptionTrends.reduce(
        (sum: number, item: RedemptionTrendRecord) => sum + (item.count || 0),
        0
      );
      responseData.summary.totalPointsRedeemed = redemptionTrends.reduce(
        (sum: number, item: RedemptionTrendRecord) => sum + (item.points_redeemed || 0),
        0
      );
    }

    // Get most popular reward name
    if (responseData.popularRewards.length > 0) {
      responseData.summary.mostPopularReward = responseData.popularRewards[0].reward_name || '';
    }

    // Calculate redemption rate
    // We need to fetch total number of active members and total number of members who redeemed
    const [activeMembers, redeemedMembers] = await Promise.all([
      supabase.rpc('get_active_members_count', {
        p_start_date: startDate,
        p_end_date: endDate,
      }),
      supabase.rpc('get_members_with_redemptions', {
        p_start_date: startDate,
        p_end_date: endDate,
      }),
    ]);

    const activeMemberCount = activeMembers.data || 0;
    const redeemedMemberCount = redeemedMembers.data || 0;

    responseData.summary.redemptionRate =
      activeMemberCount > 0
        ? Math.round((redeemedMemberCount / activeMemberCount) * 100)
        : 0;

    // If comparison is requested, get previous period metrics
    if (showComparison && prevStartDate && prevEndDate) {
      // Fetch previous popular rewards
      const prevPopularRewardsResult = await supabase.rpc('get_popular_rewards', {
        p_start_date: prevStartDate,
        p_end_date: prevEndDate,
        p_limit: 10,
      });
      const prevPopularRewards: PopularRewardRecord[] = prevPopularRewardsResult.data || [];

      const prevRedemptionTrendsResult = await supabase.rpc('get_redemption_trends', {
        p_start_date: prevStartDate,
        p_end_date: prevEndDate,
      });
      // Cast previous trends for safe reduce usage
      const prevRedemptionTrends: RedemptionTrendRecord[] = prevRedemptionTrendsResult.data || [];

      let prevTotalRedemptions = 0;
      let prevTotalPointsRedeemed = 0;
      if (prevRedemptionTrends.length > 0) {
        prevTotalRedemptions = prevRedemptionTrends.reduce(
          (sum: number, item: RedemptionTrendRecord) => sum + (item.count || 0),
          0
        );
        prevTotalPointsRedeemed = prevRedemptionTrends.reduce(
          (sum: number, item: RedemptionTrendRecord) => sum + (item.points_redeemed || 0),
          0
        );
      }

      const prevRedeemedMembers = await supabase.rpc('get_members_with_redemptions', {
        p_start_date: prevStartDate,
        p_end_date: prevEndDate,
      });

      const prevRedeemedMemberCount = prevRedeemedMembers.data || 0;
      const prevRedemptionRate =
        activeMemberCount > 0
          ? Math.round((prevRedeemedMemberCount / activeMemberCount) * 100)
          : 0;

      responseData.comparison = {
        redemptionTrends: prevRedemptionTrendsResult.data || [],
        popularRewards: prevPopularRewards,
        totalRedemptions: prevTotalRedemptions,
        totalPointsRedeemed: prevTotalPointsRedeemed,
        redemptionRate: prevRedemptionRate,
        redemptionsChange:
          prevTotalRedemptions > 0
            ? Math.round(((responseData.summary.totalRedemptions - prevTotalRedemptions) / prevTotalRedemptions) * 100)
            : 0,
        pointsRedeemedChange:
          prevTotalPointsRedeemed > 0
            ? Math.round(((responseData.summary.totalPointsRedeemed - prevTotalPointsRedeemed) / prevTotalPointsRedeemed) * 100)
            : 0,
        redemptionRateChange:
          prevRedemptionRate > 0
            ? responseData.summary.redemptionRate - prevRedemptionRate
            : 0,
      };
    }

    // Return the response
    return NextResponse.json(responseData);

  } catch (error) {
    console.error("Error in reports rewards API:", error);
    return NextResponse.json(
      { error: "Failed to fetch rewards report data" },
      { status: 500 }
    );
  }
}