import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';
import { parseISO, format, subDays } from 'date-fns';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') || format(subDays(new Date(), 30), 'yyyy-MM-dd');
    const endDate = searchParams.get('endDate') || format(new Date(), 'yyyy-MM-dd');
    const showComparison = searchParams.get('showComparison') === 'true';
    const prevStartDate = searchParams.get('prevStartDate') || format(subDays(parseISO(startDate), 30), 'yyyy-MM-dd');
    const prevEndDate = searchParams.get('prevEndDate') || format(subDays(parseISO(endDate), 30), 'yyyy-MM-dd');

    // Create Supabase client
    const supabase = getServiceRoleClient();

    // Fetch metrics data
    const [activeMembersResult, pointsIssuedResult, pointsRedeemedResult,
           redemptionCountResult, ordersResult] = await Promise.all([
      // Active Members Count
      supabase
        .from('members')
        .select('count', { count: 'exact' })
        .gte('last_activity', startDate)
        .lte('created_at', endDate),

      // Points Issued
      supabase
        .from('transactions')
        .select('points')
        .eq('type', 'credit')
        .gte('created_at', startDate)
        .lte('created_at', endDate),

      // Points Redeemed
      supabase
        .from('transactions')
        .select('points')
        .eq('type', 'debit')
        .gte('created_at', startDate)
        .lte('created_at', endDate),

      // Redemption Count
      supabase
        .from('redemptions')
        .select('count', { count: 'exact' })
        .gte('created_at', startDate)
        .lte('created_at', endDate),

      // Orders (for conversion and AOV)
      supabase
        .from('receipts')
        .select('id, total_amount')
        .gte('created_at', startDate)
        .lte('created_at', endDate),
    ]);

    // Calculate metrics
    const activeMemberCount = activeMembersResult.count || 0;
    const pointsIssued = pointsIssuedResult.data?.reduce((sum, tx) => sum + tx.points, 0) || 0;
    const pointsRedeemed = pointsRedeemedResult.data?.reduce((sum, tx) => sum + tx.points, 0) || 0;
    const redemptionCount = redemptionCountResult.count || 0;

    // Calculate conversion rate and AOV
    const orders = ordersResult.data || [];
    const orderCount = orders.length;
    const totalOrderValue = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
    const averageOrderValue = orderCount > 0 ? totalOrderValue / orderCount : 0;
    const conversionRate = activeMemberCount > 0 ? (orderCount / activeMemberCount) * 100 : 0;

    // Fetch member growth data for chart
    const memberGrowthResult = await supabase
      .from('members')
      .select('created_at')
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at');

    // Format member growth data
    const membersByDate = (memberGrowthResult.data || []).reduce((acc, member) => {
      const date = format(parseISO(member.created_at), 'yyyy-MM-dd');
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const memberGrowth = Object.entries(membersByDate).map(([date, count]) => ({
      date,
      count
    }));

    // Fetch points activity data for chart
    const pointsActivityResult = await supabase
      .from('transactions')
      .select('created_at, type, points')
      .in('type', ['credit', 'debit'])
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      .order('created_at');

    // Format points activity data
    const pointsByDate = (pointsActivityResult.data || []).reduce((acc, tx) => {
      const date = format(parseISO(tx.created_at), 'yyyy-MM-dd');

      if (!acc[date]) {
        acc[date] = { issued: 0, redeemed: 0 };
      }

      if (tx.type === 'credit') {
        acc[date].issued += tx.points;
      } else if (tx.type === 'debit') {
        acc[date].redeemed += tx.points;
      }

      return acc;
    }, {} as Record<string, { issued: number, redeemed: number }>);

    const pointsActivity = Object.entries(pointsByDate).map(([date, data]) => ({
      date,
      issued: data.issued,
      redeemed: data.redeemed
    }));

    // Fetch recent activity
    const recentActivityResult = await supabase
      .from('transactions')
      .select(`
        id,
        created_at,
        members(name, id),
        type,
        points,
        status,
        description
      `)
      .order('created_at', { ascending: false })
      .limit(10);

    const recentActivity = (recentActivityResult.data || []).map(item => ({
      id: item.id,
      date: item.created_at,
      member_name: item.members?.[0]?.name || 'Unknown',
      member_id: item.members?.[0]?.id,
      activity_type: item.description || (item.type === 'credit' ? 'Points Earned' : 'Points Redeemed'),
      points: item.points,
      status: item.status || 'completed'
    }));

    // Fetch top redemptions
    const topRedemptionsResult = await supabase
      .from('redemptions')
      .select(`
        rewards(name, points_cost),
        count
      `)
      .gte('created_at', startDate)
      .lte('created_at', endDate)
      // Using in-line PostgreSQL GROUP BY through .select() with count aggregation
      // instead of .group() which may not be available in the type definitions
      .limit(10);

    const topRedemptions = (topRedemptionsResult.data || []).map((item: { rewards: { name: string; points_cost: number }[]; count: number }) => ({
      reward_name: item.rewards?.[0]?.name || 'Unknown',
      count: item.count || 0,
      points_spent: (item.rewards?.[0]?.points_cost || 0) * (item.count || 0)
    }));

    // Fetch comparison data if requested
    let comparison;

    if (showComparison) {
      const [prevActiveMembersResult, prevPointsIssuedResult, prevPointsRedeemedResult,
             prevRedemptionCountResult, prevOrdersResult] = await Promise.all([
        // Active Members Count - Previous
        supabase
          .from('members')
          .select('count', { count: 'exact' })
          .gte('last_activity', prevStartDate)
          .lte('created_at', prevEndDate),

        // Points Issued - Previous
        supabase
          .from('transactions')
          .select('points')
          .eq('type', 'credit')
          .gte('created_at', prevStartDate)
          .lte('created_at', prevEndDate),

        // Points Redeemed - Previous
        supabase
          .from('transactions')
          .select('points')
          .eq('type', 'debit')
          .gte('created_at', prevStartDate)
          .lte('created_at', prevEndDate),

        // Redemption Count - Previous
        supabase
          .from('redemptions')
          .select('count', { count: 'exact' })
          .gte('created_at', prevStartDate)
          .lte('created_at', prevEndDate),

        // Orders - Previous (for conversion and AOV)
        supabase
          .from('receipts')
          .select('id, total_amount')
          .gte('created_at', prevStartDate)
          .lte('created_at', prevEndDate),
      ]);

      // Calculate comparison metrics
      const prevActiveMemberCount = prevActiveMembersResult.count || 0;
      const prevPointsIssued = prevPointsIssuedResult.data?.reduce((sum, tx) => sum + tx.points, 0) || 0;
      const prevPointsRedeemed = prevPointsRedeemedResult.data?.reduce((sum, tx) => sum + tx.points, 0) || 0;
      const prevRedemptionCount = prevRedemptionCountResult.count || 0;

      // Calculate previous conversion rate and AOV
      const prevOrders = prevOrdersResult.data || [];
      const prevOrderCount = prevOrders.length;
      const prevTotalOrderValue = prevOrders.reduce((sum, order) => sum + (order.total_amount || 0), 0);
      const prevAverageOrderValue = prevOrderCount > 0 ? prevTotalOrderValue / prevOrderCount : 0;
      const prevConversionRate = prevActiveMemberCount > 0 ? (prevOrderCount / prevActiveMemberCount) * 100 : 0;

      // Calculate percentage changes
      const calculatePercentChange = (current: number, previous: number): number => {
        if (previous === 0) return current > 0 ? 100 : 0;
        return ((current - previous) / previous) * 100;
      };

      comparison = {
        activeMemberCount: prevActiveMemberCount,
        pointsIssued: prevPointsIssued,
        pointsRedeemed: prevPointsRedeemed,
        redemptionCount: prevRedemptionCount,
        conversionRate: prevConversionRate,
        averageOrderValue: prevAverageOrderValue,
        activeMemberCountChange: calculatePercentChange(activeMemberCount, prevActiveMemberCount),
        pointsIssuedChange: calculatePercentChange(pointsIssued, prevPointsIssued),
        pointsRedeemedChange: calculatePercentChange(pointsRedeemed, prevPointsRedeemed),
        redemptionCountChange: calculatePercentChange(redemptionCount, prevRedemptionCount),
        conversionRateChange: calculatePercentChange(conversionRate, prevConversionRate),
        averageOrderValueChange: calculatePercentChange(averageOrderValue, prevAverageOrderValue)
      };
    }

    return NextResponse.json({
      metrics: {
        activeMemberCount,
        pointsIssued,
        pointsRedeemed,
        redemptionCount,
        conversionRate,
        averageOrderValue
      },
      memberGrowth,
      pointsActivity,
      recentActivity,
      topRedemptions,
      comparison
    });

  } catch (error) {
    console.error('Error in reports/overview API:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}