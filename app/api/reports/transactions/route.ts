import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

// Add TS types for API response
interface TransactionVolumeRecord {
  count?: number;
  points_issued?: number;
}
interface SeasonalPatternRecord {
  count: number;
  day_of_week?: string;
}
interface ComparisonData {
  transactionVolume: unknown[];
  pointsByType: unknown[];
  totalTransactions: number;
  totalPointsIssued: number;
  averagePointsPerTransaction: number;
  transactionsChange: number;
  pointsIssuedChange: number;
}
interface ReportResponse {
  transactionVolume: TransactionVolumeRecord[];
  pointsByType: unknown[];
  seasonalPatterns: SeasonalPatternRecord[];
  receiptValueDistribution: unknown[];
  recentTransactions: unknown[];
  summary: {
    totalTransactions: number;
    averagePointsPerTransaction: number;
    highestTransactionDay: string;
    totalPointsIssued: number;
  };
  comparison?: ComparisonData;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') || '';
    const endDate = searchParams.get('endDate') || '';
    const prevStartDate = searchParams.get('prevStartDate') || '';
    const prevEndDate = searchParams.get('prevEndDate') || '';
    const showComparison = searchParams.get('showComparison') === 'true';

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Fix RPC calls - Supabase expects the return type as a type argument
    const transactionVolumeResult = await supabase.rpc('get_transaction_volume', {
      p_start_date: startDate,
      p_end_date: endDate,
    });

    const pointsIssuanceByTypeResult = await supabase.rpc('get_points_by_transaction_type', {
      p_start_date: startDate,
      p_end_date: endDate,
    });

    const seasonalPatternsResult = await supabase.rpc('get_transactions_by_day_of_week', {
      p_start_date: startDate,
      p_end_date: endDate,
    });

    const receiptValueResult = await supabase.rpc('get_receipt_value_distribution', {
      p_start_date: startDate,
      p_end_date: endDate,
    });

    // Fetch recent transactions for table data
    const recentTransactionsResult = await supabase
      .from('points_transactions')
      .select(`
        id,
        transaction_date,
        points_amount,
        transaction_type,
        loyalty_members!inner (
          id,
          name,
          loyalty_id
        )
      `)
      .order('transaction_date', { ascending: false })
      .limit(20);

    // Initialize response data with proper type assertions to ensure arrays
    const responseData: ReportResponse = {
      transactionVolume: Array.isArray(transactionVolumeResult.data) ? transactionVolumeResult.data : [],
      pointsByType: Array.isArray(pointsIssuanceByTypeResult.data) ? pointsIssuanceByTypeResult.data : [],
      seasonalPatterns: Array.isArray(seasonalPatternsResult.data) ? seasonalPatternsResult.data : [],
      receiptValueDistribution: Array.isArray(receiptValueResult.data) ? receiptValueResult.data : [],
      recentTransactions: Array.isArray(recentTransactionsResult.data) ? recentTransactionsResult.data : [],
      summary: {
        totalTransactions: 0,
        averagePointsPerTransaction: 0,
        highestTransactionDay: '',
        totalPointsIssued: 0,
      },
    };

    // Calculate summary values
    if (responseData.transactionVolume.length > 0) {
      // Calculate total transactions
      responseData.summary.totalTransactions = responseData.transactionVolume.reduce(
        (sum: number, item: TransactionVolumeRecord) => sum + (item.count || 0),
        0
      );

      // Calculate total points issued
      const totalPointsIssued = responseData.transactionVolume.reduce(
        (sum: number, item: TransactionVolumeRecord) => sum + (item.points_issued || 0),
        0
      );
      responseData.summary.totalPointsIssued = totalPointsIssued;

      // Calculate average points per transaction
      responseData.summary.averagePointsPerTransaction =
        responseData.summary.totalTransactions > 0
          ? Math.round(totalPointsIssued / responseData.summary.totalTransactions)
          : 0;

      // Find highest transaction day
      if (responseData.seasonalPatterns.length > 0) {
        const highestDay = responseData.seasonalPatterns.reduce(
          (max: SeasonalPatternRecord, day: SeasonalPatternRecord) => (day.count > max.count ? day : max),
          responseData.seasonalPatterns[0]
        );
        responseData.summary.highestTransactionDay = highestDay.day_of_week || '';
      }
    }

    // If comparison is requested, get previous period metrics
    if (showComparison && prevStartDate && prevEndDate) {
      const prevTransactionVolumeResult = await supabase.rpc('get_transaction_volume', {
        p_start_date: prevStartDate,
        p_end_date: prevEndDate,
      });

      const prevPointsIssuanceByTypeResult = await supabase.rpc('get_points_by_transaction_type', {
        p_start_date: prevStartDate,
        p_end_date: prevEndDate,
      });

      let prevTotalTransactions = 0;
      let prevTotalPointsIssued = 0;

      const prevTransactionVolumeData: TransactionVolumeRecord[] =
        Array.isArray(prevTransactionVolumeResult.data) ? prevTransactionVolumeResult.data : [];

      if (prevTransactionVolumeData.length > 0) {
        prevTotalTransactions = prevTransactionVolumeData.reduce(
          (sum: number, item: TransactionVolumeRecord) => sum + (item.count || 0),
          0
        );

        prevTotalPointsIssued = prevTransactionVolumeData.reduce(
          (sum: number, item: TransactionVolumeRecord) => sum + (item.points_issued || 0),
          0
        );
      }

      responseData.comparison = {
        transactionVolume: Array.isArray(prevTransactionVolumeResult.data) ? prevTransactionVolumeResult.data : [],
        pointsByType: Array.isArray(prevPointsIssuanceByTypeResult.data) ? prevPointsIssuanceByTypeResult.data : [],
        totalTransactions: prevTotalTransactions,
        totalPointsIssued: prevTotalPointsIssued,
        averagePointsPerTransaction:
          prevTotalTransactions > 0
            ? Math.round(prevTotalPointsIssued / prevTotalTransactions)
            : 0,
        transactionsChange:
          prevTotalTransactions > 0
            ? Math.round(((responseData.summary.totalTransactions - prevTotalTransactions) / prevTotalTransactions) * 100)
            : 0,
        pointsIssuedChange:
          prevTotalPointsIssued > 0
            ? Math.round(((responseData.summary.totalPointsIssued - prevTotalPointsIssued) / prevTotalPointsIssued) * 100)
            : 0,
      };
    }

    // Return the response
    return NextResponse.json(responseData);

  } catch (error) {
    console.error("Error in reports transactions API:", error);
    return NextResponse.json(
      { error: "Failed to fetch transaction report data" },
      { status: 500 }
    );
  }
}