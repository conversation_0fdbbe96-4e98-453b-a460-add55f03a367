import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';
import { parseISO, format, subDays } from 'date-fns';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') || format(subDays(new Date(), 30), 'yyyy-MM-dd');
    const endDate = searchParams.get('endDate') || format(new Date(), 'yyyy-MM-dd');

    // Create Supabase client
    const supabase = getServiceRoleClient();

    // Fetch member metrics
    const [
      totalMembersResult,
      newMembersResult,
      activeMembersResult,
      tierDistributionResult,
      topMembersResult,
      memberGrowthData
    ] = await Promise.all([
      // Total Members
      supabase
        .from('members')
        .select('count', { count: 'exact' })
        .lte('created_at', endDate),

      // New Members in period
      supabase
        .from('members')
        .select('count', { count: 'exact' })
        .gte('created_at', startDate)
        .lte('created_at', endDate),

      // Active Members in period
      supabase
        .from('members')
        .select('count', { count: 'exact' })
        .gte('last_activity', startDate)
        .lte('last_activity', endDate),

      // Tier Distribution
      supabase
        .from('members')
        .select('tier_id, tiers(name)')
        .lte('created_at', endDate),

      // Top Members by points
      supabase
        .from('members')
        .select('id, name, email, points_balance, tier_id, tiers(name)')
        .order('points_balance', { ascending: false })
        .limit(10),

      // Member Growth Data for chart
      supabase
        .from('members')
        .select('created_at')
        .gte('created_at', startDate)
        .lte('created_at', endDate)
        .order('created_at')
    ]);

    // Format tier distribution
    const tierDistribution = tierDistributionResult.data?.reduce(
      (acc: Record<string, number>, member: { tier_id: number; tiers: { name: string }[] }) => {
        const tierName = member.tiers?.[0]?.name || 'Unknown';
        acc[tierName] = (acc[tierName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>
    );

    const tierDistributionFormatted = Object.entries(tierDistribution || {}).map(([tier, count]) => ({
      tier,
      count,
      percentage: totalMembersResult.count ? (count / totalMembersResult.count) * 100 : 0
    }));

    // Format member growth chart data
    const membersByDate = (memberGrowthData.data || []).reduce(
      (acc: Record<string, number>, member: { created_at: string }) => {
        const date = format(parseISO(member.created_at), 'yyyy-MM-dd');
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>
    );

    // Convert to array format for chart
    const memberGrowth = Object.entries(membersByDate).map(
      ([date, count]): { date: string; count: number } => ({ date, count })
    );

    // Calculate cumulative growth
    let cumulativeCount = 0;
    const cumulativeMemberGrowth = memberGrowth.map(
      (day: { date: string; count: number }): { date: string; count: number } => {
        cumulativeCount += day.count;
        return { date: day.date, count: cumulativeCount };
      }
    );

    // Format top members
    const topMembers = topMembersResult.data?.map(
      (member: { id: string; name: string; email: string; points_balance: number; tier_id: number; tiers: { name: string }[] }): { id: string; name: string; email: string; points_balance: number; tier: string } => ({
        id: member.id,
        name: member.name,
        email: member.email,
        points_balance: member.points_balance,
        tier: member.tiers?.[0]?.name || 'Unknown'
      })
    );

    // Retention and churn data
    // For simplicity, we're considering "churned" as members who haven't had activity in 30 days
    const thirtyDaysAgo = format(subDays(new Date(), 30), 'yyyy-MM-dd');

    const [retainedMembersResult, churnedMembersResult] = await Promise.all([
      // Retained members (active in the period)
      supabase
        .from('members')
        .select('count', { count: 'exact' })
        .gte('last_activity', thirtyDaysAgo),

      // Churned members (not active in the period)
      supabase
        .from('members')
        .select('count', { count: 'exact' })
        .lt('last_activity', thirtyDaysAgo)
        .gt('created_at', format(subDays(parseISO(thirtyDaysAgo), 90), 'yyyy-MM-dd')) // Only count members who joined within 90 days
    ]);

    const retainedCount = retainedMembersResult.count || 0;
    const churnedCount = churnedMembersResult.count || 0;
    const totalForRetention = retainedCount + churnedCount;

    const retentionRate = totalForRetention > 0 ? (retainedCount / totalForRetention) * 100 : 0;
    const churnRate = totalForRetention > 0 ? (churnedCount / totalForRetention) * 100 : 0;

    return NextResponse.json({
      metrics: {
        totalMembers: totalMembersResult.count || 0,
        newMembers: newMembersResult.count || 0,
        activeMembers: activeMembersResult.count || 0,
        retentionRate,
        churnRate
      },
      memberGrowth,
      cumulativeMemberGrowth,
      tierDistribution: tierDistributionFormatted,
      topMembers
    });

  } catch (error) {
    console.error('Error in reports/members API:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}