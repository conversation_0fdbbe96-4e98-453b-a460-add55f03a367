import { NextResponse } from 'next/server';
import { getServiceRoleClient } from '@/lib/supabase';

export const dynamic = 'force-dynamic'; // Ensures the API route is not cached

export async function GET() {
  try {
    // Get a Supabase client with service role to bypass RLS
    const supabase = getServiceRoleClient();

    // Perform a simple query to check database connection
    const { data, error } = await supabase
      .from('companies')
      .select('id, name')
      .limit(5);

    if (error) {
      console.error('Database connection error:', error);
      return NextResponse.json(
        { success: false, error: error.message, code: error.code },
        { status: 500 }
      );
    }

    // Return success with data count
    return NextResponse.json({
      success: true,
      message: 'Successfully connected to the database',
      count: data?.length || 0,
      companies: data
    });

  } catch (err) {
    console.error('Unexpected error:', err);
    return NextResponse.json(
      { success: false, error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
