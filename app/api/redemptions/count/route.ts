import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

/**
 * GET /api/redemptions/count - Get redemption count for rewards
 */
export async function GET(request: NextRequest) {
  try {
    // Parse URL to get query parameters
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');
    const rewardId = searchParams.get('rewardId');

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // Build query based on parameters
    let query = supabase
      .from('reward_redemptions')
      .select('id', { count: 'exact', head: true })
      .eq('company_id', companyId);

    if (rewardId) {
      query = query.eq('reward_id', rewardId);
    }

    const { count, error } = await query;

    if (error) {
      console.error('Error fetching redemption count:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ count });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}