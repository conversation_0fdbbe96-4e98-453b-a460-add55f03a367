import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Define types for our data structures
interface Member {
  id: string;
  name: string;
  loyalty_id: string;
}

interface Reward {
  id: string;
  title: string;
  description: string;
  points_required: number;
}

interface Redemption {
  id: string;
  created_at: string;
  redemption_date: string;
  points_used: number;
  member_id: string;
  reward_id: string;
  status: string;
  notes: string | null;
  reward?: Reward | null;
  member?: Member | null;
}

// Validation schema for redemption request
const redemptionSchema = z.object({
  member_id: z.string().uuid({ message: 'Valid member ID is required' }),
  reward_id: z.string().uuid({ message: 'Valid reward ID is required' }),
  company_id: z.string().uuid({ message: 'Valid company ID is required' }),
  description: z.string().optional(),
  calculatedOutcome: z.object({
    originalAmount: z.number(),
    discountAmount: z.number(),
    finalAmount: z.number(),
    pointsUsed: z.number(),
    pointsRemaining: z.number(),
    savingsPercentage: z.number(),
    confidence: z.number(),
  }).optional(),
})

// Used for type checking the request body
// eslint-disable-next-line @typescript-eslint/no-unused-vars
type RedemptionData = z.infer<typeof redemptionSchema>

/**
 * GET /api/redemptions - Get recent redemptions
 */
export async function GET(request: NextRequest) {
  try {
    // Parse URL to get query parameters
    const { searchParams } = new URL(request.url);
    const companyId = searchParams.get('companyId');
    const limit = parseInt(searchParams.get('limit') || '10', 10);

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Use the service role client to bypass RLS
    const supabase = getServiceRoleClient();

    // First get the redemptions
    const rewardId = searchParams.get('rewardId');
    // Build query with optional reward filter
    let query = supabase
      .from('reward_redemptions')
      .select(
        `id, created_at, redemption_date, points_used, member_id, reward_id, status, notes`
      )
      .eq('company_id', companyId);
    if (rewardId) {
      query = query.eq('reward_id', rewardId);
    }
    const { data, error } = await query
      .order('redemption_date', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching redemptions:', error);
      return NextResponse.json(
        { error: error.message },
        { status: 500 }
      );
    }

    // Now fetch the reward details for these redemptions
    if (data && data.length > 0) {
      // Get unique reward IDs
      const rewardIds = [...new Set(data.map((r: Redemption) => r.reward_id))];

      // Fetch reward details
      const { data: rewardsData, error: rewardsError } = await supabase
        .from('rewards')
        .select('id, title, description, points_required')
        .in('id', rewardIds);

      if (rewardsError) {
        console.error('Error fetching rewards for redemptions:', rewardsError);
      } else if (rewardsData) {
        // Create a lookup map for quick access
        const rewardMap = new Map<string, Reward>();
        rewardsData.forEach((reward: Reward) => {
          rewardMap.set(reward.id, reward);
        });

        // Attach reward data to each redemption
        data.forEach((redemption: Redemption) => {
          redemption.reward = rewardMap.get(redemption.reward_id) || null;
        });
      }

      // Fetch member details
      const memberIds = [...new Set(data.map((r: Redemption) => r.member_id))];

      const { data: membersData, error: membersError } = await supabase
        .from('loyalty_members')
        .select('id, name, loyalty_id')
        .in('id', memberIds);

      if (membersError) {
        console.error('Error fetching members for redemptions:', membersError);
      } else if (membersData) {
        // Create a lookup map for quick access
        const memberMap = new Map<string, Member>();
        membersData.forEach((member: Member) => {
          memberMap.set(member.id, member);
        });

        // Attach member data to each redemption
        data.forEach((redemption: Redemption) => {
          redemption.member = memberMap.get(redemption.member_id) || null;
        });
      }
    }

    return NextResponse.json({ data });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/redemptions - Redeem a reward for a member
 * Validates points availability and creates a redemption record
 */
export async function POST(request: NextRequest) {
  try {
    // Get auth session from cookies
    // Use synchronous cookies() call to avoid Promise<ReadonlyRequestCookies> type error
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            // Use synchronous get method
            return cookieStore.get(name)?.value
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          set(_name: string, _value: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes as we can't set cookies in the response directly
            // We're just satisfying the type requirements
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          remove(_name: string, _options: Record<string, unknown>) {
            // This is a no-op in API routes as we can't remove cookies in the response directly
            // We're just satisfying the type requirements
          },
        },
      }
    )

    // Check authentication using getUser() for server-side security
    const { data: { user }, error: authError } = await serverSupabase.auth.getUser()
    if (!user || authError) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    console.log('📥 Redemption request body:', JSON.stringify(body, null, 2))

    const validationResult = redemptionSchema.safeParse(body)

    if (!validationResult.success) {
      console.log('❌ Validation failed:', validationResult.error.format())
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const redemptionData = validationResult.data
    console.log('✅ Validated redemption data:', JSON.stringify(redemptionData, null, 2))

    // Use service role client to bypass RLS
    const supabase = getServiceRoleClient()

    // Verify member exists and belongs to the company
    console.log(`🔍 Looking for member: ${redemptionData.member_id} in company: ${redemptionData.company_id}`)

    const { data: member, error: memberError } = await supabase
      .from('member_points_live')
      .select('id, name, available_points, lifetime_points, redeemed_points, expired_points')
      .eq('id', redemptionData.member_id)
      .eq('company_id', redemptionData.company_id)
      .single()

    console.log('👤 Member lookup result:', { member, error: memberError })

    if (memberError || !member) {
      console.log('❌ Member not found or company mismatch')
      return NextResponse.json(
        { error: 'Member not found or does not belong to this company' },
        { status: 404 }
      )
    }

    // Verify reward exists and belongs to the company
    const { data: reward, error: rewardError } = await supabase
      .from('rewards')
      .select('id, title, points_required, reward_value, reward_value_type, is_active, expiration_date, reward_type')
      .eq('id', redemptionData.reward_id)
      .eq('company_id', redemptionData.company_id)
      .single()

    if (rewardError || !reward) {
      return NextResponse.json(
        { error: 'Reward not found or does not belong to this company' },
        { status: 404 }
      )
    }

    // Check if reward is active
    if (!reward.is_active) {
      return NextResponse.json(
        { error: 'This reward is not currently active' },
        { status: 400 }
      )
    }

    // Check if reward has expired
    const now = new Date()
    const expirationDate = new Date(reward.expiration_date)

    if (expirationDate < now) {
      return NextResponse.json(
        { error: 'This reward has expired' },
        { status: 400 }
      )
    }

    // Check birthday eligibility for birthday rewards
    if (reward.reward_type === 'BIRTHDAY') {
      // Call the is_member_birthday_eligible function to check if member is eligible
      const { data: isBirthdayEligible, error: birthdayCheckError } = await supabase
        .rpc('is_member_birthday_eligible', { member_id: redemptionData.member_id })

      if (birthdayCheckError) {
        console.error('Error checking birthday eligibility:', birthdayCheckError)
        return NextResponse.json(
          { error: 'Failed to verify birthday eligibility', details: birthdayCheckError.message },
          { status: 500 }
        )
      }

      // If not eligible for birthday reward, return error
      if (!isBirthdayEligible) {
        return NextResponse.json(
          { error: 'This birthday reward is only available during your birthday period (±7 days from your birthday)' },
          { status: 403 }
        )
      }
    }

    // Check if member has enough points (use available_points from the view)
    if (member.available_points < reward.points_required) {
      return NextResponse.json(
        {
          error: 'Insufficient points',
          details: `Member has ${member.available_points} points, but ${reward.points_required} are required`
        },
        { status: 400 }
      )
    }

    // Calculate applied value based on reward type
    let appliedValue = 0
    if (reward.reward_value_type === 'percentage') {
      // For percentage discounts, we store the percentage value
      appliedValue = parseFloat(reward.reward_value)
    } else if (reward.reward_value_type === 'fixed_amount') {
      // For fixed amount discounts, we store the exact amount
      appliedValue = parseFloat(reward.reward_value)
    }

    // Create redemption record
    const { data: redemption, error: redemptionError } = await supabase
      .from('reward_redemptions')
      .insert({
        reward_id: redemptionData.reward_id,
        member_id: redemptionData.member_id,
        company_id: redemptionData.company_id,
        redemption_date: now.toISOString(),
        points_used: reward.points_required,
        applied_value: appliedValue,
        status: 'REDEEMED',
        notes: redemptionData.description,
      })
      .select()
      .single()

    if (redemptionError) {
      return NextResponse.json(
        { error: 'Failed to create redemption record', details: redemptionError.message },
        { status: 500 }
      )
    }

    // Deduct points using the deduct_points_transaction function
    const { error: deductError } = await supabase.rpc(
      'deduct_points_transaction',
      {
        p_member_id: redemptionData.member_id,
        p_company_id: redemptionData.company_id,
        p_points: reward.points_required,
        p_description: `Redeemed ${reward.title}`,
        p_transaction_type: 'REDEEM'
      }
    )

    if (deductError) {
      // If points deduction fails, mark the redemption as failed
      await supabase
        .from('reward_redemptions')
        .update({ status: 'failed', notes: `Failed to deduct points: ${deductError.message}` })
        .eq('id', redemption.id)

      return NextResponse.json(
        { error: 'Failed to deduct points', details: deductError.message },
        { status: 500 }
      )
    }

    // Get updated member data with new points balance
    const { data: updatedMember } = await supabase
      .from('member_points_live')
      .select('id, name, available_points, lifetime_points, redeemed_points')
      .eq('id', redemptionData.member_id)
      .single()

    return NextResponse.json({
      success: true,
      redemption,
      reward: {
        title: reward.title,
        points_required: reward.points_required,
        reward_value: reward.reward_value,
        reward_value_type: reward.reward_value_type
      },
      member: updatedMember,
    })
  } catch (error: unknown) {
    console.error('Error in /api/redemptions:', error)
    const errorMessage = error instanceof Error ? error.message : 'Internal server error'
    return NextResponse.json({ error: errorMessage }, { status: 500 })
  }
}
