import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/server';

/**
 * POST /api/redemptions/validate-client
 * Client-side validation for reward redemption eligibility
 * This is a fallback when AI validation fails
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Client-side validation fallback endpoint called');
    const body = await request.json();
    const { memberId, rewardId, receiptTotal } = body;

    if (!memberId || !rewardId) {
      return NextResponse.json(
        { error: 'Member ID and Reward ID are required' },
        { status: 400 }
      );
    }

    console.log('Fetching data for validation:', { memberId, rewardId });
    const supabase = createServiceRoleClient();

    // Get member directly from loyalty_members table only
    let member = null;
    let memberFields = null;

    const { data: loyaltyMembersData } = await supabase
      .from('loyalty_members')
      .select('*')
      .eq('id', memberId)
      .maybeSingle();

    if (loyaltyMembersData) {
      member = loyaltyMembersData;
      memberFields = Object.keys(loyaltyMembersData);
      console.log('Found member in loyalty_members table with fields:', memberFields);
    }

    if (!member) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Get reward data
    const { data: reward } = await supabase
      .from('rewards')
      .select('*')
      .eq('id', rewardId)
      .maybeSingle();

    if (!reward) {
      return NextResponse.json({ error: 'Reward not found' }, { status: 404 });
    }

    // Get points, regardless of field name - calculate available points properly
    const lifetimePoints = member.lifetime_points || member.total_points || member.points_earned || 0;
    const redeemedPoints = member.redeemed_points || member.points_redeemed || 0;
    const expiredPoints = member.expired_points || member.points_expired || 0;
    const availablePoints = member.available_points || (lifetimePoints - redeemedPoints - expiredPoints) || member.points || 0;

    const requiredPoints = reward.points_required || reward.points_cost || 0;
    const isEligible = availablePoints >= requiredPoints;

    // Calculate outcome if receipt total provided
    let calculatedOutcome = null;
    if (receiptTotal && receiptTotal > 0) {
      let discountAmount = 0;

      // Apply discount based on reward type
      if (reward.reward_value_type === 'PERCENTAGE') {
        discountAmount = receiptTotal * (reward.reward_value / 100);
      } else if (reward.reward_value_type === 'FIXED_AMOUNT') {
        discountAmount = Math.min(receiptTotal, reward.reward_value);
      }

      calculatedOutcome = {
        originalAmount: receiptTotal,
        discountAmount: discountAmount,
        finalAmount: receiptTotal - discountAmount,
        pointsUsed: requiredPoints,
        pointsRemaining: availablePoints - requiredPoints,
        savingsPercentage: (discountAmount / receiptTotal) * 100,
        confidence: 1.0  // High confidence since this is deterministic
      };
    }

    // Create warnings
    const warnings = [];
    if (!isEligible) {
      warnings.push({
        type: 'insufficient_points',
        message: `You need ${requiredPoints - availablePoints} more points for this reward`,
        severity: 'error'
      });
    }

    return NextResponse.json({
      eligible: isEligible,
      member: {
        id: member.id,
        availablePoints: availablePoints,
        memberFields  // Include for debugging
      },
      reward: {
        id: reward.id,
        title: reward.title || reward.name,
        pointsRequired: requiredPoints
      },
      calculation: calculatedOutcome,
      warnings,
      metadata: {
        processingTime: Date.now(),
        fallbackMode: true,
        aiSkipped: true
      }
    });

  } catch (error) {
    console.error('Client-side validation error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
