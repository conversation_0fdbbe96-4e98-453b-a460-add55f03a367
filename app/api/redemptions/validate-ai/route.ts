import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createServiceRoleClient } from '@/lib/supabase/server';
import { aiValidationService, type Member, type Reward } from '@/lib/ai-redemption-service';

// Request validation schema
const aiValidationRequestSchema = z.object({
  memberId: z.string().uuid(),
  rewardId: z.string().uuid(),
  receiptTotal: z.number().positive().optional(),
  receiptData: z.object({
    total_amount: z.number().positive(),
    business_name: z.string().optional(),
    receipt_date: z.string().optional(),
    service_description: z.string().optional(),
    financial_system_number: z.string().optional(),
  }).optional(),
});

/**
 * POST /api/redemptions/validate-ai
 * AI-powered validation for reward redemption eligibility
 */
export async function POST(request: NextRequest) {
  try {
    console.log('AI validation endpoint called');

    const body = await request.json();
    console.log('Request body:', body);

    // Validate request body
    const validationResult = aiValidationRequestSchema.safeParse(body);
    if (!validationResult.success) {
      console.log('Validation failed:', validationResult.error);
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { memberId, rewardId, receiptTotal, receiptData } = validationResult.data;
    console.log('Validated data:', { memberId, rewardId, receiptTotal });

    // Get Supabase client - using service role to bypass RLS
    console.log('Creating Supabase service role client for database access');
    const supabase = createServiceRoleClient();
    console.log('Supabase client created successfully');

    // Fetch member data directly from loyalty_members table
    console.log('Fetching member with ID:', memberId);
    const { data: memberData, error: memberError } = await supabase
      .from('loyalty_members')
      .select('*')
      .eq('id', memberId)
      .single();

    if (memberError) {
      console.error('Member lookup error:', memberError);
      if (memberError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Member not found', memberId },
          { status: 404 }
        );
      }
      return NextResponse.json(
        { error: `Member lookup failed: ${memberError.message}` },
        { status: 500 }
      );
    }

    if (!memberData) {
      console.log('Member not found in database');
      return NextResponse.json(
        { error: 'Member not found', memberId },
        { status: 404 }
      );
    }

    console.log('Found member:', memberData);

    // Fetch reward data
    console.log('Fetching reward with ID:', rewardId);

    const { data: rewardData, error: rewardError } = await supabase
      .from('rewards')
      .select('id, title, description, points_required, reward_value_type, reward_value, is_active, expiration_date')
      .eq('id', rewardId)
      .single();

    if (rewardError) {
      console.error('Reward lookup error:', rewardError);
      return NextResponse.json(
        { error: `Reward lookup failed: ${rewardError.message}` },
        { status: 500 }
      );
    }

    if (!rewardData) {
      console.log('Reward not found in database');
      return NextResponse.json(
        { error: 'Reward not found', rewardId },
        { status: 404 }
      );
    }

    console.log('Found reward:', rewardData);

    // Transform data to service types with flexible field mapping
    const member: Member = {
      id: memberData.id,
      name: memberData.name || memberData.display_name || memberData.full_name || 'Unknown',
      loyalty_tier: memberData.loyalty_tier || memberData.tier || 'BRONZE',
      available_points: memberData.available_points ||
                       (memberData.lifetime_points - memberData.redeemed_points - memberData.expired_points) ||
                       memberData.points ||
                       memberData.current_points || 0,
      lifetime_points: memberData.lifetime_points || memberData.total_points || memberData.points_earned || 0,
      redeemed_points: memberData.redeemed_points || memberData.points_redeemed || 0,
      expired_points: memberData.expired_points || memberData.points_expired || 0,
    };

    console.log('Transformed member data:', member);

    const reward: Reward = {
      id: rewardData.id,
      title: rewardData.title,
      description: rewardData.description,
      points_required: rewardData.points_required,
      reward_value_type: rewardData.reward_value_type,
      reward_value: rewardData.reward_value,
      is_active: rewardData.is_active,
      expiration_date: rewardData.expiration_date,
    };

    // Perform AI validation
    const eligibility = await aiValidationService.validateMemberEligibility(
      member,
      reward,
      receiptTotal
    );

    // Calculate outcome if eligible and receipt total provided
    let calculatedOutcome = null;
    if (eligibility.eligible && (receiptTotal || receiptData?.total_amount)) {
      const amount = receiptTotal || receiptData!.total_amount;
      calculatedOutcome = await aiValidationService.calculateRewardOutcome(
        member,
        reward,
        amount
      );
    }

    // Get alternative recommendations if not eligible or for comparison
    const { data: allRewards } = await supabase
      .from('rewards')
      .select('id, title, description, points_required, reward_value_type, reward_value, is_active, expiration_date')
      .eq('is_active', true)
      .neq('id', rewardId);

    const availableRewards: Reward[] = (allRewards || []).map((r: Record<string, unknown>) => ({
      id: r.id as string,
      title: r.title as string,
      description: r.description as string,
      points_required: r.points_required as number,
      reward_value_type: r.reward_value_type as 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SERVICE' | 'POINTS_BONUS' | 'PRODUCT_GIFT',
      reward_value: r.reward_value as number,
      is_active: r.is_active as boolean,
      expiration_date: r.expiration_date as string | null,
    }));

    const recommendations = await aiValidationService.recommendAlternatives(
      member,
      availableRewards,
      receiptTotal || receiptData?.total_amount,
      rewardId
    );

    // Enhanced receipt analysis if receipt data provided
    let receiptAnalysis = null;
    if (receiptData) {
      receiptAnalysis = await aiValidationService.analyzeReceipt(
        receiptData,
        member,
        [reward, ...availableRewards]
      );
    }

    // Generate AI-powered transaction description
    let generatedDescription = null;
    if (eligibility.eligible) {
      try {
        generatedDescription = await aiValidationService.generateTransactionDescription(
          reward,
          receiptData,
          member
        );
        console.log('Generated description:', generatedDescription);
      } catch (error) {
        console.error('Error generating description:', error);
        // Continue without description if generation fails
      }
    }

    // Build qualification summary
    const qualification = {
      memberId: member.id,
      currentTier: member.loyalty_tier,
      availablePoints: member.available_points,
      lifetimePoints: member.lifetime_points,
      qualifiedRewards: [eligibility],
      recommendations,
    };

    // Generate warnings
    const warnings = [];
    if (!eligibility.eligible) {
      if (eligibility.missingPoints) {
        warnings.push({
          type: 'insufficient_points',
          message: `You need ${eligibility.missingPoints} more points for this reward`,
          severity: 'error',
        });
      }
      if (eligibility.tierRequirement) {
        warnings.push({
          type: 'tier_requirement',
          message: `This reward requires ${eligibility.tierRequirement} tier`,
          severity: 'warning',
        });
      }
    }

    // Add tier progression insights
    if (member.available_points > reward.points_required) {
      const nextTierInfo = getNextTierInfo(member.lifetime_points);
      if (nextTierInfo) {
        warnings.push({
          type: 'tier_progress',
          message: `You're ${nextTierInfo.pointsToNext} points away from ${nextTierInfo.nextTier} tier`,
          severity: 'info',
        });
      }
    }

    return NextResponse.json({
      eligible: eligibility.eligible,
      qualification,
      calculation: calculatedOutcome,
      receiptAnalysis,
      transactionDescription: generatedDescription,
      aiInsights: {
        confidence: eligibility.confidence,
        reasoning: eligibility.reasons,
        alternatives: recommendations,
      },
      warnings,
      metadata: {
        processingTime: Date.now(),
        aiProvider: 'gemini-1.5-flash',
        fallbackUsed: eligibility.confidence < 0.8,
      },
    });

  } catch (error) {
    console.error('AI validation error:', error);
    if (error instanceof Error) {
      console.error('Error stack:', error.stack);
    }
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function to get next tier information
 */
function getNextTierInfo(lifetimePoints: number) {
  const tiers = [
    { name: 'BRONZE', min: 0 },
    { name: 'SILVER', min: 1000 },
    { name: 'GOLD', min: 5000 },
    { name: 'PLATINUM', min: 10000 },
  ];

  const nextTier = tiers.find(t => t.min > lifetimePoints);
  if (!nextTier) return null;

  return {
    nextTier: nextTier.name,
    pointsToNext: nextTier.min - lifetimePoints,
  };
}
