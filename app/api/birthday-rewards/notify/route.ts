import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { sendTelegramMessage } from '@/lib/telegram'

/**
 * POST /api/birthday-rewards/notify
 * Sends notifications to members who are eligible for birthday rewards
 * Requires company_id
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json()
    const { companyId } = body

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Use service role client to bypass RLS
    const supabase = getServiceRoleClient()

    // 1. Find all members who are currently birthday eligible
    const { data: eligibleMembers, error: eligibleError } = await supabase.rpc(
      'get_birthday_eligible_members',
      { p_company_id: companyId }
    )

    if (eligibleError) {
      console.error('Error finding birthday eligible members:', eligibleError)
      return NextResponse.json(
        { error: 'Failed to find birthday eligible members' },
        { status: 500 }
      )
    }

    // If no eligible members, return early
    if (!eligibleMembers || eligibleMembers.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No birthday eligible members found',
        notified: 0
      })
    }

    // 2. Find birthday rewards for this company
    const { data: birthdayRewards, error: rewardsError } = await supabase
      .from('rewards')
      .select('id, title, description, points_required')
      .eq('company_id', companyId)
      .eq('reward_type', 'BIRTHDAY')
      .eq('is_active', true)
      .gte('expiration_date', new Date().toISOString())

    if (rewardsError) {
      console.error('Error finding birthday rewards:', rewardsError)
      return NextResponse.json(
        { error: 'Failed to find birthday rewards' },
        { status: 500 }
      )
    }

    // If no active birthday rewards, return early
    if (!birthdayRewards || birthdayRewards.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No active birthday rewards found',
        notified: 0
      })
    }

    // 3. Send notifications to eligible members with telegram_chat_id
    let notifiedCount = 0
    const notificationResults = []

    for (const member of eligibleMembers) {
      // Skip members without telegram_chat_id
      if (!member.telegram_chat_id) continue

      try {
        // Prepare personalized message with available rewards
        const rewardsText = birthdayRewards
          .map(reward => `- ${reward.title} (${reward.points_required} points)`)
          .join('\n')

        const message = `🎂 *Happy Birthday ${member.name}!* 🎉\n\n` +
          `You're eligible for special birthday rewards:\n\n${rewardsText}\n\n` +
          `Visit our app or store to redeem these special offers! Your birthday rewards are available for 7 days before and after your birthday.`

        // Send message via Telegram
        const sent = await sendTelegramMessage(
          member.telegram_chat_id,
          message,
          { parse_mode: 'Markdown' }
        )

        if (sent) {
          notifiedCount++
          notificationResults.push({
            member_id: member.id,
            success: true
          })
        } else {
          notificationResults.push({
            member_id: member.id,
            success: false,
            error: 'Failed to send Telegram message'
          })
        }
      } catch (error) {
        console.error(`Error sending notification to member ${member.id}:`, error)
        notificationResults.push({
          member_id: member.id,
          success: false,
          error: String(error)
        })
      }
    }

    // 4. Return results
    return NextResponse.json({
      success: true,
      eligible_members: eligibleMembers.length,
      notified: notifiedCount,
      results: notificationResults
    })
  } catch (error) {
    console.error('Unexpected error in birthday notification API:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
