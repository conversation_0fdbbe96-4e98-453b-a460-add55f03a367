import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

/**
 * GET /api/birthday-rewards/eligibility
 * Checks if a member is eligible for birthday rewards
 * Requires memberId and companyId as query parameters
 */
export async function GET(request: NextRequest) {
  try {
    // Parse URL to get query parameters
    const { searchParams } = new URL(request.url)
    const memberId = searchParams.get('memberId')
    const companyId = searchParams.get('companyId')

    if (!memberId || !companyId) {
      return NextResponse.json(
        { error: 'Member ID and Company ID are required' },
        { status: 400 }
      )
    }

    // Use service role client to bypass RLS
    const supabase = getServiceRoleClient()

    // Check if member belongs to the company
    const { data: member, error: memberError } = await supabase
      .from('loyalty_members')
      .select('id')
      .eq('id', memberId)
      .eq('company_id', companyId)
      .single()

    if (memberError || !member) {
      return NextResponse.json(
        { error: 'Member not found or does not belong to this company' },
        { status: 404 }
      )
    }

    // Check if member is birthday eligible using the SQL function
    const { data: isEligible, error: eligibilityError } = await supabase
      .rpc('is_member_birthday_eligible', { member_id: memberId })

    if (eligibilityError) {
      console.error('Error checking birthday eligibility:', eligibilityError)
      return NextResponse.json(
        { error: 'Failed to check birthday eligibility' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      isEligible: !!isEligible,
      memberId
    })
  } catch (error) {
    console.error('Unexpected error in birthday eligibility API:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
