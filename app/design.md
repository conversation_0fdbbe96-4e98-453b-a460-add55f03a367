1. Page Structure & Grid System

Base Layout:

1440px max-width container
24px grid gap
Responsive 12-column grid
Layout splits:
Sidebar: 250px fixed width
Main content: Remaining space
Top nav: 64px height
2. Top Navigation Details

Height: 64pxComponents (left to right):

Logo section (250px width):
LoyaltyHub logo (32px height)
Tagline Premium Loyalty Management
Search bar (400px width):
Search icon in gold
Placeholder: Search members, rewards...
Right section:
Language selector
Notification bell with counter
Profile section:
40px circular avatar
Name and role
Dropdown arrow
3. Sidebar Navigation Specifications

Width: 250px

Sections:

User business info (80px height):
Business logo (40px)
Business name
Subscription type in gold badge
Main navigation:
Each item height: 48px
Icon size: 20px
Left padding: 24px
Active indicator: 3px purple line
Bottom section:
Help center link
Support contact
Collapse sidebar button
4. Dashboard Main Content

Header Section (88px height):

Welcome message
Date/Time
Quick action buttons:
+ New Reward
Export Data
Settings
KPI Cards (160px height):

4 cards layout
Each card:
Icon (32px)
Metric value (32px font)
Label text (14px font)
Percentage change
Mini graph
5. Charts & Analytics

Growth Chart Section:

Height: 400px
Filters:
Time period dropdown
Metrics selector
Export button
Chart area:
Y-axis: Members/Points
X-axis: Last 6 months
Interactive tooltips
Legend
6. Recent Transactions Table

Table Specifications:

Full width
Headers:
Member Name (25%)
Date (20%)
Points (15%)
Transaction Type (20%)
Status (10%)
Actions (10%)
Row height: 56px
Pagination:
10 items per page
Page controls
Items per page selector
7. Rewards Management Grid

Grid Layout:

3 columns
Card dimensions: 280px × 320px
Each card contains:

Reward image (160px height)
Title (max 2 lines)
Points value
Tier badge
Validity period
Quick action buttons
Usage statistics
8. Color Implementation

Primary Colors:

Purple (#8A2BE2):
Primary buttons
Active states
Important text
Gold (#D4AF37):
Accents
Icons
Borders
Special indicatorsBackground Hierarchy:
Main background: White (#FFFFFF)
Card background: White with gold tint
Active states: Deep purple (#6B24B2)
Hover states: Light gold (#F2E4BB)
9. Typography System

Font Families:

Headings: Poppins
Body: Inter
Monospace: Roboto Mono (for numbers)
Size Scale:

H1: 32px/40px
H2: 24px/32px
H3: 20px/28px
Body: 16px/24px
Small: 14px/20px
Tiny: 12px/16px
10. Component Library

Buttons:

Primary: Purple background, white text
Secondary: Gold border, purple text
Text: No background, purple text
Sizes: Small (32px), Medium (40px), Large (48px)
Cards:

Default padding: 24px
Border radius: 8px
Shadow: 0 2px 8px rgba(212, 175, 55, 0.1)
Form Elements:

Input height: 40px
Dropdown height: 40px
Checkbox size: 20px
Radio size: 20px
11. Responsive Breakpoints

Desktop: 1440px
Laptop: 1200px
Tablet: 768px
Mobile: 375px
12. Animation Specifications

Transitions:

Duration: 0.3s
Easing: cubic-bezier( 0.4, 0, 0.2, 1)
Hover states: 0.2s
Page transitions: 0.4s
Loading States:

Skeleton screens
Progress indicators
Smooth fade-ins

<!-- REACT/SCHADCN CODE -->

// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.

import React, { useState } from 'react';
import { Bell, ChevronDown, ChevronRight, ChevronUp, Search, Settings, Plus, Download, HelpCircle, MessageSquare, Menu, X } from 'lucide-react';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import * as echarts from 'echarts';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';

const App: React.FC = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('all');

  React.useEffect(() => {
    // Initialize member growth chart
    const chartDom = document.getElementById('memberGrowthChart');
    if (chartDom) {
      const myChart = echarts.init(chartDom);
      const option = {
        animation: false,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['Total Members', 'Active Points'],
          right: '10%',
          textStyle: {
            color: '#333'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'],
          axisLine: {
            lineStyle: {
              color: '#D4AF37'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: 'Members',
            axisLine: {
              lineStyle: {
                color: '#8A2BE2'
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(138, 43, 226, 0.1)'
              }
            }
          },
          {
            type: 'value',
            name: 'Points',
            axisLine: {
              lineStyle: {
                color: '#D4AF37'
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: 'Total Members',
            type: 'line',
            smooth: true,
            data: [120, 132, 145, 160, 178, 195],
            lineStyle: {
              color: '#8A2BE2',
              width: 3
            },
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#8A2BE2'
            }
          },
          {
            name: 'Active Points',
            type: 'bar',
            yAxisIndex: 1,
            data: [2500, 3000, 3600, 4200, 4800, 5500],
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#D4AF37' },
                { offset: 1, color: 'rgba(212, 175, 55, 0.5)' }
              ])
            },
            barWidth: '40%'
          }
        ]
      };
      myChart.setOption(option);

      window.addEventListener('resize', () => {
        myChart.resize();
      });

      return () => {
        window.removeEventListener('resize', () => {
          myChart.resize();
        });
        myChart.dispose();
      };
    }
  }, []);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  // Sample data for KPI cards
  const kpiData = [
    {
      title: 'Total Members',
      value: '1,248',
      change: '+12.5%',
      icon: 'fa-users',
      color: '#8A2BE2'
    },
    {
      title: 'Active Points',
      value: '458,320',
      change: '+8.3%',
      icon: 'fa-coins',
      color: '#D4AF37'
    },
    {
      title: 'Points Redeemed',
      value: '124,580',
      change: '+15.2%',
      icon: 'fa-exchange-alt',
      color: '#8A2BE2'
    },
    {
      title: 'Redemption Rate',
      value: '27.2%',
      change: '+2.8%',
      icon: 'fa-chart-line',
      color: '#D4AF37'
    }
  ];

  // Sample data for transactions
  const transactions = [
    {
      id: 1,
      member: 'Sarah Johnson',
      date: 'Apr 22, 2025',
      points: '+250',
      type: 'Purchase',
      status: 'Completed'
    },
    {
      id: 2,
      member: 'Michael Tesfaye',
      date: 'Apr 21, 2025',
      points: '-500',
      type: 'Redemption',
      status: 'Completed'
    },
    {
      id: 3,
      member: 'Abeba Haile',
      date: 'Apr 21, 2025',
      points: '+180',
      type: 'Purchase',
      status: 'Completed'
    },
    {
      id: 4,
      member: 'David Wilson',
      date: 'Apr 20, 2025',
      points: '+320',
      type: 'Referral',
      status: 'Completed'
    },
    {
      id: 5,
      member: 'Tigist Bekele',
      date: 'Apr 20, 2025',
      points: '-750',
      type: 'Redemption',
      status: 'Completed'
    }
  ];

  // Sample data for rewards
  const rewards = [
    {
      id: 1,
      title: 'Premium Facial Treatment',
      points: 1500,
      tier: 'Silver',
      image: 'https://readdy.ai/api/search-image?query=luxury%20facial%20treatment%20in%20spa%20setting%20with%20gold%20accents%2C%20premium%20skincare%20products%20arranged%20elegantly%2C%20soft%20lighting%2C%20marble%20countertop%2C%20high-end%20beauty%20salon%20atmosphere%2C%20photorealistic%2C%20professional%20photography&width=280&height=160&seq=1&orientation=landscape',
      validity: 'Valid until Jun 30, 2025',
      redemptions: 48
    },
    {
      id: 2,
      title: '30% Off Hair Styling',
      points: 2500,
      tier: 'Gold',
      image: 'https://readdy.ai/api/search-image?query=elegant%20hair%20salon%20with%20professional%20styling%20tools%2C%20luxury%20salon%20chair%2C%20gold%20and%20purple%20decor%20elements%2C%20soft%20lighting%2C%20high-end%20beauty%20salon%20atmosphere%2C%20photorealistic%2C%20professional%20photography&width=280&height=160&seq=2&orientation=landscape',
      validity: 'Valid until May 31, 2025',
      redemptions: 36
    },
    {
      id: 3,
      title: 'Deluxe Manicure & Pedicure',
      points: 2000,
      tier: 'Silver',
      image: 'https://readdy.ai/api/search-image?query=luxury%20nail%20salon%20setting%20with%20manicure%20and%20pedicure%20station%2C%20elegant%20nail%20polish%20bottles%20in%20gold%20and%20purple%20shades%2C%20marble%20surfaces%2C%20soft%20lighting%2C%20high-end%20beauty%20salon%20atmosphere%2C%20photorealistic%2C%20professional%20photography&width=280&height=160&seq=3&orientation=landscape',
      validity: 'Valid until Jul 15, 2025',
      redemptions: 52
    },
    {
      id: 4,
      title: 'Full Body Massage',
      points: 3500,
      tier: 'Platinum',
      image: 'https://readdy.ai/api/search-image?query=luxury%20massage%20room%20with%20elegant%20massage%20table%2C%20essential%20oils%2C%20candles%20with%20gold%20accents%2C%20dim%20lighting%2C%20purple%20towels%2C%20high-end%20spa%20atmosphere%2C%20photorealistic%2C%20professional%20photography&width=280&height=160&seq=4&orientation=landscape',
      validity: 'Valid until Jun 30, 2025',
      redemptions: 29
    },
    {
      id: 5,
      title: '50% Off Product Bundle',
      points: 4000,
      tier: 'Platinum',
      image: 'https://readdy.ai/api/search-image?query=luxury%20beauty%20products%20arranged%20elegantly%20in%20gift%20box%20with%20gold%20and%20purple%20packaging%2C%20premium%20skincare%20bottles%2C%20high-end%20cosmetics%20on%20marble%20surface%2C%20soft%20lighting%2C%20photorealistic%2C%20professional%20photography&width=280&height=160&seq=5&orientation=landscape',
      validity: 'Valid until May 15, 2025',
      redemptions: 18
    },
    {
      id: 6,
      title: 'Free Hair Coloring',
      points: 3000,
      tier: 'Gold',
      image: 'https://readdy.ai/api/search-image?query=professional%20hair%20color%20products%20with%20gold%20and%20purple%20packaging%2C%20elegant%20hair%20salon%20setting%2C%20color%20swatches%2C%20professional%20styling%20tools%2C%20high-end%20beauty%20salon%20atmosphere%2C%20photorealistic%2C%20professional%20photography&width=280&height=160&seq=6&orientation=landscape',
      validity: 'Valid until Jul 31, 2025',
      redemptions: 24
    }
  ];

  return (
    <div className="flex h-screen bg-gray-50 text-gray-800 font-sans">
      {/* Sidebar */}
      <div
        className={`bg-white border-r border-gray-200 transition-all duration-300 flex flex-col ${
          sidebarCollapsed ? 'w-20' : 'w-64'
        }`}
      >
        {/* Logo */}
        <div className="h-16 flex items-center justify-between px-4 border-b border-gray-100">
          {!sidebarCollapsed && (
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-md bg-gradient-to-br from-[#8A2BE2] to-[#D4AF37] flex items-center justify-center text-white font-bold">
                LH
              </div>
              <span className="ml-2 font-semibold text-lg">LoyaltyHub</span>
            </div>
          )}
          {sidebarCollapsed && (
            <div className="h-8 w-8 mx-auto rounded-md bg-gradient-to-br from-[#8A2BE2] to-[#D4AF37] flex items-center justify-center text-white font-bold">
              LH
            </div>
          )}
          <button
            onClick={toggleSidebar}
            className="text-gray-500 hover:text-gray-700 cursor-pointer !rounded-button whitespace-nowrap"
          >
            {sidebarCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
          </button>
        </div>

        {/* Business Info */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center">
            <Avatar className="h-10 w-10 border-2 border-[#D4AF37]">
              <AvatarImage src="https://readdy.ai/api/search-image?query=elegant%20beauty%20salon%20logo%20with%20minimalist%20design%2C%20gold%20and%20purple%20color%20scheme%2C%20premium%20and%20luxurious%20feel%2C%20clean%20background%2C%20professional%20branding&width=40&height=40&seq=7&orientation=squarish" />
              <AvatarFallback className="bg-[#8A2BE2] text-white">BS</AvatarFallback>
            </Avatar>
            {!sidebarCollapsed && (
              <div className="ml-3">
                <p className="font-medium text-sm">Beauty Serenity</p>
                <Badge className="bg-[#D4AF37] text-white text-xs hover:bg-[#c39c31]">Premium</Badge>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="px-2 space-y-1">
            {[
              { name: 'Dashboard', icon: 'fa-tachometer-alt', active: true },
              { name: 'Members', icon: 'fa-users', active: false },
              { name: 'Transactions', icon: 'fa-exchange-alt', active: false },
              { name: 'Rewards', icon: 'fa-gift', active: false },
              { name: 'Reports', icon: 'fa-chart-bar', active: false },
              { name: 'Settings', icon: 'fa-cog', active: false }
            ].map((item) => (
              <a
                key={item.name}
                href="#"
                className={`flex items-center ${
                  sidebarCollapsed ? 'justify-center' : 'px-4'
                } py-3 text-sm font-medium rounded-md cursor-pointer !rounded-button whitespace-nowrap ${
                  item.active
                    ? 'text-[#8A2BE2] bg-purple-50 border-l-4 border-[#8A2BE2]'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-[#8A2BE2]'
                }`}
              >
                <i className={`fas ${item.icon} ${sidebarCollapsed ? 'text-xl' : 'mr-3'}`}></i>
                {!sidebarCollapsed && <span>{item.name}</span>}
              </a>
            ))}
          </nav>
        </div>

        {/* Bottom Section */}
        <div className="p-4 border-t border-gray-100">
          {!sidebarCollapsed && (
            <>
              <a href="#" className="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-[#8A2BE2] cursor-pointer !rounded-button whitespace-nowrap">
                <i className="fas fa-question-circle mr-3"></i>
                <span>Help Center</span>
              </a>
              <a href="#" className="flex items-center px-4 py-2 text-sm text-gray-600 hover:text-[#8A2BE2] cursor-pointer !rounded-button whitespace-nowrap">
                <i className="fas fa-headset mr-3"></i>
                <span>Support</span>
              </a>
            </>
          )}
          {sidebarCollapsed && (
            <div className="flex flex-col items-center space-y-4">
              <button className="text-gray-600 hover:text-[#8A2BE2] cursor-pointer !rounded-button whitespace-nowrap">
                <i className="fas fa-question-circle text-xl"></i>
              </button>
              <button className="text-gray-600 hover:text-[#8A2BE2] cursor-pointer !rounded-button whitespace-nowrap">
                <i className="fas fa-headset text-xl"></i>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <header className="bg-white border-b border-gray-200 h-16 flex items-center justify-between px-6 z-10">
          <div className="flex items-center">
            <div className="relative w-96">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="h-5 w-5 text-[#D4AF37]" />
              </div>
              <Input
                type="search"
                placeholder="Search members, rewards..."
                className="pl-10 h-10 border-gray-200 focus:border-[#8A2BE2] focus:ring-[#8A2BE2] text-sm"
              />
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center text-gray-600 cursor-pointer !rounded-button whitespace-nowrap">
                  <i className="fas fa-globe mr-2 text-[#D4AF37]"></i>
                  <span>English</span>
                  <ChevronDown size={16} className="ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>English</DropdownMenuItem>
                <DropdownMenuItem>Amharic</DropdownMenuItem>
                <DropdownMenuItem>French</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="relative">
              <button className="text-gray-600 hover:text-gray-900 cursor-pointer !rounded-button whitespace-nowrap">
                <div className="relative">
                  <Bell size={20} />
                  <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-[#8A2BE2] text-white text-xs flex items-center justify-center">3</span>
                </div>
              </button>
            </div>

            <div className="border-l border-gray-200 h-8"></div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="flex items-center cursor-pointer !rounded-button whitespace-nowrap">
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage src="https://readdy.ai/api/search-image?query=professional%20female%20beauty%20salon%20owner%20portrait%2C%20elegant%20business%20attire%2C%20confident%20expression%2C%20neutral%20studio%20background%2C%20high%20quality%20professional%20headshot&width=40&height=40&seq=8&orientation=squarish" />
                    <AvatarFallback className="bg-[#8A2BE2] text-white">HG</AvatarFallback>
                  </Avatar>
                  <div className="text-left mr-2">
                    <p className="text-sm font-medium">Helen Girma</p>
                    <p className="text-xs text-gray-500">Admin</p>
                  </div>
                  <ChevronDown size={16} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Account Settings</DropdownMenuItem>
                <DropdownMenuItem>Billing</DropdownMenuItem>
                <DropdownMenuItem className="text-red-600">Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-y-auto bg-gray-50 p-6">
          {/* Header Section */}
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
              <p className="text-gray-500">Tuesday, April 22, 2025</p>
            </div>
            <div className="flex space-x-3">
              <Button className="bg-[#8A2BE2] hover:bg-[#7B24CC] text-white cursor-pointer !rounded-button whitespace-nowrap">
                <Plus size={16} className="mr-2" />
                New Reward
              </Button>
              <Button variant="outline" className="border-[#D4AF37] text-[#D4AF37] hover:bg-[#FDF8E8] hover:text-[#C39C31] cursor-pointer !rounded-button whitespace-nowrap">
                <Download size={16} className="mr-2" />
                Export Data
              </Button>
              <Button variant="ghost" className="text-gray-600 hover:text-gray-900 cursor-pointer !rounded-button whitespace-nowrap">
                <Settings size={16} />
              </Button>
            </div>
          </div>

          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {kpiData.map((kpi, index) => (
              <Card key={index} className="p-6 border border-gray-200 hover:border-[#D4AF37] transition-all">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-gray-500 text-sm">{kpi.title}</p>
                    <h3 className="text-3xl font-bold mt-2" style={{ color: kpi.color }}>{kpi.value}</h3>
                    <div className="flex items-center mt-2">
                      <span className="text-green-500 text-sm flex items-center">
                        <i className="fas fa-arrow-up mr-1"></i>
                        {kpi.change}
                      </span>
                      <span className="text-gray-400 text-xs ml-2">vs last month</span>
                    </div>
                  </div>
                  <div className={`h-12 w-12 rounded-full flex items-center justify-center`} style={{ backgroundColor: `${kpi.color}15` }}>
                    <i className={`fas ${kpi.icon} text-xl`} style={{ color: kpi.color }}></i>
                  </div>
                </div>
                <div className="mt-4 h-10">
                  {/* Mini graph would go here */}
                  <div className="h-full w-full bg-gray-100 rounded-sm opacity-50"></div>
                </div>
              </Card>
            ))}
          </div>

          {/* Charts & Tables Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Member Growth Chart */}
            <Card className="col-span-2 p-6 border border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Member Growth & Activity</h2>
                <div className="flex items-center space-x-3">
                  <Select defaultValue="6months">
                    <SelectTrigger className="w-[180px] h-9 text-sm">
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30days">Last 30 days</SelectItem>
                      <SelectItem value="3months">Last 3 months</SelectItem>
                      <SelectItem value="6months">Last 6 months</SelectItem>
                      <SelectItem value="year">Last year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div id="memberGrowthChart" className="h-[350px] w-full"></div>
            </Card>

            {/* Top Rewards */}
            <Card className="p-6 border border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Top Rewards</h2>
                <Button variant="ghost" className="text-[#8A2BE2] hover:text-[#7B24CC] hover:bg-purple-50 text-sm cursor-pointer !rounded-button whitespace-nowrap">
                  View All
                </Button>
              </div>
              <div className="space-y-4">
                {rewards.slice(0, 4).map((reward, index) => (
                  <div key={index} className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-all">
                    <div className="h-12 w-12 rounded-md overflow-hidden mr-4 flex-shrink-0">
                      <img
                        src={reward.image}
                        alt={reward.title}
                        className="h-full w-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{reward.title}</p>
                      <div className="flex items-center mt-1">
                        <Badge className={`mr-2 ${
                          reward.tier === 'Platinum'
                            ? 'bg-[#D4AF37] text-white'
                            : reward.tier === 'Gold'
                              ? 'bg-amber-500 text-white'
                              : 'bg-gray-200 text-gray-700'
                        }`}>
                          {reward.tier}
                        </Badge>
                        <p className="text-xs text-gray-500">{reward.redemptions} redeemed</p>
                      </div>
                    </div>
                    <div className="flex-shrink-0 text-right">
                      <p className="text-[#8A2BE2] font-bold">{reward.points}</p>
                      <p className="text-xs text-gray-500">points</p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Recent Transactions */}
          <Card className="p-6 border border-gray-200 mb-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-semibold">Recent Transactions</h2>
              <div className="flex items-center space-x-3">
                <Input
                  type="text"
                  placeholder="Search transactions..."
                  className="h-9 text-sm w-64"
                />
                <Button variant="outline" className="h-9 text-sm cursor-pointer !rounded-button whitespace-nowrap">
                  <i className="fas fa-filter mr-2 text-[#D4AF37]"></i>
                  Filter
                </Button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Member</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Points</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.member}</TableCell>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell className={transaction.points.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
                        {transaction.points}
                      </TableCell>
                      <TableCell>{transaction.type}</TableCell>
                      <TableCell>
                        <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                          {transaction.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" className="h-8 px-2 cursor-pointer !rounded-button whitespace-nowrap">
                          <i className="fas fa-ellipsis-h text-gray-500"></i>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            <div className="flex items-center justify-between mt-6">
              <p className="text-sm text-gray-500">Showing 5 of 24 transactions</p>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" className="h-8 cursor-pointer !rounded-button whitespace-nowrap">
                  Previous
                </Button>
                <Button variant="outline" size="sm" className="h-8 bg-[#8A2BE2] text-white border-[#8A2BE2] hover:bg-[#7B24CC] cursor-pointer !rounded-button whitespace-nowrap">
                  1
                </Button>
                <Button variant="outline" size="sm" className="h-8 cursor-pointer !rounded-button whitespace-nowrap">
                  2
                </Button>
                <Button variant="outline" size="sm" className="h-8 cursor-pointer !rounded-button whitespace-nowrap">
                  3
                </Button>
                <Button variant="outline" size="sm" className="h-8 cursor-pointer !rounded-button whitespace-nowrap">
                  Next
                </Button>
              </div>
            </div>
          </Card>

          {/* Rewards Management */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold">Rewards Management</h2>
              <div className="flex items-center space-x-3">
                <Tabs defaultValue="all" className="w-[400px]">
                  <TabsList className="grid grid-cols-4">
                    <TabsTrigger value="all" onClick={() => setActiveTab('all')}>All</TabsTrigger>
                    <TabsTrigger value="silver" onClick={() => setActiveTab('silver')}>Silver</TabsTrigger>
                    <TabsTrigger value="gold" onClick={() => setActiveTab('gold')}>Gold</TabsTrigger>
                    <TabsTrigger value="platinum" onClick={() => setActiveTab('platinum')}>Platinum</TabsTrigger>
                  </TabsList>
                </Tabs>
                <Button className="bg-[#8A2BE2] hover:bg-[#7B24CC] text-white cursor-pointer !rounded-button whitespace-nowrap">
                  <Plus size={16} className="mr-2" />
                  Add Reward
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rewards
                .filter(reward => activeTab === 'all' || reward.tier.toLowerCase() === activeTab)
                .map((reward) => (
                <Card key={reward.id} className="overflow-hidden border border-gray-200 hover:border-[#D4AF37] hover:shadow-md transition-all">
                  <div className="h-40 overflow-hidden">
                    <img
                      src={reward.image}
                      alt={reward.title}
                      className="w-full h-full object-cover object-top"
                    />
                  </div>
                  <div className="p-5">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-lg font-semibold line-clamp-2">{reward.title}</h3>
                      <Badge className={`${
                        reward.tier === 'Platinum'
                          ? 'bg-[#D4AF37] text-white'
                          : reward.tier === 'Gold'
                            ? 'bg-amber-500 text-white'
                            : 'bg-gray-200 text-gray-700'
                      }`}>
                        {reward.tier}
                      </Badge>
                    </div>
                    <div className="flex items-center mb-4">
                      <div className="text-xl font-bold text-[#8A2BE2]">{reward.points} <span className="text-sm font-normal">points</span></div>
                    </div>
                    <p className="text-sm text-gray-500 mb-4">{reward.validity}</p>
                    <div className="flex items-center justify-between">
                      <div className="text-sm">
                        <span className="text-gray-500">Redemptions: </span>
                        <span className="font-medium">{reward.redemptions}</span>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm" className="h-8 text-[#8A2BE2] border-[#8A2BE2] hover:bg-purple-50 cursor-pointer !rounded-button whitespace-nowrap">
                          <i className="fas fa-edit mr-1"></i>
                          Edit
                        </Button>
                        <Button variant="ghost" size="sm" className="h-8 text-gray-600 hover:text-gray-900 cursor-pointer !rounded-button whitespace-nowrap">
                          <i className="fas fa-ellipsis-v"></i>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            <div className="flex justify-center mt-8">
              <Button variant="outline" className="text-[#8A2BE2] border-[#8A2BE2] hover:bg-purple-50 cursor-pointer !rounded-button whitespace-nowrap">
                View All Rewards
                <ChevronRight size={16} className="ml-2" />
              </Button>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

// ChevronLeft component since it was used but not imported
const ChevronLeft = ({ size = 24, ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <polyline points="15 18 9 12 15 6" />
  </svg>
);

export default App;

