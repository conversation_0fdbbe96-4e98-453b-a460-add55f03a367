'use client'

import { useEffect, useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { getSupabaseClient } from '@/lib/supabase'

export default function DebugAuthPage() {
  const { user, isLoading, session, isAuthenticated } = useRequireAuth()
  const [cookies, setCookies] = useState<string[]>([])
  const [sessionData, setSessionData] = useState<unknown>(null)

  useEffect(() => {
    // Check what cookies are available
    if (typeof window !== 'undefined') {
      const allCookies = document.cookie.split(';').map(c => c.trim())
      setCookies(allCookies)
    }

    // Check session directly
    const checkSession = async () => {
      try {
        const supabase = getSupabaseClient()
        const { data, error } = await supabase.auth.getSession()
        console.log('Direct session check:', data, error)
        setSessionData(data)
      } catch (error) {
        console.error('Session check error:', error)
      }
    }

    checkSession()
  }, [])

  const handleClearAll = () => {
    // Clear all browser storage
    localStorage.clear()
    sessionStorage.clear()

    // Clear all cookies
    document.cookie.split(";").forEach(function(c) {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });

    // Force reload
    window.location.reload()
  }

  const handleLogin = async () => {
    try {
      const supabase = getSupabaseClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'password123'  // You'll need to use the correct password
      })
      console.log('Login result:', data, error)

      // Force check cookies after login
      setTimeout(() => {
        const newCookies = document.cookie.split(';').map(c => c.trim())
        setCookies(newCookies)

        // Check session again
        supabase.auth.getSession().then(({ data, error }) => {
          console.log('Session after login:', data, error)
          setSessionData(data)
        })
      }, 1000)
    } catch (error) {
      console.error('Login error:', error)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Auth Debug Page</h1>

      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Auth Hook Status</h2>
          <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
          <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          <p><strong>User ID:</strong> {user?.id || 'None'}</p>
          <p><strong>User Email:</strong> {user?.email || 'None'}</p>
          <p><strong>Session:</strong> {session ? 'Present' : 'None'}</p>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Direct Session Check</h2>
          <pre className="bg-white p-2 rounded text-xs overflow-auto">
            {JSON.stringify(sessionData, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Browser Cookies</h2>
          <div className="space-y-1">
            {cookies.map((cookie, index) => (
              <p key={index} className="text-xs font-mono">{cookie}</p>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <button
            onClick={handleLogin}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Test Login
          </button>

          <button
            onClick={handleClearAll}
            className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 ml-2"
          >
            Clear All & Reload
          </button>
        </div>
      </div>
    </div>
  )
}
