'use client'

import React, { createContext, useContext, useState } from 'react';
import { format, subDays, subMonths } from 'date-fns';

export type DateRange = '7d' | '30d' | '90d' | '6m' | '1y' | 'custom';

interface DateRangeOption {
  label: string;
  value: DateRange;
}

export const DATE_RANGE_OPTIONS: DateRangeOption[] = [
  { label: 'Last 7 days', value: '7d' },
  { label: 'Last 30 days', value: '30d' },
  { label: 'Last 90 days', value: '90d' },
  { label: 'Last 6 months', value: '6m' },
  { label: 'Last year', value: '1y' },
  { label: 'Custom range', value: 'custom' },
];

interface ReportFilters {
  dateRange: DateRange;
  startDate: Date | null;
  endDate: Date | null;
  showComparison: boolean;
  comparisonType: 'previous' | 'year';
  memberTiers: string[];
  transactionTypes: string[];
}

interface ReportContextType {
  filters: ReportFilters;
  setDateRange: (range: DateRange) => void;
  setStartDate: (date: Date | null) => void;
  setEndDate: (date: Date | null) => void;
  setShowComparison: (show: boolean) => void;
  setComparisonType: (type: 'previous' | 'year') => void;
  setMemberTiers: (tiers: string[]) => void;
  setTransactionTypes: (types: string[]) => void;
  resetFilters: () => void;
  getStartDate: () => string;
  getEndDate: () => string;
  getPreviousStartDate: () => string;
  getPreviousEndDate: () => string;
}

const defaultFilters: ReportFilters = {
  dateRange: '30d',
  startDate: null,
  endDate: null,
  showComparison: false,
  comparisonType: 'previous',
  memberTiers: [],
  transactionTypes: [],
};

const ReportContext = createContext<ReportContextType | undefined>(undefined);

export function ReportProvider({ children }: { children: React.ReactNode }) {
  const [filters, setFilters] = useState<ReportFilters>({ ...defaultFilters });

  const setDateRange = (range: DateRange) => {
    setFilters(prev => ({ ...prev, dateRange: range }));
  };

  const setStartDate = (date: Date | null) => {
    setFilters(prev => ({ ...prev, startDate: date }));
  };

  const setEndDate = (date: Date | null) => {
    setFilters(prev => ({ ...prev, endDate: date }));
  };

  const setShowComparison = (show: boolean) => {
    setFilters(prev => ({ ...prev, showComparison: show }));
  };

  const setComparisonType = (type: 'previous' | 'year') => {
    setFilters(prev => ({ ...prev, comparisonType: type }));
  };

  const setMemberTiers = (tiers: string[]) => {
    setFilters(prev => ({ ...prev, memberTiers: tiers }));
  };

  const setTransactionTypes = (types: string[]) => {
    setFilters(prev => ({ ...prev, transactionTypes: types }));
  };

  const resetFilters = () => {
    setFilters({ ...defaultFilters });
  };

  // Helper function to calculate date range based on selected option
  const calculateDateRange = (): { start: Date; end: Date } => {
    const today = new Date();
    today.setHours(23, 59, 59, 999);

    const end = today;
    let start = today;

    switch (filters.dateRange) {
      case '7d':
        start = subDays(end, 7);
        break;
      case '30d':
        start = subDays(end, 30);
        break;
      case '90d':
        start = subDays(end, 90);
        break;
      case '6m':
        start = subMonths(end, 6);
        break;
      case '1y':
        start = subMonths(end, 12);
        break;
      case 'custom':
        if (filters.startDate && filters.endDate) {
          return { start: filters.startDate, end: filters.endDate };
        }
        // Default to 30d if custom range is not set
        start = subDays(end, 30);
        break;
    }

    start.setHours(0, 0, 0, 0);
    return { start, end };
  };

  // Get formatted start date for API
  const getStartDate = (): string => {
    const { start } = calculateDateRange();
    return format(start, 'yyyy-MM-dd');
  };

  // Get formatted end date for API
  const getEndDate = (): string => {
    const { end } = calculateDateRange();
    return format(end, 'yyyy-MM-dd');
  };

  // Get the previous period start date for comparison
  const getPreviousStartDate = (): string => {
    const { start, end } = calculateDateRange();

    if (filters.comparisonType === 'year') {
      // Same period last year
      const lastYearStart = subMonths(start, 12);
      return format(lastYearStart, 'yyyy-MM-dd');
    } else {
      // Previous period of same length
      const periodLength = end.getTime() - start.getTime();
      const prevEnd = new Date(start.getTime() - 1); // Day before current start
      const prevStart = new Date(prevEnd.getTime() - periodLength);
      return format(prevStart, 'yyyy-MM-dd');
    }
  };

  // Get the previous period end date for comparison
  const getPreviousEndDate = (): string => {
    const { start, end } = calculateDateRange();

    if (filters.comparisonType === 'year') {
      // Same period last year
      const lastYearEnd = subMonths(end, 12);
      return format(lastYearEnd, 'yyyy-MM-dd');
    } else {
      // Previous period of same length
      const prevEnd = new Date(start.getTime() - 1); // Day before current start
      return format(prevEnd, 'yyyy-MM-dd');
    }
  };

  return (
    <ReportContext.Provider
      value={{
        filters,
        setDateRange,
        setStartDate,
        setEndDate,
        setShowComparison,
        setComparisonType,
        setMemberTiers,
        setTransactionTypes,
        resetFilters,
        getStartDate,
        getEndDate,
        getPreviousStartDate,
        getPreviousEndDate,
      }}
    >
      {children}
    </ReportContext.Provider>
  );
}

export function useReportContext() {
  const context = useContext(ReportContext);
  if (context === undefined) {
    throw new Error('useReportContext must be used within a ReportProvider');
  }
  return context;
}