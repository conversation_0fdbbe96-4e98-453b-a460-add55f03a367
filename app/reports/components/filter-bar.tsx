'use client'

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Calendar,
  RefreshCcw,
  FileDown,
  ChevronDown,
  BarChart4,
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"; 
import { Calendar as CalendarComponent } from "@/components/ui/calendar"; 
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { format } from 'date-fns';
import { useReportContext, DATE_RANGE_OPTIONS, type DateRange } from './report-context';

interface FilterBarProps {
  onRefresh: () => void;
  onExport: () => void;
}

export function FilterBar({ onRefresh, onExport }: FilterBarProps) {
  const {
    filters,
    setDateRange,
    setStartDate,
    setEndDate,
    setShowComparison,
    setComparisonType
  } = useReportContext();

  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Format dates for display
  const formatDate = (date: Date | null): string => {
    if (!date) return '';
    return format(date, 'MMM dd, yyyy');
  };

  // Generate a human-readable date range description
  const getDateRangeText = () => {
    if (filters.dateRange === 'custom' && filters.startDate && filters.endDate) {
      return `${formatDate(filters.startDate)} - ${formatDate(filters.endDate)}`;
    }

    const option = DATE_RANGE_OPTIONS.find(opt => opt.value === filters.dateRange);
    return option ? option.label : 'Select a date range';
  };

  return (
    <Card className="p-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 flex gap-2">
          <Select
            value={filters.dateRange}
            onValueChange={(value) => setDateRange(value as DateRange)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              {DATE_RANGE_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {filters.dateRange === 'custom' && (
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[150px] justify-start">
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.startDate ? formatDate(filters.startDate) : 'Start date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={filters.startDate || undefined}
                    onSelect={(date: Date | null | undefined) => setStartDate(date || null)}
                    disabled={(date: Date) =>
                      filters.endDate ? date > filters.endDate : false
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[150px] justify-start">
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.endDate ? formatDate(filters.endDate) : 'End date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={filters.endDate || undefined}
                    onSelect={(date: Date | null | undefined) => setEndDate(date || null)}
                    disabled={(date: Date) =>
                      filters.startDate ? date < filters.startDate : false
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}

          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="ml-auto">
                <BarChart4 className="mr-2 h-4 w-4" />
                Filters
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="grid gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium leading-none">Compare with</h4>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={filters.showComparison}
                      onCheckedChange={setShowComparison}
                      id="show-comparison"
                    />
                    <Label htmlFor="show-comparison">Show comparison</Label>
                  </div>

                  {filters.showComparison && (
                    <Select
                      value={filters.comparisonType}
                      onValueChange={(value) => setComparisonType(value as 'previous' | 'year')}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select comparison type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="previous">Previous period</SelectItem>
                        <SelectItem value="year">Same period last year</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>

                {/* Additional filters can be added here based on report needs */}
              </div>
            </PopoverContent>
          </Popover>
        </div>

        <div className="flex gap-2">
          <Button variant="outline" onClick={onRefresh} size="icon">
            <RefreshCcw className="h-4 w-4" />
          </Button>
          <Button variant="outline" onClick={onExport} size="icon">
            <FileDown className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="mt-2 text-sm text-muted-foreground">
        Showing data for: <span className="font-medium">{getDateRangeText()}</span>
        {filters.showComparison && (
          <span> (comparing with {filters.comparisonType === 'previous' ? 'previous period' : 'same period last year'})</span>
        )}
      </div>
    </Card>
  );
}