'use client'

import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

interface ChartSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  isLoading?: boolean;
  actions?: React.ReactNode;
  downloadable?: boolean;
  onDownload?: () => void;
  className?: string;
}

export function ChartSection({
  title,
  description,
  children,
  isLoading = false,
  actions,
  downloadable = false,
  onDownload,
  className = '',
}: ChartSectionProps) {
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else {
      console.log('Downloading chart:', title);
      // Default download implementation could go here
      // e.g., convert canvas to image and trigger download
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {downloadable && (
              <Button variant="ghost" size="icon" onClick={handleDownload}>
                <Download className="h-4 w-4" />
              </Button>
            )}
            {actions}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            <Skeleton className="h-[350px] w-full rounded-md" />
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
}