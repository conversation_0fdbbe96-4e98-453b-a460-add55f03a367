'use client'

import React from 'react';
import { Card } from '@/components/ui/card';
import {
  ArrowDownIcon,
  ArrowUpIcon
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';

export interface KpiData {
  title: string;
  value: number | string;
  icon?: React.ReactNode;
  change?: number;
  changeLabel?: string;
  isLoading?: boolean;
  formatValue?: (value: number) => string;
}

interface KpiSummaryProps {
  metrics: KpiData[];
  showComparison?: boolean;
  className?: string;
}

export function KpiSummary({
  metrics,
  showComparison = false,
  className = '',
}: KpiSummaryProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {metrics.map((metric, index) => (
        <Card key={index} className="p-4">
          <div className="flex items-center gap-2 text-muted-foreground">
            {metric.icon && <div>{metric.icon}</div>}
            <h3 className="text-sm font-medium">{metric.title}</h3>
          </div>

          <div className="mt-2 mb-1">
            {metric.isLoading ? (
              <Skeleton className="h-9 w-24 mt-1 rounded-md" />
            ) : typeof metric.value === 'number' && metric.formatValue ? (
              <p className="text-3xl font-bold">
                {metric.formatValue(metric.value)}
              </p>
            ) : (
              <p className="text-3xl font-bold">{metric.value}</p>
            )}
          </div>

          {showComparison && metric.change !== undefined && !metric.isLoading && (
            <div className="flex items-center mt-1 text-sm">
              <div
                className={`flex items-center ${
                  metric.change > 0
                    ? 'text-green-500'
                    : metric.change < 0
                    ? 'text-red-500'
                    : 'text-gray-500'
                }`}
              >
                {metric.change > 0 ? (
                  <ArrowUpIcon className="h-3 w-3 mr-1" />
                ) : metric.change < 0 ? (
                  <ArrowDownIcon className="h-3 w-3 mr-1" />
                ) : null}
                <span>{Math.abs(metric.change)}%</span>
              </div>
              <p className="ml-1 text-muted-foreground">
                {metric.changeLabel || 'vs. previous'}
              </p>
            </div>
          )}

          {metric.changeLabel && !showComparison && !metric.isLoading && (
            <p className="text-xs text-muted-foreground">{metric.changeLabel}</p>
          )}
        </Card>
      ))}
    </div>
  );
}