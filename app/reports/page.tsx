'use client'

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { PageHeader } from '@/components/page-header';
import { ReportProvider } from './components/report-context';
import { FilterBar } from './components/filter-bar';

// Lazy load the tab components to improve initial page load performance
import dynamic from 'next/dynamic';

const OverviewDashboard = dynamic(() => import('./tabs/overview'), {
  loading: () => <p>Loading overview dashboard...</p>
});

const MemberReports = dynamic(() => import('./tabs/members'), {
  loading: () => <p>Loading member reports...</p>
});

const TransactionReports = dynamic(() => import('./tabs/transactions'), {
  loading: () => <p>Loading transaction reports...</p>
});

const RewardReports = dynamic(() => import('./tabs/rewards'), {
  loading: () => <p>Loading reward reports...</p>
});

const BusinessImpact = dynamic(() => import('./tabs/business-impact'), {
  loading: () => <p>Loading business impact reports...</p>
});

const ItemAnalytics = dynamic(() => import('./tabs/item-analytics'), {
  loading: () => <p>Loading item analytics...</p>
});

export default function ReportsPage() {
  // Remove unused activeTab state
  // Just keep the setter function which is used in onValueChange
  const [, setActiveTab] = useState('overview');

  const handleRefresh = () => {
    console.log('Refreshing reports data');
    // Implement refresh logic here
  };

  const handleExport = () => {
    console.log('Exporting reports data');
    // Implement export logic here
  };

  return (
    <ReportProvider>
      <div className="container mx-auto py-4">
        <PageHeader
          heading="Reports & Analytics"
        >
          <p className="text-muted-foreground">
            Track and analyze your loyalty program performance.
          </p>
        </PageHeader>

        <FilterBar
          onRefresh={handleRefresh}
          onExport={handleExport}
        />

        <Tabs
          defaultValue="overview"
          onValueChange={setActiveTab}
          className="mt-6"
        >
          <TabsList className="grid grid-cols-2 md:grid-cols-6 gap-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="members">Members</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="rewards">Rewards</TabsTrigger>
            <TabsTrigger value="business">Business Impact</TabsTrigger>
            <TabsTrigger value="items">Item Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <OverviewDashboard />
          </TabsContent>

          <TabsContent value="members" className="mt-6">
            <MemberReports />
          </TabsContent>

          <TabsContent value="transactions" className="mt-6">
            <TransactionReports />
          </TabsContent>

          <TabsContent value="rewards" className="mt-6">
            <RewardReports />
          </TabsContent>

          <TabsContent value="business" className="mt-6">
            <BusinessImpact />
          </TabsContent>

          <TabsContent value="items" className="mt-6">
            <ItemAnalytics />
          </TabsContent>
        </Tabs>
      </div>
    </ReportProvider>
  );
}