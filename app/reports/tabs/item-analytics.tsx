'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, Users, ShoppingBag, Star, AlertCircle } from 'lucide-react';

interface CustomerPreference {
  member_id: string;
  first_name: string;
  last_name: string;
  item_name: string;
  item_category: string;
  purchase_count: number;
  total_spent: number;
  preference_score: number;
}

interface BusinessPerformance {
  summary: {
    total_revenue: number;
    total_sales: number;
    unique_customers: number;
    average_order_value: number;
  };
  item_performance: {
    top_performers: Array<{
      item_name: string;
      item_category: string;
      total_sales: number;
      total_revenue: number;
      popularity_score: number;
    }>;
  };
  category_analysis: {
    revenue_by_category: Array<{
      category: string;
      revenue: number;
      percentage: number;
    }>;
  };
}

interface TemplatePerformance {
  summary: {
    total_templates: number;
    active_templates: number;
    avg_success_rate: number;
    avg_confidence: number;
  };
  insights: {
    overall_health: string;
    confidence_level: string;
    recommendations: string[];
  };
}

export default function ItemAnalytics() {
  const [customerData, setCustomerData] = useState<{
    customer_preferences: CustomerPreference[];
    category_insights: Array<{ category: string; total_revenue: number; unique_customers: number }>;
  } | null>(null);
  const [businessData, setBusinessData] = useState<BusinessPerformance | null>(null);
  const [templateData, setTemplateData] = useState<TemplatePerformance | null>(null);
  const [loading, setLoading] = useState(true);
  const [companyId] = useState('7c4b5389-b630-4d2b-b9b1-9f6460117371'); // FUFIS company ID

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);

        // Fetch all analytics data in parallel
        const [customerRes, businessRes, templateRes] = await Promise.all([
          fetch(`/api/analytics/customers?company_id=${companyId}`),
          fetch(`/api/analytics/business-performance?company_id=${companyId}`),
          fetch(`/api/analytics/templates?company_id=${companyId}`)
        ]);

        const [customerResult, businessResult, templateResult] = await Promise.all([
          customerRes.json(),
          businessRes.json(),
          templateRes.json()
        ]);

        setCustomerData(customerResult);
        setBusinessData(businessResult);
        setTemplateData(templateResult);

      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [companyId]);

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Item-Level Analytics</h1>
        <Badge variant="outline" className="text-sm">
          FUFIS Beauty Salon
        </Badge>
      </div>

      {/* Key Metrics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold">
                  {businessData?.summary.total_revenue?.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'ETB'
                  }) || '0 ETB'}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sales</p>
                <p className="text-2xl font-bold">{businessData?.summary.total_sales || 0}</p>
              </div>
              <ShoppingBag className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Unique Customers</p>
                <p className="text-2xl font-bold">{businessData?.summary.unique_customers || 0}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Template Health</p>
                <p className="text-2xl font-bold">
                  <Badge
                    variant={templateData?.insights.overall_health === 'Excellent' ? 'default' :
                           templateData?.insights.overall_health === 'Good' ? 'secondary' : 'destructive'}
                  >
                    {templateData?.insights.overall_health || 'Unknown'}
                  </Badge>
                </p>
              </div>
              <Star className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="items" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="items">Item Performance</TabsTrigger>
          <TabsTrigger value="customers">Customer Insights</TabsTrigger>
          <TabsTrigger value="categories">Category Analysis</TabsTrigger>
          <TabsTrigger value="templates">Template Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Items</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={businessData?.item_performance.top_performers.slice(0, 8) || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="item_name"
                    angle={-45}
                    textAnchor="end"
                    height={100}
                    fontSize={12}
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="total_revenue" fill="#0088FE" name="Revenue (ETB)" />
                  <Bar dataKey="total_sales" fill="#00C49F" name="Sales Count" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Preferences Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="text-lg font-semibold mb-2">Top Customer Favorites</h4>
                  <div className="space-y-2">
                    {customerData?.customer_preferences.slice(0, 5).map((pref, index) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div>
                          <p className="font-medium">{pref.first_name} {pref.last_name}</p>
                          <p className="text-sm text-gray-600">{pref.item_name}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{pref.purchase_count}x</p>
                          <p className="text-sm text-gray-600">{pref.total_spent} ETB</p>
                        </div>
                      </div>
                    )) || <p>No customer data available</p>}
                  </div>
                </div>
                <div>
                  <h4 className="text-lg font-semibold mb-2">Category Preferences</h4>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={customerData?.category_insights || []}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({category, total_revenue}) => `${category}: ${total_revenue} ETB`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="total_revenue"
                      >
                        {(customerData?.category_insights || []).map((_, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue by Category</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={businessData?.category_analysis.revenue_by_category || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="category" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="revenue" fill="#FFBB28" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Template Performance Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Active Templates:</span>
                    <span className="font-semibold">
                      {templateData?.summary.active_templates || 0}/{templateData?.summary.total_templates || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Success Rate:</span>
                    <span className="font-semibold">
                      {templateData?.summary.avg_success_rate?.toFixed(1) || 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Confidence:</span>
                    <span className="font-semibold">
                      {templateData?.summary.avg_confidence?.toFixed(2) || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Overall Health:</span>
                    <Badge
                      variant={templateData?.insights.overall_health === 'Excellent' ? 'default' :
                             templateData?.insights.overall_health === 'Good' ? 'secondary' : 'destructive'}
                    >
                      {templateData?.insights.overall_health || 'Unknown'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {templateData?.insights?.recommendations && templateData.insights.recommendations.length > 0 ? (
                    templateData.insights.recommendations.map((rec, index) => (
                      <div key={index} className="flex items-start gap-2 p-2 bg-blue-50 rounded">
                        <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                        <p className="text-sm">{rec}</p>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500">No specific recommendations at this time.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
