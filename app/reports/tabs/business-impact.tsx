'use client'

import React, { useEffect, useState } from 'react';
import { useReportContext } from '../components/report-context';
import { KpiSummary, KpiData } from '../components/kpi-summary';
import { ChartSection } from '../components/chart-section';
import { DataTable } from '../components/data-table';
import { ExportControls } from '../components/export-controls';
import { TrendingUp, DollarSign, BarChart3, Briefcase, Users, LineChart } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';

// Define specific types for the business impact data
interface RevenueCorrelationRecord extends Record<string, unknown> {
  date: string;
  revenue: number;
  loyalty_activity: number;
  correlation_score: number;
}

interface CustomerValueByTierRecord extends Record<string, unknown> {
  tier_name: string;
  member_count: number;
  average_order_value: number;
  total_revenue: number;
  average_orders_per_year: number;
}

interface PromotionalImpactRecord extends Record<string, unknown> {
  promotion_name: string;
  start_date: string;
  end_date: string;
  members_engaged: number;
  revenue_generated: number;
  points_issued: number;
  redemptions: number;
  roi: number;
}

interface LifetimeValueRecord extends Record<string, unknown> {
  cohort: string;
  avg_lifetime_value: number;
  retention_rate: number;
  avg_purchase_frequency: number;
  avg_order_value: number;
}

interface BusinessImpactData {
  revenueCorrelation: RevenueCorrelationRecord[];
  customerValueByTier: CustomerValueByTierRecord[];
  roi: {
    program_roi: number;
    retention_value: number;
    customer_acquisition_cost: number;
    program_cost: number;
    revenue_generated: number;
  };
  promotionalImpact: PromotionalImpactRecord[];
  lifetimeValue: LifetimeValueRecord[];
  summary: {
    totalRevenue: number;
    revenuePerMember: number;
    programROI: number;
    retentionValue: number;
  };
  comparison?: {
    revenueCorrelation: RevenueCorrelationRecord[];
    customerValueByTier: CustomerValueByTierRecord[];
    roi: {
      program_roi: number;
      retention_value: number;
    };
    totalRevenue: number;
    revenuePerMember: number;
    programROI: number;
    retentionValue: number;
    revenueChange: number;
    revenuePerMemberChange: number;
    roiChange: number;
  };
}

export default function BusinessImpact() {
  const {
    filters,
    getStartDate,
    getEndDate,
    getPreviousStartDate,
    getPreviousEndDate
  } = useReportContext();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<BusinessImpactData | null>(null);

  // Fetch data from the API
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Construct query parameters for the API call
        const params = new URLSearchParams({
          startDate: getStartDate(),
          endDate: getEndDate(),
          showComparison: filters.showComparison ? 'true' : 'false',
        });

        if (filters.showComparison) {
          params.append('prevStartDate', getPreviousStartDate());
          params.append('prevEndDate', getPreviousEndDate());
        }

        // Fetch data from the API
        const response = await fetch(`/api/reports/business?${params}`);
        if (!response.ok) {
          throw new Error('Failed to fetch business impact data');
        }

        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error fetching business impact data:', error);
        // Handle error appropriately
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filters, getStartDate, getEndDate, getPreviousStartDate, getPreviousEndDate]);

  // Format numbers for display
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat().format(value);
  };

  // Format currency values
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Format ROI as percentage
  const formatROI = (value: number) => {
    return `${value}%`;
  };

  // Prepare metrics for KPI summary
  const getKpiMetrics = (): KpiData[] => {
    if (isLoading) {
      return [
        { title: 'Total Revenue', value: '-', icon: <DollarSign />, isLoading: true },
        { title: 'Revenue per Member', value: '-', icon: <Users />, isLoading: true },
        { title: 'Program ROI', value: '-', icon: <TrendingUp />, isLoading: true },
        { title: 'Retention Value', value: '-', icon: <Briefcase />, isLoading: true },
      ];
    }

    if (!data) return [];

    return [
      {
        title: 'Total Revenue',
        value: data.summary.totalRevenue,
        icon: <DollarSign />,
        change: data.comparison?.revenueChange,
        formatValue: formatCurrency,
      },
      {
        title: 'Revenue per Member',
        value: data.summary.revenuePerMember,
        icon: <Users />,
        change: data.comparison?.revenuePerMemberChange,
        formatValue: formatCurrency,
      },
      {
        title: 'Program ROI',
        value: data.summary.programROI,
        icon: <TrendingUp />,
        change: data.comparison?.roiChange,
        formatValue: formatROI,
      },
      {
        title: 'Retention Value',
        value: data.summary.retentionValue,
        icon: <Briefcase />,
        formatValue: formatCurrency,
      },
    ];
  };

  // Define columns for customer value by tier table
  const customerValueColumns: ColumnDef<CustomerValueByTierRecord>[] = [
    {
      header: 'Tier',
      accessorKey: 'tier_name',
    },
    {
      header: 'Members',
      accessorKey: 'member_count',
      cell: ({ row }) => formatNumber(row.original.member_count),
    },
    {
      header: 'Avg Order Value',
      accessorKey: 'average_order_value',
      cell: ({ row }) => formatCurrency(row.original.average_order_value),
    },
    {
      header: 'Orders per Year',
      accessorKey: 'average_orders_per_year',
      cell: ({ row }) => row.original.average_orders_per_year.toFixed(1),
    },
    {
      header: 'Total Revenue',
      accessorKey: 'total_revenue',
      cell: ({ row }) => formatCurrency(row.original.total_revenue),
    }
  ];

  // Define columns for promotional impact table
  const promotionColumns: ColumnDef<PromotionalImpactRecord>[] = [
    {
      header: 'Promotion',
      accessorKey: 'promotion_name',
    },
    {
      header: 'Period',
      accessorKey: 'start_date',
      cell: ({ row }) => `${row.original.start_date} - ${row.original.end_date}`,
    },
    {
      header: 'Members Engaged',
      accessorKey: 'members_engaged',
      cell: ({ row }) => formatNumber(row.original.members_engaged),
    },
    {
      header: 'Revenue Generated',
      accessorKey: 'revenue_generated',
      cell: ({ row }) => formatCurrency(row.original.revenue_generated),
    },
    {
      header: 'ROI',
      accessorKey: 'roi',
      cell: ({ row }) => `${row.original.roi}%`,
    }
  ];

  // Define columns for lifetime value table
  const lifetimeValueColumns: ColumnDef<LifetimeValueRecord>[] = [
    {
      header: 'Cohort',
      accessorKey: 'cohort',
    },
    {
      header: 'Lifetime Value',
      accessorKey: 'avg_lifetime_value',
      cell: ({ row }) => formatCurrency(row.original.avg_lifetime_value),
    },
    {
      header: 'Retention Rate',
      accessorKey: 'retention_rate',
      cell: ({ row }) => `${row.original.retention_rate}%`,
    },
    {
      header: 'Purchase Frequency',
      accessorKey: 'avg_purchase_frequency',
      cell: ({ row }) => row.original.avg_purchase_frequency.toFixed(1),
    },
    {
      header: 'Avg Order Value',
      accessorKey: 'avg_order_value',
      cell: ({ row }) => formatCurrency(row.original.avg_order_value),
    }
  ];

  return (
    <div className="space-y-6">
      <KpiSummary
        metrics={getKpiMetrics()}
        showComparison={filters.showComparison}
      />

      <div className="grid gap-6 md:grid-cols-2">
        <ChartSection
          title="Revenue Correlation"
          description="Correlation between loyalty activity and revenue"
          isLoading={isLoading}
          downloadable
        >
          {/* Replace with actual chart component */}
          <div className="h-[350px] flex items-center justify-center text-muted-foreground border rounded">
            <div className="text-center">
              <LineChart className="mx-auto h-8 w-8 mb-2" />
              <p>Revenue Correlation Chart</p>
              <p className="text-sm">
                Data points: {data?.revenueCorrelation.length ?? 0}
              </p>
            </div>
          </div>
        </ChartSection>

        <ChartSection
          title="ROI Breakdown"
          description="Return on investment metrics for your loyalty program"
          isLoading={isLoading}
          downloadable
        >
          {/* Replace with actual chart component */}
          <div className="h-[350px] flex items-center justify-center text-muted-foreground border rounded">
            <div className="text-center">
              <TrendingUp className="mx-auto h-8 w-8 mb-2" />
              <p>ROI Breakdown Chart</p>
              <p className="text-sm">
                Program ROI: {data?.roi?.program_roi ?? 0}%
              </p>
            </div>
          </div>
        </ChartSection>
      </div>

      <ChartSection
        title="Customer Lifetime Value"
        description="Analysis of customer lifetime value by cohort"
        isLoading={isLoading}
        downloadable
      >
        {/* Replace with actual chart component */}
        <div className="h-[350px] flex items-center justify-center text-muted-foreground border rounded">
          <div className="text-center">
            <BarChart3 className="mx-auto h-8 w-8 mb-2" />
            <p>Customer Lifetime Value Chart</p>
            <p className="text-sm">
              Data points: {data?.lifetimeValue.length ?? 0}
            </p>
          </div>
        </div>
      </ChartSection>

      <ChartSection
        title="Customer Value by Tier"
        description="Revenue and order metrics by loyalty tier"
        isLoading={isLoading}
        actions={
          <ExportControls
            onExportCsv={() => console.log('Exporting CSV')}
            onExportExcel={() => console.log('Exporting Excel')}
          />
        }
      >
        <DataTable
          columns={customerValueColumns}
          data={data?.customerValueByTier || []}
          searchKey="tier_name"
          pagination={true}
        />
      </ChartSection>

      <div className="grid gap-6 md:grid-cols-2">
        <ChartSection
          title="Promotional Impact"
          description="Performance of marketing promotions and campaigns"
          isLoading={isLoading}
          actions={
            <ExportControls
              onExportCsv={() => console.log('Exporting CSV')}
            />
          }
        >
          <DataTable
            columns={promotionColumns}
            data={data?.promotionalImpact || []}
            searchKey="promotion_name"
            pagination={true}
          />
        </ChartSection>

        <ChartSection
          title="Customer Lifetime Value Analysis"
          description="Cohort analysis of customer lifetime value"
          isLoading={isLoading}
          actions={
            <ExportControls
              onExportCsv={() => console.log('Exporting CSV')}
            />
          }
        >
          <DataTable
            columns={lifetimeValueColumns}
            data={data?.lifetimeValue || []}
            searchKey="cohort"
            pagination={true}
          />
        </ChartSection>
      </div>
    </div>
  );
}