'use client'

import React, { useEffect, useState } from 'react';
import { useReportContext } from '../components/report-context';
import { KpiSummary, KpiData } from '../components/kpi-summary';
import { ChartSection } from '../components/chart-section';
import { DataTable } from '../components/data-table';
import { ExportControls } from '../components/export-controls';
import { DollarSign, TrendingUp, Calendar, ShoppingBag, Clock } from 'lucide-react';
import { format } from "date-fns";

interface TransactionsData {
  metrics: {
    totalTransactions: number;
    totalAmount: number;
    averageAmount: number;
    pointsIssued: number;
    conversionRate: number;
  };
  transactionsOverTime: Array<{
    date: string;
    count: number;
    amount: number;
  }>;
  hourlyDistribution: Record<string, number>;
  topCategories: Array<{
    category: string;
    count: number;
    amount: number;
    percentage: number;
  }>;
  recentTransactions: Array<{
    transaction_id: string;
    member_name: string;
    member_id: string;
    amount: number;
    points: number;
    date: string;
    items: number;
    method: string;
  }>;
  comparison?: {
    totalTransactionsChange: number;
    totalAmountChange: number;
    averageAmountChange: number;
    pointsIssuedChange: number;
    conversionRateChange: number;
  };
}

interface Transaction extends Record<string, unknown> {
  transaction_id: string;
  date: string;
  member_name: string;
  member_id: string;
  amount: number;
  points: number;
  items: number;
  method: string;
}

interface CategoryBreakdown extends Record<string, unknown> {
  category: string;
  count: number;
  amount: number;
  percentage: number;
}

export default function TransactionsDashboard() {
  const { filters, getStartDate, getEndDate, getPreviousStartDate, getPreviousEndDate } = useReportContext();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<TransactionsData | null>(null);

  // Fetch data from the API
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Construct query parameters
        const params = new URLSearchParams({
          startDate: getStartDate(),
          endDate: getEndDate(),
          showComparison: filters.showComparison ? 'true' : 'false',
        });

        if (filters.showComparison) {
          params.append('prevStartDate', getPreviousStartDate());
          params.append('prevEndDate', getPreviousEndDate());
        }

        // Fetch data from the API
        const response = await fetch(`/api/reports/transactions?${params}`);
        if (!response.ok) {
          throw new Error('Failed to fetch transactions data');
        }

        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error fetching transactions data:', error);
        // Handle error appropriately
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filters, getStartDate, getEndDate, getPreviousStartDate, getPreviousEndDate]);

  // Format currency values
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  // Format numbers for display
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat().format(value);
  };

  // Format percentage values
  const formatPercent = (value: number) => {
    return `${value}%`;
  };

  // Prepare metrics for KPI cards
  const getKpiMetrics = (): KpiData[] => {
    if (isLoading) {
      return [
        { title: 'Total Transactions', value: '-', icon: <ShoppingBag />, isLoading: true },
        { title: 'Total Revenue', value: '-', icon: <DollarSign />, isLoading: true },
        { title: 'Average Order Value', value: '-', icon: <DollarSign />, isLoading: true },
        { title: 'Points Issued', value: '-', icon: <TrendingUp />, isLoading: true },
        { title: 'Conversion Rate', value: '-', icon: <TrendingUp />, isLoading: true },
      ];
    }

    if (!data || !data.metrics) return [];
    const { metrics, comparison } = data;

    return [
      {
        title: 'Total Transactions',
        value: metrics.totalTransactions,
        icon: <ShoppingBag />,
        change: comparison?.totalTransactionsChange,
        formatValue: formatNumber,
      },
      {
        title: 'Total Revenue',
        value: metrics.totalAmount,
        icon: <DollarSign />,
        change: comparison?.totalAmountChange,
        formatValue: formatCurrency,
      },
      {
        title: 'Average Order Value',
        value: metrics.averageAmount,
        icon: <DollarSign />,
        change: comparison?.averageAmountChange,
        formatValue: formatCurrency,
      },
      {
        title: 'Points Issued',
        value: metrics.pointsIssued,
        icon: <TrendingUp />,
        change: comparison?.pointsIssuedChange,
        formatValue: formatNumber,
      },
      {
        title: 'Conversion Rate',
        value: metrics.conversionRate,
        icon: <TrendingUp />,
        change: comparison?.conversionRateChange,
        formatValue: formatPercent,
      },
    ];
  };

  // Columns for categories table
  const categoriesColumns = [
    {
      accessorKey: "category",
      header: "Category",
    },
    {
      accessorKey: "count",
      header: "Transactions",
      cell: ({ row }: { row: { original: CategoryBreakdown } }) => formatNumber(row.original.count),
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }: { row: { original: CategoryBreakdown } }) => formatCurrency(row.original.amount),
    },
    {
      accessorKey: "percentage",
      header: "Percentage",
      cell: ({ row }: { row: { original: CategoryBreakdown } }) => `${row.original.percentage.toFixed(1)}%`,
    },
  ];

  // Columns for recent transactions table
  const transactionsColumns = [
    {
      accessorKey: "member_name",
      header: "Member",
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }: { row: { original: Transaction } }) => formatCurrency(row.original.amount),
    },
    {
      accessorKey: "points",
      header: "Points",
      cell: ({ row }: { row: { original: Transaction } }) => formatNumber(row.original.points),
    },
    {
      accessorKey: "date",
      header: "Date",
      cell: ({ row }: { row: { original: Transaction } }) => {
        const date = new Date(row.original.date);
        return format(date, "MMM d, yyyy h:mm a");
      },
    },
    {
      accessorKey: "items",
      header: "Items",
      cell: ({ row }: { row: { original: Transaction } }) => formatNumber(row.original.items),
    },
    {
      accessorKey: "method",
      header: "Method",
    },
  ];

  return (
    <div className="space-y-6">
      {/* KPI Summary Cards */}
      <KpiSummary
        metrics={getKpiMetrics()}
        showComparison={filters.showComparison}
      />

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartSection
          title="Transaction Volume Over Time"
          description="Number of transactions and revenue by date"
          isLoading={isLoading}
          downloadable
        >
          {/* Replace with actual chart component */}
          <div className="h-[350px] flex items-center justify-center text-muted-foreground border rounded">
            <div className="text-center">
              <Calendar className="mx-auto h-8 w-8 mb-2" />
              <p>Transactions Over Time Chart</p>
              <p className="text-sm">
                Data points: {data?.transactionsOverTime?.length ?? 0}
              </p>
            </div>
          </div>
        </ChartSection>

        <ChartSection
          title="Hourly Distribution"
          description="Transaction volume by hour of day"
          isLoading={isLoading}
          downloadable
        >
          {/* Replace with actual chart component */}
          <div className="h-[350px] flex items-center justify-center text-muted-foreground border rounded">
            <div className="text-center">
              <Clock className="mx-auto h-8 w-8 mb-2" />
              <p>Hourly Distribution Chart</p>
              <p className="text-sm">
                Hours analyzed: {Object.keys(data?.hourlyDistribution || {}).length}
              </p>
            </div>
          </div>
        </ChartSection>
      </div>

      {/* Tables */}
      <ChartSection
        title="Top Categories"
        description="Transaction volume by category"
        isLoading={isLoading}
        actions={
          <ExportControls
            onExportCsv={() => console.log('Exporting categories CSV')}
          />
        }
      >
        <DataTable
          columns={categoriesColumns}
          data={data?.topCategories || []}
        />
      </ChartSection>

      {/* Recent Transactions Table */}
      <ChartSection
        title="Recent Transactions"
        description="Latest customer purchases"
        isLoading={isLoading}
        actions={
          <ExportControls
            onExportCsv={() => console.log('Exporting transactions CSV')}
            onExportExcel={() => console.log('Exporting transactions Excel')}
          />
        }
      >
        <DataTable
          columns={transactionsColumns}
          data={data?.recentTransactions || []}
        />
      </ChartSection>
    </div>
  );
}