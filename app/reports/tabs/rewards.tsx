'use client'

import React, { useEffect, useState } from 'react';
import { format } from "date-fns";
import { ColumnDef } from '@tanstack/react-table';

import { DataTable } from '../components/data-table';
import { ExportControls } from '../components/export-controls';
import { useReportContext } from '../components/report-context';
import { ChartSection } from '../components/chart-section';
import { KpiSummary, KpiData } from '../components/kpi-summary';

// Define interfaces with the exact field names as in the data
interface CategoryPerformance extends Record<string, unknown> {
  category: string;
  redemption_count: number;
  points_redeemed: number;
  percentage: number;
}

interface PopularRewards extends Record<string, unknown> {
  reward_name: string;
  redemption_count: number;
  points_redeemed: number;
  percentage: number;
}

interface InventoryStatus extends Record<string, unknown> {
  id: string;
  name: string;
  code: string;
  points_cost: number;
  is_active: boolean;
  redemption_count: number;
}

interface RecentRedemptions extends Record<string, unknown> {
  id: string;
  redeemed_at: string;
  points_used: number;
  rewards: {
    id: string;
    name: string;
    code: string;
  };
  loyalty_members: {
    id: string;
    name: string;
    loyalty_id: string;
  };
}

// Define mock data structure
interface RewardsData {
  popularRewards: PopularRewards[];
  inventoryStatus: InventoryStatus[];
  categoryPerformance: CategoryPerformance[];
  recentRedemptions: RecentRedemptions[];
  redemptionTrends: Array<{
    date: string;
    count: number;
    points: number;
  }>;
  rewardEfficiency: Array<{
    reward_id: string;
    name: string;
    points_cost: number;
    perceived_value: number;
    efficiency_score: number;
  }>;
  kpiData: {
    totalRewards: number;
    redeemedRewards: number;
    redemptionRate: number;
    avgValue: number;
  };
}

// Define column definitions for tables using standard ColumnDef type
type CategoryPerformanceColumn = ColumnDef<CategoryPerformance>;
type PopularRewardsColumn = ColumnDef<PopularRewards>;
type InventoryStatusColumn = ColumnDef<InventoryStatus>;
type RecentRedemptionsColumn = ColumnDef<RecentRedemptions>;

export default function RewardReports() {
  const { filters, getStartDate, getEndDate } = useReportContext();
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<RewardsData | null>(null);

  // Fetch data when filters change
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      // Simulate API call with timeout
      setTimeout(() => {
        setData({
          popularRewards: [],
          inventoryStatus: [],
          categoryPerformance: [],
          recentRedemptions: [],
          redemptionTrends: [],
          rewardEfficiency: [],
          kpiData: {
            totalRewards: 0,
            redeemedRewards: 0,
            redemptionRate: 0,
            avgValue: 0,
          },
        });
        setIsLoading(false);
      }, 1000);
    };

    fetchData();
  }, [filters, getStartDate, getEndDate]);

  // Format numbers for display
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  const formatPercent = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value);
  };

  // Get KPI metrics for display
  const getKpiMetrics = (): KpiData[] => {
    if (!data || isLoading) {
      return [];
    }
    
    return [
      {
        title: 'Total Rewards',
        value: formatNumber(data.kpiData.totalRewards),
        change: 5.2,
        changeLabel: 'increase',
      },
      {
        title: 'Redeemed Rewards',
        value: formatNumber(data.kpiData.redeemedRewards),
        change: -2.4,
        changeLabel: 'decrease',
      },
      {
        title: 'Redemption Rate',
        value: formatPercent(data.kpiData.redemptionRate),
        change: 1.8,
        changeLabel: 'increase',
      },
      {
        title: 'Avg. Redemption Value',
        value: formatCurrency(data.kpiData.avgValue),
        change: 3.1,
        changeLabel: 'increase',
      },
    ];
  };

  // Handle exporting data
  const handleExport = (type: string) => {
    console.log(`Exporting ${type}`);
  };

  // Define columns for popular rewards table
  const popularRewardsColumns: PopularRewardsColumn[] = [
    {
      header: 'Reward',
      accessorKey: 'reward_name',
    },
    {
      header: 'Redemptions',
      accessorKey: 'redemption_count',
      cell: ({ row }) => formatNumber(row.original.redemption_count),
    },
    {
      header: 'Points Used',
      accessorKey: 'points_redeemed',
      cell: ({ row }) => formatNumber(row.original.points_redeemed),
    },
    {
      header: 'Percentage',
      accessorKey: 'percentage',
      cell: ({ row }) => `${row.original.percentage.toFixed(1)}%`,
    }
  ];

  // Define columns for category performance table
  const categoryColumns: CategoryPerformanceColumn[] = [
    {
      header: 'Category',
      accessorKey: 'category',
    },
    {
      header: 'Redemptions',
      accessorKey: 'redemption_count',
      cell: ({ row }) => formatNumber(row.original.redemption_count),
    },
    {
      header: 'Points Used',
      accessorKey: 'points_redeemed',
      cell: ({ row }) => formatNumber(row.original.points_redeemed),
    },
    {
      header: 'Percentage',
      accessorKey: 'percentage',
      cell: ({ row }) => `${row.original.percentage.toFixed(1)}%`,
    }
  ];

  // Define columns for inventory status table
  const inventoryColumns: InventoryStatusColumn[] = [
    {
      header: 'Reward',
      accessorKey: 'name',
    },
    {
      header: 'Code',
      accessorKey: 'code',
    },
    {
      header: 'Points',
      accessorKey: 'points_cost',
      cell: ({ row }) => formatNumber(row.original.points_cost),
    },
    {
      header: 'Status',
      accessorKey: 'is_active',
      cell: ({ row }) => (
        <span className={row.original.is_active ? 'text-green-600' : 'text-red-600'}>
          {row.original.is_active ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      header: 'Redemptions',
      accessorKey: 'redemption_count',
      cell: ({ row }) => formatNumber(row.original.redemption_count),
    }
  ];

  // Define columns for recent redemptions table
  const redemptionsColumns: RecentRedemptionsColumn[] = [
    {
      header: 'Date',
      accessorKey: 'redeemed_at',
      cell: ({ row }) => {
        const date = new Date(row.original.redeemed_at);
        return format(date, 'MMM d, yyyy h:mm a');
      },
    },
    {
      header: 'Member',
      accessorKey: 'loyalty_members.name',
      cell: ({ row }) => row.original.loyalty_members.name,
    },
    {
      header: 'ID',
      accessorKey: 'loyalty_members.loyalty_id',
      cell: ({ row }) => row.original.loyalty_members.loyalty_id,
    },
    {
      header: 'Reward',
      accessorKey: 'rewards.name',
      cell: ({ row }) => row.original.rewards.name,
    },
    {
      header: 'Points',
      accessorKey: 'points_used',
      cell: ({ row }) => formatNumber(row.original.points_used),
    }
  ];

  return (
    <div className="space-y-8">
      <KpiSummary
        metrics={getKpiMetrics()}
        showComparison={filters.showComparison}
      />

      <div className="space-y-6">
        {/* Popular Rewards */}
        <ChartSection
          isLoading={isLoading}
          title="Popular Rewards"
          description="Most frequently redeemed rewards"
          downloadable
          onDownload={() => handleExport('popularRewards')}
        >
          <DataTable
            columns={popularRewardsColumns}
            data={data?.popularRewards || []}
            searchKey="reward_name"
          />
        </ChartSection>

        {/* Inventory Status */}
        <ChartSection
          isLoading={isLoading}
          title="Inventory Status"
          description="Current reward inventory and redemption counts"
          downloadable
          onDownload={() => handleExport('inventory')}
        >
          <DataTable
            columns={inventoryColumns}
            data={data?.inventoryStatus || []}
            searchKey="name"
          />
        </ChartSection>

        {/* Redemption by Category */}
        <ChartSection 
          isLoading={isLoading}
          title="Redemption by Category"
          description="Performance metrics by reward category"
          downloadable
          onDownload={() => handleExport('categoryPerformance')}
        >
          <DataTable
            columns={categoryColumns}
            data={data?.categoryPerformance || []}
            searchKey="category"
          />
        </ChartSection>

        {/* Recent Redemptions */}
        <ChartSection
          title="Recent Redemptions"
          description="Latest reward redemptions"
          isLoading={isLoading}
          actions={
            <ExportControls
              onExportCsv={() => handleExport('recentRedemptions')}
            />
          }
        >
          <DataTable
            columns={redemptionsColumns}
            data={data?.recentRedemptions || []}
            searchKey="id"
          />
        </ChartSection>
      </div>
    </div>
  );
}