'use client'

import React, { useEffect, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';
import { useReportContext } from '../components/report-context';
import { KpiSummary, KpiData } from '../components/kpi-summary';
import { ChartSection } from '../components/chart-section';
import { DataTable } from '../components/data-table';
import { ExportControls } from '../components/export-controls';
import {
  Users,
  Award,
  BadgeDollarSign,
  ShoppingBag,
  TrendingUp,
  BarChart,
} from 'lucide-react';
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart as RechartsBarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend
} from 'recharts';

// Define properly typed interfaces for data records
interface MemberGrowthRecord extends Record<string, unknown> {
  date: string;
  count: number;
}

interface PointsActivityRecord extends Record<string, unknown> {
  date: string;
  issued: number;
  redeemed: number;
}

interface RecentActivityRecord extends Record<string, unknown> {
  id: string;
  date: string;
  member_name: string;
  activity_type: string;
  points: number;
  status: string;
}

interface TopRedemptionRecord extends Record<string, unknown> {
  reward_name: string;
  count: number;
  points_spent: number;
}

// Define the full data structure
interface OverviewData {
  metrics: {
    activeMemberCount: number;
    pointsIssued: number;
    pointsRedeemed: number;
    redemptionCount: number;
    conversionRate: number;
    averageOrderValue: number;
  };
  memberGrowth: MemberGrowthRecord[];
  pointsActivity: PointsActivityRecord[];
  recentActivity: RecentActivityRecord[];
  topRedemptions: TopRedemptionRecord[];
  comparison?: {
    activeMemberCount: number;
    pointsIssued: number;
    pointsRedeemed: number;
    redemptionCount: number;
    conversionRate: number;
    averageOrderValue: number;
    activeMemberCountChange: number;
    pointsIssuedChange: number;
    pointsRedeemedChange: number;
    redemptionCountChange: number;
    conversionRateChange: number;
    averageOrderValueChange: number;
  };
}

export default function OverviewDashboard() {
  const { filters, getStartDate, getEndDate, getPreviousStartDate, getPreviousEndDate } = useReportContext();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = useState<OverviewData | null>(null);

  // Fetch data from the API
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Construct query parameters for the API call
        const params = new URLSearchParams({
          startDate: getStartDate(),
          endDate: getEndDate(),
          showComparison: filters.showComparison ? 'true' : 'false',
        });

        if (filters.showComparison) {
          params.append('prevStartDate', getPreviousStartDate());
          params.append('prevEndDate', getPreviousEndDate());
        }

        // Fetch data from the API
        const response = await fetch(`/api/reports/overview?${params}`);
        if (!response.ok) {
          throw new Error('Failed to fetch overview data');
        }

        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error fetching overview data:', error);
        // Handle error appropriately
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filters, getStartDate, getEndDate, getPreviousStartDate, getPreviousEndDate]);

  // Format helpers
  const formatNumber = (value: number): string => {
    return new Intl.NumberFormat().format(value);
  };

  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercent = (value: number): string => {
    return `${value}%`;
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return format(date, 'MMM d, yyyy');
  };

  // Prepare metrics for KPI cards
  const getKpiMetrics = (): KpiData[] => {
    if (isLoading) {
      return [
        { title: 'Active Members', value: '-', icon: <Users />, isLoading: true },
        { title: 'Points Issued', value: '-', icon: <BadgeDollarSign />, isLoading: true },
        { title: 'Points Redeemed', value: '-', icon: <Award />, isLoading: true },
        { title: 'Redemption Rate', value: '-', icon: <TrendingUp />, isLoading: true },
        { title: 'Avg Order Value', value: '-', icon: <ShoppingBag />, isLoading: true },
        { title: 'Conversion Rate', value: '-', icon: <BarChart />, isLoading: true },
      ];
    }

    if (!data || !data.metrics) return [];

    const { metrics, comparison } = data;

    const metricsArray: KpiData[] = [
      {
        title: 'Active Members',
        value: metrics.activeMemberCount,
        icon: <Users />,
        change: comparison?.activeMemberCountChange,
        formatValue: formatNumber,
      },
      {
        title: 'Points Issued',
        value: metrics.pointsIssued,
        icon: <BadgeDollarSign />,
        change: comparison?.pointsIssuedChange,
        formatValue: formatNumber,
      },
      {
        title: 'Points Redeemed',
        value: metrics.pointsRedeemed,
        icon: <Award />,
        change: comparison?.pointsRedeemedChange,
        formatValue: formatNumber,
      },
      {
        title: 'Redemption Count',
        value: metrics.redemptionCount,
        icon: <ShoppingBag />,
        change: comparison?.redemptionCountChange,
        formatValue: formatNumber,
      },
      {
        title: 'Conversion Rate',
        value: metrics.conversionRate,
        icon: <TrendingUp />,
        change: comparison?.conversionRateChange,
        formatValue: formatPercent,
      },
      {
        title: 'Avg Order Value',
        value: metrics.averageOrderValue,
        icon: <BarChart />,
        change: comparison?.averageOrderValueChange,
        formatValue: formatCurrency,
      },
    ];

    return metricsArray;
  };

  // Column definitions
  const activityColumns: ColumnDef<RecentActivityRecord>[] = [
    {
      accessorKey: "date",
      header: "Date",
      cell: ({ row }) => formatDate(row.original.date),
    },
    {
      accessorKey: "member_name",
      header: "Member",
    },
    {
      accessorKey: "activity_type",
      header: "Activity",
    },
    {
      accessorKey: "points",
      header: "Points",
      cell: ({ row }) => formatNumber(row.original.points),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const getStatusClass = (status: string) => {
          switch (status.toLowerCase()) {
            case 'completed':
              return 'bg-green-100 text-green-800';
            case 'pending':
              return 'bg-yellow-100 text-yellow-800';
            case 'cancelled':
              return 'bg-red-100 text-red-800';
            default:
              return 'bg-gray-100 text-gray-800';
          }
        };

        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(row.original.status)}`}>
            {row.original.status}
          </span>
        );
      },
    },
  ];

  const redemptionsColumns: ColumnDef<TopRedemptionRecord>[] = [
    {
      accessorKey: "reward_name",
      header: "Reward",
    },
    {
      accessorKey: "count",
      header: "Count",
      cell: ({ row }) => formatNumber(row.original.count),
    },
    {
      accessorKey: "points_spent",
      header: "Points Spent",
      cell: ({ row }) => formatNumber(row.original.points_spent),
    },
  ];

  return (
    <div className="space-y-6">
      {/* KPI Summary Cards */}
      <KpiSummary
        metrics={getKpiMetrics()}
        showComparison={filters.showComparison}
      />

      {/* Main Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartSection
          title="Member Growth"
          description="New member registrations over time"
          isLoading={isLoading}
          downloadable
        >
          <ResponsiveContainer width="100%" height={350}>
            <AreaChart data={data?.memberGrowth} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <defs>
                <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                </linearGradient>
              </defs>
              <XAxis dataKey="date" />
              <YAxis />
              <CartesianGrid strokeDasharray="3 3" />
              <Tooltip />
              <Legend />
              <Area type="monotone" dataKey="count" stroke="#8884d8" fillOpacity={1} fill="url(#colorUv)" />
            </AreaChart>
          </ResponsiveContainer>
        </ChartSection>

        <ChartSection
          title="Points Activity"
          description="Points issued vs. redeemed over time"
          isLoading={isLoading}
          downloadable
        >
          <ResponsiveContainer width="100%" height={350}>
            <RechartsBarChart data={data?.pointsActivity} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <CartesianGrid strokeDasharray="3 3" />
              <Bar dataKey="issued" fill="#8884d8" />
              <Bar dataKey="redeemed" fill="#82ca9d" />
            </RechartsBarChart>
          </ResponsiveContainer>
        </ChartSection>
      </div>

      {/* Tables */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartSection
          title="Recent Activity"
          description="Latest member transactions and rewards"
          isLoading={isLoading}
          actions={
            <ExportControls
              onExportCsv={() => console.log('Exporting recent activity CSV')}
              onExportExcel={() => console.log('Exporting recent activity Excel')}
            />
          }
        >
          <DataTable
            columns={activityColumns}
            data={data?.recentActivity || []}
            searchKey="id"
          />
        </ChartSection>

        <ChartSection
          title="Top Redemptions"
          description="Most popular rewards being redeemed"
          isLoading={isLoading}
          actions={
            <ExportControls
              onExportCsv={() => console.log('Exporting top redemptions CSV')}
            />
          }
        >
          <DataTable
            columns={redemptionsColumns}
            data={data?.topRedemptions || []}
            searchKey="reward_name"
          />
        </ChartSection>
      </div>
    </div>
  );
}