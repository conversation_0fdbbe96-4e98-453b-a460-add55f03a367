'use client'

import React, { useEffect, useState } from 'react';
import { useReportContext } from '../components/report-context';
import { KpiSummary, KpiData } from '../components/kpi-summary';
import { ChartSection } from '../components/chart-section';
import { DataTable } from '../components/data-table';
import { ExportControls } from '../components/export-controls';
import { Calendar, MapPin, TrendingUp } from 'lucide-react';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';

// Existing MembersData interface to stay compatible with current data structures
interface MembersData {
  topMembers: MemberRecord[];
  locationDistribution: LocationRecord[];
  tierDistribution: TierRecord[];
  membersOverTime: {
    date: string;
    count: number;
  }[];
  pointsActivity: {
    date: string;
    earned: number;
    spent: number;
    balance: number;
  }[];
  metrics: {
    totalMembers: number;
    activeMembers: number;
    newMembers: number;
    engagementRate: number;
    totalPoints: number;
    averagePoints: number;
  };
  comparison?: {
    totalMembers: number;
    activeMembers: number;
    newMembers: number;
    engagementRate: number;
    totalMembersChange: number;
    activeMembersChange: number;
    newMembersChange: number;
    engagementRateChange: number;
  };
}

// Define properly typed interfaces for data records
interface MemberRecord extends Record<string, unknown> {
  member_id: string;
  name: string;
  email: string;
  points_balance: number;
  tier: string;
  join_date: string;
  last_activity: string;
}

interface TierRecord extends Record<string, unknown> {
  tier: string;
  count: number;
  percentage: number;
}

interface LocationRecord extends Record<string, unknown> {
  region: string;
  count: number;
  percentage: number;
}

export default function MembersDashboard() {
  const { filters, getStartDate, getEndDate, getPreviousStartDate, getPreviousEndDate } = useReportContext();

  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<MembersData | null>(null);

  // Fetch data from the API
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Construct query parameters for the API call
        const params = new URLSearchParams({
          startDate: getStartDate(),
          endDate: getEndDate(),
          showComparison: filters.showComparison ? 'true' : 'false',
        });

        if (filters.showComparison) {
          params.append('prevStartDate', getPreviousStartDate());
          params.append('prevEndDate', getPreviousEndDate());
        }

        // Fetch data from the API
        const response = await fetch(`/api/reports/members?${params}`);
        if (!response.ok) {
          throw new Error('Failed to fetch members data');
        }

        const result = await response.json();
        setData(result);
      } catch (error) {
        console.error('Error fetching members data:', error);
        // Handle error appropriately
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filters, getStartDate, getEndDate, getPreviousStartDate, getPreviousEndDate]);

  // Format numbers for display
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat().format(value);
  };

  // Format percentage values
  const formatPercent = (value: number) => {
    return `${value}%`;
  };

  // Columns for top members table
  const membersColumns: ColumnDef<MemberRecord>[] = [
    {
      accessorKey: "name",
      header: "Name",
    },
    {
      accessorKey: "email",
      header: "Email",
    },
    {
      accessorKey: "points_balance",
      header: "Points Balance",
      cell: ({ row }) => formatNumber(row.original.points_balance),
    },
    {
      accessorKey: "tier",
      header: "Tier",
    },
    {
      accessorKey: "join_date",
      header: "Join Date",
      cell: ({ row }) => format(new Date(row.original.join_date), 'MMM d, yyyy'),
    },
    {
      accessorKey: "last_activity",
      header: "Last Activity",
      cell: ({ row }) => {
        const date = new Date(row.original.last_activity);
        return format(date, 'MMM d, yyyy') + ' ' + format(date, 'hh:mm a');
      },
    },
  ];

  // Columns for location distribution table
  const locationColumns: ColumnDef<LocationRecord>[] = [
    {
      accessorKey: "region",
      header: "Region",
    },
    {
      accessorKey: "count",
      header: "Members",
      cell: ({ row }) => formatNumber(Number(row.original.count)),
    },
    {
      accessorKey: "percentage",
      header: "Percentage",
      cell: ({ row }) => formatPercent(Number(row.original.percentage)),
    },
  ];

  // Columns for tier distribution table
  const tierColumns: ColumnDef<TierRecord>[] = [
    {
      accessorKey: "tier",
      header: "Tier",
    },
    {
      accessorKey: "count",
      header: "Members",
      cell: ({ row }) => formatNumber(Number(row.original.count)),
    },
    {
      accessorKey: "percentage",
      header: "Percentage",
      cell: ({ row }) => formatPercent(Number(row.original.percentage)),
    },
  ];

  // Get KPI metrics for display
  const getKpiMetrics = (): KpiData[] => {
    if (isLoading || !data?.metrics) {
      return [];
    }

    return [
      {
        title: 'Total Members',
        value: formatNumber(data.metrics.totalMembers),
        change: data.comparison?.totalMembersChange,
        changeLabel: data.comparison?.totalMembersChange && data.comparison.totalMembersChange > 0 ? 'increase' : 'decrease',
      },
      {
        title: 'Active Members',
        value: formatNumber(data.metrics.activeMembers),
        change: data.comparison?.activeMembersChange,
        changeLabel: data.comparison?.activeMembersChange && data.comparison.activeMembersChange > 0 ? 'increase' : 'decrease',
      },
      {
        title: 'New Members',
        value: formatNumber(data.metrics.newMembers),
        change: data.comparison?.newMembersChange,
        changeLabel: data.comparison?.newMembersChange && data.comparison.newMembersChange > 0 ? 'increase' : 'decrease',
      },
      {
        title: 'Engagement Rate',
        value: formatPercent(data.metrics.engagementRate),
        change: data.comparison?.engagementRateChange,
        changeLabel: data.comparison?.engagementRateChange && data.comparison.engagementRateChange > 0 ? 'increase' : 'decrease',
      },
    ];
  };

  return (
    <div className="space-y-6">
      {/* KPI Summary Cards */}
      <KpiSummary
        metrics={getKpiMetrics()}
        showComparison={filters.showComparison}
      />

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartSection
          title="Member Acquisition"
          description="New member sign-ups over time"
          downloadable
        >
          {/* Replace with actual chart component */}
          <div className="h-[350px] flex items-center justify-center text-muted-foreground border rounded">
            <div className="text-center">
              <Calendar className="mx-auto h-8 w-8 mb-2" />
              <p>Member Acquisition Chart</p>
              <p className="text-sm">
                Data points: {data?.membersOverTime?.length ?? 0}
              </p>
            </div>
          </div>
        </ChartSection>

        <ChartSection
          title="Member Engagement"
          description="Active vs total members over time"
          downloadable
        >
          {/* Replace with actual chart component */}
          <div className="h-[350px] flex items-center justify-center text-muted-foreground border rounded">
            <div className="text-center">
              <TrendingUp className="mx-auto h-8 w-8 mb-2" />
              <p>Member Engagement Chart</p>
              <p className="text-sm">
                Data points: {data?.pointsActivity?.length ?? 0}
              </p>
            </div>
          </div>
        </ChartSection>
      </div>

      {/* Tables */}
      <div className="grid gap-6 md:grid-cols-2">
        <ChartSection
          title="Tier Distribution"
          description="Members by loyalty tier"
          actions={
            <ExportControls
              onExportCsv={() => console.log('Exporting tier data CSV')}
            />
          }
        >
          <DataTable
            columns={tierColumns}
            data={data?.tierDistribution || []}
            searchKey="tier"
          />
        </ChartSection>

        <ChartSection
          title="Location Distribution"
          description="Members by geographic region"
          actions={
            <ExportControls
              onExportCsv={() => console.log('Exporting location data CSV')}
            />
          }
        >
          {/* Location map or table */}
          <div className="mb-4 h-[200px] flex items-center justify-center text-muted-foreground border rounded">
            <div className="text-center">
              <MapPin className="mx-auto h-8 w-8 mb-2" />
              <p>Location Distribution Map</p>
            </div>
          </div>

          <DataTable
            columns={locationColumns}
            data={data?.locationDistribution || []}
            searchKey="region"
          />
        </ChartSection>
      </div>

      {/* Top Members Table */}
      <ChartSection
        title="Top Members"
        description="Members with highest point balances"
        actions={
          <ExportControls
            onExportCsv={() => console.log('Exporting top members CSV')}
            onExportExcel={() => console.log('Exporting top members Excel')}
          />
        }
      >
        <DataTable
          columns={membersColumns}
          data={data?.topMembers || []}
          searchKey="member_id"
        />
      </ChartSection>
    </div>
  );
}