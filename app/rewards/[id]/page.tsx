'use client'

import { useState, useEffect, useCallback } from 'react'
import { use } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import { format } from 'date-fns'
import { useCompany } from '@/contexts/company-context'
import { ArrowLeft, CalendarIcon, CheckCircleIcon, XCircleIcon, Users, Clock, Edit, Award } from 'lucide-react'

type Reward = {
  id: string
  title: string
  description: string
  points_required: number
  is_active: boolean
  created_at: string
  expiration_date: string | null
}

type RedemptionHistory = {
  id: string
  memberName: string
  redemption_date: string
  points_spent: number
}

// Types for API responses
interface APIReward {
  id: string
  title: string
  description: string
  points_required: number
  is_active: boolean
  expiration_date: string | null
  created_at: string
}

interface APIRedemption {
  id: string
  points_used: number
  redemption_date: string
  member?: { name: string } | null
}

export default function RewardDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const unwrappedParams = use(params)
  const { id } = unwrappedParams

  const { company } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [loading, setLoading] = useState(true)
  const [reward, setReward] = useState<Reward | null>(null)
  const [redemptions, setRedemptions] = useState<RedemptionHistory[]>([])
  const [totalRedemptionCount, setTotalRedemptionCount] = useState(0)
  const router = useRouter()

  // Handle hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  const fetchReward = useCallback(async (rewardId: string) => {
    if (!company) return
    setLoading(true)
    try {
      // Call internal API route for reward data
      const res = await fetch(`/api/rewards?companyId=${company.id}`)
      if (!res.ok) throw new Error('Failed to fetch reward')
      const json = await res.json()
      const items = (json.data || []) as APIReward[]
      const item = items.find(r => r.id === rewardId)
      if (!item) {
        router.replace('/rewards')
        return
      }
      setReward({
        id: item.id,
        title: item.title || '',
        description: item.description || '',
        points_required: item.points_required,
        is_active: item.is_active,
        expiration_date: item.expiration_date,
        created_at: item.created_at
      })
    } catch (err) {
      console.error('Error fetching reward:', err)
      router.replace('/rewards')
    } finally {
      setLoading(false)
    }
  }, [company, router])

  const fetchRedemptionCount = useCallback(async (rewardId: string) => {
    if (!company) return
    try {
      // Use the dedicated count endpoint to get total redemptions
      const res = await fetch(`/api/redemptions/count?companyId=${company.id}&rewardId=${rewardId}`)
      if (!res.ok) throw new Error('Failed to fetch redemption count')
      const json = await res.json()
      setTotalRedemptionCount(json.count || 0)
    } catch (err) {
      console.error('Error fetching redemption count:', err)
      setTotalRedemptionCount(0)
    }
  }, [company])

  const fetchRedemptionHistory = useCallback(async (rewardId: string) => {
    if (!company) return
    try {
      const res = await fetch(`/api/redemptions?companyId=${company.id}&rewardId=${rewardId}&limit=5`)
      if (!res.ok) throw new Error('Failed to fetch redemptions')
      const json = await res.json()
      const rows = (json.data || []) as APIRedemption[]
      // Map API response to UI model
      const history: RedemptionHistory[] = rows.map(r => ({
        id: r.id,
        memberName: r.member?.name || 'Unknown Member',
        redemption_date: r.redemption_date,
        points_spent: r.points_used
      }))
      setRedemptions(history)
    } catch (err) {
      console.error('Error fetching redemption history:', err)
      setRedemptions([])
    }
  }, [company])

  useEffect(() => {
    if (mounted && company && id) {
      fetchReward(id)
      fetchRedemptionHistory(id)
      fetchRedemptionCount(id)
    }
  }, [mounted, company, id, fetchReward, fetchRedemptionHistory, fetchRedemptionCount])

  if (!mounted) {
    return null // Prevent hydration mismatch
  }

  if (loading || !reward) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="pt-6 flex justify-center items-center min-h-[200px]">
            <p>Loading reward details...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const isExpired = reward.expiration_date && new Date(reward.expiration_date) < new Date()

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Reward Details</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            View and manage information for this reward
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Button variant="outline" size="sm" onClick={() => router.push('/rewards')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Rewards
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main reward details card */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-2">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <div className="mb-2 sm:mb-0">
                <CardTitle className="text-xl">{reward.title}</CardTitle>
                <CardDescription className="mt-1">{reward.description}</CardDescription>
              </div>
              <div className="flex space-x-2">
                {reward.is_active ? (
                  <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>
                ) : (
                  <Badge variant="outline" className="text-gray-500 border-gray-500">Inactive</Badge>
                )}
                {isExpired && <Badge variant="destructive">Expired</Badge>}
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            {/* Key metrics */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              <div className="bg-muted/40 p-4 rounded-lg border border-border flex items-center space-x-3">
                <div className="bg-primary/10 p-2 rounded-full">
                  <Award className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Points Required</p>
                  <p className="text-2xl font-semibold">{reward.points_required}</p>
                </div>
              </div>

              <div className="bg-muted/40 p-4 rounded-lg border border-border flex items-center space-x-3">
                <div className="bg-blue-500/10 p-2 rounded-full">
                  <Users className="h-5 w-5 text-blue-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Total Redemptions</p>
                  <p className="text-2xl font-semibold">{totalRedemptionCount}</p>
                </div>
              </div>

              <div className="bg-muted/40 p-4 rounded-lg border border-border flex items-center space-x-3">
                <div className="bg-orange-500/10 p-2 rounded-full">
                  <CalendarIcon className="h-5 w-5 text-orange-500" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Expiry Date</p>
                  <p className="text-xl font-semibold">
                    {reward.expiration_date
                      ? format(new Date(reward.expiration_date), 'MMM d, yyyy')
                      : 'No expiry'}
                  </p>
                </div>
              </div>
            </div>

            <Separator className="my-6" />

            {/* Additional reward details */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Reward Details</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-4 gap-x-6">
                <div>
                  <p className="text-sm text-muted-foreground">Created On</p>
                  <p className="font-medium">{format(new Date(reward.created_at), 'MMM d, yyyy')}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Status</p>
                  <div className="flex items-center">
                    {reward.is_active ? (
                      <>
                        <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                        <span>Active</span>
                      </>
                    ) : (
                      <>
                        <XCircleIcon className="h-4 w-4 text-gray-500 mr-1" />
                        <span>Inactive</span>
                      </>
                    )}
                  </div>
                </div>
                {/* Add other reward properties as needed */}
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between pt-6">
            <Button
              variant="outline"
              onClick={() => router.push('/rewards')}
            >
              Back
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline"
                asChild
              >
                <Link href={`/rewards/${id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Reward
                </Link>
              </Button>
              <Button
                variant={reward.is_active ? "default" : "secondary"}
                disabled={!reward.is_active || !!isExpired}
              >
                Record Redemption
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* Recent redemptions card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              Recent Redemptions
            </CardTitle>
            <CardDescription>
              Members who have redeemed this reward
            </CardDescription>
          </CardHeader>
          <CardContent>
            {redemptions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground bg-muted/20 rounded-md border border-border">
                No redemptions recorded yet
              </div>
            ) : (
              <div className="space-y-4">
                {redemptions.slice(0, 5).map((redemption) => (
                  <div
                    key={redemption.id}
                    className="p-3 bg-muted/30 rounded-lg border border-border hover:bg-muted/40 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">{redemption.memberName}</p>
                        <p className="text-sm text-muted-foreground">
                          {format(new Date(redemption.redemption_date), 'MMM d, yyyy h:mm a')}
                        </p>
                      </div>
                      <Badge variant="secondary">{redemption.points_spent} pts</Badge>
                    </div>
                  </div>
                ))}

                {redemptions.length > 5 && (
                  <div className="text-center pt-2">
                    <Button variant="link" asChild>
                      <Link href={`/rewards/${id}/redemptions`}>
                        View all {redemptions.length} redemptions
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
