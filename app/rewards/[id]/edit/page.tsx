'use client'

import { useState, useEffect, useRef } from 'react'
import { use } from 'react'
import { useReward, useUpdateReward } from '@/hooks/use-rewards'
import { useUploadRewardImage, useDeleteRewardImage } from '@/hooks/use-reward-image'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { useForm, SubmitHandler } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import { Upload, X } from 'lucide-react'
import Image from 'next/image'

const rewardSchema = z.object({
  name: z.string().min(3, { message: 'Name must be at least 3 characters' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  pointsCost: z.number().min(1, { message: 'Points cost must be at least 1' }),
  isActive: z.boolean(),
  expiresAt: z.string().optional().nullable(),
  redemptionCount: z.number().optional(),
})

type RewardFormValues = z.infer<typeof rewardSchema>

export default function EditRewardPage({ params }: { params: Promise<{ id: string }> }) {
  const unwrappedParams = use(params);
  const [mounted, setMounted] = useState(false)
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const router = useRouter()
  const { id } = unwrappedParams
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Use hooks for data fetching and mutations
  const { data: reward, isLoading: loading } = useReward(id)
  const updateRewardMutation = useUpdateReward()
  const uploadImageMutation = useUploadRewardImage()
  const deleteImageMutation = useDeleteRewardImage()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<RewardFormValues>({
    resolver: zodResolver(rewardSchema),
    defaultValues: {
      name: '',
      description: '',
      pointsCost: 100,
      isActive: true,
      expiresAt: null,
      redemptionCount: 0,
    },
  })

  const isActive = watch('isActive')  // Handle image file selection
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('=== handleImageSelect called ===')
    const file = event.target.files?.[0]
    console.log('Selected file:', file)
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
      if (!allowedTypes.includes(file.type)) {
        toast.error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.')
        return
      }

      // Validate file size (5MB max)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        toast.error('File too large. Maximum size is 5MB.')
        return
      }

      setSelectedImage(file)

      // Create preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        console.log('Image preview created')
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  // Handle image upload
  const handleImageUpload = async () => {
    console.log('=== handleImageUpload called ===')
    console.log('selectedImage:', selectedImage)
    if (!selectedImage) return

    try {
      console.log('Calling uploadImageMutation.mutateAsync with:', { rewardId: id, file: selectedImage })
      await uploadImageMutation.mutateAsync({
        rewardId: id,
        file: selectedImage
      })
      toast.success('Image uploaded successfully')
      setSelectedImage(null)
      setImagePreview(null)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image')
    }
  }

  // Handle image delete
  const handleImageDelete = async () => {
    try {
      await deleteImageMutation.mutateAsync({
        rewardId: id
      })
      toast.success('Image removed successfully')
    } catch (error) {
      console.error('Error deleting image:', error)
      toast.error('Failed to remove image')
    }
  }

  // Clear image selection
  const clearImageSelection = () => {
    setSelectedImage(null)
    setImagePreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Handle hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Update form when reward data is loaded
  useEffect(() => {
    if (reward) {
      // Format date for input field (YYYY-MM-DD)
      const formattedExpiryDate = reward.expiration_date
        ? new Date(reward.expiration_date).toISOString().split('T')[0]
        : null

      // Reset form with fetched data
      reset({
        name: reward.title,
        description: reward.description,
        pointsCost: reward.points_required,
        isActive: reward.is_active,
        expiresAt: formattedExpiryDate,
        redemptionCount: 0, // Not tracking this for now
      })
    }
  }, [reward, reset])

  const onSubmit: SubmitHandler<RewardFormValues> = async (data) => {
    try {
      // Update using the mutation hook
      await updateRewardMutation.mutateAsync({
        rewardId: id,
        rewardData: {
          name: data.name,
          description: data.description,
          points_cost: data.pointsCost,
          active: data.isActive,
          title: data.name,
          points_required: data.pointsCost,
          is_active: data.isActive,
        }
      })

      toast.success('Reward updated successfully')
      router.push('/rewards')
    } catch (error) {
      console.error('Error updating reward:', error)
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      toast.error('Error updating reward', {
        description: errorMessage
      })
    }
  }

  if (!mounted) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <p>Loading...</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-loyal-black">
        <Card className="card w-96 text-center">
          <CardContent className="pt-6">
            <p>Loading reward details...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col p-4 bg-loyal-black">
      <header className="flex justify-between items-center mb-8 p-4 bg-loyal-charcoal rounded-xl shadow-gold">
        <h1 className="text-2xl font-bold gold-gradient-text">Edit Reward</h1>
        <Button className="btn-outline" onClick={() => router.push('/rewards')}>
          Back to Rewards
        </Button>
      </header>

      <Card className="card-premium max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-white">Edit Reward</CardTitle>
          <CardDescription>
            Update the details of your loyalty reward
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Reward Name</Label>
              <Input
                id="name"
                className="input"
                placeholder="e.g., Free Coffee"
                {...register('name')}
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                className="input min-h-[100px]"
                placeholder="Describe what members will get with this reward"
                {...register('description')}
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
              )}
            </div>

            {/* Image Upload Section */}
            <div className="space-y-4">
              <Label>Reward Image</Label>

              {/* Current Image Display */}
              {reward?.reward_image_url && !imagePreview && (
                <div className="space-y-2">
                  <div className="relative inline-block">
                    <Image
                      src={reward.reward_image_url}
                      alt="Current reward image"
                      width={128}
                      height={128}
                      className="w-32 h-32 object-cover rounded-lg border border-gray-600"
                      unoptimized // For external Supabase URLs
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                      onClick={handleImageDelete}
                      disabled={deleteImageMutation.isPending}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-sm text-gray-400">Current reward image</p>
                </div>
              )}

              {/* Image Preview */}
              {imagePreview && (
                <div className="space-y-2">
                  <div className="relative inline-block">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src={imagePreview}
                      alt="Image preview"
                      className="w-32 h-32 object-cover rounded-lg border border-gray-600"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                      onClick={clearImageSelection}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      onClick={handleImageUpload}
                      disabled={uploadImageMutation.isPending}
                      className="btn-primary"
                    >
                      {uploadImageMutation.isPending ? 'Uploading...' : 'Upload Image'}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={clearImageSelection}
                      className="btn-outline"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}

              {/* File Input */}
              {!imagePreview && (
                <div className="space-y-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/jpeg,image/jpg,image/png,image/webp"
                    onChange={handleImageSelect}
                    className="hidden"
                    id="image-upload"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    className="btn-outline flex items-center gap-2"
                  >
                    <Upload className="h-4 w-4" />
                    {reward?.reward_image_url ? 'Change Image' : 'Upload Image'}
                  </Button>
                  <p className="text-xs text-gray-400">
                    Supported formats: JPEG, PNG, WebP. Max size: 5MB.
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pointsCost">Points Cost</Label>
              <Input
                id="pointsCost"
                className="input"
                type="number"
                min="1"
                placeholder="100"
                {...register('pointsCost', { valueAsNumber: true })}
              />
              {errors.pointsCost && (
                <p className="text-red-500 text-sm mt-1">{errors.pointsCost.message}</p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="isActive">Active Status</Label>
                <p className="text-sm text-gray-400">
                  {isActive ? 'This reward is active and can be redeemed' : 'This reward is inactive and cannot be redeemed'}
                </p>
              </div>
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked: boolean) => setValue('isActive', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="expiresAt">Expiry Date (Optional)</Label>
              <Input
                id="expiresAt"
                className="input"
                type="date"
                {...register('expiresAt')}
              />
              <p className="text-sm text-gray-400">
                Leave blank if the reward does not expire
              </p>
            </div>

            {reward && (
              <div className="p-4 bg-loyal-charcoal/50 rounded-lg">
                <p className="text-sm text-gray-400">
                  This reward has been redeemed {reward.redemptionCount} times since creation on {new Date(reward.created_at).toLocaleDateString()}.
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              className="btn-outline"
              onClick={() => router.push('/rewards')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="btn-primary"
              disabled={updateRewardMutation.isPending}
            >
              {updateRewardMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
