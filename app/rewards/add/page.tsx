'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useRouter } from 'next/navigation'
import { useCompany } from '@/contexts/company-context'
import { useCreateReward } from '@/hooks/use-rewards'
import { useTiers } from '@/app/tiers/hooks/use-tiers'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { useForm, SubmitHandler, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Circle } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

// Reward code pattern: 2 uppercase letters followed by 2 digits
const codePattern = /^[A-Z]{2}\d{2}$/

const rewardSchema = z.object({
  title: z.string().min(3, { message: 'Title must be at least 3 characters' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
  code: z.string().regex(codePattern, { message: 'Code must be 2 uppercase letters followed by 2 digits (e.g., AB12)' }),
  rewardType: z.enum(['SEASONAL', 'HOLIDAY', 'BIRTHDAY', 'GENERAL', 'LIMITED'], {
    required_error: 'Please select a reward type'
  }),
  rewardValueType: z.enum(['PERCENTAGE', 'FIXED_AMOUNT', 'FREE_SERVICE', 'POINTS_BONUS', 'PRODUCT_GIFT', 'DOUBLE_POINTS'], {
    required_error: 'Please select a reward value type'
  }),
  rewardValue: z.number()
    .min(1, { message: 'Reward value must be at least 1' }),
  pointsRequired: z.number().min(0, { message: 'Points required must be at least 0' }),
  isActive: z.boolean(),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  expirationDate: z.string().min(1, { message: 'Expiration date is required' }),
  eligibleTiers: z.array(z.string()).optional(), // Made optional since eligible_tiers column doesn't exist
  // Optional image file
  image: z.instanceof(File).optional(),
})

type RewardFormValues = z.infer<typeof rewardSchema>

export default function AddRewardPage() {
  const { user, isLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [codeExists, setCodeExists] = useState(false)
  const [codeChecking, setCodeChecking] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const router = useRouter()

  // Use hooks for data fetching and mutations
  const createRewardMutation = useCreateReward()
  const { data: tiersData, isLoading: tiersLoading } = useTiers()

  // Debouncing timer ref for code checking
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Get current date in YYYY-MM-DD format for default values
  const today = new Date().toISOString().split('T')[0]
  const oneMonthLater = new Date()
  oneMonthLater.setMonth(oneMonthLater.getMonth() + 1)
  const defaultExpirationDate = oneMonthLater.toISOString().split('T')[0]

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
    trigger,
  } = useForm<RewardFormValues>({
    resolver: zodResolver(rewardSchema),
    defaultValues: {
      title: '',
      description: '',
      code: '',
      rewardType: 'GENERAL',
      rewardValueType: 'PERCENTAGE',
      rewardValue: 10,
      pointsRequired: 100,
      isActive: true,
      startDate: today,
      expirationDate: defaultExpirationDate,
      eligibleTiers: [],
    },
  })
  
  // Watch the reward type to show birthday guidance when appropriate
  const rewardType = watch('rewardType')

  const isActive = watch('isActive')
  const rewardValueType = watch('rewardValueType')
  const code = watch('code')

  // Handle hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !isLoading && !user) {
      router.push('/login')
    }
  }, [mounted, isLoading, user, router])

  // Check if code exists (with debounce) - stable function that doesn't depend on mutation
  const checkCodeExists = useCallback(async (codeToCheck: string) => {
    if (!codeToCheck || !codePattern.test(codeToCheck) || !company?.id) {
      return false;
    }

    setCodeChecking(true);

    try {
      const response = await fetch(`/api/rewards/check-code?code=${encodeURIComponent(codeToCheck)}&companyId=${company.id}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        console.warn('Failed to check reward code:', response.statusText);
        return false;
      }

      const result = await response.json();
      const exists = result.exists;
      setCodeExists(exists);
      return exists;
    } catch (error) {
      console.error('Error checking code:', error);
      return false;
    } finally {
      setCodeChecking(false);
    }
  }, [company?.id]); // Only depend on company ID, not the mutation

  // Debounced code checking effect
  useEffect(() => {
    // Clear any existing timeout
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    if (code && codePattern.test(code)) {
      // Set a new timeout for debounced checking
      debounceTimerRef.current = setTimeout(() => {
        checkCodeExists(code);
      }, 500); // 500ms debounce
    } else {
      setCodeExists(false);
    }

    // Cleanup timeout on unmount or code change
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [code, checkCodeExists]);

  // Generate a random reward code
  const generateRewardCode = async () => {
    if (!company?.id) {
      toast.error('Company not found');
      return;
    }

    let code = '';
    let isUnique = false;

    setCodeChecking(true);

    try {
      while (!isUnique) {
        // Generate 2 random uppercase letters
        const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const letter1 = letters.charAt(Math.floor(Math.random() * letters.length));
        const letter2 = letters.charAt(Math.floor(Math.random() * letters.length));

        // Generate 2 random digits
        const digit1 = Math.floor(Math.random() * 10);
        const digit2 = Math.floor(Math.random() * 10);

        code = `${letter1}${letter2}${digit1}${digit2}`;

        // Check if the code exists in the database using direct fetch
        const response = await fetch(`/api/rewards/check-code?code=${encodeURIComponent(code)}&companyId=${company.id}`, {
          credentials: 'include'
        });

        if (!response.ok) {
          throw new Error('Failed to check code uniqueness');
        }

        const result = await response.json();
        const exists = result.exists;

        if (!exists) {
          isUnique = true;
        }
      }

      setValue('code', code);
      trigger('code');
    } catch (error) {
      console.error('Error generating code:', error);
      toast.error('Failed to generate a unique code. Please try again.');
    } finally {
      setCodeChecking(false);
    }
  };

  const onSubmit: SubmitHandler<RewardFormValues> = async (data) => {
    try {
      // Check if code exists
      const exists = await checkCodeExists(data.code)
      if (exists) {
        toast.error('Code already exists', {
          description: 'Please choose a different code for your reward.'
        })
        return
      }

      // Check if company exists
      if (!company) {
        toast.error('No company selected. Please create a company first.')
        return
      }

      // Prepare reward data with all required fields matching database schema
      const rewardData = {
        title: data.title,
        description: data.description,
        code: data.code,
        reward_code: data.code, // Database has both code and reward_code fields
        reward_type: data.rewardType,
        reward_value_type: data.rewardValueType,
        reward_value: data.rewardValue,
        points_required: data.pointsRequired,
        is_active: data.isActive,
        start_date: new Date(data.startDate).toISOString(),
        expiration_date: new Date(data.expirationDate).toISOString(),
      };

      // Create reward first
      const createdReward = await createRewardMutation.mutateAsync(rewardData);

      // If user uploaded an image, upload it after reward creation
      if (selectedImage && createdReward?.id) {
        try {
          const formData = new FormData();
          formData.append('file', selectedImage);
          formData.append('rewardId', createdReward.id);
          formData.append('companyId', company.id);

          const uploadResponse = await fetch('/api/upload/reward-image', {
            method: 'POST',
            body: formData,
          });

          if (!uploadResponse.ok) {
            console.error('Image upload failed, but reward was created');
            toast.warning('Reward created but image upload failed', {
              description: 'You can upload an image later by editing the reward.'
            });
          } else {
            toast.success('Reward created with image', {
              description: `"${data.title}" has been created successfully with your image.`
            });
          }
        } catch (imageError) {
          console.error('Error uploading image:', imageError);
          toast.warning('Reward created but image upload failed', {
            description: 'You can upload an image later by editing the reward.'
          });
        }
      } else {
        toast.success('Reward created', {
          description: `"${data.title}" has been created successfully.`
        });
      }

      // Redirect to rewards list
      router.push('/rewards');
    } catch (error: unknown) {
      console.error('Error creating reward:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      toast.error('Error creating reward', {
        description: errorMessage
      });
    }
  };

  // Before showing form, ensure company exists
  if (!companyLoading && !company) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">No Company Account Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a company profile before adding rewards to your loyalty program.</p>
        <Button onClick={() => router.push('/company/create')}>
          Create Company Profile
        </Button>
      </div>
    )
  }

  if (companyLoading || !mounted || isLoading || tiersLoading) {
    // still loading company check or auth
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-background">
        <div className="animate-shimmer w-48 h-12 rounded-lg"></div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect in the effect
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4 gap-2 text-muted-foreground hover:text-foreground"
          asChild
        >
          <Link href="/rewards">
            <ArrowLeft className="h-4 w-4" />
            Back to Rewards
          </Link>
        </Button>
      </div>
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Add New Reward</CardTitle>
            <CardDescription>
              Create a new reward for your loyalty program members
            </CardDescription>
          </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title">Reward Title</Label>
              <Input
                id="title"
                placeholder="e.g., Free Coffee"
                {...register('title')}
              />
              {errors.title && (
                <p className="text-destructive text-sm mt-1">{errors.title.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                className="min-h-[100px]"
                placeholder="Describe what members will get with this reward"
                {...register('description')}
              />
              {errors.description && (
                <p className="text-destructive text-sm mt-1">{errors.description.message}</p>
              )}
            </div>

            {/* Image Upload Section */}
            <div className="space-y-2">
              <Label htmlFor="image">Reward Image (Optional)</Label>
              <div className="flex flex-col space-y-4">
                {/* Image Preview */}
                {imagePreview && (
                  <div className="relative w-full h-48 rounded-lg overflow-hidden border">
                    <Image
                      src={imagePreview}
                      alt="Reward preview"
                      fill
                      className="object-cover"
                      unoptimized={true}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setImagePreview(null)
                        setSelectedImage(null)
                        setValue('image', undefined)
                      }}
                      className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 z-10"
                    >
                      ✕
                    </button>
                  </div>
                )}

                {/* File Input */}
                <input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) {
                      setSelectedImage(file)
                      setValue('image', file)

                      // Create preview
                      const reader = new FileReader()
                      reader.onload = (e) => {
                        setImagePreview(e.target?.result as string)
                      }
                      reader.readAsDataURL(file)
                    }
                  }}
                  className="w-full p-2 border rounded-md"
                />
                <p className="text-sm text-muted-foreground">
                  Upload an image for your reward. If no image is uploaded, a beautiful color scheme will be used as default.
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="code">Reward Code</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={generateRewardCode}
                  disabled={codeChecking}
                >
                  {codeChecking ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    'Generate Code'
                  )}
                </Button>
              </div>
              <Input
                id="code"
                placeholder="e.g., AB12"
                {...register('code')}
                className={codeExists ? 'border-destructive' : ''}
              />
              {errors.code && (
                <p className="text-destructive text-sm mt-1">{errors.code.message}</p>
              )}
              {codeExists && !errors.code && (
                <p className="text-destructive text-sm mt-1">This code already exists. Please try another.</p>
              )}
              {codeChecking && !errors.code && !codeExists && (
                <p className="text-muted-foreground text-sm mt-1">Checking code availability...</p>
              )}
              <p className="text-muted-foreground text-sm">
                Code must be 2 uppercase letters followed by 2 digits (e.g., AB12)
              </p>
            </div>

            {rewardType === 'BIRTHDAY' && (
              <div className="col-span-full mt-4 mb-2">
                <div className="bg-pink-50 border border-pink-200 rounded-lg p-6 relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                    <div className="w-full h-full relative">
                      <div className="absolute inset-0 bg-pink-300 rounded-full transform translate-x-1/2 -translate-y-1/2"></div>
                      <div className="absolute inset-0 bg-pink-400 rounded-full transform -translate-x-1/3 translate-y-1/3"></div>
                      <div className="absolute inset-0 bg-pink-500 rounded-full transform translate-x-1/4 translate-y-1/4"></div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-pink-100 p-2 rounded-full">
                      <Cake className="h-6 w-6 text-pink-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-pink-700">Birthday Rewards Guide</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-pink-700 mb-2">How Birthday Rewards Work</h4>
                      <p className="text-sm text-pink-700 mb-3">
                        Birthday rewards are automatically restricted to members during their birthday period (±7 days from their birthday).
                        Only members with connected Telegram accounts will receive notifications when they become eligible.
                      </p>
                      
                      <div className="bg-white p-4 rounded-lg border border-pink-200 mb-3">
                        <p className="text-sm font-medium text-pink-700 mb-2">Telegram Integration</p>
                        <p className="text-sm text-pink-600">
                          Members interact with your loyalty program exclusively through Telegram. The chatbot will automatically:
                        </p>
                        <ul className="text-sm text-pink-600 list-disc pl-5 mt-1 space-y-1">
                          <li>Notify members when they become eligible for birthday rewards</li>
                          <li>Allow them to view and redeem available birthday rewards</li>
                          <li>Guide them through the redemption process</li>
                        </ul>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-pink-700 mb-2">Best Practices</h4>
                      <div className="bg-white p-4 rounded-lg border border-pink-200">
                        <ul className="text-sm text-pink-600 list-disc pl-5 space-y-2">
                          <li><span className="font-medium">Make it special:</span> Create rewards that make members feel valued on their birthday</li>
                          <li><span className="font-medium">Clear value:</span> Use a compelling reward value that feels like a genuine birthday gift</li>
                          <li><span className="font-medium">Low barrier:</span> Consider requiring fewer points for birthday rewards to increase redemption</li>
                          <li><span className="font-medium">Personalization:</span> Include the member&apos;s name in the reward description for the Telegram bot to use</li>
                        </ul>
                      </div>
                      
                      <div className="bg-pink-100 p-3 rounded-lg mt-3 border border-pink-200">
                        <div className="flex items-center gap-2">
                          <HelpCircle className="h-4 w-4 text-pink-500" />
                          <p className="text-sm font-medium text-pink-700">Need Help?</p>
                        </div>
                        <p className="text-xs text-pink-600 mt-1">
                          The system will automatically handle eligibility checking when members attempt to redeem birthday rewards through the Telegram bot.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="rewardType">Reward Type</Label>
                <Controller
                  name="rewardType"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select reward type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="GENERAL">General</SelectItem>
                        <SelectItem value="SEASONAL">Seasonal</SelectItem>
                        <SelectItem value="HOLIDAY">Holiday</SelectItem>
                        <SelectItem value="BIRTHDAY">Birthday</SelectItem>
                        <SelectItem value="LIMITED">Limited Time</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.rewardType && (
                  <p className="text-destructive text-sm mt-1">{errors.rewardType.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="rewardValueType">Reward Value Type</Label>
                <Controller
                  name="rewardValueType"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select reward value type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="PERCENTAGE">Percentage Off (%)</SelectItem>
                        <SelectItem value="FIXED_AMOUNT">Fixed Amount (Birr)</SelectItem>
                        <SelectItem value="FREE_SERVICE">Free Service</SelectItem>
                        <SelectItem value="POINTS_BONUS">Points Bonus</SelectItem>
                        <SelectItem value="PRODUCT_GIFT">Product Gift</SelectItem>
                        <SelectItem value="DOUBLE_POINTS">Double Points Multiplier</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.rewardValueType && (
                  <p className="text-destructive text-sm mt-1">{errors.rewardValueType.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="rewardValue">
                {rewardValueType === 'PERCENTAGE' ? 'Percentage Off (%)' :
                 rewardValueType === 'FIXED_AMOUNT' ? 'Amount Off (Birr)' :
                 rewardValueType === 'POINTS_BONUS' ? 'Bonus Points' :
                 rewardValueType === 'DOUBLE_POINTS' ? 'Points Multiplier (2x = 2.0)' : 'Value'}
              </Label>
              <Input
                id="rewardValue"
                type="number"
                min="1"
                max={rewardValueType === 'PERCENTAGE' ? "100" : undefined}
                placeholder={rewardValueType === 'PERCENTAGE' ? "10" :
                           rewardValueType === 'DOUBLE_POINTS' ? "2.0" : "100"}
                step={rewardValueType === 'DOUBLE_POINTS' ? "0.1" : "1"}
                {...register('rewardValue', { valueAsNumber: true })}
              />
              {rewardValueType === 'DOUBLE_POINTS' && (
                <p className="text-sm text-muted-foreground mt-1">
                  💡 Double Points rewards multiply the points earned on future transactions. Value of 2.0 = double points, 3.0 = triple points, etc.
                </p>
              )}
              {errors.rewardValue && (
                <p className="text-destructive text-sm mt-1">{errors.rewardValue.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="pointsRequired">Points Required</Label>
              <Input
                id="pointsRequired"
                type="number"
                min="0"
                placeholder="100"
                {...register('pointsRequired', { valueAsNumber: true })}
              />
              {rewardValueType === 'DOUBLE_POINTS' && (
                <p className="text-sm text-muted-foreground mt-1">
                  💡 For Double Points rewards, set this to 0 to apply automatically, or set a minimum points requirement for eligibility.
                </p>
              )}
              {errors.pointsRequired && (
                <p className="text-destructive text-sm mt-1">{errors.pointsRequired.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="eligibleTiers">Eligible Tiers</Label>
              <Controller
                name="eligibleTiers"
                control={control}
                render={({ field }) => (
                  <div className="space-y-1">
                    {tiersData && tiersData.map((tier) => (
                      <div key={tier.id} className="flex items-center">
                        <input
                          id={`tier-${tier.id}`}
                          type="checkbox"
                          checked={(field.value || []).includes(tier.id)}
                          onChange={e => {
                            const currentValue = field.value || []
                            const newValue = e.target.checked
                              ? [...currentValue, tier.id]
                              : currentValue.filter(id => id !== tier.id);
                            field.onChange(newValue);
                          }}
                          className="h-4 w-4 rounded border"
                        />
                        <label htmlFor={`tier-${tier.id}`} className="ml-2 text-sm">
                          {tier.tier_name}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              />
              {errors.eligibleTiers && (
                <p className="text-destructive text-sm mt-1">
                  {errors.eligibleTiers.message}
                </p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="isActive">Active Status</Label>
                <p className="text-sm text-muted-foreground">
                  {isActive ? 'This reward is active and can be redeemed' : 'This reward is inactive and cannot be redeemed'}
                </p>
              </div>
              <Switch
                id="isActive"
                checked={isActive}
                onCheckedChange={(checked: boolean) => setValue('isActive', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date</Label>
              <Input
                id="startDate"
                type="date"
                {...register('startDate')}
              />
              {errors.startDate && (
                <p className="text-destructive text-sm mt-1">{errors.startDate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="expirationDate">Expiration Date</Label>
              <Input
                id="expirationDate"
                type="date"
                {...register('expirationDate')}
              />
              {errors.expirationDate && (
                <p className="text-destructive text-sm mt-1">{errors.expirationDate.message}</p>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createRewardMutation.isPending || codeExists || codeChecking}
            >
              {createRewardMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Reward'
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
      </div>
    </div>
  )
}
