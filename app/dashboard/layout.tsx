'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  Users,
  Gift,
  Award,
  CreditCard,
  Settings,
  ChevronLeft,
  ChevronRight,
  Home,
  Menu,
  X,
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useCompany } from '@/contexts/company-context'
import { Skeleton } from '@/components/ui/skeleton'
import { ThemeToggle } from '@/components/ui/theme-toggle'
import { Button } from '@/components/ui/button'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Members', href: '/members', icon: Users },
  { name: 'Rewards', href: '/rewards', icon: Gift },
  { name: 'Tiers', href: '/tiers', icon: Award },
  { name: 'Transactions', href: '/transactions', icon: CreditCard },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [collapsed, setCollapsed] = useState(true) // Default to closed/collapsed
  const [mobileOpen, setMobileOpen] = useState(false)
  const pathname = usePathname()
  const { company, isLoading } = useCompany()

  useEffect(() => {
    setMobileOpen(false)
  }, [pathname])

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 1024 && mobileOpen) setMobileOpen(false)
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [mobileOpen])

  return (
    <div className="flex min-h-[calc(100vh-4rem)] flex-col lg:flex-row">
      {/* Mobile Backdrop */}
      {mobileOpen && (
        <div
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm lg:hidden"
          onClick={() => setMobileOpen(false)}
        />
      )}

      {/* Mobile Header */}
      {/* Mobile header removed to prevent duplicate navbar */}
      {false && (
        <div className="flex h-14 items-center border-b border-border px-4 lg:hidden">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2"
            onClick={() => setMobileOpen(!mobileOpen)}
            aria-label={mobileOpen ? 'Close sidebar' : 'Open sidebar'}
          >
            <Menu className="h-5 w-5" />
          </Button>
          <div className="flex-1">
            {isLoading ? (
              <Skeleton className="h-6 w-32" />
            ) : (
              <div className="flex flex-col">
                <h2 className="font-medium text-sm truncate max-w-[180px]">
                  {company?.name || 'Loading...'}
                </h2>
              </div>
            )}
          </div>
          <ThemeToggle />
        </div>
      )}

      {/* Sidebar */}
      {true && (
        <div
          className={cn(
            "bg-card text-card-foreground border-r border-border flex flex-col transition-all duration-300 z-50",
            collapsed ? "w-[70px]" : "w-64",
            mobileOpen ? "fixed inset-y-0 left-0 animate-slide-in-right" : "hidden lg:flex",
          )}
        >
          <div className="flex h-14 items-center border-b border-border px-4 lg:hidden">
            <Button variant="ghost" size="icon" onClick={() => setMobileOpen(false)} aria-label="Close sidebar">
              <X className="h-5 w-5" />
            </Button>
            <div className="ml-4 flex-1 font-medium">Navigation</div>
          </div>
          <div className="hidden p-4 border-b border-border lg:flex items-center justify-between">
            {!collapsed && (
              <>
                {isLoading ? (
                  <Skeleton className="h-6 w-32" />
                ) : (
                  <div className="flex items-center gap-3">
                    {/* Company Logo */}
                    {company?.logo_url && (
                      <div className="flex items-center justify-center w-8 h-8 rounded-md overflow-hidden bg-muted/30 border flex-shrink-0">
                        <Image
                          src={company!.logo_url!}
                          alt={`${company!.name || 'Company'} logo`}
                          width={32}
                          height={32}
                          className="object-contain"
                        />
                      </div>
                    )}
                    <div className="flex flex-col min-w-0">
                      <h2 className="font-medium text-sm truncate max-w-[180px]">{company?.name || 'Loading...'}</h2>
                      <span className="text-xs text-muted-foreground">{new Date().toLocaleDateString()}</span>
                    </div>
                  </div>
                )}
              </>
            )}
            <button onClick={() => setCollapsed(!collapsed)} className="flex h-8 w-8 items-center justify-center rounded-full text-muted-foreground hover:text-foreground hover:bg-accent transition-colors" aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}>
              {collapsed ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
            </button>
          </div>
          <nav className="flex-1 py-4 overflow-y-auto">
            <ul className="space-y-1 px-2">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <li key={item.name}>
                    <Link href={item.href} className={cn(
                        "flex items-center gap-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200",
                        collapsed && "justify-center px-2",
                        isActive && "text-primary",
                        isActive && !collapsed && "scale-110"
                      )}>
                      <item.icon size={collapsed ? 20 : 18} className={cn("transition-transform duration-200", isActive && "text-primary", isActive && !collapsed && "scale-110")} />
                      {!collapsed && <span className={cn("transition-transform origin-left duration-200", isActive && "scale-105 font-semibold")}>{item.name}</span>}
                      {isActive && !collapsed && <span className="ml-auto h-1.5 w-1.5 rounded-full bg-primary" />}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </nav>
          <div className="p-4 border-t border-border mt-auto">
            <div className="flex items-center justify-between mb-4">{!collapsed && <ThemeToggle />}</div>
            <Link href="/" className={cn(
                "flex items-center gap-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 text-foreground/80 hover:bg-accent hover:text-accent-foreground",
                collapsed && "justify-center px-2"
              )}>
              <Home size={collapsed ? 20 : 18} />
              {!collapsed && <span>Home</span>}
            </Link>
          </div>
        </div>
      )}
      {/* Main Content */}
      <div className="flex-1 overflow-auto bg-background dashboard-pattern">
        <div className="container py-6 px-4 md:px-6 max-w-7xl mx-auto animate-fade-in">
          {children}
        </div>
      </div>
    </div>
  )
}
