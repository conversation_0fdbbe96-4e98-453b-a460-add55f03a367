'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export default function KpiCardsSkeleton() {
  return (
    <div className="grid gap-5 md:grid-cols-2 lg:grid-cols-4" data-testid="kpi-cards-skeleton">
      {[1, 2, 3, 4].map((index) => (
        <Card key={index} className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-5">
              <div className="flex items-baseline">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-5 w-16 ml-3" />
              </div>
              <div className="flex items-baseline justify-between">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Skeleton className="h-8 w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
