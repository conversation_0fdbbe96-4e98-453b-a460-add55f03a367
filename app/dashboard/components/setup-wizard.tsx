'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Upload,
  UserPlus,
  Mail,
  X
} from 'lucide-react'
import { useCompany } from '@/contexts/company-context'

interface SetupWizardProps {
  onComplete?: () => void
  onSkip?: () => void
}

const WIZARD_STEPS = [
  { id: 'branding', title: 'Company Branding', description: 'Upload logo and set colors' },
  { id: 'reward', title: 'First Reward', description: 'Create your first customer reward' },
  { id: 'staff', title: 'Invite Staff', description: 'Add team members (optional)' },
  { id: 'complete', title: 'All Done!', description: 'Your loyalty program is ready' }
]

export default function SetupWizard({ onComplete, onSkip }: SetupWizardProps) {
  const { company } = useCompany()
  const [currentStep, setCurrentStep] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  // Form data state
  const [formData, setFormData] = useState({
    logoUrl: '',
    primaryColor: '#3b82f6',
    rewardTitle: '',
    rewardDescription: '',
    rewardPointsCost: 100,
    staffEmails: ['']
  })

  const currentStepData = WIZARD_STEPS[currentStep]
  const progress = ((currentStep + 1) / WIZARD_STEPS.length) * 100

  const handleNext = () => {
    if (currentStep < WIZARD_STEPS.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    setIsVisible(false)
    onComplete?.()
  }

  const handleSkip = () => {
    setIsVisible(false)
    onSkip?.()
  }

  const addStaffEmail = () => {
    setFormData(prev => ({
      ...prev,
      staffEmails: [...prev.staffEmails, '']
    }))
  }

  const removeStaffEmail = (index: number) => {
    setFormData(prev => ({
      ...prev,
      staffEmails: prev.staffEmails.filter((_, i) => i !== index)
    }))
  }

  const updateStaffEmail = (index: number, email: string) => {
    setFormData(prev => ({
      ...prev,
      staffEmails: prev.staffEmails.map((e, i) => i === index ? email : e)
    }))
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-auto">
        <CardHeader className="text-center space-y-4">
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-xs">
              Setup Wizard
            </Badge>
            <Button variant="ghost" size="icon" onClick={handleSkip} className="h-8 w-8">
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div>
            <CardTitle className="text-2xl">
              {currentStepData.title}
            </CardTitle>
            <CardDescription className="text-base mt-2">
              {currentStepData.description}
            </CardDescription>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-center gap-2">
              <span className="text-sm font-medium">Step {currentStep + 1} of {WIZARD_STEPS.length}</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Step 1: Branding */}
          {currentStep === 0 && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="logo-upload">Company Logo (Optional)</Label>
                  <div className="mt-2 border-2 border-dashed border-muted rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Click to upload or drag and drop
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      PNG, JPG up to 2MB
                    </p>
                  </div>
                </div>

                <div>
                  <Label htmlFor="primary-color">Primary Color</Label>
                  <div className="mt-2 flex items-center gap-3">
                    <input
                      type="color"
                      id="primary-color"
                      value={formData.primaryColor}
                      onChange={(e) => setFormData(prev => ({ ...prev, primaryColor: e.target.value }))}
                      className="h-10 w-20 rounded border cursor-pointer"
                    />
                    <Input
                      value={formData.primaryColor}
                      onChange={(e) => setFormData(prev => ({ ...prev, primaryColor: e.target.value }))}
                      placeholder="#3b82f6"
                      className="flex-1"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    This will be used for your loyalty program branding
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: First Reward */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="reward-title">Reward Title</Label>
                <Input
                  id="reward-title"
                  value={formData.rewardTitle}
                  onChange={(e) => setFormData(prev => ({ ...prev, rewardTitle: e.target.value }))}
                  placeholder="e.g., 10% Off Next Purchase"
                  className="mt-2"
                />
              </div>

              <div>
                <Label htmlFor="reward-description">Description</Label>
                <Textarea
                  id="reward-description"
                  value={formData.rewardDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, rewardDescription: e.target.value }))}
                  placeholder="Describe what customers get with this reward"
                  className="mt-2"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="points-cost">Points Required</Label>
                <Input
                  id="points-cost"
                  type="number"
                  value={formData.rewardPointsCost}
                  onChange={(e) => setFormData(prev => ({ ...prev, rewardPointsCost: parseInt(e.target.value) || 100 }))}
                  placeholder="100"
                  className="mt-2"
                  min="1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  How many points customers need to redeem this reward
                </p>
              </div>
            </div>
          )}

          {/* Step 3: Staff Invitation */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <div>
                <Label>Staff Email Addresses (Optional)</Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Invite team members to help manage your loyalty program
                </p>

                <div className="space-y-3">
                  {formData.staffEmails.map((email, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <Input
                        type="email"
                        value={email}
                        onChange={(e) => updateStaffEmail(index, e.target.value)}
                        placeholder="<EMAIL>"
                        className="flex-1"
                      />
                      {formData.staffEmails.length > 1 && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeStaffEmail(index)}
                          className="h-8 w-8"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={addStaffEmail}
                  className="mt-2"
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Another Email
                </Button>
              </div>
            </div>
          )}

          {/* Step 4: Complete */}
          {currentStep === 3 && (
            <div className="text-center space-y-4 py-8">
              <div className="mx-auto h-16 w-16 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>

              <div>
                <h3 className="text-xl font-semibold">Setup Complete!</h3>
                <p className="text-muted-foreground mt-2">
                  {company?.name || 'Your business'} is ready to start building customer loyalty
                </p>
              </div>

              <div className="bg-muted/50 rounded-lg p-4 text-left">
                <h4 className="font-medium mb-2">What&apos;s next?</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Start adding your real customers</li>
                  <li>• Process your first transactions</li>
                  <li>• Watch your loyalty program grow</li>
                </ul>
              </div>
            </div>
          )}
        </CardContent>

        {/* Navigation */}
        <div className="flex items-center justify-between p-6 border-t">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>

          <div className="flex items-center gap-2">
            <Button variant="ghost" onClick={handleSkip}>
              Skip Setup
            </Button>
            <Button onClick={handleNext} className="flex items-center gap-2">
              {currentStep === WIZARD_STEPS.length - 1 ? (
                <>
                  Complete
                  <CheckCircle className="h-4 w-4" />
                </>
              ) : (
                <>
                  Next
                  <ArrowRight className="h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}
