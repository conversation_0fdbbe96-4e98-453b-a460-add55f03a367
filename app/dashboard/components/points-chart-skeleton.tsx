'use client'

import { Skeleton } from '@/components/ui/skeleton'

export default function PointsChartSkeleton() {
  return (
    <div className="space-y-4" data-testid="points-chart-skeleton">
      <div className="flex justify-between items-center mb-4">
        <div className="space-y-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-48" />
        </div>
        <Skeleton className="h-9 w-24 rounded-md" />
      </div>
      
      <Skeleton className="h-[280px] w-full rounded-md" />
      
      <div className="flex justify-between pt-2">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-16" />
      </div>
    </div>
  )
}
