'use client'

import { Skeleton } from '@/components/ui/skeleton'

export default function ActivityFeedSkeleton() {
  return (
    <div className="space-y-4 p-4" data-testid="activity-feed-skeleton">
      {[1, 2, 3, 4, 5].map((index) => (
        <div key={index} className="flex items-start gap-4 animate-pulse" style={{ animationDelay: `${index * 100}ms` }}>
          <Skeleton className="h-10 w-10 rounded-full flex-shrink-0" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-5 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <div className="flex gap-2 mt-1">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
