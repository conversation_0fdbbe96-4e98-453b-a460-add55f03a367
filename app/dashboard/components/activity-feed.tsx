'use client'

import { useEffect, useState } from 'react'
import { useCompany } from '@/contexts/company-context'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, ArrowUpRight, Clock, Activity, Gift, Plus, Users } from 'lucide-react'
import Link from 'next/link'
import { useQuery } from '@tanstack/react-query'

interface Transaction {
  id: string;
  member_id: string;
  member_name?: string;
  points_change: number;
  transaction_type: string;
  transaction_date?: string;
  created_at?: string;
  description?: string;
}

interface Redemption {
  id: string;
  member_id: string;
  member_name?: string;
  reward_id: string;
  reward_name?: string;
  points_cost: number;
  points_used: number;
  created_at?: string;
  reward?: {
    id: string;
    name: string;
  };
}

interface Member {
  id: string;
  name: string;
  registration_date: string;
  loyalty_tier: string | null;
  loyalty_id: string;
}

interface ActivityItem {
  id: string;
  type: 'member' | 'transaction' | 'redemption' | 'notification';
  title: string;
  description: string;
  timestamp: string;
  icon: React.ReactNode;
  link?: string;
  badge?: {
    text: string;
    variant: 'default' | 'secondary' | 'destructive' | 'outline';
  };
}

export default function MemberActivityFeed() {
  const { company } = useCompany()
  const [mounted, setMounted] = useState(false)

  // Use React Query for data fetching with proper types
  const { data: membersData, isLoading: membersLoading } = useQuery({
    queryKey: ['members', company?.id, 5],
    queryFn: async () => {
      if (!company?.id) return { data: [] };
      const response = await fetch(`/api/members?companyId=${company.id}&limit=5`);
      if (!response.ok) throw new Error('Failed to fetch members');
      return response.json();
    },
    enabled: !!company?.id,
    staleTime: 60 * 1000, // 1 minute
  });

  const { data: transactionsData, isLoading: transactionsLoading } = useQuery({
    queryKey: ['transactions', company?.id, 5],
    queryFn: async () => {
      if (!company?.id) return { data: [] };
      const response = await fetch(`/api/transactions?companyId=${company.id}&limit=5`);
      if (!response.ok) throw new Error('Failed to fetch transactions');
      return response.json();
    },
    enabled: !!company?.id,
    staleTime: 60 * 1000, // 1 minute
  });

  const { data: redemptionsData, isLoading: redemptionsLoading, error } = useQuery({
    queryKey: ['redemptions', company?.id, 5],
    queryFn: async () => {
      if (!company?.id) return { data: [] };
      const response = await fetch(`/api/redemptions?companyId=${company.id}&limit=5`);
      if (!response.ok) throw new Error('Failed to fetch redemptions');
      return response.json();
    },
    enabled: !!company?.id,
    staleTime: 60 * 1000, // 1 minute
  });

  // Handle hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Show skeleton during client-side render
  if (!mounted) {
    return <ActivityFeedSkeleton />;
  }

  // Check if data is loading
  const dataLoading = !company || membersLoading || transactionsLoading || redemptionsLoading;

  if (dataLoading) {
    return <ActivityFeedSkeleton />;
  }

  const transactions: Transaction[] = transactionsData?.data || [];
  const redemptions: Redemption[] = redemptionsData?.data || [];
  const members: Member[] = membersData?.data || [];

  // Create a member lookup map for faster access
  const memberMap = new Map<string, Member>();
  members.forEach(m => memberMap.set(m.id, m));

  // Enhance transactions with member data
  const enhancedTransactions = transactions.map((tx: Transaction) => {
    const member = memberMap.get(tx.member_id);
    return {
      ...tx,
      member_name: member?.name || tx.member_name || 'Unknown Member',
      date: tx.transaction_date || tx.created_at || new Date().toISOString()
    };
  });

  // Enhance redemptions with member data
  const enhancedRedemptions = redemptions.map((r: Redemption) => {
    const member = memberMap.get(r.member_id);
    return {
      ...r,
      member_name: member?.name || r.member_name || 'Unknown Member',
      date: r.created_at || new Date().toISOString()
    };
  });

  // Combine and sort activity
  const activity: ActivityItem[] = [
    ...members.map((member: Member) => ({
      id: `member-${member.id}`,
      type: 'member' as const,
      title: 'New Member',
      description: `${member.name} joined the loyalty program`,
      timestamp: new Date(member.registration_date).toISOString(),
      icon: <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
      link: `/members/${member.id}`,
      badge: {
        text: member.loyalty_id || 'New Member',
        variant: 'secondary' as 'default' | 'secondary' | 'destructive' | 'outline'
      }
    })),
    ...enhancedTransactions.map((tx: Transaction & { date: string }) => ({
      id: `transaction-${tx.id}`,
      type: 'transaction' as const,
      title: 'Points Earned',
      description: `${tx.member_name} earned ${Math.abs(tx.points_change)} points from ${tx.description || 'a purchase'}`,
      timestamp: tx.date,
      icon: <Plus className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />,
      badge: {
        text: `${Math.abs(tx.points_change)} points`,
        variant: 'default' as 'default' | 'secondary' | 'destructive' | 'outline'
      }
    })),
    ...enhancedRedemptions.map((r: Redemption & { date: string }) => ({
      id: `redemption-${r.id}`,
      type: 'redemption' as const,
      title: 'Reward Redeemed',
      description: `Redeemed ${r.reward?.name || 'a reward'}`,
      timestamp: r.date,
      icon: <Gift className="h-4 w-4 text-purple-600 dark:text-purple-400" />,
      badge: {
        text: `${r.points_used} points`,
        variant: 'destructive' as 'default' | 'secondary' | 'destructive' | 'outline'
      }
    }))
  ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  .slice(0, 10);

  return (
    <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-200 h-full overflow-hidden">
      <CardHeader className="pb-2 space-y-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Activity className="h-5 w-5 text-primary/80" />
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70 dark:from-primary-light dark:to-primary">
              Recent Activity
            </span>
          </CardTitle>
          <Button variant="ghost" size="icon" className="h-7 w-7 rounded-full">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="sr-only">View history</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {error ? (
          <div className="flex flex-col items-center justify-center py-8 px-4 text-muted-foreground">
            <AlertCircle className="h-10 w-10 mb-2 opacity-30" />
            <p className="text-sm font-medium text-center">{String(error)}</p>
            <p className="text-xs mt-2 text-center opacity-70">
              Unable to load recent activity
            </p>
          </div>
        ) : (
          <div className="divide-y divide-border">
            {activity.map((activityItem) => (
              <div
                key={activityItem.id}
                className="p-4 hover:bg-muted/30 transition-colors"
              >
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-full flex items-center justify-center mt-0.5 ${
                    activityItem.type === 'member' ? 'bg-blue-100/80 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                    activityItem.type === 'transaction' && activityItem.title.includes('Earned') ? 'bg-emerald-100/80 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400' :
                    activityItem.type === 'transaction' ? 'bg-amber-100/80 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400' :
                    activityItem.type === 'redemption' ? 'bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-light' :
                    'bg-destructive/10 text-destructive dark:bg-destructive/20 dark:text-destructive'
                  }`}>
                    {activityItem.icon}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-center gap-2 mb-1">
                      <p className="text-xs font-medium text-foreground truncate">{activityItem.title}</p>
                      {activityItem.badge && (
                        <Badge variant={activityItem.badge.variant} className="text-[10px] px-2 py-0 h-4">
                          {activityItem.badge.text}
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground truncate mb-1">{activityItem.description}</p>
                    <p className="text-[10px] text-muted-foreground/80">
                      {new Date(activityItem.timestamp).toLocaleString(undefined, {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2 pb-3">
        <Button asChild variant="ghost" size="sm" className="w-full justify-center text-xs text-muted-foreground hover:text-foreground gap-1">
          <Link href="/activity">
            <span>View All Activity</span>
            <ArrowUpRight className="h-3 w-3" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

// Skeleton loader component
function ActivityFeedSkeleton() {
  return (
    <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-200 h-full overflow-hidden">
      <CardHeader className="pb-2 space-y-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Activity className="h-5 w-5 text-primary/80" />
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70 dark:from-primary-light dark:to-primary">
              Recent Activity
            </span>
          </CardTitle>
          <Button variant="ghost" size="icon" className="h-7 w-7 rounded-full">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="sr-only">View history</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="flex flex-col space-y-4 p-4">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="flex items-start gap-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-32" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="pt-2 pb-3">
        <Button asChild variant="ghost" size="sm" className="w-full justify-center text-xs text-muted-foreground hover:text-foreground gap-1">
          <Link href="/activity">
            <span>View All Activity</span>
            <ArrowUpRight className="h-3 w-3" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
