'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useCompany } from '@/contexts/company-context'
import {
  TrendingUp,
  Users,
  DollarSign,
  Target,
  Activity,
  Award,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Info
} from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'
import { useQuery } from '@tanstack/react-query'

interface BusinessMetrics {
  memberAcquisitionRate: {
    last30Days: number
    previous30Days: number
    growthRate: number
    trend: 'up' | 'down'
  }
  memberRetention: {
    retentionRate: number
    benchmark: number
    status: 'good' | 'fair' | 'poor'
  }
  averageTransactionValue: {
    value: number
    currency: string
    sampleSize: number
  }
  customerLifetimeValue: {
    avgPoints: number
    estimatedValue: number
    currency: string
  }
  programROI: {
    roi: number
    totalValue: number
    totalCost: number
    status: 'excellent' | 'good' | 'break-even' | 'loss'
  }
  memberEngagement: {
    score: number
    activeMembers: number
    totalTransactions: number
    frequency: 'high' | 'medium' | 'low'
  }
  rewardEffectiveness: {
    rewards: Array<{
      id: string
      title: string
      pointsRequired: number
      redemptions: number
      effectivenessScore: number
    }>
    topPerforming: {
      id: string
      title: string
      pointsRequired: number
      redemptions: number
      effectivenessScore: number
    } | null
  }
}

export default function BusinessMetricsSection() {
  const { company } = useCompany()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const { data: businessData, isLoading, error } = useQuery({
    queryKey: ['businessMetrics', company?.id],
    queryFn: async () => {
      if (!company?.id) return { data: null }
      const response = await fetch(`/api/business-metrics?companyId=${company.id}`)
      if (!response.ok) throw new Error('Failed to fetch business metrics')
      return response.json()
    },
    enabled: !!company?.id && mounted,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })

  if (!mounted) {
    return <BusinessMetricsSkeleton />
  }

  if (isLoading) {
    return <BusinessMetricsSkeleton />
  }

  if (error || !businessData?.data) {
    return (
      <div className="grid gap-6">
        <div className="text-center p-8 text-muted-foreground">
          <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-30" />
          <p>Unable to load business metrics</p>
          <p className="text-sm mt-1">Please try again later</p>
        </div>
      </div>
    )
  }

  const metrics: BusinessMetrics = businessData.data

  const businessCards = [
    {
      title: "Member Acquisition",
      explanation: "Number of new customers who joined your loyalty program in the last 30 days, with growth trend vs. previous period",
      value: metrics.memberAcquisitionRate.last30Days,
      subtitle: "New members (30d)",
      trend: {
        value: metrics.memberAcquisitionRate.growthRate,
        direction: metrics.memberAcquisitionRate.trend
      },
      icon: <Users className="h-5 w-5" />,
      color: "blue"
    },
    {
      title: "Member Retention",
      explanation: "Percentage of members (registered 30+ days ago) who remained active in the last 30 days. Industry benchmark for loyalty programs is 60%",
      value: `${metrics.memberRetention.retentionRate}%`,
      subtitle: `vs ${metrics.memberRetention.benchmark}% benchmark`,
      status: metrics.memberRetention.status,
      icon: <Target className="h-5 w-5" />,
      color: metrics.memberRetention.status === 'good' ? 'green' :
             metrics.memberRetention.status === 'fair' ? 'yellow' : 'red'
    },
    {
      title: "Avg Transaction Value",
      explanation: "Average monetary value of customer transactions that earned points in the last 30 days",
      value: `${metrics.averageTransactionValue.value} ${metrics.averageTransactionValue.currency}`,
      subtitle: `${metrics.averageTransactionValue.sampleSize} transactions`,
      icon: <DollarSign className="h-5 w-5" />,
      color: "emerald"
    },
    {
      title: "Customer Lifetime Value",
      explanation: "Average total points earned per customer since joining, representing their engagement and spending with your business",
      value: `${metrics.customerLifetimeValue.avgPoints} pts`,
      subtitle: `≈ ${metrics.customerLifetimeValue.estimatedValue} ${metrics.customerLifetimeValue.currency}`,
      icon: <TrendingUp className="h-5 w-5" />,
      color: "purple"
    },
    {
      title: "Program ROI",
      explanation: "Return on Investment of your loyalty program: (Total Customer Value - Reward Costs) / Reward Costs × 100",
      value: `${metrics.programROI.roi}%`,
      subtitle: `Status: ${metrics.programROI.status}`,
      status: metrics.programROI.status,
      icon: <BarChart3 className="h-5 w-5" />,
      color: metrics.programROI.status === 'excellent' ? 'green' :
             metrics.programROI.status === 'good' ? 'blue' :
             metrics.programROI.status === 'break-even' ? 'yellow' : 'red'
    },
    {
      title: "Member Engagement",
      explanation: "Average number of transactions per active member in the last 30 days. Higher scores indicate more frequent customer interactions",
      value: `${metrics.memberEngagement.score}`,
      subtitle: `${metrics.memberEngagement.frequency} frequency`,
      detail: `${metrics.memberEngagement.activeMembers} active members`,
      icon: <Activity className="h-5 w-5" />,
      color: metrics.memberEngagement.frequency === 'high' ? 'green' :
             metrics.memberEngagement.frequency === 'medium' ? 'yellow' : 'red'
    }
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "from-blue-50 to-blue-100/30 dark:from-blue-950/20 dark:to-blue-900/10 text-blue-600",
      green: "from-green-50 to-green-100/30 dark:from-green-950/20 dark:to-green-900/10 text-green-600",
      yellow: "from-yellow-50 to-yellow-100/30 dark:from-yellow-950/20 dark:to-yellow-900/10 text-yellow-600",
      red: "from-red-50 to-red-100/30 dark:from-red-950/20 dark:to-red-900/10 text-red-600",
      emerald: "from-emerald-50 to-emerald-100/30 dark:from-emerald-950/20 dark:to-emerald-900/10 text-emerald-600",
      purple: "from-purple-50 to-purple-100/30 dark:from-purple-950/20 dark:to-purple-900/10 text-purple-600",
      gray: "from-gray-50 to-gray-100/30 dark:from-gray-950/20 dark:to-gray-900/10 text-gray-600"
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Business KPIs Grid */}
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            Business Intelligence
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {businessCards.map((card, index) => (
              <Card key={index} className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
                <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r ${getColorClasses(card.color)}`}>
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help transition-colors" />
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <p className="text-sm">{card.explanation}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className={`rounded-full p-1.5 bg-background/80`}>
                    {card.icon}
                  </div>
                </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-baseline">
                    <div className="text-2xl font-bold font-numeric">
                      {typeof card.value === 'number' && card.value.toLocaleString ? card.value.toLocaleString() : card.value}
                    </div>
                    {card.trend && (
                      <div className={`flex items-center ml-3 text-xs font-medium ${
                        card.trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {card.trend.direction === 'up' ? (
                          <ArrowUpRight className="h-3 w-3 mr-1" />
                        ) : (
                          <ArrowDownRight className="h-3 w-3 mr-1" />
                        )}
                        {Math.abs(card.trend.value)}%
                      </div>
                    )}
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">{card.subtitle}</p>
                    {card.detail && (
                      <p className="text-xs text-muted-foreground">{card.detail}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Reward Performance Section */}
      {metrics.rewardEffectiveness.rewards.length > 0 ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Award className="h-5 w-5 text-primary" />
              Reward Performance
            </h3>
            <div className="text-sm text-muted-foreground">
              Last 30 days
            </div>
          </div>

          {/* Top Performing Reward Highlight */}
          {metrics.rewardEffectiveness.topPerforming && (
            <Card className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
              <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r ${getColorClasses('purple')}`}>
                <div className="flex items-center gap-2">
                  <CardTitle className="text-sm font-medium">🏆 Top Performing Reward</CardTitle>
                </div>
                <div className={`rounded-full p-1.5 bg-background/80`}>
                  <Award className="h-5 w-5" />
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-baseline">
                    <div className="text-2xl font-bold font-numeric">
                      {metrics.rewardEffectiveness.topPerforming.redemptions}
                    </div>
                    <div className="text-xs text-muted-foreground ml-2">redemptions</div>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{metrics.rewardEffectiveness.topPerforming.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {metrics.rewardEffectiveness.topPerforming.pointsRequired} points required
                    </p>
                    <div className="flex items-center gap-1 text-xs">
                      <div className="h-2 w-2 rounded-full bg-green-500"></div>
                      <span className="text-green-600 font-medium">
                        {((metrics.rewardEffectiveness.topPerforming.redemptions /
                          Math.max(1, metrics.rewardEffectiveness.rewards.reduce((sum, r) => sum + r.redemptions, 0))) * 100).toFixed(1)}% of all redemptions
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Reward Performance Grid */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {metrics.rewardEffectiveness.rewards
              .sort((a, b) => b.redemptions - a.redemptions)
              .slice(0, 6)
              .map((reward, index) => {
                const totalRedemptions = metrics.rewardEffectiveness.rewards.reduce((sum, r) => sum + r.redemptions, 0)
                const redemptionShare = totalRedemptions > 0 ? (reward.redemptions / totalRedemptions) * 100 : 0
                const popularityLevel = reward.redemptions >= 5 ? 'high' : reward.redemptions >= 2 ? 'medium' : 'low'

                // Use the same color system as other cards
                const cardColor = popularityLevel === 'high' ? 'green' :
                                 popularityLevel === 'medium' ? 'yellow' : 'blue'

                return (
                  <Card key={reward.id} className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
                    <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r ${getColorClasses(cardColor)}`}>
                      <div className="flex items-center gap-2">
                        <div className={`
                          w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold bg-background/80
                          ${index === 0 ? 'text-yellow-600' :
                            index === 1 ? 'text-gray-600' :
                            index === 2 ? 'text-orange-600' :
                            'text-blue-600'}
                        `}>
                          {index + 1}
                        </div>
                        <CardTitle className="text-sm font-medium truncate">{reward.title}</CardTitle>
                      </div>
                      <div className={`rounded-full p-1.5 bg-background/80`}>
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          popularityLevel === 'high' ? 'bg-green-100 text-green-700' :
                          popularityLevel === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-blue-100 text-blue-700'
                        }`}>
                          {popularityLevel}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-6">
                      <div className="space-y-3">
                        {/* Main Metrics */}
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="text-center">
                            <div className="text-2xl font-bold font-numeric text-primary">{reward.redemptions}</div>
                            <div className="text-xs text-muted-foreground">Redemptions</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold font-numeric">{reward.pointsRequired}</div>
                            <div className="text-xs text-muted-foreground">Points Cost</div>
                          </div>
                        </div>

                        {/* Market Share Progress */}
                        <div className="space-y-2">
                          <div className="flex justify-between items-center text-xs">
                            <span className="text-muted-foreground">Market Share</span>
                            <span className="font-medium">
                              {redemptionShare.toFixed(1)}%
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-500 ${
                                popularityLevel === 'high' ? 'bg-green-500' :
                                popularityLevel === 'medium' ? 'bg-yellow-500' :
                                'bg-blue-500'
                              }`}
                              style={{ width: `${Math.max(5, redemptionShare)}%` }}
                            />
                          </div>
                        </div>

                        {/* Value Efficiency */}
                        <div className="flex items-center justify-between text-xs pt-1 border-t">
                          <span className="text-muted-foreground">Value Efficiency</span>
                          <div className="flex items-center gap-1">
                            {reward.pointsRequired <= 100 ? (
                              <>
                                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                <span className="text-green-600 font-medium">High</span>
                              </>
                            ) : reward.pointsRequired <= 300 ? (
                              <>
                                <div className="h-2 w-2 rounded-full bg-yellow-500"></div>
                                <span className="text-yellow-600 font-medium">Medium</span>
                              </>
                            ) : (
                              <>
                                <div className="h-2 w-2 rounded-full bg-red-500"></div>
                                <span className="text-red-600 font-medium">Premium</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
          </div>

          {/* Reward Performance Insights */}
          <Card className="premium-card overflow-hidden">
            <CardHeader className={`bg-gradient-to-r ${getColorClasses('blue')}`}>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Reward Performance Insights
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="grid md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-green-500"></div>
                    <span>
                      <strong>{metrics.rewardEffectiveness.rewards.filter(r => r.redemptions >= 3).length}</strong> high-performing rewards
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-yellow-500"></div>
                    <span>
                      <strong>{metrics.rewardEffectiveness.rewards.filter(r => r.redemptions === 0).length}</strong> unused rewards need attention
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-purple-500"></div>
                    <span>
                      Most popular: <strong>{metrics.rewardEffectiveness.rewards.filter(r => r.pointsRequired <= 150).length}</strong> low-cost rewards
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    <span>
                      Avg. redemption rate: <strong>
                        {(metrics.rewardEffectiveness.rewards.reduce((sum, r) => sum + r.redemptions, 0) /
                          Math.max(1, metrics.rewardEffectiveness.rewards.length)).toFixed(1)}
                      </strong> per reward
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      ) : (
        /* No Rewards Placeholder */
        <div className="space-y-6">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Award className="h-5 w-5 text-primary" />
            Reward Performance
          </h3>
          <Card className="premium-card overflow-hidden">
            <CardHeader className={`bg-gradient-to-r ${getColorClasses('gray')}`}>
              <CardTitle className="text-lg flex items-center gap-2">
                <Award className="h-5 w-5" />
                No Active Rewards
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6 pb-12 text-center">
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground max-w-md mx-auto">
                  Create your first rewards to start tracking performance and engaging your customers.
                </p>
                <div className="bg-blue-50 rounded-lg p-4 max-w-sm mx-auto">
                  <p className="text-xs text-blue-700">
                    💡 <strong>Tip:</strong> Start with low-cost rewards (50-100 points) to encourage initial engagement.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Insights & Recommendations */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Key Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            {metrics.memberRetention.retentionRate < 50 && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 rounded-full bg-orange-500 mt-1.5 flex-shrink-0" />
                <p><strong>Retention Alert:</strong> Member retention is below industry benchmark. Consider implementing engagement campaigns.</p>
              </div>
            )}

            {metrics.programROI.roi > 150 && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500 mt-1.5 flex-shrink-0" />
                <p><strong>Excellent ROI:</strong> Your loyalty program is generating strong returns. Consider scaling your efforts.</p>
              </div>
            )}

            {metrics.memberEngagement.frequency === 'low' && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 rounded-full bg-red-500 mt-1.5 flex-shrink-0" />
                <p><strong>Low Engagement:</strong> Members are not frequently engaging. Review reward accessibility and communication.</p>
              </div>
            )}

            {metrics.memberAcquisitionRate.growthRate > 0 && (
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500 mt-1.5 flex-shrink-0" />
                <p><strong>Growing Membership:</strong> You&apos;re acquiring new members consistently. Focus on retention strategies.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      </div>
    </TooltipProvider>
  )
}

function BusinessMetricsSkeleton() {
  return (
    <div className="space-y-6">
      <div>
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-5 w-5 rounded" />
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <Skeleton className="h-8 w-16 mb-3" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
