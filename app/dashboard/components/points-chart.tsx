'use client'

import React, { useState, useEffect } from 'react'
import { useCompany } from '@/contexts/company-context'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { CircleOff, TrendingUp } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface TransactionData {
  created_at: string;
  points_change: number;
  transaction_type: 'EARN' | 'REDEEM';
}

interface PointsData {
  date: string;
  earned: number;
  redeemed: number;
  expired: number;
}

interface PointsChartProps {
  activeTimeRange?: string;
}

export default function PointsOverTimeChart({
  activeTimeRange = '30d'
}: PointsChartProps) {
  const { company } = useCompany()
  const [isLoading, setIsLoading] = useState(true)
  const [pointsData, setPointsData] = useState<PointsData[]>([])
  const [error, setError] = useState<string | null>(null)

  const effectiveTimeRange = activeTimeRange

  useEffect(() => {
    if (!company?.id) return

    const fetchPointsData = async () => {
      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/points-data?companyId=${company.id}&timeRange=${effectiveTimeRange}`)
        if (!response.ok) {
          throw new Error('Failed to fetch points data')
        }

        const result = await response.json()

        // Process real transaction data into chart format
        const processedData: PointsData[] = []
        const transactionsByDate = new Map<string, { earned: number; redeemed: number; expired: number }>()

        // Initialize with empty data for the time range
        const days = effectiveTimeRange === '7d' ? 7 :
                    effectiveTimeRange === '30d' ? 30 :
                    effectiveTimeRange === '90d' ? 90 :
                    effectiveTimeRange === '1y' ? 365 : 30 // default to 30
        const today = new Date()

        for (let i = days - 1; i >= 0; i--) {
          const date = new Date(today)
          date.setDate(date.getDate() - i)
          const dateKey = date.toISOString().split('T')[0]
          transactionsByDate.set(dateKey, { earned: 0, redeemed: 0, expired: 0 })
        }

        // Process actual transactions
        if (result.data && Array.isArray(result.data)) {
          result.data.forEach((transaction: TransactionData) => {
            const date = new Date(transaction.created_at).toISOString().split('T')[0]
            const points = Math.abs(transaction.points_change || 0)

            if (transactionsByDate.has(date)) {
              const dayData = transactionsByDate.get(date)!
              if (transaction.transaction_type === 'EARN') {
                dayData.earned += points
              } else if (transaction.transaction_type === 'REDEEM') {
                dayData.redeemed += points
              }
            }
          })
        }

        // Convert to chart format
        transactionsByDate.forEach((data, dateKey) => {
          const date = new Date(dateKey)
          processedData.push({
            date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            earned: data.earned,
            redeemed: data.redeemed,
            expired: data.expired // Always 0 for now, will be updated when expiration is implemented
          })
        })

        setPointsData(processedData)
      } catch (err: unknown) {
        console.error('Error fetching points data:', err)
        setError('Failed to load points data. Please try again later.')
        // Don't set empty data on error - keep previous data if available
      } finally {
        setIsLoading(false)
      }
    }

    fetchPointsData()
  }, [company?.id, effectiveTimeRange])

  const placeholderData = Array.from({ length: 7 }).map((_, i) => ({
    date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    earned: Math.floor(Math.random() * 100),
    redeemed: Math.floor(Math.random() * 50),
    expired: Math.floor(Math.random() * 20)
  }))

  const displayData = isLoading || pointsData.length === 0 ? placeholderData : pointsData

  const formatter = (value: number) => `${value.toLocaleString()} pts`

  return (
    <Card className="border-0 shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden h-full">
      <CardHeader className="pb-2 space-y-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-primary/80" />
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70 dark:from-primary-light dark:to-primary">
              Points Activity
            </span>
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-[300px] w-full flex items-center justify-center">
            <Skeleton className="h-[250px] w-full" />
          </div>
        ) : error && !displayData.length ? (
          <div className="h-[300px] w-full flex flex-col items-center justify-center text-muted-foreground">
            <CircleOff className="h-10 w-10 mb-2 opacity-30" />
            <p className="text-sm">No data available</p>
            <p className="text-xs mt-1">{error}</p>
          </div>
        ) : (
          <>
            <div className="h-[280px] w-full">
              <ResponsiveContainer width="100%" height="100%" className="dark:[&_.recharts-cartesian-axis-tick-value]:fill-white [&_.recharts-cartesian-axis-tick-value]:fill-foreground">
                <LineChart
                  data={displayData}
                  margin={{
                    top: 20,
                    right: 10,
                    left: 0,
                    bottom: 10,
                  }}
                >
                  <defs>
                    <linearGradient id="earnedGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0} />
                    </linearGradient>
                    <linearGradient id="redeemedGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="#f59e0b" stopOpacity={0} />
                    </linearGradient>
                    <linearGradient id="expiredGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#ef4444" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} vertical={false} stroke="var(--border)" />
                  <XAxis
                    dataKey="date"
                    axisLine={false}
                    tickLine={false}
                    stroke="var(--color-white, #ffffff)"
                    fontSize={11}
                    tickMargin={8}
                    tickFormatter={(date: string | number | Date) => {
                      if (typeof date === 'string') {
                        return date;
                      }
                      const d = new Date(date)
                      return `${d.getMonth() + 1}/${d.getDate()}`
                    }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    stroke="var(--color-white, #ffffff)"
                    fontSize={11}
                    tickMargin={8}
                    width={40}
                    tickFormatter={formatter}
                  />
                  <Tooltip
                    formatter={(value: number, name: string) => [
                      formatter(value),
                      name === 'earned' ? 'Earned' :
                      name === 'redeemed' ? 'Redeemed' : 'Expired'
                    ]}
                    labelFormatter={(label: string | number) => {
                      if (typeof label === 'string') {
                        return label;
                      }
                      return new Date(label).toLocaleDateString();
                    }}
                    contentStyle={{
                      backgroundColor: 'var(--card)',
                      border: '1px solid var(--border)',
                      borderRadius: '6px',
                      boxShadow: 'var(--shadow-sm)',
                      fontSize: '12px',
                      padding: '8px 12px'
                    }}
                    itemStyle={{
                      padding: '2px 0',
                      color: 'var(--foreground)'
                    }}
                    cursor={{
                      stroke: 'var(--foreground)',
                      strokeWidth: 1,
                      strokeDasharray: '3 3',
                    }}
                  />
                  <Line
                    type="monotone"
                    dataKey="earned"
                    stroke="#8b5cf6"
                    strokeWidth={.5}
                    dot={{ r: 4, fill: "#8b5cf6", strokeWidth: 0 }}
                    activeDot={{ r: 6, strokeWidth: 0, fill: '#8b5cf6' }}
                    fillOpacity={0.8}
                    fill="url(#earnedGradient)"
                  />
                  <Line
                    type="monotone"
                    dataKey="redeemed"
                    stroke="#f59e0b"
                    strokeWidth={.5}
                    dot={{ r: 4, fill: "#f59e0b", strokeWidth: 0 }}
                    activeDot={{ r: 6, strokeWidth: 0, fill: '#f59e0b' }}
                    fillOpacity={0.8}
                    fill="url(#redeemedGradient)"
                  />
                  <Line
                    type="monotone"
                    dataKey="expired"
                    stroke="#ef4444"
                    strokeWidth={.5}
                    dot={{ r: 4, fill: "#ef4444", strokeWidth: 0 }}
                    activeDot={{ r: 6, strokeWidth: 0, fill: '#ef4444' }}
                    fillOpacity={0.8}
                    fill="url(#expiredGradient)"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="flex flex-wrap justify-center mt-2 gap-4 sm:gap-6">
              <div className="flex items-center text-foreground">
                <div className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: '#8b5cf6' }}></div>
                <span className="text-xs">Earned</span>
              </div>
              <div className="flex items-center text-foreground">
                <div className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: '#f59e0b' }}></div>
                <span className="text-xs">Redeemed</span>
              </div>
              <div className="flex items-center text-foreground">
                <div className="w-4 h-4 rounded-full mr-2" style={{ backgroundColor: '#ef4444' }}></div>
                <span className="text-xs">Expired</span>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
