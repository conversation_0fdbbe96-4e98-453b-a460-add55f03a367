'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ArrowUpIcon, ArrowDownIcon, TrendingUpIcon, Users, Gift, Activity } from 'lucide-react'

interface DemoStatsProps {
  showDemoLabel?: boolean
}

export default function DemoData({ showDemoLabel = true }: DemoStatsProps) {
  const demoMembers = [
    { name: '<PERSON>', points: 450, tier: 'Silver', joined: '2 days ago' },
    { name: '<PERSON>', points: 280, tier: 'Bronze', joined: '1 week ago' },
    { name: '<PERSON>', points: 820, tier: 'Gold', joined: '3 days ago' }
  ]

  const demoTransactions = [
    { member: '<PERSON>', action: 'Earned 25 points', amount: 'Purchase: $25.00', time: '2 hours ago' },
    { member: '<PERSON>', action: 'Redeemed reward', amount: '10% off discount', time: '1 day ago' },
    { member: '<PERSON>', action: 'Earned 15 points', amount: 'Purchase: $15.00', time: '2 days ago' }
  ]

  return (
    <div className="space-y-6">
      {/* Demo Data Label */}
      {showDemoLabel && (
        <div className="flex items-center justify-center">
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            📊 Demo Data - Replace with real customers
          </Badge>
        </div>
      )}

      {/* KPI Cards with Demo Data */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="premium-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUpIcon className="inline h-3 w-3 mr-1" />
              +3 from last month
            </p>
          </CardContent>
        </Card>

        <Card className="premium-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Points Issued</CardTitle>
            <Gift className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,550</div>
            <p className="text-xs text-muted-foreground">
              <ArrowUpIcon className="inline h-3 w-3 mr-1" />
              +40 points today
            </p>
          </CardContent>
        </Card>

        <Card className="premium-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Rewards</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1</div>
            <p className="text-xs text-muted-foreground">
              Sample reward created
            </p>
          </CardContent>
        </Card>

        <Card className="premium-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <ArrowUpIcon className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$65</div>
            <p className="text-xs text-muted-foreground">
              <ArrowDownIcon className="inline h-3 w-3 mr-1" />
              Sample transactions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Demo Members List */}
      <Card className="premium-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Sample Members</CardTitle>
              <CardDescription>Examples of what your member list will look like</CardDescription>
            </div>
            {showDemoLabel && (
              <Badge variant="secondary" className="text-xs">Demo</Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {demoMembers.map((member, index) => (
            <div key={index} className="flex items-center justify-between p-3 rounded-lg border bg-muted/30">
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="text-xs">
                    {member.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">{member.name}</p>
                  <p className="text-xs text-muted-foreground">Joined {member.joined}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium">{member.points} points</p>
                <Badge variant="outline" className="text-xs">
                  {member.tier}
                </Badge>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Demo Activity Feed */}
      <Card className="premium-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Sample Activity</CardTitle>
              <CardDescription>Recent customer interactions</CardDescription>
            </div>
            {showDemoLabel && (
              <Badge variant="secondary" className="text-xs">Demo</Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {demoTransactions.map((transaction, index) => (
            <div key={index} className="flex items-start gap-3 p-3 rounded-lg border bg-muted/30">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs">
                  {transaction.member.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium">{transaction.member}</p>
                <p className="text-sm text-muted-foreground">{transaction.action}</p>
                <p className="text-xs text-muted-foreground">{transaction.amount}</p>
              </div>
              <span className="text-xs text-muted-foreground">{transaction.time}</span>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
}
