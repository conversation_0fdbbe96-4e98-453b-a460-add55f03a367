'use client'

import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import Link from 'next/link'
import { PlusCircle, Award, Activity, LineChart, Palette, ArrowUpRight } from 'lucide-react'

// Action button component
interface ActionButtonProps {
  href: string
  icon: React.ReactNode
  label: string
  color: 'primary' | 'gold' | 'blue' | 'green' | 'purple' | 'amber'
}

const colorVariants = {
  primary: 'primary/90',
  gold: 'from-gold/90 to-gold/60',
  blue: 'from-blue-500/90 to-blue-500/60',
  green: 'from-green-500/90 to-green-500/60',
  purple: 'from-purple-500/90 to-purple-500/60',
  amber: 'from-amber-500/90 to-amber-500/60',
}

const ActionButton = ({ href, icon, label, color }: ActionButtonProps) => {
  const gradient = colorVariants[color] || colorVariants.primary;

  return (
    <Link href={href} className="group">
      <div className="flex flex-col items-center gap-2 p-3 rounded-lg hover:bg-muted/50 transition-colors">
        <div className={`h-10 w-10 rounded-full bg-gradient-to-br ${gradient} flex items-center justify-center transition-transform group-hover:scale-110`}>
          <div className="text-white">{icon}</div>
        </div>
        <span className="text-xs font-medium text-center">{label}</span>
      </div>
    </Link>
  )
}

export default function QuickActions() {
  return (
    <Card className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
      <CardHeader className="pb-2 space-y-1 border-b border-border/40">
        <div className="flex items-center gap-2">
          <div className="h-5 w-5 rounded-full bg-gradient-to-br from-primary/90 to-primary/60 flex items-center justify-center">
            <ArrowUpRight className="h-3 w-3 text-white" />
          </div>
          <CardTitle className="text-base font-medium">Quick Actions</CardTitle>
        </div>
        <CardDescription className="text-xs">
          Frequent tasks and shortcuts
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
          <ActionButton
            href="/members/add"
            icon={<PlusCircle className="h-5 w-5" />}
            label="Add Member"
            color="blue"
          />
          <ActionButton
            href="/rewards/add"
            icon={<Award className="h-5 w-5" />}
            label="Add Reward"
            color="gold"
          />
          <ActionButton
            href="/transactions/add"
            icon={<Activity className="h-5 w-5" />}
            label="Add Points"
            color="blue"
          />
          <ActionButton
            href="/tiers"
            icon={<LineChart className="h-5 w-5" />}
            label="View Tiers"
            color="green"
          />
          <ActionButton
            href="/reports"
            icon={<LineChart className="h-5 w-5" />}
            label="Reports"
            color="purple"
          />
          <ActionButton
            href="/settings/appearance"
            icon={<Palette className="h-5 w-5" />}
            label="Appearance"
            color="amber"
          />
        </div>
      </CardContent>
    </Card>
  )
}
