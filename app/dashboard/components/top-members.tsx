'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/components/ui/use-toast'
import Image from 'next/image'
import Link from 'next/link'
import { formatNumberWithCommas } from '@/lib/utils'
import { useQuery } from '@tanstack/react-query'
import { CACHE_TIMES, COMMON_QUERY_OPTIONS } from '@/lib/query-config'
import { useCompany } from '@/contexts/company-context'
import { calculateMemberTier, getTierColorClass } from '@/lib/tier-utils'
import { useTiers } from '@/app/tiers/hooks/use-tiers'
import { Users, ArrowUpRight } from 'lucide-react'

// Define the shape of a top member
interface TopMember {
  id: string
  name: string
  email: string
  loyalty_tier: string
  value: number
  metric: string
  profile_image_url?: string | null
  lifetime_points?: number // Add this to get the lifetime points for tier calculation
}

type TopMembersProps = {
  initialMetric?: string
  limit?: number
}

export function TopMembersSkeleton() {
  return (
    <Card className="premium-card">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-xl">Top Members</CardTitle>
          <CardDescription className="mt-1">Your most valuable loyalty program members</CardDescription>
        </div>
        <Skeleton className="h-10 w-[180px]" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex flex-col items-center p-4 border rounded-lg bg-muted/30">
              <Skeleton className="h-16 w-16 rounded-full mb-3" />
              <Skeleton className="h-4 w-[120px] mb-2" />
              <Skeleton className="h-3 w-[80px] mb-2" />
              <Skeleton className="h-6 w-[80px] mb-2" />
              <Skeleton className="h-6 w-[60px]" />
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="pt-4 border-t">
        <Skeleton className="h-10 w-full" />
      </CardFooter>
    </Card>
  )
}

export default function TopMembers({ initialMetric = 'lifetime_points', limit = 5 }: TopMembersProps) {
  const [metric, setMetric] = useState(initialMetric)
  const { toast } = useToast()
  const { company } = useCompany()
  const companyId = company?.id

  // Fetch tier definitions for calculating correct tiers
  const { data: tiersData } = useTiers()

  // Use React Query to fetch top members data with centralized configuration
  const { data: members, isLoading, error } = useQuery<TopMember[]>({
    queryKey: ['topMembers', companyId, metric, limit],
    queryFn: async () => {
      if (!companyId) {
        return []
      }

      const response = await fetch(`/api/top-members?companyId=${companyId}&metric=${metric}&limit=${limit}`)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch top members')
      }

      const { data } = await response.json()
      return Array.isArray(data) ? data : []
    },
    enabled: !!companyId,
    staleTime: CACHE_TIMES.REALTIME, // Use centralized cache times for real-time data
    gcTime: CACHE_TIMES.NORMAL,
    ...COMMON_QUERY_OPTIONS
  })

  // Process members with correct tier calculation
  const processedMembers = members?.map(member => {
    let lifetimePoints = 0

    // If metric is lifetime_points, use the value directly
    if (metric === 'lifetime_points') {
      lifetimePoints = member.value
    } else {
      // For other metrics, we need the actual lifetime points to calculate tier
      // The current API doesn't provide this, so we'll use the existing tier as fallback
      // TODO: Update API to always include lifetime_points for proper tier calculation
      lifetimePoints = member.lifetime_points || 0
    }

    // Calculate the correct tier using the same logic as members page
    const correctTier = tiersData && tiersData.length > 0
      ? calculateMemberTier(lifetimePoints, tiersData)
      : member.loyalty_tier || 'Basic'

    return {
      ...member,
      loyalty_tier: correctTier
    }
  }) || []

  // Show toast when there's an error
  if (error) {
    console.error('Error fetching top members:', error)
    // Use the toast function directly with message and options
    toast.error(error instanceof Error ? error.message : 'Failed to fetch top members')
  }

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    // Check if name is likely a UUID (contains dashes or is very long)
    if (!name || name.includes('-') || name.length > 20) {
      // Extract first letter of email username as fallback
      return '??'
    }

    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  // Format the value based on metric
  const formatValue = (value: number, metric: string) => {
    if (metric === 'redemption_count') {
      return value === 1 ? '1 redemption' : `${value} redemptions`
    }
    return formatNumberWithCommas(value) + ' pts'
  }

  return (
    <Card className="premium-card">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-xl">Top Members</CardTitle>
          <CardDescription className="mt-1">Your most valuable loyalty program members</CardDescription>
        </div>
        <Select value={metric} onValueChange={setMetric}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select metric" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="lifetime_points">Lifetime Points</SelectItem>
            <SelectItem value="available_points">Available Points</SelectItem>
            <SelectItem value="redeemed_points">Redeemed Points</SelectItem>
            <SelectItem value="redemption_count">Redemption Count</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex flex-col items-center p-4 border rounded-lg bg-muted/30">
                <Skeleton className="h-16 w-16 rounded-full mb-3" />
                <Skeleton className="h-4 w-[120px] mb-2" />
                <Skeleton className="h-3 w-[80px] mb-2" />
                <Skeleton className="h-6 w-[80px] mb-2" />
                <Skeleton className="h-6 w-[60px]" />
              </div>
            ))}
          </div>
        ) : processedMembers && processedMembers.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            {processedMembers.map((member: TopMember, index: number) => (
              <div key={member.id} className="flex flex-col items-center p-4 border rounded-lg bg-gradient-to-br from-background to-muted/30 hover:shadow-md transition-all duration-200 relative">
                {/* Ranking Badge */}
                <div className="absolute -top-2 -left-2 h-6 w-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">
                  {index + 1}
                </div>

                {/* Profile Image */}
                <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-primary/20 bg-gray-100 flex-shrink-0 mb-3">
                  {member.profile_image_url ? (
                    <Image
                      src={member.profile_image_url}
                      alt={`${member.name} profile`}
                      className="w-full h-full object-cover"
                      width={64}
                      height={64}
                      onError={(e) => {
                        // Hide image and show fallback
                        const imgElement = e.target as HTMLImageElement;
                        imgElement.style.display = 'none';
                        const fallback = imgElement.parentElement?.querySelector('[data-initials]');
                        if (fallback) {
                          fallback.classList.remove('hidden');
                          fallback.classList.add('flex');
                        }
                      }}
                    />
                  ) : null}
                  {/* Fallback initials */}
                  <div
                    data-initials="true"
                    className={`absolute inset-0 flex items-center justify-center text-lg font-bold text-gray-600 ${member.profile_image_url ? 'hidden' : 'flex'}`}
                  >
                    {getInitials(member.name)}
                  </div>
                </div>

                {/* Member Info */}
                <div className="text-center space-y-2 w-full">
                  <h3 className="text-sm font-semibold leading-tight overflow-hidden text-ellipsis">{member.name}</h3>
                  <p className="text-xs text-muted-foreground truncate">{member.email}</p>

                  {/* Value Display */}
                  <div className="bg-primary/10 rounded-lg px-3 py-2">
                    <div className="text-lg font-bold text-primary">
                      {formatValue(member.value, member.metric)}
                    </div>
                    <div className="text-xs text-muted-foreground capitalize">
                      {metric.replace('_', ' ')}
                    </div>
                  </div>

                  {/* Tier Badge */}
                  {member.loyalty_tier && (
                    <Badge variant="outline" className={`${getTierColorClass(member.loyalty_tier)} font-medium`}>
                      {member.loyalty_tier}
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-12 text-center text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">No top members found</p>
            <p className="text-sm">Start building your loyalty program to see top performing members here.</p>
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-4 border-t">
        <Button asChild variant="outline" className="w-full">
          <Link href="/members" className="flex items-center justify-center gap-2">
            <Users className="h-4 w-4" />
            View All Members
            <ArrowUpRight className="h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  )
}
