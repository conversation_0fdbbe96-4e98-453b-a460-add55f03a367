'use client'

import { useCompany } from '@/contexts/company-context'
import { useRequireAuth } from '@/hooks/use-auth'
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
// Link import removed as we're using router.push instead
import { useRouter } from 'next/navigation'
import { Building, Mail, Hash, Calendar, Settings } from 'lucide-react'
import { format } from 'date-fns'

// Business detail item component
interface BusinessDetailItemProps {
  icon: React.ReactNode
  title: string
  description: string
  highlight?: boolean
}

const BusinessDetailItem = ({ icon, title, description, highlight = false }: BusinessDetailItemProps) => (
  <div className="flex items-start gap-3">
    <div className="h-9 w-9 rounded-full bg-muted/50 flex items-center justify-center">
      <div className="text-muted-foreground">{icon}</div>
    </div>
    <div>
      <div className={`font-medium ${highlight ? 'text-lg' : 'text-sm'}`}>{title}</div>
      <div className="text-xs text-muted-foreground">{description}</div>
    </div>
  </div>
)

export default function BusinessStats() {
  const { company, isLoading } = useCompany()
  const { user } = useRequireAuth()
  const router = useRouter()

  // Get business information from company and user data
  const businessName = company?.name || 'Your Business'
  const businessType = company?.business_type || 'Business'
  const adminEmail = user?.email || 'No email set'
  const businessId = company?.id?.slice(-10) || 'Not set'
  const memberSince = company?.created_at
    ? format(new Date(company.created_at), 'MMMM d, yyyy')
    : 'Not available'

  if (isLoading) {
    return (
      <Card className="premium-card overflow-hidden">
        <CardHeader className="pb-2 space-y-1 border-b border-border/40">
          <div className="flex items-center gap-2">
            <div className="h-5 w-5 rounded-full bg-muted animate-pulse" />
            <div className="h-4 w-24 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-3 w-32 bg-muted animate-pulse rounded" />
        </CardHeader>
        <CardContent className="pt-6">
          <div className="space-y-5">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-start gap-3">
                <div className="h-9 w-9 rounded-full bg-muted animate-pulse" />
                <div className="space-y-1">
                  <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                  <div className="h-3 w-16 bg-muted animate-pulse rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter className="pt-3 pb-4">
          <div className="h-8 w-full bg-muted animate-pulse rounded-full" />
        </CardFooter>
      </Card>
    )
  }

  return (
    <Card className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
      <CardHeader className="pb-2 space-y-1 border-b border-border/40">
        <div className="flex items-center gap-2">
          <div className="h-5 w-5 rounded-full bg-gradient-to-br from-gold/90 to-gold/60 flex items-center justify-center">
            <Building className="h-3 w-3 text-white" />
          </div>
          <CardTitle className="text-base font-medium">Business Details</CardTitle>
        </div>
        <CardDescription className="text-xs">
          Company information and stats
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-5">
          <BusinessDetailItem
            icon={<Building className="h-5 w-5" />}
            title={businessName}
            description={businessType.charAt(0).toUpperCase() + businessType.slice(1)}
            highlight
          />
          <BusinessDetailItem
            icon={<Mail className="h-5 w-5" />}
            title={adminEmail}
            description="Primary Contact Email"
          />
          <BusinessDetailItem
            icon={<Hash className="h-5 w-5" />}
            title={businessId}
            description="Business ID"
          />
          <BusinessDetailItem
            icon={<Calendar className="h-5 w-5" />}
            title={memberSince}
            description="Member Since"
          />
        </div>
      </CardContent>
      <CardFooter className="pt-3 pb-4">
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full rounded-full bg-background/60 hover:bg-background transition-colors border-border/60 hover:border-border"
          onClick={() => {
            // Debug information to help identify issues
            console.log('Edit button clicked');
            console.log('Company context:', company);
            console.log('User context:', user);
            
            // Check localStorage for company ID
            const storedCompanyId = localStorage.getItem('loyal_app_company_id') || 
                                   localStorage.getItem('current_company_id');
            console.log('Stored company ID:', storedCompanyId);
            
            // Ensure company ID is set in localStorage before navigation
            if (company?.id) {
              localStorage.setItem('loyal_app_company_id', company.id);
              localStorage.setItem('current_company_id', company.id);
              console.log('Company ID set in localStorage:', company.id);
            }
            
            // Navigate to edit page
            router.push('/company/edit');
          }}
        >
          <div className="flex items-center justify-center gap-2">
            <Settings className="h-3.5 w-3.5" />
            <span className="text-xs">Edit Business Profile</span>
          </div>
        </Button>
      </CardFooter>
    </Card>
  )
}
