'use client'

import { useState, useEffect, useMemo } from 'react'
import { Card, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useCompany } from '@/contexts/company-context'
import { Users, Award, TrendingUp, Percent, ArrowUpRight, ArrowDownRight, Info } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Skeleton } from '@/components/ui/skeleton'
import { useQuery } from '@tanstack/react-query'

type KpiData = {
  totalMembers: number
  activeMembers: number
  totalRewards: number
  activeRewards: number
  totalPoints: number
  redeemedPoints: number
  redemptionRate: number
}

export default function KpiCards() {
  const { company } = useCompany()
  const [mounted, setMounted] = useState(false)

  // Use the new unified dashboard metrics API
  const { data: dashboardData, isLoading: metricsLoading, error } = useQuery({
    queryKey: ['dashboardMetrics', company?.id],
    queryFn: async () => {
      if (!company?.id) return { data: null };
      const response = await fetch(`/api/dashboard-metrics?companyId=${company.id}`);
      if (!response.ok) throw new Error('Failed to fetch dashboard metrics');
      return response.json();
    },
    enabled: !!company?.id,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  });

  // Calculate KPI data from unified API response
  const kpiData = useMemo(() => {
    // Default values
    const data: KpiData = {
      totalMembers: 0,
      activeMembers: 0,
      totalRewards: 0,
      activeRewards: 0,
      totalPoints: 0,
      redeemedPoints: 0,
      redemptionRate: 0
    };

    // Use data from unified dashboard API
    if (dashboardData?.data) {
      const metrics = dashboardData.data;
      data.totalMembers = metrics.totalMembers || 0;
      data.activeMembers = metrics.activeMembers || 0;
      data.totalRewards = metrics.totalRewards || 0;
      data.activeRewards = metrics.activeRewards || 0;
      data.totalPoints = metrics.totalPoints || 0;
      data.redeemedPoints = metrics.redeemedPoints || 0;
      data.redemptionRate = metrics.redemptionRate || 0;
    }

    return data;
  }, [dashboardData]);

  // Handle hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <KpiCardsSkeleton />;
  }

  const isLoading = !company || metricsLoading;
  const hasError = error;

  if (isLoading) {
    return <KpiCardsSkeleton />;
  }

  // Card data for cleaner rendering
  const kpiCardData = [
    {
      title: "Total Members",
      explanation: "Total number of customers enrolled in your loyalty program. Active Members shows those with recent activity (last 30 days).",
      value: kpiData.totalMembers,
      icon: <Users className="h-5 w-5" />,
      description: "Active Members",
      subValue: kpiData.activeMembers,
      trend: null, // Real trends will be calculated from historical data
      link: "/members",
      bgGradient: "from-primary-50 to-primary-100/30 dark:from-primary-950/20 dark:to-primary-900/10",
      iconClass: "text-primary",
    },
    {
      title: "Total Rewards",
      explanation: "Number of reward offers you've created. Active Rewards shows currently available rewards that haven't expired.",
      value: kpiData.totalRewards,
      icon: <Award className="h-5 w-5" />,
      description: "Active Rewards",
      subValue: kpiData.activeRewards,
      trend: null, // Real trends will be calculated from historical data
      link: "/rewards",
      bgGradient: "from-amber-50 to-amber-100/30 dark:from-amber-950/20 dark:to-amber-900/10",
      iconClass: "text-amber-600",
    },
    {
      title: "Total Points",
      explanation: "Total lifetime points awarded to customers. Redeemed Points shows how many have been used for rewards.",
      value: kpiData.totalPoints,
      icon: <TrendingUp className="h-5 w-5" />,
      description: "Redeemed Points",
      subValue: kpiData.redeemedPoints,
      trend: null, // Real trends will be calculated from historical data
      link: "/transactions",
      bgGradient: "from-emerald-50 to-emerald-100/30 dark:from-emerald-950/20 dark:to-emerald-900/10",
      iconClass: "text-emerald-600",
    },
    {
      title: "Redemption Rate",
      explanation: "Percentage of awarded points that have been redeemed for rewards. Higher rates indicate active engagement with your loyalty program.",
      value: kpiData.redemptionRate.toFixed(1) + "%",
      icon: <Percent className="h-5 w-5" />,
      description: "Points utilization",
      subValue: null,
      trend: null, // Real trends will be calculated from historical data
      link: "/rewards",
      bgGradient: "from-purple-50 to-purple-100/30 dark:from-purple-950/20 dark:to-purple-900/10",
      iconClass: "text-purple-600",
    },
  ];

  return (
    <TooltipProvider>
      <div className="grid gap-5 md:grid-cols-2 lg:grid-cols-4" data-testid="kpi-cards">
        {kpiCardData.map((card, index) => (
          <Card key={index} className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
            <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r ${card.bgGradient}`}>
              <div className="flex items-center gap-2">
                <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help transition-colors" />
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-xs">
                    <p className="text-sm">{card.explanation}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className={`rounded-full p-1.5 bg-background/80 ${card.iconClass}`}>
                {card.icon}
              </div>
            </CardHeader>
          <CardContent className="pt-6">
            {isLoading ? (
              <Skeleton className="h-10 w-24" />
            ) : hasError ? (
              <div className="text-destructive text-sm">Error loading data</div>
            ) : (
              <div className="space-y-5">
                <div className="flex items-baseline">
                  <div className="text-3xl font-bold font-numeric">
                    {card.value.toLocaleString ? card.value.toLocaleString() : card.value}
                  </div>
                  {card.trend && (
                    <div className={`flex items-center ml-3 text-xs font-medium ${
                      card.trend > 0 ? 'text-success' : 'text-destructive'
                    }`}>
                      {card.trend > 0 ? (
                        <ArrowUpRight className="h-3.5 w-3.5 mr-1" />
                      ) : (
                        <ArrowDownRight className="h-3.5 w-3.5 mr-1" />
                      )}
                      {Math.abs(card.trend)}%
                    </div>
                  )}
                </div>
                {card.description && (
                  <div className="flex items-baseline justify-between">
                    <p className="text-xs text-muted-foreground">{card.description}</p>
                    {card.subValue !== undefined && card.subValue !== null && (
                      <span className="text-sm font-medium">{card.subValue.toLocaleString()}</span>
                    )}
                  </div>
                )}
              </div>
            )}
          </CardContent>
          <CardFooter className="pt-0">
            <Button asChild variant="ghost" size="sm" className="w-full justify-start p-0 h-8 text-xs text-muted-foreground hover:text-foreground">
              <Link href={card.link} className="flex items-center gap-1">
                <span>View Details</span>
                <ArrowUpRight className="h-3 w-3 rotate-45" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      ))}
      </div>
    </TooltipProvider>
  )
}

// Skeleton loader component
function KpiCardsSkeleton() {
  return (
    <div className="grid gap-5 md:grid-cols-2 lg:grid-cols-4" data-testid="kpi-cards-skeleton">
      {[...Array(4)].map((_, index) => (
        <Card key={index} className="overflow-hidden border-0 shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-5 w-5 rounded-full" />
          </CardHeader>
          <CardContent className="p-6">
            <Skeleton className="h-10 w-24 mb-2" />
            <Skeleton className="h-4 w-32" />
          </CardContent>
          <CardFooter className="p-0">
            <Skeleton className="h-9 w-full rounded-t-none rounded-b-lg" />
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
