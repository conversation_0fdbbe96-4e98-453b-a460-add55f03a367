'use client'

import { useEffect, useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompanyAdminQuery } from '@/hooks/use-company-admin-query'

export default function DebugAdminPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { adminData, companyId, isLoading: adminLoading, isAdmin } = useCompanyAdminQuery()
  const [apiResponse, setApiResponse] = useState<unknown>(null)
  const [apiError, setApiError] = useState<string | null>(null)

  // Test the API endpoint directly
  useEffect(() => {
    const testAPI = async () => {
      try {
        console.log('Testing /api/admin-status endpoint...')
        const response = await fetch('/api/admin-status', {
          method: 'GET',
          credentials: 'include',
        })

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`)
        }

        const data = await response.json()
        console.log('API Response:', data)
        setApiResponse(data)
      } catch (error) {
        console.error('API Error:', error)
        setApiError(error instanceof Error ? error.message : 'Unknown error')
      }
    }

    // Only test API if we have a user or after a delay
    if (user || !authLoading) {
      testAPI()
    }
  }, [user, authLoading])

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Admin Debug Page</h1>

      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Auth Status</h2>
          <p><strong>Auth Loading:</strong> {authLoading ? 'Yes' : 'No'}</p>
          <p><strong>User ID:</strong> {user?.id || 'None'}</p>
          <p><strong>User Email:</strong> {user?.email || 'None'}</p>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Admin Query Status</h2>
          <p><strong>Admin Loading:</strong> {adminLoading ? 'Yes' : 'No'}</p>
          <p><strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}</p>
          <p><strong>Company ID:</strong> {companyId || 'None'}</p>
          <p><strong>Admin Data:</strong> {adminData ? JSON.stringify(adminData, null, 2) : 'None'}</p>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Direct API Test</h2>
          {apiError ? (
            <div className="text-red-600">
              <p><strong>API Error:</strong> {apiError}</p>
            </div>
          ) : apiResponse ? (
            <div className="text-green-600">
              <p><strong>API Success:</strong></p>
              <pre className="bg-white p-2 rounded mt-2 text-black">
                {JSON.stringify(apiResponse, null, 2)}
              </pre>
            </div>
          ) : (
            <p>Testing API...</p>
          )}
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Storage Check</h2>
          <p><strong>localStorage user_id:</strong> {typeof window !== 'undefined' ? localStorage.getItem('loyal_app_user_id') : 'N/A'}</p>
          <p><strong>sessionStorage user_id:</strong> {typeof window !== 'undefined' ? sessionStorage.getItem('loyal_app_user_id') : 'N/A'}</p>
        </div>
      </div>
    </div>
  )
}
