'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { MemberCombobox, Member } from '@/components/ui/member-combobox'
import { Loader2, Users, Target, User, Filter, X } from 'lucide-react'
import { useMembers } from '@/hooks/use-members'
import { useTiers } from '@/app/tiers/hooks/use-tiers'

interface TargetAudienceStepProps {
  data: {
    target_type: 'all' | 'tier' | 'individual' | 'custom'
    target_criteria: Record<string, unknown>
  }
  updateData: (data: Partial<TargetAudienceStepProps['data']>) => void
  onRecipientCountChange?: (count: number) => void
  errors?: Record<string, string>
}

interface PreviewData {
  recipients: Array<{
    id: string
    name: string
    tier: string
    telegram_chat_id: string | null
  }>
  total_count: number
  telegram_enabled_count: number
}

export default function TargetAudienceStep({ data, updateData, onRecipientCountChange, errors }: TargetAudienceStepProps) {
  const [previewData, setPreviewData] = useState<PreviewData | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Hooks for data
  const { data: membersData } = useMembers()
  const { data: tiersData } = useTiers()

  // Extract current criteria
  const selectedTierNames = (data.target_criteria?.tier_names as string[]) || []
  const selectedMemberIds = (data.target_criteria?.member_ids as string[]) || []
  const minPoints = (data.target_criteria?.min_points as number) || ''
  const maxPoints = (data.target_criteria?.max_points as number) || ''
  const selectedBalanceSegments = (data.target_criteria?.balance_segments as string[]) || []
  const selectedAgeSegments = (data.target_criteria?.member_age_segments as string[]) || []

  // Fetch preview data when target type or criteria changes
  useEffect(() => {
    const fetchPreview = async () => {
      if (!data.target_type) return

      setIsLoading(true)
      try {
        console.log('🚀 Fetching preview with data:', { target_type: data.target_type, target_criteria: data.target_criteria });

        const response = await fetch('/api/marketing/members/preview', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            target_type: data.target_type,
            target_criteria: data.target_criteria
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Preview result:', result);
          setPreviewData(result)
          // Update parent component with recipient count
          onRecipientCountChange?.(result.total_count || 0)
        } else {
          const errorData = await response.json();
          console.error('❌ Preview API error:', errorData);
          // Reset count on error
          onRecipientCountChange?.(0)
        }
      } catch (error) {
        console.error('Error fetching preview:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPreview()
  }, [data.target_type, data.target_criteria, onRecipientCountChange])

  const handleTargetTypeChange = (targetType: 'all' | 'tier' | 'individual' | 'custom') => {
    updateData({
      target_type: targetType,
      target_criteria: {} // Reset criteria when type changes
    })
  }

  const updateCriteria = (newCriteria: Record<string, unknown>) => {
    updateData({
      target_criteria: {
        ...data.target_criteria,
        ...newCriteria
      }
    })
  }

  // Tier selection handlers
  const handleTierChange = (tierName: string) => {
    const currentTiers = [...selectedTierNames]
    if (currentTiers.includes(tierName)) {
      updateCriteria({
        tier_names: currentTiers.filter(t => t !== tierName)
      })
    } else {
      updateCriteria({
        tier_names: [...currentTiers, tierName]
      })
    }
  }

  // Member selection handlers
  const handleMemberChange = (memberId?: string) => {
    if (!memberId) return

    console.log('🎯 Selected member ID:', memberId);

    const currentMembers = [...selectedMemberIds]
    if (currentMembers.includes(memberId)) {
      updateCriteria({
        member_ids: currentMembers.filter(m => m !== memberId)
      })
    } else {
      const newMemberIds = [...currentMembers, memberId];
      console.log('📝 Updating criteria with member IDs:', newMemberIds);
      updateCriteria({
        member_ids: newMemberIds
      })
    }
  }

  const removeMember = (memberId: string) => {
    updateCriteria({
      member_ids: selectedMemberIds.filter(m => m !== memberId)
    })
  }

  // Custom segment handlers
  const handleBalanceSegmentChange = (segment: string, checked: boolean) => {
    const current = [...selectedBalanceSegments]
    if (checked) {
      updateCriteria({
        balance_segments: [...current, segment]
      })
    } else {
      updateCriteria({
        balance_segments: current.filter(s => s !== segment)
      })
    }
  }

  const handleAgeSegmentChange = (segment: string, checked: boolean) => {
    const current = [...selectedAgeSegments]
    if (checked) {
      updateCriteria({
        member_age_segments: [...current, segment]
      })
    } else {
      updateCriteria({
        member_age_segments: current.filter(s => s !== segment)
      })
    }
  }

  // Get member name by ID
  const getMemberName = (memberId: string) => {
    return membersData?.data?.find((m: Member) => m.id === memberId)?.name || 'Unknown Member'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Target Audience</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup
          value={data.target_type}
          onValueChange={handleTargetTypeChange}
          className="space-y-4"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="all" id="target-all" />
            <Label htmlFor="target-all" className="flex items-center space-x-2 cursor-pointer">
              <Users className="h-4 w-4" />
              <span>All members with Telegram</span>
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <RadioGroupItem value="tier" id="target-tier" />
            <Label htmlFor="target-tier" className="flex items-center space-x-2 cursor-pointer">
              <Target className="h-4 w-4" />
              <span>Specific loyalty tier</span>
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <RadioGroupItem value="individual" id="target-individual" />
            <Label htmlFor="target-individual" className="flex items-center space-x-2 cursor-pointer">
              <User className="h-4 w-4" />
              <span>Individual members</span>
            </Label>
          </div>

          <div className="flex items-center space-x-2">
            <RadioGroupItem value="custom" id="target-custom" />
            <Label htmlFor="target-custom" className="flex items-center space-x-2 cursor-pointer">
              <Filter className="h-4 w-4" />
              <span>Custom segment</span>
            </Label>
          </div>
        </RadioGroup>

        {errors?.target_type && (
          <p className="text-sm text-red-500">{errors.target_type}</p>
        )}

        {/* Tier Selection UI */}
        {data.target_type === 'tier' && (
          <div className="p-4 border rounded-lg bg-gray-50 space-y-4">
            <h4 className="font-medium">Select Loyalty Tiers</h4>
            <div className="grid grid-cols-2 gap-3">
              {tiersData?.map((tier) => (
                <div key={tier.id} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`tier-${tier.id}`}
                    checked={selectedTierNames.includes(tier.tier_name)}
                    onChange={() => handleTierChange(tier.tier_name)}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <Label htmlFor={`tier-${tier.id}`} className="text-sm">
                    {tier.tier_name} ({tier.minimum_points}+ pts)
                  </Label>
                </div>
              ))}
            </div>
            {selectedTierNames.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-3">
                {selectedTierNames.map((tierName) => (
                  <Badge key={tierName} variant="secondary">
                    {tierName}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Individual Member Selection UI */}
        {data.target_type === 'individual' && (
          <div className="p-4 border rounded-lg bg-gray-50 space-y-4">
            <h4 className="font-medium">Select Individual Members</h4>
            <MemberCombobox
              value=""
              onValueChange={handleMemberChange}
              members={membersData?.data || []}
              placeholder="Search and select members..."
            />
            {selectedMemberIds.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Selected members:</p>
                <div className="flex flex-wrap gap-2">
                  {selectedMemberIds.map((memberId) => (
                    <Badge key={memberId} variant="secondary" className="flex items-center gap-1">
                      {getMemberName(memberId)}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-auto p-0 ml-1"
                        onClick={() => removeMember(memberId)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Custom Segment UI */}
        {data.target_type === 'custom' && (
          <div className="p-4 border rounded-lg bg-gray-50 space-y-4">
            <h4 className="font-medium">Custom Segment Filters</h4>

            {/* Points Range */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Points Range</Label>
              <div className="flex items-center space-x-2">
                <Input
                  type="number"
                  placeholder="Min points"
                  value={minPoints}
                  onChange={(e) => updateCriteria({ min_points: Number(e.target.value) || undefined })}
                  className="w-24"
                />
                <span className="text-sm text-gray-500">to</span>
                <Input
                  type="number"
                  placeholder="Max points"
                  value={maxPoints}
                  onChange={(e) => updateCriteria({ max_points: Number(e.target.value) || undefined })}
                  className="w-24"
                />
              </div>
            </div>

            {/* Balance Segments */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Balance Segments</Label>
              <div className="grid grid-cols-3 gap-2">
                {['low_balance', 'medium_balance', 'high_balance'].map((segment) => (
                  <div key={segment} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`balance-${segment}`}
                      checked={selectedBalanceSegments.includes(segment)}
                      onChange={(e) => handleBalanceSegmentChange(segment, e.target.checked)}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <Label htmlFor={`balance-${segment}`} className="text-sm capitalize">
                      {segment.replace('_', ' ')}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Member Age Segments */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Member Age Segments</Label>
              <div className="grid grid-cols-3 gap-2">
                {['new', 'recent', 'established'].map((segment) => (
                  <div key={segment} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`age-${segment}`}
                      checked={selectedAgeSegments.includes(segment)}
                      onChange={(e) => handleAgeSegmentChange(segment, e.target.checked)}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <Label htmlFor={`age-${segment}`} className="text-sm capitalize">
                      {segment}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Preview Section */}
        <div className="border-t pt-4">
          <h4 className="font-medium mb-3">Preview Recipients</h4>

          {isLoading ? (
            <div className="flex items-center space-x-2 text-gray-500">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading preview...</span>
            </div>
          ) : previewData ? (
            <div className="space-y-3">
              <div className="flex items-center space-x-4">
                <Badge variant="secondary">
                  {previewData.total_count} eligible recipients
                </Badge>
                {previewData.telegram_enabled_count > previewData.total_count && (
                  <span className="text-sm text-gray-500">
                    ({previewData.telegram_enabled_count - previewData.total_count} without Telegram)
                  </span>
                )}
              </div>

              {previewData.recipients.length > 0 && (
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {previewData.recipients.slice(0, 5).map((recipient) => (
                    <div key={recipient.id} className="text-sm">
                      {recipient.name} ({recipient.tier})
                    </div>
                  ))}
                  {previewData.recipients.length > 5 && (
                    <div className="text-sm text-gray-500">
                      ... and {previewData.recipients.length - 5} more
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <p className="text-sm text-gray-500">
              Select a target type to see preview
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
