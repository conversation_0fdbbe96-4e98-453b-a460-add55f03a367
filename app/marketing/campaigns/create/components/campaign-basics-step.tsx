'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

interface CampaignBasicsStepProps {
  data: {
    name: string
    description?: string
    message_title?: string
  }
  updateData: (data: Partial<CampaignBasicsStepProps['data']>) => void
  errors?: Record<string, string>
}

export default function CampaignBasicsStep({ data, updateData, errors }: CampaignBasicsStepProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Campaign Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="campaign-name">Campaign Name *</Label>
          <Input
            id="campaign-name"
            value={data.name}
            onChange={(e) => updateData({ name: e.target.value })}
            placeholder="Enter campaign name..."
            className={errors?.name ? 'border-red-500' : ''}
          />
          {errors?.name && (
            <p className="text-sm text-red-500">{errors.name}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="campaign-description">Description</Label>
          <Textarea
            id="campaign-description"
            value={data.description || ''}
            onChange={(e) => updateData({ description: e.target.value })}
            placeholder="Brief description of this campaign..."
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="message-title">Message Title (Optional)</Label>
          <Input
            id="message-title"
            value={data.message_title || ''}
            onChange={(e) => updateData({ message_title: e.target.value })}
            placeholder="Optional title for your message..."
          />
          <p className="text-sm text-gray-500">
            This will appear as a bold header in your Telegram message
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
