'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Eye, EyeOff, Sparkles, Edit3 } from 'lucide-react'
import { AIMessageGenerator } from '@/components/marketing/AIMessageGenerator'

interface MessageComposerStepProps {
  data: {
    message_content: string
  }
  updateData: (data: Partial<MessageComposerStepProps['data']>) => void
  errors?: Record<string, string>
}

const AVAILABLE_VARIABLES = [
  { key: '{{name}}', description: 'Member name' },
  { key: '{{tier}}', description: 'Membership tier' },
  { key: '{{points}}', description: 'Current points balance' },
  { key: '{{company}}', description: 'Company name' }
]

const SAMPLE_DATA = {
  name: '<PERSON>',
  tier: 'Gold',
  points: '1,250',
  company: 'Coffee Shop'
}

export default function MessageComposerStep({ data, updateData, errors }: MessageComposerStepProps) {
  const [showPreview, setShowPreview] = useState(false)

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('message-content') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const text = data.message_content
      const newText = text.substring(0, start) + variable + text.substring(end)
      updateData({ message_content: newText })

      // Restore cursor position
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + variable.length, start + variable.length)
      }, 0)
    }
  }

  const generatePreview = () => {
    let preview = data.message_content
    AVAILABLE_VARIABLES.forEach(variable => {
      const regex = new RegExp(variable.key.replace(/[{}]/g, '\\$&'), 'g')
      preview = preview.replace(regex, SAMPLE_DATA[variable.key.slice(2, -2) as keyof typeof SAMPLE_DATA])
    })
    return preview
  }

  const characterCount = data.message_content.length
  const isNearLimit = characterCount > 3500
  const isOverLimit = characterCount > 4096

  return (
    <div className="space-y-6">
      <Tabs defaultValue="compose" className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-gray-100 p-1 rounded-lg">
          <TabsTrigger value="compose" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Edit3 className="w-4 h-4 mr-2" />
            Manual Compose
          </TabsTrigger>
          <TabsTrigger value="ai-generate" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
            <Sparkles className="w-4 h-4 mr-2" />
            AI Generate
          </TabsTrigger>
        </TabsList>

        <TabsContent value="compose" className="mt-6">
          <Card className="bg-white shadow-sm border border-gray-200">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
              <CardTitle className="text-blue-900">Compose Message</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              <div className="space-y-2">
                <Label htmlFor="message-content" className="text-sm font-medium text-gray-700">Message Content *</Label>
                <Textarea
                  id="message-content"
                  value={data.message_content}
                  onChange={(e) => updateData({ message_content: e.target.value })}
                  placeholder="Write your message here... Use variables like {{name}} for personalization."
                  rows={10}
                  className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none ${errors?.message_content ? 'border-red-500' : ''}`}
                />

                <div className="flex items-center justify-between text-sm">
                  <span className={`${isOverLimit ? 'text-red-500' : isNearLimit ? 'text-yellow-500' : 'text-gray-500'}`}>
                    {characterCount}/4096 characters
                  </span>
                  {isOverLimit && (
                    <span className="text-red-500 font-medium">Message too long!</span>
                  )}
                </div>

                {errors?.message_content && (
                  <p className="text-sm text-red-500 bg-red-50 p-2 rounded">{errors.message_content}</p>
                )}
              </div>

              {/* Variable Buttons */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-gray-700">Available Variables</Label>
                <div className="grid grid-cols-2 gap-3">
                  {AVAILABLE_VARIABLES.map((variable) => (
                    <Button
                      key={variable.key}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => insertVariable(variable.key)}
                      className="justify-start hover:bg-blue-50 hover:border-blue-300"
                    >
                      <Copy className="h-3 w-3 mr-2" />
                      {variable.key}
                    </Button>
                  ))}
                </div>
                <p className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg">
                  💡 Click to insert variables that will be replaced with member data
                </p>
              </div>

              {/* Preview Toggle */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-700">Message Preview</Label>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPreview(!showPreview)}
                    className="hover:bg-gray-100"
                  >
                    {showPreview ? (
                      <>
                        <EyeOff className="h-4 w-4 mr-2" />
                        Hide Preview
                      </>
                    ) : (
                      <>
                        <Eye className="h-4 w-4 mr-2" />
                        Show Preview
                      </>
                    )}
                  </Button>
                </div>

                {showPreview && (
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 max-w-md mx-auto">
                      <div className="p-4 border-b border-gray-200">
                        <div className="text-sm text-gray-600 mb-1">
                          Preview as: {SAMPLE_DATA.name} ({SAMPLE_DATA.tier} tier)
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="whitespace-pre-wrap text-sm leading-relaxed">
                          {generatePreview() || 'Your message will appear here...'}
                        </div>
                      </div>
                      <div className="p-4 bg-gray-50 rounded-b-lg">
                        <p className="text-xs text-gray-500 text-center">
                          Sent via Your Loyalty Program
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Variable Reference */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Variable Reference</Label>
                <div className="grid gap-3">
                  {AVAILABLE_VARIABLES.map((variable) => (
                    <div key={variable.key} className="flex items-center justify-between text-sm p-3 bg-gray-50 rounded-lg">
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800">{variable.key}</Badge>
                      <span className="text-gray-600">{variable.description}</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ai-generate" className="mt-6">
          <AIMessageGenerator
            onMessageGenerated={(message) => {
              updateData({ message_content: message })
            }}
            businessName="Your Business"
            businessType="retail"
            initialMessage={data.message_content}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
