'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Send, AlertTriangle, Clock, Users } from 'lucide-react'

interface ReviewAndSendStepProps {
  data: {
    name: string
    description?: string
    message_title?: string
    message_content: string
    target_type: 'all' | 'tier' | 'individual' | 'custom'
    target_criteria: Record<string, unknown>
    scheduled_at?: string
  }
  onSend: () => Promise<void>
  isSubmitting: boolean
  recipientCount?: number
}

export default function ReviewAndSendStep({
  data,
  onSend,
  isSubmitting,
  recipientCount = 0
}: ReviewAndSendStepProps) {
  const [confirmed, setConfirmed] = useState(false)
  const [sendingType, setSendingType] = useState<'immediate' | 'scheduled'>('immediate')
  const [actualRecipientCount, setActualRecipientCount] = useState(recipientCount)
  const [isLoadingPreview, setIsLoadingPreview] = useState(false)

  // Fetch actual recipient count when component mounts or data changes
  useEffect(() => {
    const fetchRecipientCount = async () => {
      if (!data.target_type) return

      setIsLoadingPreview(true)
      try {
        console.log('🔍 ReviewAndSendStep fetching preview with data:', { target_type: data.target_type, target_criteria: data.target_criteria })

        const response = await fetch('/api/marketing/members/preview', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            target_type: data.target_type,
            target_criteria: data.target_criteria
          })
        })

        if (response.ok) {
          const result = await response.json()
          console.log('📊 ReviewAndSendStep preview result:', result)
          setActualRecipientCount(result.total_count || 0)
        } else {
          console.error('❌ ReviewAndSendStep preview error')
          setActualRecipientCount(0)
        }
      } catch (error) {
        console.error('Error fetching preview in ReviewAndSendStep:', error)
        setActualRecipientCount(0)
      } finally {
        setIsLoadingPreview(false)
      }
    }

    fetchRecipientCount()
  }, [data.target_type, data.target_criteria])

  const getTargetTypeDisplay = () => {
    switch (data.target_type) {
      case 'all':
        return 'All members with Telegram'
      case 'tier':
        return 'Specific loyalty tiers'
      case 'individual':
        return 'Individual members'
      case 'custom':
        return 'Custom segment'
      default:
        return 'Unknown'
    }
  }

  const estimatedDuration = Math.ceil(actualRecipientCount / 20) // Rough estimate: 20 messages per minute

  const handleSend = async () => {
    if (!confirmed) return
    await onSend()
  }

  return (
    <div className="space-y-6">
      {/* Campaign Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Campaign Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div>
              <Label className="text-sm font-medium">Campaign Name</Label>
              <p className="text-sm text-gray-600">{data.name}</p>
            </div>

            {data.description && (
              <div>
                <Label className="text-sm font-medium">Description</Label>
                <p className="text-sm text-gray-600">{data.description}</p>
              </div>
            )}

            <div>
              <Label className="text-sm font-medium">Target Audience</Label>
              <p className="text-sm text-gray-600">{getTargetTypeDisplay()}</p>
            </div>

            <div>
              <Label className="text-sm font-medium">Recipients</Label>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {isLoadingPreview ? (
                  <>
                    <Loader2 className="h-3 w-3 animate-spin" />
                    Loading...
                  </>
                ) : (
                  <>
                    {actualRecipientCount} members
                  </>
                )}
              </Badge>
              {actualRecipientCount > 0 && (
                  <span className="text-sm text-gray-500">
                    Estimated delivery: {estimatedDuration} minute{estimatedDuration !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Message Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Message Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 border rounded-lg bg-gray-50 space-y-2">
            {data.message_title && (
              <div className="font-semibold text-sm">{data.message_title}</div>
            )}
            <div className="whitespace-pre-wrap text-sm">
              {data.message_content}
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Variables like {'{'}{'{'} name {'}'}{'}'} will be replaced with actual member data
          </p>
        </CardContent>
      </Card>

      {/* Delivery Options */}
      <Card>
        <CardHeader>
          <CardTitle>Delivery Options</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="immediate"
                name="delivery"
                checked={sendingType === 'immediate'}
                onChange={() => setSendingType('immediate')}
                className="w-4 h-4"
              />
              <label htmlFor="immediate" className="text-sm cursor-pointer">
                Send immediately
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="scheduled"
                name="delivery"
                checked={sendingType === 'scheduled'}
                onChange={() => setSendingType('scheduled')}
                className="w-4 h-4"
              />
              <label htmlFor="scheduled" className="text-sm cursor-pointer">
                Schedule for later
              </label>
            </div>
          </div>

          {sendingType === 'scheduled' && (
            <div className="p-4 border rounded-lg bg-blue-50">
              <div className="flex items-center space-x-2 text-blue-600">
                <Clock className="h-4 w-4" />
                <span className="text-sm">Scheduled delivery coming soon!</span>
              </div>
              <p className="text-sm text-blue-600 mt-1">
                This feature will be available in the next update.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Warnings */}
      {actualRecipientCount > 50 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            This campaign will send to {actualRecipientCount} members. Large campaigns may take several minutes to complete.
          </AlertDescription>
        </Alert>
      )}

      {actualRecipientCount === 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">
            No eligible recipients found. Please review your targeting criteria.
          </AlertDescription>
        </Alert>
      )}

      {/* Confirmation */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="confirm-send"
                checked={confirmed}
                onChange={(e) => setConfirmed(e.target.checked)}
                disabled={actualRecipientCount === 0}
                className="w-4 h-4"
              />
              <label htmlFor="confirm-send" className="text-sm cursor-pointer">
                I confirm I want to send this campaign to {actualRecipientCount} members
              </label>
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={handleSend}
                disabled={!confirmed || actualRecipientCount === 0 || isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending Campaign...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Campaign
                  </>
                )}
              </Button>
            </div>

            {sendingType === 'immediate' && confirmed && actualRecipientCount > 0 && (
              <Alert>
                <AlertDescription>
                  ⚠️ This will immediately send {actualRecipientCount} Telegram messages. This action cannot be undone.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Missing Label component - let's add it
function Label({ children, className = '', ...props }: {
  children: React.ReactNode;
  className?: string;
  [key: string]: unknown
}) {
  return (
    <label className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`} {...props}>
      {children}
    </label>
  )
}
