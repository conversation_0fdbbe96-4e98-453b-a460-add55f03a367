'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/layout/MainLayout'
import { PageHeader } from '@/components/page-header'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { MessageSquare, ArrowLeft, ArrowRight } from 'lucide-react'

// Import wizard steps
import CampaignBasicsStep from './components/campaign-basics-step'
import TargetAudienceStep from './components/target-audience-step'
import MessageComposerStep from './components/message-composer-step'
import ReviewAndSendStep from './components/review-and-send-step'

interface CampaignData {
  name: string
  description?: string
  message_title?: string
  message_content: string
  target_type: 'all' | 'tier' | 'individual' | 'custom'
  target_criteria: Record<string, unknown>
  scheduled_at?: string
}

const steps = [
  {
    title: 'Campaign Details',
    description: 'Basic campaign information'
  },
  {
    title: 'Target Audience',
    description: 'Select who will receive your message'
  },
  {
    title: 'Compose Message',
    description: 'Create your marketing message'
  },
  {
    title: 'Review & Send',
    description: 'Review and send your campaign'
  }
]

export default function CreateCampaignPage() {
  useRequireAuth()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: '',
    description: '',
    message_title: '',
    message_content: '',
    target_type: 'all',
    target_criteria: {},
    scheduled_at: undefined
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [recipientCount, setRecipientCount] = useState(0)

  const updateCampaignData = (updates: Partial<CampaignData>) => {
    setCampaignData(prev => ({ ...prev, ...updates }))
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      console.log(`🚶 Moving to step ${currentStep + 1}, current campaign data:`, campaignData)
      console.log(`📊 Current recipient count:`, recipientCount)
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const sendCampaign = async () => {
    try {
      setIsSubmitting(true)
      setError(null)

      // First create the campaign
      const createResponse = await fetch('/api/marketing/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(campaignData)
      })

      if (!createResponse.ok) {
        const errorData = await createResponse.json()
        throw new Error(errorData.error || 'Failed to create campaign')
      }

      const createData = await createResponse.json()
      const campaignId = createData.campaign.id

      // Then send the campaign
      const sendResponse = await fetch(`/api/marketing/campaigns/${campaignId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ confirm: true })
      })

      if (!sendResponse.ok) {
        const errorData = await sendResponse.json()
        throw new Error(errorData.error || 'Failed to send campaign')
      }

      router.push(`/marketing/campaigns/${campaignId}`)
    } catch (error) {
      console.error('Error sending campaign:', error)
      setError(error instanceof Error ? error.message : 'Failed to send campaign')
    } finally {
      setIsSubmitting(false)
    }
  }

  const progress = ((currentStep + 1) / steps.length) * 100

  return (
    <MainLayout>
      <div className="space-y-8">
        <PageHeader
          heading="Create Campaign"
          subheading="Create a new marketing campaign to send to your members"
          icon={<MessageSquare className="h-8 w-8" />}
        />

        {/* Progress Indicator */}
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="font-medium">
                  Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
                </h3>
                <span className="text-sm text-muted-foreground">
                  {Math.round(progress)}% complete
                </span>
              </div>
              <Progress value={progress} className="w-full" />
              <p className="text-sm text-muted-foreground">
                {steps[currentStep].description}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <p className="text-red-600">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Step Content */}
        <Card>
          <CardContent className="p-6">
            {currentStep === 0 && (
              <CampaignBasicsStep
                data={campaignData}
                updateData={updateCampaignData}
              />
            )}
            {currentStep === 1 && (
              <TargetAudienceStep
                data={campaignData}
                updateData={updateCampaignData}
                onRecipientCountChange={setRecipientCount}
              />
            )}
            {currentStep === 2 && (
              <MessageComposerStep
                data={campaignData}
                updateData={updateCampaignData}
              />
            )}
            {currentStep === 3 && (
              <ReviewAndSendStep
                data={campaignData}
                onSend={sendCampaign}
                isSubmitting={isSubmitting}
                recipientCount={recipientCount}
              />
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={() => router.push('/marketing/campaigns')}
                disabled={isSubmitting}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Cancel
              </Button>

              <div className="flex items-center gap-2">
                {currentStep > 0 && (
                  <Button
                    variant="outline"
                    onClick={prevStep}
                    disabled={isSubmitting}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>
                )}

                {currentStep < steps.length - 1 && (
                  <Button
                    onClick={nextStep}
                    disabled={isSubmitting}
                  >
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
