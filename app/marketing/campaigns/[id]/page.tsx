'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { ArrowLeft, Users, CheckCircle, XCircle, Clock, Send, TrendingUp, Eye, MessageSquare, Sparkles } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { createClient } from '@/lib/supabase/client'
import { toast } from 'sonner'
import { AIMessageGenerator } from '@/components/marketing/AIMessageGenerator'

interface TargetCriteria {
  tiers?: string[]
  memberIds?: string[]
  filters?: Record<string, string | number | boolean>
}

interface Campaign {
  id: string
  name: string
  message: string
  status: 'draft' | 'sent' | 'scheduled'
  target_type: 'all' | 'tier' | 'individual' | 'custom'
  target_criteria?: TargetCriteria
  created_at: string
  sent_at?: string
  business_id: string
}

interface CampaignRecipient {
  id: string
  member_id: string
  status: 'pending' | 'sent' | 'failed'
  sent_at?: string
  error_message?: string
  member: {
    id: string
    name: string
    email: string
    phone_number: string
    loyalty_tier: string
    lifetime_points: number
    profile_image_url?: string
  }
}

interface CampaignStats {
  total_recipients: number
  sent_count: number
  failed_count: number
  pending_count: number
}

export default function CampaignDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [campaign, setCampaign] = useState<Campaign | null>(null)
  const [recipients, setRecipients] = useState<CampaignRecipient[]>([])
  const [stats, setStats] = useState<CampaignStats>({
    total_recipients: 0,
    sent_count: 0,
    failed_count: 0,
    pending_count: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!params.id) return

    const fetchCampaignDetails = async () => {
      try {
        const supabase = createClient()

        // Fetch campaign details
        const { data: campaignData, error: campaignError } = await supabase
          .from('marketing_campaigns')
          .select('*')
          .eq('id', params.id)
          .single()

        if (campaignError) throw campaignError

        setCampaign(campaignData)

        // Fetch campaign recipients with member details
        const { data: recipientsData, error: recipientsError } = await supabase
          .from('campaign_recipients')
          .select(`
            id,
            member_id,
            status,
            sent_at,
            error_message,
            member:loyalty_members(
              id,
              name,
              email,
              phone_number,
              loyalty_tier,
              lifetime_points,
              profile_image_url
            )
          `)
          .eq('campaign_id', params.id)

        if (recipientsError) throw recipientsError

        // Transform the data to handle the member array
        const transformedRecipients = recipientsData?.map(recipient => ({
          ...recipient,
          member: Array.isArray(recipient.member) ? recipient.member[0] : recipient.member
        })) || []

        setRecipients(transformedRecipients)

        // Calculate stats
        const total = transformedRecipients.length
        const sent = transformedRecipients.filter(r => r.status === 'sent').length
        const failed = transformedRecipients.filter(r => r.status === 'failed').length
        const pending = transformedRecipients.filter(r => r.status === 'pending').length

        setStats({
          total_recipients: total,
          sent_count: sent,
          failed_count: failed,
          pending_count: pending
        })

      } catch (error) {
        console.error('Error fetching campaign details:', error)
        toast.error('Failed to load campaign details')
      } finally {
        setLoading(false)
      }
    }

    fetchCampaignDetails()
  }, [params.id])

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return <Badge variant="secondary" className="text-green-700 bg-green-100"><CheckCircle className="w-3 h-3 mr-1" />Sent</Badge>
      case 'draft':
        return <Badge variant="outline"><Clock className="w-3 h-3 mr-1" />Draft</Badge>
      case 'scheduled':
        return <Badge variant="secondary" className="text-blue-700 bg-blue-100"><Clock className="w-3 h-3 mr-1" />Scheduled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getRecipientStatusBadge = (status: string) => {
    switch (status) {
      case 'sent':
        return <Badge variant="secondary" className="text-green-700 bg-green-100"><CheckCircle className="w-3 h-3 mr-1" />Delivered</Badge>
      case 'failed':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Failed</Badge>
      case 'pending':
        return <Badge variant="outline"><Clock className="w-3 h-3 mr-1" />Pending</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTargetTypeDisplay = (targetType: string, targetCriteria?: TargetCriteria) => {
    switch (targetType) {
      case 'all':
        return 'All Members'
      case 'tier':
        return `Tier: ${targetCriteria?.tiers?.join(', ') || 'Unknown'}`
      case 'individual':
        return 'Selected Members'
      case 'custom':
        return 'Custom Segment'
      default:
        return targetType
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!campaign) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold text-gray-900">Campaign not found</h2>
          <p className="text-gray-600 mt-2">The campaign you&apos;re looking for doesn&apos;t exist or has been deleted.</p>
          <Button
            onClick={() => router.push('/marketing/campaigns')}
            className="mt-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Campaigns
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto max-w-7xl p-6 space-y-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/marketing/campaigns')}
                className="hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{campaign.name}</h1>
                <p className="text-gray-600 mt-1">
                  Created {formatDistanceToNow(new Date(campaign.created_at))} ago
                  {campaign.sent_at && ` • Sent ${formatDistanceToNow(new Date(campaign.sent_at))} ago`}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(campaign.status)}
              {campaign.status !== 'sent' && (
                <Button
                  variant="outline"
                  onClick={() => router.push(`/marketing/campaigns/${campaign.id}/edit`)}
                  className="hover:bg-gray-50"
                >
                  Edit Campaign
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-800">Total Recipients</CardTitle>
              <Users className="h-5 w-5 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-900">{stats.total_recipients}</div>
              <p className="text-xs text-blue-700 mt-1">Campaign reach</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-800">Successfully Sent</CardTitle>
              <CheckCircle className="h-5 w-5 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-900">{stats.sent_count}</div>
              <div className="flex items-center justify-between mt-2">
                <p className="text-xs text-green-700">
                  {stats.total_recipients > 0 ? Math.round((stats.sent_count / stats.total_recipients) * 100) : 0}% success rate
                </p>
                <Progress 
                  value={stats.total_recipients > 0 ? (stats.sent_count / stats.total_recipients) * 100 : 0} 
                  className="w-16 h-2"
                />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-red-800">Failed</CardTitle>
              <XCircle className="h-5 w-5 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-900">{stats.failed_count}</div>
              <p className="text-xs text-red-700 mt-1">
                {stats.total_recipients > 0 ? Math.round((stats.failed_count / stats.total_recipients) * 100) : 0}% failure rate
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200 hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-yellow-800">Pending</CardTitle>
              <Clock className="h-5 w-5 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-yellow-900">{stats.pending_count}</div>
              <p className="text-xs text-yellow-700 mt-1">Awaiting delivery</p>
            </CardContent>
          </Card>
        </div>

        {/* Campaign Details & Recipients */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-gray-100 p-1 rounded-lg">
            <TabsTrigger value="overview" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <Eye className="w-4 h-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="recipients" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <Users className="w-4 h-4 mr-2" />
              Recipients ({stats.total_recipients})
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <TrendingUp className="w-4 h-4 mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="ai-insights" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <Sparkles className="w-4 h-4 mr-2" />
              AI Insights
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Campaign Info */}
              <Card className="bg-white shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                  <CardTitle className="text-blue-900">Campaign Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 p-6">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Target Audience</label>
                      <p className="text-sm text-gray-900 font-semibold">{getTargetTypeDisplay(campaign.target_type, campaign.target_criteria)}</p>
                    </div>
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Status</label>
                      <div className="mt-1">{getStatusBadge(campaign.status)}</div>
                    </div>
                    <Clock className="w-5 h-5 text-gray-400" />
                  </div>
                  {campaign.sent_at && (
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Sent At</label>
                        <p className="text-sm text-gray-900 font-semibold">
                          {new Date(campaign.sent_at).toLocaleString()}
                        </p>
                      </div>
                      <Send className="w-5 h-5 text-gray-400" />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Message Preview */}
              <Card className="bg-white shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
                  <CardTitle className="text-green-900">Message Content</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                          <MessageSquare className="w-5 h-5 text-white" />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                          <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">{campaign.message}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="recipients" className="space-y-6">
            <Card className="bg-white shadow-sm border border-gray-200">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-t-lg">
                <CardTitle className="text-purple-900">Recipient Details</CardTitle>
                <CardDescription className="text-purple-700">
                  Delivery status for each campaign recipient
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                {recipients.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="h-8 w-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No recipients</h3>
                    <p className="text-sm text-gray-500">This campaign doesn&apos;t have any recipients yet.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {recipients.map((recipient) => (
                      <div
                        key={recipient.id}
                        className="flex items-center justify-between p-4 border border-gray-200 rounded-xl hover:shadow-md transition-shadow bg-gradient-to-r from-white to-gray-50"
                      >
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12 ring-2 ring-gray-200">
                            <AvatarImage src={recipient.member.profile_image_url} />
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                              {recipient.member.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-semibold text-gray-900">{recipient.member.name}</p>
                            <p className="text-sm text-gray-600">{recipient.member.email}</p>
                            <div className="flex items-center space-x-2 mt-2">
                              <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                                {recipient.member.loyalty_tier}
                              </Badge>
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {recipient.member.lifetime_points} points
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          {getRecipientStatusBadge(recipient.status)}
                          {recipient.sent_at && (
                            <p className="text-xs text-gray-500 mt-1">
                              {formatDistanceToNow(new Date(recipient.sent_at))} ago
                            </p>
                          )}
                          {recipient.error_message && (
                            <p className="text-xs text-red-600 mt-1 max-w-48 truncate" title={recipient.error_message}>
                              {recipient.error_message}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white shadow-sm border border-gray-200">
                <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg">
                  <CardTitle className="text-green-900">Delivery Performance</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">Success Rate</span>
                      <span className="text-lg font-bold text-green-600">
                        {stats.total_recipients > 0 ? Math.round((stats.sent_count / stats.total_recipients) * 100) : 0}%
                      </span>
                    </div>
                    <Progress 
                      value={stats.total_recipients > 0 ? (stats.sent_count / stats.total_recipients) * 100 : 0} 
                      className="h-3"
                    />
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="font-bold text-green-600">{stats.sent_count}</div>
                        <div className="text-green-700">Delivered</div>
                      </div>
                      <div className="text-center p-3 bg-red-50 rounded-lg">
                        <div className="font-bold text-red-600">{stats.failed_count}</div>
                        <div className="text-red-700">Failed</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white shadow-sm border border-gray-200">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                  <CardTitle className="text-blue-900">Campaign Insights</CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm font-medium text-gray-700">Total Reach</span>
                      <span className="text-lg font-bold text-blue-600">{stats.total_recipients}</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm font-medium text-gray-700">Pending Delivery</span>
                      <span className="text-lg font-bold text-yellow-600">{stats.pending_count}</span>
                    </div>
                    <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">
                        {campaign.sent_at ? 'Sent' : 'Draft'}
                      </div>
                      <div className="text-sm text-blue-700">Campaign Status</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="ai-insights" className="space-y-6">
            <AIMessageGenerator
              onMessageGenerated={() => {
                // This could be used to suggest improvements to the current campaign
                toast.success('AI suggestions generated!')
              }}
              businessName="Your Business"
              businessType="retail"
              initialMessage={campaign.message}
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
