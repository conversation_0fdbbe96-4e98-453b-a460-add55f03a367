'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ArrowLeft, Save, Sparkles, Edit3, Eye } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { toast } from 'sonner'
import { AIMessageGenerator } from '@/components/marketing/AIMessageGenerator'

interface TargetCriteria {
  tiers?: string[]
  memberIds?: string[]
  filters?: Record<string, string | number | boolean>
}

interface Campaign {
  id: string
  name: string
  message: string
  status: 'draft' | 'sent' | 'scheduled'
  target_type: 'all' | 'tier' | 'individual' | 'custom'
  target_criteria?: TargetCriteria
  created_at: string
  sent_at?: string
  business_id: string
}

export default function EditCampaignPage() {
  const params = useParams()
  const router = useRouter()
  const [campaign, setCampaign] = useState<Campaign | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    message: ''
  })

  useEffect(() => {
    if (!params.id) return

    const fetchCampaign = async () => {
      try {
        const supabase = createClient()
        const { data, error } = await supabase
          .from('marketing_campaigns')
          .select('*')
          .eq('id', params.id)
          .single()

        if (error) throw error

        setCampaign(data)
        setFormData({
          name: data.name,
          message: data.message
        })
      } catch (error) {
        console.error('Error fetching campaign:', error)
        toast.error('Failed to load campaign')
      } finally {
        setLoading(false)
      }
    }

    fetchCampaign()
  }, [params.id])

  const handleSave = async () => {
    if (!campaign) return

    // Don't allow editing sent campaigns
    if (campaign.status === 'sent') {
      toast.error('Cannot edit campaigns that have already been sent')
      return
    }

    setSaving(true)
    try {
      const supabase = createClient()
      const { error } = await supabase
        .from('marketing_campaigns')
        .update({
          name: formData.name,
          message: formData.message,
          updated_at: new Date().toISOString()
        })
        .eq('id', campaign.id)

      if (error) throw error

      toast.success('Campaign updated successfully')
      router.push(`/marketing/campaigns/${campaign.id}`)
    } catch (error) {
      console.error('Error updating campaign:', error)
      toast.error('Failed to update campaign')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto max-w-4xl p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!campaign) {
    return (
      <div className="container mx-auto max-w-4xl p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold text-gray-900">Campaign not found</h2>
          <p className="text-gray-600 mt-2">The campaign you&apos;re looking for doesn&apos;t exist or has been deleted.</p>
          <Button
            onClick={() => router.push('/marketing/campaigns')}
            className="mt-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Campaigns
          </Button>
        </div>
      </div>
    )
  }

  // Don't allow editing sent campaigns
  if (campaign.status === 'sent') {
    return (
      <div className="container mx-auto max-w-4xl p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold text-gray-900">Cannot Edit Sent Campaign</h2>
          <p className="text-gray-600 mt-2">This campaign has already been sent and cannot be modified.</p>
          <Button
            onClick={() => router.push(`/marketing/campaigns/${campaign.id}`)}
            className="mt-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            View Campaign
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto max-w-6xl p-6 space-y-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push(`/marketing/campaigns/${campaign.id}`)}
                className="hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Edit Campaign</h1>
                <p className="text-gray-600 mt-1">Enhance your campaign with AI-powered suggestions</p>
              </div>
            </div>
            <Button 
              onClick={handleSave} 
              disabled={saving}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {/* Edit Form with Tabs */}
        <Tabs defaultValue="edit" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-100 p-1 rounded-lg">
            <TabsTrigger value="edit" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <Edit3 className="w-4 h-4 mr-2" />
              Edit Campaign
            </TabsTrigger>
            <TabsTrigger value="ai-enhance" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <Sparkles className="w-4 h-4 mr-2" />
              AI Enhancement
            </TabsTrigger>
            <TabsTrigger value="preview" className="data-[state=active]:bg-white data-[state=active]:shadow-sm">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="edit" className="space-y-6">
            <Card className="bg-white shadow-sm border border-gray-200">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                <CardTitle className="text-blue-900">Campaign Details</CardTitle>
                <CardDescription className="text-blue-700">
                  Update your campaign name and message content
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6 p-6">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium text-gray-700">Campaign Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter campaign name"
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message" className="text-sm font-medium text-gray-700">Message Content</Label>
                  <Textarea
                    id="message"
                    value={formData.message}
                    onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Enter your campaign message"
                    rows={8}
                    className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 resize-none"
                  />
                  <p className="text-sm text-gray-500 bg-blue-50 p-3 rounded-lg">
                    💡 Tip: Use the AI Enhancement tab to generate compelling marketing messages automatically!
                  </p>
                </div>

                {/* Campaign Info (Read-only) */}
                <div className="border-t pt-6 space-y-4">
                  <h3 className="font-semibold text-gray-900 flex items-center">
                    <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                    Campaign Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</span>
                      <p className="text-sm font-semibold text-gray-900 capitalize mt-1">{campaign.status}</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Target Type</span>
                      <p className="text-sm font-semibold text-gray-900 capitalize mt-1">{campaign.target_type.replace('_', ' ')}</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Created</span>
                      <p className="text-sm font-semibold text-gray-900 mt-1">{new Date(campaign.created_at).toLocaleDateString()}</p>
                    </div>
                    {campaign.sent_at && (
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Sent</span>
                        <p className="text-sm font-semibold text-gray-900 mt-1">{new Date(campaign.sent_at).toLocaleDateString()}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ai-enhance" className="space-y-6">
            <AIMessageGenerator
              onMessageGenerated={(message, subjectLines) => {
                setFormData(prev => ({ ...prev, message }))
                toast.success(`AI message generated! ${subjectLines.length > 0 ? `Got ${subjectLines.length} subject line suggestions too.` : ''}`)
              }}
              businessName="Your Business"
              businessType="retail"
              initialMessage={formData.message}
            />
          </TabsContent>

          <TabsContent value="preview" className="space-y-6">
            <Card className="bg-white shadow-sm border border-gray-200">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-lg">
                <CardTitle className="text-green-900">Campaign Preview</CardTitle>
                <CardDescription className="text-green-700">
                  See how your campaign will look to recipients
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
                  <div className="bg-white rounded-lg shadow-sm border border-gray-200 max-w-md mx-auto">
                    <div className="p-4 border-b border-gray-200">
                      <h3 className="font-semibold text-gray-900">{formData.name || 'Campaign Name'}</h3>
                      <p className="text-xs text-gray-500 mt-1">Marketing Campaign</p>
                    </div>
                    <div className="p-4">
                      <div className="prose prose-sm max-w-none">
                        <p className="text-gray-900 whitespace-pre-wrap leading-relaxed">
                          {formData.message || 'Your campaign message will appear here...'}
                        </p>
                      </div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-b-lg">
                      <p className="text-xs text-gray-500 text-center">
                        Sent via Your Loyalty Program
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
