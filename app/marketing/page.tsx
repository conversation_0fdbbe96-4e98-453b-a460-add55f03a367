'use client'

import { useEffect, useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import MainLayout from '@/components/layout/MainLayout'
import { PageHeader } from '@/components/page-header'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Users,
  Send,
  TrendingUp,
  Plus,
  Edit,
  Eye,
  Copy
} from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'

interface Campaign {
  id: string
  name: string
  description?: string
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
  target_type: string
  total_recipients: number
  successful_sends: number
  failed_sends: number
  delivery_rate: number
  scheduled_at?: string
  sent_at?: string
  created_at: string
  updated_at: string
}

interface MarketingStats {
  total_sent: number
  active_campaigns: number
  telegram_members: number
  recent_activity: Array<{
    campaign_name: string
    action: string
    timestamp: string
  }>
}

const StatusBadge = ({ status }: { status: Campaign['status'] }) => {
  const variants = {
    draft: 'secondary',
    scheduled: 'outline',
    sending: 'default',
    sent: 'default',
    failed: 'destructive'
  } as const

  const labels = {
    draft: 'Draft',
    scheduled: 'Scheduled',
    sending: 'Sending...',
    sent: 'Sent',
    failed: 'Failed'
  }

  return (
    <Badge variant={variants[status]}>
      {labels[status]}
    </Badge>
  )
}

export default function MarketingDashboard() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [stats, setStats] = useState<MarketingStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  const fetchData = async () => {
    try {
      setLoading(true)

      // Fetch campaigns
      const campaignResponse = await fetch('/api/marketing/campaigns')
      if (!campaignResponse.ok) {
        throw new Error('Failed to fetch campaigns')
      }
      const campaignData = await campaignResponse.json()
      setCampaigns(campaignData.campaigns || [])

      // Calculate stats
      const totalSent = campaignData.campaigns?.reduce((sum: number, c: Campaign) => sum + c.successful_sends, 0) || 0
      const activeCampaigns = campaignData.campaigns?.filter((c: Campaign) =>
        ['scheduled', 'sending'].includes(c.status)
      ).length || 0

      // Fetch member segments for telegram count
      const segmentResponse = await fetch('/api/marketing/members/segments')
      let telegramMembers = 0
      if (segmentResponse.ok) {
        const segmentData = await segmentResponse.json()
        telegramMembers = segmentData.statistics?.telegram_members || 0
      }

      setStats({
        total_sent: totalSent,
        active_campaigns: activeCampaigns,
        telegram_members: telegramMembers,
        recent_activity: campaignData.campaigns
          ?.filter((c: Campaign) => c.sent_at)
          ?.slice(0, 5)
          ?.map((c: Campaign) => ({
            campaign_name: c.name,
            action: `Sent to ${c.successful_sends} members`,
            timestamp: c.sent_at!
          })) || []
      })

    } catch (error) {
      console.error('Error fetching marketing data:', error)
      setError('Failed to load marketing data')
    } finally {
      setLoading(false)
    }
  }

  if (authLoading || loading) {
    return (
      <MainLayout>
        <div className="space-y-8">
          <PageHeader
            heading="Marketing"
            subheading="Send targeted messages to your loyalty program members"
            icon={<MessageSquare className="h-8 w-8" />}
          />

          {/* Stats Cards Skeleton */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-4" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-3 w-24" />
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Recent Campaigns Skeleton */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-48" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-8 w-20" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    )
  }

  if (error) {
    return (
      <MainLayout>
        <div className="space-y-8">
          <PageHeader
            heading="Marketing"
            subheading="Send targeted messages to your loyalty program members"
            icon={<MessageSquare className="h-8 w-8" />}
          />
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-red-600">{error}</p>
              <Button onClick={fetchData} className="mt-4">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-8">
        <PageHeader
          heading="Marketing"
          subheading="Send targeted messages to your loyalty program members"
          icon={<MessageSquare className="h-8 w-8" />}
        >
          <div className="flex items-center gap-2 mt-4">
            <Link href="/marketing/campaigns/create">
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Campaign
              </Button>
            </Link>
          </div>
        </PageHeader>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sent</CardTitle>
              <Send className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.total_sent?.toLocaleString() || 0}</div>
              <p className="text-xs text-muted-foreground">Messages delivered</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.active_campaigns || 0}</div>
              <p className="text-xs text-muted-foreground">Currently running</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Telegram Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.telegram_members || 0}</div>
              <p className="text-xs text-muted-foreground">Can receive messages</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Delivery Rate</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {campaigns.length > 0
                  ? Math.round(campaigns.reduce((sum, c) => sum + c.delivery_rate, 0) / campaigns.length)
                  : 0
                }%
              </div>
              <p className="text-xs text-muted-foreground">Average success rate</p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Campaigns */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Recent Campaigns</CardTitle>
              <CardDescription>Your latest marketing campaigns</CardDescription>
            </div>
            <Link href="/marketing/campaigns">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            {campaigns.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No campaigns yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first campaign to start sending targeted messages to your members.
                </p>
                <Link href="/marketing/campaigns/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Campaign
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {campaigns.slice(0, 5).map((campaign) => (
                  <div
                    key={campaign.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                  >
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{campaign.name}</h4>
                        <StatusBadge status={campaign.status} />
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="capitalize">{campaign.target_type} targeting</span>
                        <span>•</span>
                        <span>{campaign.total_recipients} recipients</span>
                        {campaign.sent_at && (
                          <>
                            <span>•</span>
                            <span>{formatDistanceToNow(new Date(campaign.sent_at))} ago</span>
                          </>
                        )}
                      </div>
                      {campaign.delivery_rate > 0 && (
                        <div className="text-sm text-green-600">
                          {campaign.delivery_rate}% delivery rate
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Link href={`/marketing/campaigns/${campaign.id}`}>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </Link>
                      {campaign.status === 'draft' && (
                        <Link href={`/marketing/campaigns/${campaign.id}/edit`}>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                      )}
                      <Button variant="ghost" size="sm">
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Activity */}
        {stats?.recent_activity && stats.recent_activity.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest campaign actions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats.recent_activity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="h-2 w-2 bg-green-500 rounded-full" />
                    <div className="flex-1">
                      <span className="font-medium">{activity.campaign_name}</span>
                      <span className="text-muted-foreground ml-2">{activity.action}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(activity.timestamp))} ago
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  )
}
