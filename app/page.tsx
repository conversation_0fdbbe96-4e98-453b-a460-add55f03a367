'use client'

import Link from "next/link"
import { motion } from "framer-motion"
import {
  <PERSON>geCheck,
  BarChart4,
  Gift,
  Layers,
  Sparkles,
  Users,
  ArrowRight,
  Star,
  CheckCircle2,
  Zap
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import DashboardPreview from "@/components/demo/DashboardPreview"

const features = [
  {
    icon: <Users className="h-6 w-6 text-purple-600 dark:text-purple-400" />,
    title: "Member Management",
    description: "Easily manage your loyalty program members, track their engagement, and personalize their experience."
  },
  {
    icon: <Gift className="h-6 w-6 text-amber-600 dark:text-amber-400" />,
    title: "Rewards System",
    description: "Create compelling rewards that drive customer engagement and build lasting relationships."
  },
  {
    icon: <Layers className="h-6 w-6 text-purple-600 dark:text-purple-400" />,
    title: "Tiered Programs",
    description: "Design multi-level loyalty tiers to encourage customer progression and increased spending."
  },
  {
    icon: <BarChart4 className="h-6 w-6 text-amber-600 dark:text-amber-400" />,
    title: "Analytics Dashboard",
    description: "Gain insights into customer behavior with comprehensive analytics and reporting tools."
  },
  {
    icon: <BadgeCheck className="h-6 w-6 text-purple-600 dark:text-purple-400" />,
    title: "Points Management",
    description: "Flexible points systems with customizable earning and redemption rules to fit your business."
  },
  {
    icon: <Sparkles className="h-6 w-6 text-amber-600 dark:text-amber-400" />,
    title: "Customer Engagement",
    description: "Drive repeat business with automated communications and personalized incentives."
  }
];

const testimonials = [
  {
    quote: "Loyal has transformed how we connect with our customers. Our repeat business is up 38% since implementation.",
    author: "Sarah Johnson",
    company: "Café Delight",
    avatar: "/avatars/avatar-1.png"
  },
  {
    quote: "The analytics dashboard gives us insights we never had before. We've been able to optimize our rewards structure based on real data.",
    author: "Michael Chen",
    company: "Urban Boutique",
    avatar: "/avatars/avatar-2.png"
  },
  {
    quote: "Setting up our tiered loyalty program was surprisingly easy. Our VIP customers love the exclusive rewards.",
    author: "Aisha Patel",
    company: "Wellness Studio",
    avatar: "/avatars/avatar-3.png"
  }
];

const benefits = [
  "Increase customer retention by up to 30%",
  "Boost average order value by 25%",
  "Gather valuable customer data and insights",
  "Create personalized marketing campaigns",
  "Reduce customer acquisition costs",
  "Build a community around your brand"
];

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-16 md:py-24 overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated gradient orbs */}
          <motion.div
            initial={{ opacity: 0.5 }}
            animate={{
              opacity: [0.5, 0.7, 0.5],
              scale: [1, 1.05, 1]
            }}
            transition={{
              repeat: Infinity,
              duration: 8,
              ease: "easeInOut"
            }}
            className="absolute top-0 right-0 w-[300px] md:w-[500px] h-[300px] md:h-[500px] bg-purple-200/30 dark:bg-purple-900/20 rounded-full filter blur-3xl -translate-y-1/2 translate-x-1/3"
          ></motion.div>

          <motion.div
            initial={{ opacity: 0.5 }}
            animate={{
              opacity: [0.5, 0.8, 0.5],
              scale: [1, 1.03, 1]
            }}
            transition={{
              repeat: Infinity,
              duration: 7,
              delay: 2,
              ease: "easeInOut"
            }}
            className="absolute bottom-0 left-0 w-[300px] md:w-[500px] h-[300px] md:h-[500px] bg-amber-200/30 dark:bg-amber-900/20 rounded-full filter blur-3xl translate-y-1/2 -translate-x-1/3"
          ></motion.div>

          {/* Grid pattern */}
          <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.02]"
            style={{
              backgroundImage: 'linear-gradient(to right, currentColor 1px, transparent 1px), linear-gradient(to bottom, currentColor 1px, transparent 1px)',
              backgroundSize: '60px 60px'
            }}>
          </div>

          {/* Abstract SVG shapes */}
          <svg className="absolute top-20 left-10 w-24 h-24 text-purple-200 dark:text-purple-900/30 opacity-40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 20 L80 20 L80 80 L20 80 Z" stroke="currentColor" strokeWidth="2" />
            <path d="M35 35 L65 65" stroke="currentColor" strokeWidth="2" />
            <path d="M35 65 L65 35" stroke="currentColor" strokeWidth="2" />
          </svg>

          <svg className="absolute bottom-20 right-10 w-32 h-32 text-amber-200 dark:text-amber-900/30 opacity-40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="20" y="20" width="60" height="60" rx="10" stroke="currentColor" strokeWidth="2" />
            <circle cx="50" cy="50" r="20" stroke="currentColor" strokeWidth="2" />
          </svg>
        </div>

        <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col min-[1400px]:flex-row items-center gap-8 md:gap-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center min-[1400px]:w-1/2 min-[1400px]:text-left"
            >
              <div className="inline-flex items-center px-3 py-1 mb-4 md:mb-6 text-xs font-medium rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
                <Zap className="w-3.5 h-3.5 mr-1.5" />
                <span className="hidden sm:inline">The premium loyalty platform for modern businesses</span>
                <span className="sm:hidden">Premium loyalty platform</span>
              </div>

              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-4 md:mb-6">
                Elevate Your
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-amber-500 dark:from-purple-400 dark:to-amber-400"> Customer Loyalty</span>
              </h1>

              <p className="text-base sm:text-lg md:text-xl text-gray-700 dark:text-gray-300 mb-6 md:mb-8 max-w-2xl mx-auto lg:mx-0">
                Build lasting relationships with your customers through our premium loyalty program management solution.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center min-[1400px]:justify-start">
                <Button asChild size="lg" className="bg-gradient-to-r from-purple-600 to-amber-500 hover:from-purple-700 hover:to-amber-600 text-white font-medium rounded-lg transition-all duration-200 h-12 px-6 shadow-md hover:shadow-lg">
                  <Link href="/signup" className="inline-flex items-center">
                    Get Started Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-gray-300 dark:border-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 font-medium rounded-lg transition-all duration-200 h-12 px-6">
                  <Link href="#features">
                    Learn More
                  </Link>
                </Button>
              </div>

              <div className="mt-8 md:mt-10 flex items-center justify-center min-[1400px]:justify-start">
                <div className="flex -space-x-3">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="w-8 sm:w-10 h-8 sm:h-10 rounded-full bg-gradient-to-br from-purple-200 to-amber-200 dark:from-purple-800 dark:to-amber-800 flex items-center justify-center text-xs font-medium text-gray-800 dark:text-gray-200 border-2 border-white dark:border-gray-900 shadow-sm">
                      {i}
                    </div>
                  ))}
                </div>
                <p className="ml-3 sm:ml-4 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-semibold">1,000+</span> businesses trust Loyal
                </p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="w-full relative mt-8 min-[1400px]:w-1/2 min-[1400px]:mt-0"
            >
              <div className="relative rounded-xl overflow-hidden shadow-2xl border border-gray-200 dark:border-gray-800">
                <DashboardPreview showCompact={true} />
              </div>
              <div className="absolute -bottom-6 -right-6 bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 shadow-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-1 sm:space-x-2 text-amber-500">
                  <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-current" />
                  <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-current" />
                  <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-current" />
                  <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-current" />
                  <Star className="h-4 w-4 sm:h-5 sm:w-5 fill-current" />
                </div>
                <p className="text-xs sm:text-sm font-medium text-gray-900 dark:text-white mt-1">
                  &quot;Game-changing loyalty platform!&quot;
                </p>
              </div>

              {/* Floating badges */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.8 }}
                className="absolute -left-4 md:-left-10 top-1/4 bg-white dark:bg-gray-800 rounded-lg py-2 px-3 shadow-lg border border-gray-200 dark:border-gray-700 hidden md:flex items-center"
              >
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mr-2">
                  <CheckCircle2 className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-xs font-medium">Points System</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Ready to use</p>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-12 md:py-16 bg-white dark:bg-gray-950 border-y border-gray-100 dark:border-gray-800">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-8 md:gap-10 items-center">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold mb-4">Boost Your Business with Customer Loyalty</h2>
                <p className="text-gray-700 dark:text-gray-300 mb-6">Our loyalty program management solution helps you build stronger customer relationships and increase revenue.</p>

                <ul className="space-y-3">
                  {benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <div className="flex-shrink-0 w-5 h-5 bg-gradient-to-r from-purple-600 to-amber-500 rounded-full flex items-center justify-center mt-1">
                        <CheckCircle2 className="h-3 w-3 text-white" />
                      </div>
                      <span className="ml-3 text-gray-700 dark:text-gray-300">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="relative">
                {/* Abstract SVG art background */}
                <svg className="absolute -top-10 -left-10 w-40 h-40 text-purple-100 dark:text-purple-900/20 opacity-70" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="50" cy="50" r="45" stroke="currentColor" strokeWidth="2" strokeDasharray="10 5" />
                  <circle cx="50" cy="50" r="30" stroke="currentColor" strokeWidth="2" strokeDasharray="5 3" />
                  <circle cx="50" cy="50" r="15" stroke="currentColor" strokeWidth="2" />
                </svg>

                <div className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-lg relative z-10">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">Customizable Rewards</h3>
                    <div className="w-10 h-10 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                      <Gift className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                    </div>
                  </div>

                  <div className="space-y-4 mb-6">
                    <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-md bg-gradient-to-br from-amber-100 to-purple-100 dark:from-amber-900/50 dark:to-purple-900/50 flex items-center justify-center text-amber-600 dark:text-amber-400 font-bold text-xs">
                          HA12
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium">10% Discount</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">500 points</p>
                        </div>
                      </div>
                      <div className="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full">
                        Active
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-md bg-gradient-to-br from-amber-100 to-purple-100 dark:from-amber-900/50 dark:to-purple-900/50 flex items-center justify-center text-amber-600 dark:text-amber-400 font-bold text-xs">
                          CF25
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium">Free Item</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">1000 points</p>
                        </div>
                      </div>
                      <div className="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full">
                        Active
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400">Unique codes for each reward</p>
                      <p className="text-sm font-medium">Fully customizable system</p>
                    </div>
                    <Button variant="outline" size="sm" className="text-xs">
                      View All
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Brands Section
      <section className="py-12 md:py-16 bg-gray-50 dark:bg-gray-900 border-b border-gray-100 dark:border-gray-800">
        <div className="container mx-auto px-4">
          <p className="text-center text-xs sm:text-sm text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium mb-6 md:mb-8">
            Trusted by innovative businesses worldwide
          </p>
          <div className="flex flex-wrap justify-center gap-x-8 sm:gap-x-16 gap-y-6 sm:gap-y-8">
            {['Brand 1', 'Brand 2', 'Brand 3', 'Brand 4', 'Brand 5'].map((brand, i) => (
              <div key={i} className="h-8 sm:h-10 flex items-center justify-center opacity-60 hover:opacity-100 transition-opacity">
                <div className="w-20 sm:w-28 h-8 sm:h-10 bg-gradient-to-r from-gray-300 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-md"></div>
              </div>
            ))}
          </div>
        </div>
      </section> */}
      {/* Features Section */}
      <section id="features" className="py-16 md:py-24 bg-white dark:bg-gray-950">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Everything You Need to Run a Successful Loyalty Program</h2>
            <p className="text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">Our comprehensive platform provides all the tools and features you need to create, manage, and grow your customer loyalty program.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-md hover:shadow-lg transition-shadow"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-purple-100 to-amber-100 dark:from-purple-900/30 dark:to-amber-900/30 rounded-lg flex items-center justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                <p className="text-gray-700 dark:text-gray-300">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Dashboard Demo Section (Full) */}
      <section className="py-16 md:py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-950 dark:to-gray-900 border-y border-gray-100 dark:border-gray-800 overflow-hidden">
        <div className="container mx-auto px-4 relative">
          {/* Abstract SVG art background */}
          <svg className="absolute top-0 right-0 w-64 h-64 text-purple-100 dark:text-purple-900/20 opacity-50" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M50 10 L90 50 L50 90 L10 50 Z" stroke="currentColor" strokeWidth="2" />
            <path d="M30 30 L70 30 L70 70 L30 70 Z" stroke="currentColor" strokeWidth="2" />
            <circle cx="50" cy="50" r="20" stroke="currentColor" strokeWidth="2" />
          </svg>

          <div className="text-center mb-12 md:mb-16 relative z-10">
            <h2 className="text-3xl md:text-4xl font-bold">Powerful Analytics Dashboard</h2>
            <p className="text-gray-700 dark:text-gray-300 max-w-3xl mx-auto">
              Get real-time insights into your loyalty program performance with our comprehensive analytics dashboard.
            </p>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="max-w-6xl mx-auto relative z-10"
          >
            <div className="rounded-xl overflow-hidden shadow-2xl border border-gray-200 dark:border-gray-800">
              <DashboardPreview />
            </div>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 md:py-24 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-12 md:mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4">
                What Our Customers Say
              </h2>
              <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400">
                Businesses of all sizes have seen incredible results with Loyal.
              </p>
            </motion.div>
          </div>

          <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-800 p-6 md:p-8 rounded-xl shadow-lg relative"
              >
                <div className="absolute -top-4 sm:-top-5 -left-4 sm:-left-5 w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-purple-500 to-amber-500 rounded-full flex items-center justify-center text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 sm:h-5 sm:w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                  </svg>
                </div>
                <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-6 mt-2">&quot;{testimonial.quote}&quot;</p>
                <div className="flex items-center">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-200 to-amber-200 dark:from-purple-800 dark:to-amber-800 rounded-full flex items-center justify-center text-gray-800 dark:text-gray-200 font-medium mr-3 sm:mr-4">
                    {testimonial.author.charAt(0)}
                  </div>
                  <div>
                    <h4 className="text-sm sm:text-base font-semibold">{testimonial.author}</h4>
                    <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">{testimonial.company}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 relative overflow-hidden">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-amber-500 dark:from-purple-900 dark:to-amber-700 opacity-90"></div>

        {/* Abstract SVG art background */}
        <svg className="absolute top-10 left-10 w-40 h-40 text-white opacity-10" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="50" cy="50" r="40" stroke="currentColor" strokeWidth="2" />
          <circle cx="50" cy="50" r="30" stroke="currentColor" strokeWidth="2" />
          <circle cx="50" cy="50" r="20" stroke="currentColor" strokeWidth="2" />
        </svg>

        <svg className="absolute bottom-10 right-10 w-40 h-40 text-white opacity-10" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="20" y="20" width="60" height="60" stroke="currentColor" strokeWidth="2" />
          <rect x="30" y="30" width="40" height="40" stroke="currentColor" strokeWidth="2" />
          <rect x="40" y="40" width="20" height="20" stroke="currentColor" strokeWidth="2" />
        </svg>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Ready to Transform Your Customer Loyalty?</h2>
            <p className="text-white/90 text-lg mb-8">
              Join thousands of businesses already growing their customer relationships with our premium loyalty platform.
            </p>

            <Button asChild size="lg" className="bg-white dark:bg-gray-800 hover:bg-gray-100 text-purple-600 font-medium rounded-lg transition-all duration-200 h-12 px-8 shadow-md hover:shadow-lg">
              <Link href="/signup" className="inline-flex items-center">
                Get Started Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
