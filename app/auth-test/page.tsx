'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

export default function AuthTestPage() {
  const [authState, setAuthState] = useState<Record<string, unknown> | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const testAuth = async () => {
      try {
        console.log('🧪 Testing auth directly...')
        const supabase = createClient()

        // Test session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()
        console.log('Session result:', { session: !!session, error: sessionError })

        // Test user
        const { data: { user }, error: userError } = await supabase.auth.getUser()
        console.log('User result:', { user: !!user, error: userError })

        setAuthState({
          session: session ? { id: session.user.id, email: session.user.email } : null,
          user: user ? { id: user.id, email: user.email } : null,
          sessionError,
          userError
        })
      } catch (err) {
        console.error('Auth test error:', err)
        setError(err instanceof Error ? err.message : 'Unknown error')
      } finally {
        setLoading(false)
      }
    }

    testAuth()
  }, [])

  if (loading) {
    return <div className="p-8">Testing auth system...</div>
  }

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Auth System Test</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          Error: {error}
        </div>
      )}

      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-semibold mb-2">Auth State:</h2>
        <pre className="text-sm overflow-auto">
          {JSON.stringify(authState, null, 2)}
        </pre>
      </div>

      <div className="mt-4">
        <a href="/login" className="text-blue-600 hover:underline">
          → Go to Login Page
        </a>
      </div>
    </div>
  )
}
