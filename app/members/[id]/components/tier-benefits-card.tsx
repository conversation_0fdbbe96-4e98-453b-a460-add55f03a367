import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useTiers } from "@/app/tiers/hooks/use-tiers";

interface TierBenefitsCardProps {
  memberTier: string;
  lifetimePoints: number;
}

export function TierBenefitsCard({
  memberTier,
  lifetimePoints,
}: TierBenefitsCardProps) {
  const { data: tiers, isLoading } = useTiers();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tier Benefits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <p className="text-muted-foreground">Loading tier information...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!tiers || tiers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tier Benefits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <p className="text-muted-foreground">No tier information available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sort tiers by minimum points
  const sortedTiers = [...tiers].sort((a, b) => a.minimum_points - b.minimum_points);
  
  // Find current tier and next tier
  const currentTierIndex = sortedTiers.findIndex(
    (tier) => tier.tier_name.toLowerCase() === memberTier.toLowerCase()
  );
  
  const currentTier = currentTierIndex >= 0 
    ? sortedTiers[currentTierIndex] 
    : sortedTiers[0];
  
  const nextTier = currentTierIndex < sortedTiers.length - 1 
    ? sortedTiers[currentTierIndex + 1] 
    : null;

  // Calculate progress to next tier
  let progressPercentage = 100;
  let pointsToNextTier = 0;
  
  if (nextTier) {
    const pointsRange = nextTier.minimum_points - currentTier.minimum_points;
    const pointsEarned = lifetimePoints - currentTier.minimum_points;
    pointsToNextTier = nextTier.minimum_points - lifetimePoints;
    progressPercentage = Math.min(
      Math.round((pointsEarned / pointsRange) * 100),
      100
    );
  }

  // Helper function to determine badge variant based on tier name
  const getTierVariant = (tierName: string): "default" | "secondary" | "outline" => {
    const name = tierName.toLowerCase();
    if (name.includes("silver")) return "default";
    if (name.includes("gold")) return "secondary";
    if (name.includes("platinum")) return "outline";
    return "default";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Tier Benefits</span>
          <Badge variant={getTierVariant(currentTier.tier_name)}>
            {currentTier.tier_name}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Current Benefits</h4>
          <p className="text-sm text-muted-foreground whitespace-pre-line">
            {currentTier.benefits_description}
          </p>
        </div>

        {nextTier && (
          <>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">
                  Progress to {nextTier.tier_name}
                </h4>
                <span className="text-xs text-muted-foreground">
                  {pointsToNextTier.toLocaleString()} points to go
                </span>
              </div>
              <Progress value={progressPercentage} className="h-2" />
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">Next Tier Benefits</h4>
              <p className="text-sm text-muted-foreground whitespace-pre-line">
                {nextTier.benefits_description}
              </p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
