'use client'

import { useState, useEffect, use } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useMember, useUpdateMember } from '@/hooks/use-members'
import { useTiers } from '@/app/tiers/hooks/use-tiers'
import { useUploadProfileImage } from '@/hooks/use-receipts'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'

// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Controller } from 'react-hook-form'
import Link from 'next/link'
import { ArrowLef<PERSON>, User, X } from 'lucide-react'

// Define validation schema for member form
const memberSchema = z.object({
  name: z.string().min(3, { message: 'Name must be at least 3 characters' }),
  email: z.string().email({ message: 'Please enter a valid email' }).optional().or(z.literal('')),
  phone_number: z.string().min(10, { message: 'Phone number must be at least 10 digits' }),
  birthday: z.string().optional().or(z.literal('')),
  current_tier: z.string().optional(),
  notes: z.string().optional().or(z.literal(''))
})

type MemberFormValues = z.infer<typeof memberSchema>

export default function EditMemberPage({ params }: { params: Promise<{ id: string }> }) {
  const unwrappedParams = use(params);
  const memberId = unwrappedParams.id;

  const { user, isLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [profileImage, setProfileImage] = useState<File | null>(null)
  const router = useRouter()

  // Use hooks for data fetching and mutations
  const { data: member, isLoading: memberLoading } = useMember(memberId)
  const { data: tiersData, isLoading: tiersLoading } = useTiers()
  const updateMemberMutation = useUpdateMember()
  const uploadImageMutation = useUploadProfileImage()

  // Form setup
  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<MemberFormValues>({
    resolver: zodResolver(memberSchema),
    defaultValues: {
      name: '',
      email: '',
      phone_number: '',
      birthday: '',
      current_tier: '',
      notes: ''
    }
  })

  // Fix hydration issues by ensuring client-side only rendering
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !isLoading && !user) {
      router.push('/login')
    }
  }, [mounted, isLoading, user, router])

  // Load member data into form when it becomes available
  useEffect(() => {
    if (member) {
      // Format birthday for the form if it exists
      const formattedBirthday = member.birthday
        ? new Date(member.birthday).toISOString().split('T')[0]
        : ''

      // Reset form with member data
      reset({
        name: member.name,
        email: member.email || '',
        phone_number: member.phone_number || '',
        birthday: formattedBirthday,
        current_tier: member.loyalty_tier || '',
        notes: member.notes || ''
      })
    }
  }, [member, reset])

  const onSubmit = async (data: MemberFormValues) => {
    if (!company) {
      toast.error('No company selected')
      return
    }

    try {
      setSubmitting(true)

      // Upload profile image if provided
      let profileImageUrl: string | undefined
      if (profileImage) {
        try {
          const { url } = await uploadImageMutation.mutateAsync(profileImage)
          profileImageUrl = url
        } catch (error) {
          console.error('Profile image upload error:', error)
          toast.error('Failed to upload profile image')
          return
        }
      }

      // Format birthday if provided
      const formattedBirthday = data.birthday
        ? new Date(data.birthday).toISOString().split('T')[0]
        : null

      // Update member using the mutation hook
      await updateMemberMutation.mutateAsync({
        memberId,
        memberData: {
          name: data.name,
          email: data.email || null,
          phone_number: data.phone_number,
          birthday: formattedBirthday,
          loyalty_tier: data.current_tier || null,
          notes: data.notes || null,
          ...(profileImageUrl && { profile_image_url: profileImageUrl }),
        }
      })

      toast.success('Member updated successfully', {
        description: `${data.name}'s information has been updated.`
      })

      // Redirect to member detail page
      router.push(`/members/${memberId}`)
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error updating member:', error)
      toast.error('Error updating member', {
        description: errorMessage || 'An unexpected error occurred'
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (!mounted || isLoading || companyLoading || memberLoading || tiersLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-background">
        <div className="animate-shimmer w-48 h-12 rounded-lg"></div>
      </div>
    )
  }

  if (!company) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">No Company Account Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a company profile before editing members.</p>
        <Button onClick={() => router.push('/company/create')}>
          Create Company Profile
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="outline" asChild className="mb-4">
          <Link href={`/members/${memberId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Member
          </Link>
        </Button>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Edit Member</CardTitle>
            <CardDescription>Update member information</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  placeholder="Enter member's full name"
                  {...register('name')}
                />
                {errors.name && (
                  <p className="text-destructive text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              {/* Profile Image Upload Section */}
              <div className="space-y-4">
                <Label>Profile Photo (Optional)</Label>
                <div className="space-y-3">
                  <div className="relative">
                    <Input
                      id="profile_image"
                      type="file"
                      accept="image/*"
                      className="bg-card border-dashed border-2 text-center cursor-pointer file:hidden opacity-0 absolute inset-0 w-full h-full"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          // Validate file size (max 5MB)
                          if (file.size > 5 * 1024 * 1024) {
                            toast.error('File size must be less than 5MB');
                            e.target.value = '';
                            return;
                          }
                          // Validate file type
                          if (!file.type.startsWith('image/')) {
                            toast.error('Please select an image file');
                            e.target.value = '';
                            return;
                          }
                          setProfileImage(file);
                        } else {
                          setProfileImage(null);
                        }
                      }}
                    />
                    <div className="flex flex-col items-center justify-center h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors">
                      <User className="h-6 w-6 text-gray-400 mb-1" />
                      <span className="text-sm text-gray-500">Click to upload new photo</span>
                      <span className="text-xs text-gray-400">PNG, JPG up to 5MB</span>
                    </div>
                  </div>

                  {profileImage && (
                    <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <User className="h-5 w-5 text-blue-600" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900">{profileImage.name}</p>
                        <p className="text-xs text-blue-600">{(profileImage.size / 1024 / 1024).toFixed(1)} MB</p>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setProfileImage(null);
                          const fileInput = document.getElementById('profile_image') as HTMLInputElement;
                          if (fileInput) fileInput.value = '';
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  <p className="text-xs text-muted-foreground text-center">
                    📸 Upload a new photo to replace the current one (optional)
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...register('email')}
                  />
                  {errors.email && (
                    <p className="text-destructive text-sm mt-1">{errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone_number">Phone Number <span className="text-red-500">*</span></Label>
                  <Input
                    id="phone_number"
                    placeholder="+251911234567"
                    {...register('phone_number')}
                  />
                  {errors.phone_number && (
                    <p className="text-destructive text-sm mt-1">{errors.phone_number.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="birthday">Birthday</Label>
                  <Input
                    id="birthday"
                    type="date"
                    {...register('birthday')}
                  />
                  {errors.birthday && (
                    <p className="text-destructive text-sm mt-1">{errors.birthday.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="current_tier">Loyalty Tier</Label>
                  <Controller
                    name="current_tier"
                    control={control}
                    render={({ field }) => (
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select tier" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="basic">Basic (Default)</SelectItem>
                          {tiersData?.map((tier) => (
                            <SelectItem key={tier.id} value={tier.id}>
                              {tier.tier_name}
                            </SelectItem>
                          )) || []}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push(`/members/${memberId}`)}>
                Cancel
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}
