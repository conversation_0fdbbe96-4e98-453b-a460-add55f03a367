'use client'

import { useState, useEffect, use } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useMember } from '@/hooks/use-members'
import { useCreateTransaction } from '@/hooks/use-transactions'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'

// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import Link from 'next/link'
import { ArrowLeft, Plus } from 'lucide-react'

// Define validation schema for points form
const pointsSchema = z.object({
  points: z.coerce.number().positive({ message: 'Points must be a positive number' }),
  description: z.string().min(3, { message: 'Description must be at least 3 characters' })
})

type PointsFormValues = z.infer<typeof pointsSchema>

// Remove unused type since we're using the hook
// type Member = {
//   id: string
//   name: string
//   loyalty_id: string
//   lifetime_points: number
//   redeemed_points: number
//   expired_points: number
//   available_points?: number
// }

export default function AddPointsPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params with React.use()
  const unwrappedParams = use(params);
  const memberId = unwrappedParams.id;

  const { user, isLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const router = useRouter()

  // Use hooks for data fetching and mutations
  const { data: member, isLoading: memberLoading } = useMember(memberId)
  const createTransactionMutation = useCreateTransaction()

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<PointsFormValues>({
    resolver: zodResolver(pointsSchema),
    defaultValues: {
      points: 0,
      description: ''
    }
  })

  // Fix hydration issues by ensuring client-side only rendering
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !isLoading && !user) {
      router.push('/login')
    }
  }, [mounted, isLoading, user, router])

  // Fetch data when component mounts - removed, using hook instead

  const onSubmit = async (data: PointsFormValues) => {
    if (!company || !member) {
      toast.error('Missing required information')
      return
    }

    try {
      setSubmitting(true)

      // Create a points transaction using the mutation hook
      await createTransactionMutation.mutateAsync({
        member_id: memberId,
        points_change: data.points,
        transaction_type: 'EARN' as const,
        description: data.description,
        transaction_date: new Date().toISOString(),
      })

      toast.success('Points added successfully', {
        description: `${data.points} points added to ${member.name}'s account.`
      })

      // Redirect to member detail page
      router.push(`/members/${memberId}`)
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Error adding points:', error)
      toast.error('Error adding points', {
        description: errorMessage || 'An unexpected error occurred'
      })
    } finally {
      setSubmitting(false)
    }
  }

  if (!mounted || isLoading || companyLoading || memberLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-background">
        <div className="animate-shimmer w-48 h-12 rounded-lg"></div>
      </div>
    )
  }

  if (!company) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">No Company Account Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a company profile before adding points.</p>
        <Button onClick={() => router.push('/company/create')}>
          Create Company Profile
        </Button>
      </div>
    )
  }

  if (!member) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">Member Not Found</h2>
        <p className="text-muted-foreground mb-6">The member you&apos;re looking for doesn&apos;t exist or doesn&apos;t belong to your company.</p>
        <Button onClick={() => router.push('/members')}>
          Back to Members
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="outline" asChild className="mb-4">
          <Link href={`/members/${memberId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Member
          </Link>
        </Button>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Add Points</CardTitle>
            <CardDescription>Add loyalty points to {member.name}&apos;s account</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="member">Member</Label>
                  <span className="text-sm font-medium">{member.name} ({member.loyalty_id})</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="currentPoints">Current Points</Label>
                  <span className="text-sm font-medium">{member.available_points}</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="points">Points to Add <span className="text-red-500">*</span></Label>
                <div className="relative">
                  <Plus className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="points"
                    type="number"
                    className="pl-10"
                    placeholder="Enter points to add"
                    {...register('points')}
                  />
                </div>
                {errors.points && (
                  <p className="text-destructive text-sm mt-1">{errors.points.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description <span className="text-red-500">*</span></Label>
                <Textarea
                  id="description"
                  placeholder="e.g., Purchase at store, Loyalty bonus, etc."
                  {...register('description')}
                />
                {errors.description && (
                  <p className="text-destructive text-sm mt-1">{errors.description.message}</p>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" onClick={() => router.push(`/members/${memberId}`)}>
                Cancel
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? 'Processing...' : 'Add Points'}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}
