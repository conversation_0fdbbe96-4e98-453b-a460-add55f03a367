'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useCreateMember } from '@/hooks/use-members'
import { useUploadProfileImage } from '@/hooks/use-receipts'
import { z } from 'zod'
import { useForm, Resolver } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import Link from 'next/link'
import { ArrowLeft, X, User } from 'lucide-react'

// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>verlay } from '@/components/ui/loading-spinner'

// Define validation schema for member form
const memberSchema = z.object({
  name: z.string().min(3, { message: 'Name must be at least 3 characters' }),
  email: z.string().email({ message: 'Please enter a valid email' }).optional().or(z.literal('')),
  phone_number: z.string().min(10, { message: 'Phone number is required and must be at least 10 digits' }),
  birthday: z.string().min(1, { message: 'Date of birth is required' }),
  initial_points: z.coerce.number().min(0, { message: 'Initial points must be at least 0' }).default(0),
  notes: z.string().optional().or(z.literal('')),
  profile_image: z.instanceof(File).optional()
})

type MemberFormValues = z.infer<typeof memberSchema>

export default function AddMemberPage() {
  const { user, isLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [profileImage, setProfileImage] = useState<File | null>(null)
  const router = useRouter()

  // Use hooks for data fetching and mutations
  const createMemberMutation = useCreateMember()
  const uploadImageMutation = useUploadProfileImage()

  // Fix hydration issues by ensuring client-side only rendering
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !isLoading && !user) {
      router.push('/login')
    }
  }, [mounted, isLoading, user, router])

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<MemberFormValues>({
    resolver: zodResolver(memberSchema) as Resolver<MemberFormValues>,
    defaultValues: {
      name: '',
      email: '',
      phone_number: '',
      birthday: '',
      initial_points: 0,
      notes: '',
      profile_image: undefined
    }
  })

  // Generate a unique loyalty ID (e.g., "********")
  const generateLoyaltyId = () => {
    // Generate a 7-digit number with leading zeros if needed
    const randomPart = Math.floor(Math.random() * 10000000).toString().padStart(7, '0')
    return `F${randomPart}` // Must start with 'F' followed by 7 digits
  }

  const onSubmit = async (data: MemberFormValues) => {
    if (!company) {
      toast.error('No company selected')
      return
    }

    try {
      setSubmitting(true)

      // Upload profile image if provided
      let profileImageUrl: string | undefined
      if (profileImage) {
        try {
          const { url } = await uploadImageMutation.mutateAsync(profileImage)
          profileImageUrl = url
        } catch (error) {
          console.error('Profile image upload error:', error)
          toast.error('Failed to upload profile image')
          return
        }
      }

      // Format birthday (now required field)
      const formattedBirthday = new Date(data.birthday).toISOString().split('T')[0]

      // Generate a unique loyalty ID
      const loyaltyId = generateLoyaltyId()

      // Create member using the mutation hook
      await createMemberMutation.mutateAsync({
        name: data.name,
        email: data.email || null, // Convert empty string or undefined to null
        phone_number: data.phone_number,
        birthday: formattedBirthday,
        loyalty_id: loyaltyId,
        loyalty_tier: null, // Tier will be determined automatically based on points
        available_points: data.initial_points || 0,
        total_points: data.initial_points || 0,
        notes: data.notes || null,
        profile_image_url: profileImageUrl
      })

      toast.success('Member added successfully', {
        description: `${data.name} has been added to your loyalty program.`
      })

      // Redirect to members list
      router.push('/members')
    } catch (error) {
      console.error('Error adding member:', error)
      toast.error(`Error adding member: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setSubmitting(false)
    }
  }

  if (!mounted || isLoading || companyLoading) {
    return (
      <LoadingOverlay />
    )
  }

  if (!company) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">No Company Account Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a company profile before adding members to your loyalty program.</p>
        <Button onClick={() => router.push('/company/create')}>
          Create Company Profile
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4 gap-2 text-muted-foreground hover:text-foreground"
          asChild
        >
          <Link href="/members">
            <ArrowLeft className="h-4 w-4" />
            Back to Members
          </Link>
        </Button>
      </div>
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Add New Member</CardTitle>
            <CardDescription>Add a new member to your loyalty program</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit(onSubmit)}>
            <CardContent className="space-y-8">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  placeholder="Enter member's full name"
                  {...register('name')}
                />
                {errors.name && (
                  <p className="text-destructive text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              {/* Profile Image Upload */}
              <div className="space-y-2">
                <Label htmlFor="profile_image" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  📸 Profile Photo (Optional)
                </Label>
                <div className="space-y-4">
                  <div className="relative">
                    <Input
                      id="profile_image"
                      type="file"
                      accept="image/*"
                      className="bg-card border-dashed border-2 text-center cursor-pointer file:hidden opacity-0 absolute inset-0 w-full h-full"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          // Validate file size (max 5MB)
                          if (file.size > 5 * 1024 * 1024) {
                            toast.error('File size must be less than 5MB');
                            e.target.value = '';
                            return;
                          }
                          // Validate file type
                          if (!file.type.startsWith('image/')) {
                            toast.error('Please select an image file');
                            e.target.value = '';
                            return;
                          }
                          setProfileImage(file);
                        } else {
                          setProfileImage(null);
                        }
                      }}
                    />
                    <div className="flex flex-col items-center justify-center h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors">
                      <User className="h-6 w-6 text-gray-400 mb-1" />
                      <span className="text-sm text-gray-500">Click to upload photo</span>
                      <span className="text-xs text-gray-400">PNG, JPG up to 5MB</span>
                    </div>
                  </div>

                  {profileImage && (
                    <div className="flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <User className="h-5 w-5 text-blue-600" />
                      <div className="flex-1">
                        <p className="text-sm font-medium text-blue-900">{profileImage.name}</p>
                        <p className="text-xs text-blue-600">{(profileImage.size / 1024 / 1024).toFixed(1)} MB</p>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setProfileImage(null);
                          const fileInput = document.getElementById('profile_image') as HTMLInputElement;
                          if (fileInput) fileInput.value = '';
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  <p className="text-xs text-muted-foreground text-center">
                    📸 Add a profile photo to help identify this member (optional)
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="email">Email <span className="text-muted-foreground">(optional)</span></Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    {...register('email')}
                  />
                  {errors.email && (
                    <p className="text-destructive text-sm mt-1">{errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone_number">Phone Number <span className="text-red-500">*</span></Label>
                  <Input
                    id="phone_number"
                    placeholder="+1234567890"
                    required
                    {...register('phone_number')}
                  />
                  {errors.phone_number && (
                    <p className="text-destructive text-sm mt-1">{errors.phone_number.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="birthday">Birthday <span className="text-red-500">*</span></Label>
                  <Input
                    id="birthday"
                    type="date"
                    required
                    {...register('birthday')}
                  />
                  {errors.birthday && (
                    <p className="text-destructive text-sm mt-1">{errors.birthday.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="initial_points">Initial Points</Label>
                  <Input
                    id="initial_points"
                    type="number"
                    min="0"
                    {...register('initial_points', { valueAsNumber: true })}
                  />
                  {errors.initial_points && (
                    <p className="text-destructive text-sm mt-1">{errors.initial_points.message}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    💡 The member&apos;s tier will be automatically determined based on their points
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional information about this member"
                  {...register('notes')}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" type="button" asChild>
                <Link href="/members">
                  Cancel
                </Link>
              </Button>
              <Button type="submit" disabled={submitting} className="min-w-[120px]">
                {submitting ? <><LoadingSpinner size="sm" className="mr-2" /> Adding...</> : 'Add Member'}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}
