'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useMembers } from '@/hooks/use-members'
import { useTiers } from '@/hooks/use-tiers'
import { useMembersWithTelegramStatus } from '@/hooks/use-telegram-status'
import { ClientOnly } from '@/lib/hydration-safe'
import { format } from 'date-fns'
import { Search, Plus, ArrowUpRight, ArrowUpDown, ArrowUp, ArrowDown, MessageCircle, CheckCircle } from 'lucide-react'
import Image from 'next/image'
import { calculateMemberTier, getTierColorClass } from '@/lib/tier-utils'
import { Badge } from '@/components/ui/badge'

type Member = {
  id: string
  name: string
  phone_number: string | null
  email: string | null
  lifetime_points: number
  redeemed_points: number
  available_points?: number
  loyalty_id: string
  current_tier: string | null
  registration_date: string
  expired_points?: number
  profile_image_url?: string | null
  // Telegram fields
  telegram_chat_id?: string | null
  telegram_username?: string | null
  linked_at?: string | null
  telegramStatus?: {
    isLinked: boolean
    telegramChatId?: string | null
    telegramUsername?: string | null
    linkedAt?: string | null
  }
}

export default function MembersPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { isLoading: companyLoading } = useCompany()
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState<string>('registration_date')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const router = useRouter()

  // Use React Query hooks for data fetching
  const { data: membersData, isLoading: membersLoading } = useMembers()
  const { data: membersWithTelegram, isLoading: telegramLoading } = useMembersWithTelegramStatus()
  const { data: tiersData } = useTiers()

  // Wait for auth to load before making decisions
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse text-gray-500">Loading...</div>
      </div>
    )
  }

  // Only redirect if we're sure auth is loaded and user is null
  if (!authLoading && !user) {
    router.push('/login')
    return null
  }

  // Process members data to calculate available points and correct tiers, and merge Telegram data
  const members = membersData?.data?.map((member: Member) => {
    // Check if the API already provided available_points
    let processedMember = member;
    if (member.available_points === undefined) {
      // Otherwise calculate from lifetime, redeemed, and expired points
      processedMember = {
        ...member,
        available_points: (member.lifetime_points ?? 0) - (member.redeemed_points ?? 0) - (member.expired_points ?? 0)
      };
    }

    // Calculate correct tier based on lifetime points
    if (tiersData && tiersData.length > 0) {
      const correctTier = calculateMemberTier(member.lifetime_points || 0, tiersData);
      processedMember = {
        ...processedMember,
        current_tier: correctTier
      };
    }

    // Merge Telegram status data
    const telegramMember = membersWithTelegram?.find((tm: Member) => tm.id === member.id);
    if (telegramMember?.telegramStatus) {
      processedMember = {
        ...processedMember,
        telegramStatus: telegramMember.telegramStatus,
        telegram_chat_id: telegramMember.telegram_chat_id,
        telegram_username: telegramMember.telegram_username,
        linked_at: telegramMember.linked_at
      };
    }

    return processedMember;
  }) || []

  // Filter members based on search term
  const filteredMembers: Member[] = members.filter((member: Member) =>
    member.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (member.phone_number && member.phone_number.includes(searchTerm)) ||
    (member.email && member.email?.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (member.loyalty_id && member.loyalty_id.includes(searchTerm))
  )

  // Sort members
  const sortedMembers = [...filteredMembers].sort((a, b) => {
    if (sortField === 'available_points' || sortField === 'lifetime_points') {
      const aValue = a[sortField as keyof Member] as number || 0
      const bValue = b[sortField as keyof Member] as number || 0
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    }

    if (sortField === 'registration_date') {
      return sortDirection === 'asc'
        ? new Date(a.registration_date).getTime() - new Date(b.registration_date).getTime()
        : new Date(b.registration_date).getTime() - new Date(a.registration_date).getTime()
    }

    // Default string comparison for other fields
    const aValue = a[sortField as keyof Member] || ''
    const bValue = b[sortField as keyof Member] || ''

    return sortDirection === 'asc'
      ? String(aValue).localeCompare(String(bValue))
      : String(bValue).localeCompare(String(aValue))
  })

  // Handle sort click
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Loading state
  const isLoading = authLoading || companyLoading || membersLoading || telegramLoading

  // Render loading skeleton during server-side rendering and initial client load
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <div className="h-8 w-32 mb-2 bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
          <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
            <div className="h-10 w-[250px] bg-gray-200 dark:bg-gray-700 rounded" />
            <div className="h-10 w-32 bg-gray-200 dark:bg-gray-700 rounded" />
          </div>
        </div>
        <Card>
          <CardHeader>
            <div className="h-5 w-32 bg-gray-200 dark:bg-gray-700 rounded" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="h-4 w-48 bg-gray-200 dark:bg-gray-700 rounded" />
                    <div className="h-3 w-32 bg-gray-200 dark:bg-gray-700 rounded" />
                  </div>
                  <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Members</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Manage your loyalty program members
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
            <Input
              type="search"
              placeholder="Search members..."
              className="pl-9 w-full sm:w-[250px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <Button asChild variant="outline">
              <Link href="/members/telegram-links">
                <MessageCircle className="mr-2 h-4 w-4" />
                Telegram Links
              </Link>
            </Button>
            <Button asChild>
              <Link href="/members/add">
                <Plus className="mr-2 h-4 w-4" />
                Add Member
              </Link>
            </Button>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Members ({sortedMembers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {sortedMembers.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-muted-foreground">No members found</p>
              {searchTerm && (
                <Button
                  variant="link"
                  onClick={() => setSearchTerm('')}
                  className="mt-2"
                >
                  Clear search
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center">
                        Name
                        {sortField === 'name' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'name' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('lifetime_points')}
                    >
                      <div className="flex items-center">
                        Lifetime Points
                        {sortField === 'lifetime_points' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'lifetime_points' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('available_points')}
                    >
                      <div className="flex items-center">
                        Available Points
                        {sortField === 'available_points' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'available_points' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('current_tier')}
                    >
                      <div className="flex items-center">
                        Tier
                        {sortField === 'current_tier' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'current_tier' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead>
                      <div className="flex items-center">
                        <MessageCircle className="mr-1 h-4 w-4" />
                        Telegram
                      </div>
                    </TableHead>
                    <TableHead
                      className="cursor-pointer"
                      onClick={() => handleSort('registration_date')}
                    >
                      <div className="flex items-center">
                        Joined
                        {sortField === 'registration_date' && (
                          sortDirection === 'asc' ? <ArrowUp className="ml-1 h-4 w-4" /> : <ArrowDown className="ml-1 h-4 w-4" />
                        )}
                        {sortField !== 'registration_date' && <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />}
                      </div>
                    </TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedMembers.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-3">
                          {/* Custom Avatar with better image handling */}
                          <div className="relative w-8 h-8 rounded-full overflow-hidden border border-gray-200 bg-gray-100 flex-shrink-0">
                            {member.profile_image_url ? (
                              <Image
                                src={member.profile_image_url}
                                alt={`${member.name} profile`}
                                className="w-full h-full object-cover"
                                width={32}
                                height={32}
                                unoptimized={true}
                                priority={true}
                                onLoadingComplete={() => {
                                  console.log('✅ Profile image loaded for member list:', member.name);
                                }}
                                onError={(e) => {
                                  console.log('❌ Profile image failed to load for member list:', member.name, 'URL:', member.profile_image_url);

                                  // Try again with replaced URL path as a fallback
                                  const correctedUrl = member.profile_image_url?.replace('/receipts/', '/profile-images/');
                                  if (correctedUrl && correctedUrl !== member.profile_image_url) {
                                    console.log('Trying corrected URL:', correctedUrl);
                                    e.currentTarget.src = correctedUrl;
                                  } else {
                                    // Force the fallback to show initials
                                    e.currentTarget.style.display = 'none';
                                    // Activate the fallback
                                    const fallback = e.currentTarget.parentElement?.querySelector('[data-initials]');
                                    if (fallback) {
                                      fallback.classList.remove('hidden');
                                      fallback.classList.add('flex');
                                    }
                                  }
                                }}
                              />
                            ) : null}
                            {/* Fallback initials */}
                            <div
                              data-initials="true"
                              className={`absolute inset-0 flex items-center justify-center text-xs font-semibold text-gray-600 ${
                                member.profile_image_url ? 'hidden' : 'flex'
                              }`}
                            >
                              {member.name
                                .split(' ')
                                .map((n: string) => n[0])
                                .join('')}
                            </div>
                          </div>
                          <span>{member.name}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-xs">{member.loyalty_id}</TableCell>
                      <TableCell>
                        {member.email || member.phone_number || 'N/A'}
                      </TableCell>
                      <TableCell className="font-semibold text-blue-600">
                        {member.lifetime_points?.toLocaleString() || 0}
                      </TableCell>
                      <TableCell className="font-semibold text-green-600">
                        {member.available_points?.toLocaleString() || 0}
                      </TableCell>
                      <TableCell>
                        <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getTierColorClass(member.current_tier || 'Basic')}`}>
                          {member.current_tier || 'Basic'}
                        </span>
                      </TableCell>
                      <TableCell>
                        {member.telegramStatus?.isLinked || member.telegram_chat_id ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Connected
                          </Badge>
                        ) : (
                          <Badge variant="secondary">
                            Not Connected
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <ClientOnly fallback="Loading...">
                          {() => format(new Date(member.registration_date), 'MMM d, yyyy')}
                        </ClientOnly>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="icon"
                          asChild
                        >
                          <Link href={`/members/${member.id}`}>
                            <ArrowUpRight className="h-4 w-4" />
                            <span className="sr-only">View member</span>
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
