'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { useMembersWithTelegramStatus } from '@/hooks/use-telegram-status'
import { Search, MessageCircle, Copy, ExternalLink, CheckCircle, ArrowLeft, Unlink } from 'lucide-react'
import Link from 'next/link'
import { useToast } from '@/components/ui/use-toast'

type Member = {
  id: string
  name: string
  phone_number: string | null
  email: string | null
  telegramStatus?: {
    isLinked: boolean
    telegramChatId?: string | null
    telegramUsername?: string | null
    hasLinkingToken?: boolean
  }
}

export default function TelegramLinksPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [searchTerm, setSearchTerm] = useState('')
  const [generatedLinks, setGeneratedLinks] = useState<{ [memberId: string]: string }>({})
  const [generatingLinks, setGeneratingLinks] = useState<{ [memberId: string]: boolean }>({})
  const [unlinkingMembers, setUnlinkingMembers] = useState<{ [memberId: string]: boolean }>({})
  const [copiedLinks, setCopiedLinks] = useState<{ [memberId: string]: boolean }>({})
  const [memberStates, setMemberStates] = useState<{ [memberId: string]: { isLinked: boolean } }>({})
  const { toast } = useToast()

  // Use React Query hooks for data fetching with Telegram status
  const { data: membersData, isLoading: membersLoading } = useMembersWithTelegramStatus()

  // Wait for auth to load
  if (authLoading || companyLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect in useAuth hook
  }

  if (!company) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">No Company Account Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a company profile before managing Telegram links.</p>
        <Button onClick={() => window.location.href = '/company/create'}>
          Create Company Profile
        </Button>
      </div>
    )
  }

  const members = membersData || []

  // Filter members based on search term
  const filteredMembers = members.filter((member: Member) =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.phone_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const generateTelegramLink = async (memberId: string) => {
    setGeneratingLinks(prev => ({ ...prev, [memberId]: true }))
    try {
      const response = await fetch('/api/telegram/generate-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberId: memberId,
          chatId: 'pending'
        }),
      })

      const data = await response.json()

      if (response.ok && data.link) {
        setGeneratedLinks(prev => ({ ...prev, [memberId]: data.link }))
        toast.success("Telegram link generated successfully.")
      } else {
        throw new Error(data.error || 'Failed to generate link')
      }
    } catch (error) {
      console.error('Error generating Telegram link:', error)
      toast.error("Failed to generate Telegram link. Please try again.")
    } finally {
      setGeneratingLinks(prev => ({ ...prev, [memberId]: false }))
    }
  }

  const unlinkMemberTelegram = async (memberId: string, memberName: string) => {
    setUnlinkingMembers(prev => ({ ...prev, [memberId]: true }))
    try {
      const response = await fetch('/api/telegram/unlink', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberId: memberId
        }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // Update local state to reflect the unlink
        setMemberStates(prev => ({
          ...prev,
          [memberId]: { isLinked: false }
        }))
        setGeneratedLinks(prev => {
          const newLinks = { ...prev }
          delete newLinks[memberId]
          return newLinks
        })
        toast.success(`${memberName} has been unlinked from Telegram`)
      } else {
        throw new Error(data.error || 'Failed to unlink')
      }
    } catch (error) {
      console.error('Error unlinking member:', error)
      toast.error("Failed to unlink member from Telegram. Please try again.")
    } finally {
      setUnlinkingMembers(prev => ({ ...prev, [memberId]: false }))
    }
  }

  const copyToClipboard = async (memberId: string, link: string) => {
    try {
      await navigator.clipboard.writeText(link)
      setCopiedLinks(prev => ({ ...prev, [memberId]: true }))
      setTimeout(() => {
        setCopiedLinks(prev => ({ ...prev, [memberId]: false }))
      }, 2000)
      toast.success("Telegram link copied to clipboard!")
    } catch (error) {
      console.error('Failed to copy:', error)
      toast.error("Please copy the link manually.")
    }
  }

  const shareViaWhatsApp = (member: Member, link: string) => {
    if (!member.phone_number) return

    const message = `Hi ${member.name}! 👋\n\nConnect your Telegram account to our loyalty program for instant notifications and AI assistance:\n\n${link}\n\nJust click the link and follow the instructions. Thanks!`
    const whatsappUrl = `https://wa.me/${member.phone_number.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="outline" asChild className="mb-4">
          <Link href="/members">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Members
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Telegram Integration</h1>
        <p className="text-muted-foreground">Generate and manage Telegram account linking for your members</p>
      </div>

      {/* Real-time Summary Stats */}
      {membersData && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{membersData.length}</div>
              <p className="text-xs text-muted-foreground">Total Members</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">
                {membersData.filter((m: Member) => m.telegramStatus?.isLinked).length}
              </div>
              <p className="text-xs text-muted-foreground">Connected</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">
                {membersData.filter((m: Member) => m.telegramStatus?.hasLinkingToken && !m.telegramStatus?.isLinked).length}
              </div>
              <p className="text-xs text-muted-foreground">Pending</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">
                {membersData.length > 0 ? Math.round((membersData.filter((m: Member) => m.telegramStatus?.isLinked).length / membersData.length) * 100) : 0}%
              </div>
              <p className="text-xs text-muted-foreground">Connection Rate</p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Member Telegram Links
          </CardTitle>
          <CardDescription>
            Generate secure links for members to connect their Telegram accounts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search members by name, phone, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Members Table */}
          {membersLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="p-4 border rounded-md">
                  <div className="flex justify-between mb-2">
                    <div className="h-5 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredMembers.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-muted-foreground">No members found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Member</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Telegram Status</TableHead>
                    <TableHead>Generated Link</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.map((member: Member) => {
                    const memberState = memberStates[member.id]
                    const isLinked = memberState?.isLinked !== undefined ? memberState.isLinked : !!member.telegramStatus?.isLinked
                    const hasGeneratedLink = !!generatedLinks[member.id]
                    const isGenerating = !!generatingLinks[member.id]
                    const isUnlinking = !!unlinkingMembers[member.id]
                    const isCopied = !!copiedLinks[member.id]

                    return (
                      <TableRow key={member.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{member.name}</div>
                            <div className="text-sm text-muted-foreground">ID: {member.id.slice(0, 8)}...</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {member.phone_number && <div>📱 {member.phone_number}</div>}
                            {member.email && <div>📧 {member.email}</div>}
                          </div>
                        </TableCell>
                        <TableCell>
                          {isLinked ? (
                            <div>
                              <Badge variant="default" className="bg-green-100 text-green-800 mb-1">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Connected
                              </Badge>
                              {member.telegramStatus?.telegramUsername && (
                                <div className="text-xs text-muted-foreground">@{member.telegramStatus.telegramUsername}</div>
                              )}
                            </div>
                          ) : (
                            <Badge variant="secondary">Not Connected</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {hasGeneratedLink ? (
                            <div className="max-w-xs">
                              <Input
                                value={generatedLinks[member.id]}
                                readOnly
                                className="font-mono text-xs h-8"
                              />
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">No link generated</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            {isLinked ? (
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => unlinkMemberTelegram(member.id, member.name)}
                                disabled={isUnlinking}
                              >
                                {isUnlinking ? (
                                  'Unlinking...'
                                ) : (
                                  <>
                                    <Unlink className="h-4 w-4 mr-1" />
                                    Unlink
                                  </>
                                )}
                              </Button>
                            ) : !hasGeneratedLink ? (
                              <Button
                                size="sm"
                                onClick={() => generateTelegramLink(member.id)}
                                disabled={isGenerating}
                              >
                                {isGenerating ? 'Generating...' : 'Generate Link'}
                              </Button>
                            ) : (
                              <>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => copyToClipboard(member.id, generatedLinks[member.id])}
                                >
                                  {isCopied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                                </Button>
                                {member.phone_number && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => shareViaWhatsApp(member, generatedLinks[member.id])}
                                  >
                                    <ExternalLink className="h-4 w-4" />
                                  </Button>
                                )}
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Instructions Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>How to Use</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center font-semibold text-xs">1</div>
              <div>
                <strong>Generate Link:</strong> Click &quot;Generate Link&quot; for any member you want to connect to Telegram.
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center font-semibold text-xs">2</div>
              <div>
                <strong>Share with Member:</strong> Use the WhatsApp button to send the link directly, or copy the link to share via SMS/email.
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center font-semibold text-xs">3</div>
              <div>
                <strong>Member Connects:</strong> When members click the link, they&apos;ll be taken to Telegram to start the bot and connect their account.
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center font-semibold text-xs">4</div>
              <div>
                <strong>Automatic Connection:</strong> Once connected, members can receive notifications and chat with the AI assistant.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
