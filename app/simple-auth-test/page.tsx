'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

interface AuthTestDetails {
  duration: number
  hasSession: boolean
  error?: string
  userId?: string
  userEmail?: string
  timestamp: string
}

export default function SimpleAuthTestPage() {
  const [authState, setAuthState] = useState<string>('Loading...')
  const [details, setDetails] = useState<AuthTestDetails | null>(null)

  useEffect(() => {
    const testAuth = async () => {
      try {
        console.log('🧪 Simple auth test starting...')
        const supabase = createClient()

        // Test basic auth without React Query
        const start = Date.now()
        const { data: { session }, error } = await supabase.auth.getSession()
        const duration = Date.now() - start

        console.log(`Auth test completed in ${duration}ms:`, { session: !!session, error })

        setDetails({
          duration,
          hasSession: !!session,
          error: error?.message,
          userId: session?.user?.id,
          userEmail: session?.user?.email,
          timestamp: new Date().toISOString()
        })

        if (session) {
          setAuthState('Authenticated')
        } else if (error) {
          setAuthState(`Error: ${error.message}`)
        } else {
          setAuthState('Not authenticated')
        }
      } catch (err) {
        console.error('Simple auth test failed:', err)
        setAuthState(`Failed: ${err}`)
      }
    }

    testAuth()
  }, [])

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Simple Auth Test (No React Query)</h1>

      <div className="bg-gray-100 p-4 rounded">
        <h2 className="font-semibold mb-2">Auth State:</h2>
        <div className="text-lg">{authState}</div>
      </div>

      {details && (
        <div className="mt-4 bg-gray-50 p-4 rounded">
          <h2 className="font-semibold mb-2">Details:</h2>
          <pre className="text-sm">{JSON.stringify(details, null, 2)}</pre>
        </div>
      )}

      <div className="mt-6 flex space-x-4">
        <a href="/login" className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">Go to Login</a>
        <a href="/debug-auth" className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">Debug Auth Page</a>
      </div>
    </div>
  )
}
