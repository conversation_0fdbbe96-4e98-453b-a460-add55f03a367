/**
 * Calculate the expiry date for points based on when they were earned
 * @param earnedDate Date when points were earned
 * @param expirationDays Number of days until points expire
 * @returns Date when points will expire or null if inputs are invalid
 */
export function calculatePointsExpiry(
  earnedDate: Date | null, 
  expirationDays: number | null
): Date | null {
  if (!earnedDate || !expirationDays) return null
  
  const date = new Date(earnedDate)
  date.setDate(date.getDate() + expirationDays)
  return date
}

/**
 * Format points balance for display
 * @param available Available points balance
 * @param total Total lifetime points
 * @returns Formatted points object with available, total, and percentage
 */
export function formatPointsBalance(available: number, total: number) {
  return {
    available: available.toLocaleString(),
    total: total.toLocaleString(),
    percentage: total > 0 ? Math.floor((available / total) * 100) : 0
  }
}

/**
 * Calculate available points (lifetime - redeemed - expired)
 * @param lifetimePoints Total lifetime points earned
 * @param redeemedPoints Total points redeemed
 * @param expiredPoints Total points expired
 * @returns Available points balance
 */
export function calculateAvailablePoints(
  lifetimePoints: number,
  redeemedPoints: number,
  expiredPoints: number
): number {
  return Math.max(0, lifetimePoints - redeemedPoints - expiredPoints)
}
