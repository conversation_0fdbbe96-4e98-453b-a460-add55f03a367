import { Metada<PERSON> } from "next";
import { TiersTable } from "./components/tiers-table";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "@radix-ui/react-icons";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Tier Management | Loyal",
  description: "Manage loyalty program tiers and benefits",
};

export default function TiersPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <PageHeader
          heading="Tier Management"
          subheading="Define and manage loyalty program tiers and benefits"
        />
        <Link href="/tiers/new">
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            New Tier
          </Button>
        </Link>
      </div>
      <TiersTable />
    </div>
  );
}
