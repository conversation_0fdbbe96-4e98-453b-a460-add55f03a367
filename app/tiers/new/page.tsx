import { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { TierForm } from "../components/tier-form";

export const metadata: Metadata = {
  title: "Create New Tier | Loyal",
  description: "Create a new loyalty program tier",
};

export default function NewTierPage() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-2xl mx-auto mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mb-4 gap-2 text-muted-foreground hover:text-foreground"
          asChild
        >
          <Link href="/tiers">
            <ArrowLeft className="h-4 w-4" />
            Back to Tiers
          </Link>
        </Button>
      </div>
      <div className="max-w-2xl mx-auto">
        <TierForm />
      </div>
    </div>
  );
}
