import { TierDefinition } from './types'

/**
 * Get the tier that corresponds to a member's points
 * @param tiers Array of tier definitions
 * @param points Member's current points
 * @returns The tier object the member qualifies for
 */
export function getTierForPoints(tiers: TierDefinition[], points: number): TierDefinition {
  // Sort tiers by minimum_points in descending order
  const sortedTiers = [...tiers].sort((a, b) => b.minimum_points - a.minimum_points)
  
  // Find the highest tier the member qualifies for
  for (const tier of sortedTiers) {
    if (points >= tier.minimum_points) {
      return tier
    }
  }
  
  // Default to the lowest tier if no match (should never happen with proper tier setup)
  return tiers.find(t => t.minimum_points === 0) || tiers[0]
}

/**
 * Get the next tier in the hierarchy after the current tier
 * @param tiers Array of tier definitions
 * @param currentTier The member's current tier
 * @returns The next tier object or null if already at highest tier
 */
export function getNextTier(tiers: TierDefinition[], currentTier: TierDefinition): TierDefinition | null {
  // Sort tiers by minimum_points in ascending order
  const sortedTiers = [...tiers].sort((a, b) => a.minimum_points - b.minimum_points)
  
  // Find the index of the current tier
  const currentIndex = sortedTiers.findIndex(t => t.id === currentTier.id)
  
  // Return the next tier if it exists
  if (currentIndex < sortedTiers.length - 1) {
    return sortedTiers[currentIndex + 1]
  }
  
  // Return null if the member is already at the highest tier
  return null
}

/**
 * Calculate the progress percentage towards the next tier
 * @param currentPoints Member's current points
 * @param currentTierMinPoints Minimum points for current tier
 * @param nextTierMinPoints Minimum points for next tier
 * @returns Progress percentage (0-100)
 */
export function calculateTierProgress(
  currentPoints: number,
  currentTierMinPoints: number,
  nextTierMinPoints: number
): number {
  // Calculate points needed for next tier
  const pointsNeeded = nextTierMinPoints - currentTierMinPoints
  
  // Calculate points earned towards next tier
  const pointsEarned = currentPoints - currentTierMinPoints
  
  // Calculate progress percentage
  const progress = (pointsEarned / pointsNeeded) * 100
  
  // Ensure progress is between 0 and 100
  return Math.min(Math.max(progress, 0), 100)
}
