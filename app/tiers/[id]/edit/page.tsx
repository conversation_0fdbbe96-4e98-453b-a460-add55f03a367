import { Metadata } from "next";
import { notFound } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { PageHeader } from "@/components/page-header";
import { TierForm } from "../../components/tier-form";
import type { TierDefinition } from "../../types";

interface EditTierPageProps {
  params: Promise<{
    id: string;
  }>;
}

export async function generateMetadata({
  params,
}: EditTierPageProps): Promise<Metadata> {
  const supabase = await createClient();
  const resolvedParams = await params;
  const { data: tier } = await supabase
    .from("tier_definitions")
    .select("tier_name")
    .eq("id", resolvedParams.id)
    .single();

  return {
    title: tier ? `Edit ${tier.tier_name} Tier | Loyal` : "Edit Tier | Loyal",
    description: "Edit loyalty program tier settings",
  };
}

async function getTier(id: string): Promise<TierDefinition> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("tier_definitions")
    .select("*")
    .eq("id", id)
    .single();

  if (error || !data) {
    notFound();
  }

  return data as TierDefinition;
}

export default async function EditTierPage({ params }: EditTierPageProps) {
  const resolvedParams = await params;
  const tier = await getTier(resolvedParams.id);

  return (
    <div className="flex flex-col gap-8">
      <PageHeader
        heading={`Edit ${tier.tier_name} Tier`}
        subheading="Update tier settings and benefits"
      />
      <div className="mx-auto w-full max-w-2xl">
        <TierForm tier={tier} />
      </div>
    </div>
  );
}