export interface TierDefinition {
  id: string;
  tier_name: string;
  company_id: string;
  minimum_points: number;
  benefits_description?: string;
  created_at?: string;
  member_count?: number;
  loyalty_members?: Array<{ count: number }>;
  member_stats?: {
    active: number;
    new: number;
    inactive: number;
  };
}

export interface CreateTierInput {
  tier_name: string;
  minimum_points: number;
  benefits_description: string;
}

export interface UpdateTierInput extends CreateTierInput {
  id: string;
}
