"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardFooter, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { useCreateTier, useUpdateTier } from "../hooks/use-tiers";
import { TierDefinition } from "../types";

const tierFormSchema = z.object({
  tier_name: z.string().min(1, "Tier name is required"),
  minimum_points: z.coerce
    .number()
    .int("Must be a whole number")
    .min(0, "Minimum points cannot be negative"),
  benefits_description: z.string().min(1, "Benefits description is required"),
});

type TierFormValues = z.infer<typeof tierFormSchema>;

interface TierFormProps {
  tier?: TierDefinition;
}

export function TierForm({ tier }: TierFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createTier = useCreateTier();
  const updateTier = useUpdateTier();
  const isEditing = !!tier;

  const form = useForm<TierFormValues>({
    resolver: zodResolver(tierFormSchema),
    defaultValues: {
      tier_name: tier?.tier_name || "",
      minimum_points: tier?.minimum_points || 0,
      benefits_description: tier?.benefits_description || "",
    },
  });

  const onSubmit = async (data: TierFormValues) => {
    setIsSubmitting(true);
    try {
      if (isEditing && tier) {
        await updateTier.mutateAsync({
          id: tier.id,
          ...data,
        });
        toast.success(`The ${data.tier_name} tier has been updated successfully.`);
      } else {
        await createTier.mutateAsync(data);
        toast.success(`The ${data.tier_name} tier has been created successfully.`);
      }
      router.push("/tiers");
    } catch (err) {
      toast.error(`Failed to ${isEditing ? "update" : "create"} tier. Please try again.`);
      console.error("Error saving tier:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{isEditing ? 'Edit Tier' : 'Add New Tier'}</CardTitle>
        <CardDescription>
          {isEditing ? 'Update the tier details' : 'Create a new loyalty tier for your program'}
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">{/* Removed pt-6 since we have header now */}
            <FormField
              control={form.control}
              name="tier_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tier Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Silver, Gold, Platinum" {...field} />
                  </FormControl>
                  <FormDescription>
                    Choose a name that reflects the status level of this tier.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="minimum_points"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Points</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="0"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The minimum lifetime points required to reach this tier.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="benefits_description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Benefits Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the benefits for this tier..."
                      className="min-h-[120px] resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    List all benefits members will receive at this tier level.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/tiers")}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting
                ? "Saving..."
                : isEditing
                ? "Save Changes"
                : "Create Tier"}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
