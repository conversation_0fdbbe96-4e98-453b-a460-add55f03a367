"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { TierDefinition } from "@/app/tiers/types";
import { EditTierDialog } from "./edit-tier-dialog";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ArrowLeftIcon, StarFilledIcon } from "@radix-ui/react-icons";
import {
  Users,
  Diamond,
  Award,
  Gift,
  TrendingUp,
  Clock,
  CheckCircle2,
  Star,
  PencilIcon,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";

interface TierViewProps {
  tier: TierDefinition;
  showHeaderActions?: boolean;
}

export function TierView({ tier, showHeaderActions = false }: TierViewProps) {
  const router = useRouter();
  const [showEditDialog, setShowEditDialog] = useState(false);

  // Helper functions for styling based on tier name
  const getTierBackgroundClass = (tierName: string): string => {
    const name = tierName.toLowerCase();
    if (name === "gold") return "bg-gradient-to-br from-yellow-300 to-amber-500 dark:from-yellow-500/60 dark:to-amber-700/60";
    if (name === "silver") return "bg-gradient-to-br from-gray-300 to-slate-400 dark:from-gray-400/60 dark:to-slate-600/60";
    if (name === "platinum") return "bg-gradient-to-br from-indigo-300 via-purple-300 to-pink-300 dark:from-indigo-600/60 dark:via-purple-600/50 dark:to-pink-600/60";
    return "bg-muted";
  };

  const getTierTextColor = (tierName: string): string => {
    const name = tierName.toLowerCase();
    if (name === "gold") return "text-amber-800 dark:text-amber-100";
    if (name === "silver") return "text-slate-800 dark:text-slate-100";
    if (name === "platinum") return "text-indigo-800 dark:text-indigo-100";
    return "text-foreground";
  };

  const getTierIcon = (tierName: string) => {
    const name = tierName.toLowerCase();
    if (name === "gold") return <StarFilledIcon className="h-6 w-6 inline mr-2 text-amber-600 dark:text-amber-300" />;
    if (name === "silver") return <Star className="h-6 w-6 inline mr-2 text-slate-600 dark:text-slate-300" />;
    if (name === "platinum") return <Diamond className="h-6 w-6 inline mr-2 text-indigo-600 dark:text-indigo-300" />;
    return null;
  };

  // Calculate tier level and display name
  const getTierLevel = (points: number): string => {
    return points === 0 ? "Basic" : points >= 2000 ? "Premium" : "Standard";
  };

  // Example benefits for better visualization - based on tier's benefits description
  const tierBenefits = [
    {
      title: "Priority Support",
      description: "Get faster responses to your inquiries",
      icon: <CheckCircle2 className="h-5 w-5" />
    },
    {
      title: "Exclusive Rewards",
      description: "Unlock special rewards only available to this tier",
      icon: <Gift className="h-5 w-5" />
    },
    {
      title: "Bonus Points",
      description: "Earn 1.5x points on all transactions",
      icon: <TrendingUp className="h-5 w-5" />
    },
    {
      title: "Extended Expiration",
      description: "Points valid for longer periods",
      icon: <Clock className="h-5 w-5" />
    }
  ];

  // Format large numbers with commas
  const formatNumber = (num: number | undefined): string => {
    return num !== undefined ? num.toLocaleString() : '0';
  };

  // Access member stats safely
  const memberStats = tier.member_stats || {
    active: 0,
    new: 0,
    inactive: 0
  };

  // Safely access member_count
  const memberCount = tier.member_count || 0;

  // Action buttons to display either at the top or bottom of the card
  const actionButtons = (
    <>
      <Button
        variant="outline"
        onClick={() => router.push("/tiers")}
        className="gap-2"
      >
        <ArrowLeftIcon className="h-4 w-4" />
        Back to Tiers
      </Button>
      <Button
        onClick={() => setShowEditDialog(true)}
        className="bg-primary text-primary-foreground hover:bg-primary/90 ml-2"
      >
        <PencilIcon className="h-4 w-4 mr-2" />
        Edit Tier
      </Button>
    </>
  );

  return (
    <div className="space-y-6">
      {/* Header actions if enabled */}
      {showHeaderActions && (
        <div className="flex justify-end mb-4">
          {actionButtons}
        </div>
      )}

      {/* Hero section with tier info */}
      <Card className="border-0 shadow-lg overflow-hidden">
        <CardHeader className={`${getTierBackgroundClass(tier.tier_name)} p-8 flex flex-col items-center`}>
          <div className={`text-4xl font-bold ${getTierTextColor(tier.tier_name)} flex items-center`}>
            {getTierIcon(tier.tier_name)}
            {tier.tier_name}
          </div>
          <Badge className="mt-2" variant="outline">
            {getTierLevel(tier.minimum_points)}
          </Badge>
        </CardHeader>

        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Minimum Points Required</h3>
                <div className="flex items-baseline">
                  <span className="text-3xl font-bold">{formatNumber(tier.minimum_points)}</span>
                  <span className="ml-2 text-sm text-muted-foreground">points</span>
                </div>
                <Progress
                  value={Math.min(100, tier.minimum_points / 100)}
                  className="h-2 mt-2"
                />
              </div>

              <div className="flex items-center gap-4 bg-muted/30 p-3 rounded-lg">
                <div className="p-3 rounded-full bg-primary/10">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <div className="text-sm text-muted-foreground">Members in this tier</div>
                  <div className="text-2xl font-semibold">{formatNumber(memberCount)}</div>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">About this Tier</h3>
                <p className="text-muted-foreground">
                  The {tier.tier_name} tier is designed for loyal customers who have accumulated {formatNumber(tier.minimum_points)} or more points.
                  Members enjoy premium benefits designed to enhance their experience.
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium flex items-center">
                <Award className="mr-2 h-5 w-5 text-primary" />
                Tier Benefits
              </h3>

              <div className="grid gap-3">
                {tierBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-start p-3 rounded-lg border bg-card/50">
                    <div className="rounded-full bg-primary/10 p-2 mr-3">
                      {benefit.icon}
                    </div>
                    <div>
                      <h4 className="font-medium">{benefit.title}</h4>
                      <p className="text-sm text-muted-foreground">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          <div className="space-y-4">
            <h3 className="font-medium">Benefits Description</h3>
            <div className="bg-muted/30 p-4 rounded-md">
              <p className="whitespace-pre-line">
                {tier.benefits_description || "No benefits described for this tier."}
              </p>
            </div>
          </div>
        </CardContent>

        {/* Only show footer with buttons if header actions are not enabled */}
        {!showHeaderActions && (
          <CardFooter className="px-6 py-4 border-t flex flex-col sm:flex-row justify-between gap-3">
            {actionButtons}
          </CardFooter>
        )}
      </Card>

      {/* Member distribution card */}
      {memberCount > 0 && (
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">Member Performance</h3>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <h4 className="font-medium">Point Distribution</h4>
                  <p className="text-sm text-muted-foreground">
                    Average points per member: {formatNumber(Math.round(tier.minimum_points * 1.2))}
                  </p>
                </div>
                <Badge variant="outline" className="w-fit">{formatNumber(memberCount)} Members</Badge>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 pt-2">
                <div className="h-24 bg-muted/30 rounded-md flex flex-col items-center justify-center">
                  <span className="text-2xl font-bold">{formatNumber(memberStats.new)}</span>
                  <span className="text-xs text-muted-foreground">New members</span>
                </div>
                <div className="h-24 bg-primary/10 rounded-md flex flex-col items-center justify-center">
                  <span className="text-2xl font-bold">{formatNumber(memberStats.active)}</span>
                  <span className="text-xs text-muted-foreground">Active members</span>
                </div>
                <div className="h-24 bg-muted/30 rounded-md flex flex-col items-center justify-center">
                  <span className="text-2xl font-bold">{formatNumber(memberStats.inactive)}</span>
                  <span className="text-xs text-muted-foreground">Inactive members</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <EditTierDialog
        tier={tier}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
      />
    </div>
  );
}