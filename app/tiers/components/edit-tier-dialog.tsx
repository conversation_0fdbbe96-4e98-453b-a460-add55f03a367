"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import type { TierDefinition } from "@/app/tiers/types";
import { useUpdateTier, useCreateTier } from "../hooks/use-tiers";

const tierFormSchema = z.object({
  tier_name: z.string().min(1, "Tier name is required"),
  minimum_points: z.coerce
    .number()
    .int("Must be a whole number")
    .min(0, "Minimum points cannot be negative"),
  benefits_description: z.string().min(1, "Benefits description is required"),
});

type TierFormValues = z.infer<typeof tierFormSchema>;

interface EditTierDialogProps {
  tier?: TierDefinition;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditTierDialog({
  tier,
  open,
  onOpenChange,
}: EditTierDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const updateTier = useUpdateTier();
  const createTier = useCreateTier();
  const isEditing = !!tier;

  const form = useForm<TierFormValues>({
    resolver: zodResolver(tierFormSchema),
    defaultValues: {
      tier_name: tier?.tier_name || "",
      minimum_points: tier?.minimum_points || 0,
      benefits_description: tier?.benefits_description || "",
    },
  });

  const onSubmit = async (data: TierFormValues) => {
    setIsSubmitting(true);
    try {
      if (tier) {
        // Update existing tier
        await updateTier.mutateAsync({
          id: tier.id,
          ...data,
        });
        toast.success(`The ${data.tier_name} tier has been updated successfully.`);
      } else {
        // Create new tier
        await createTier.mutateAsync(data);
        toast.success(`The ${data.tier_name} tier has been created successfully.`);
      }
      onOpenChange(false);
    } catch (err) {
      toast.error(`Failed to ${tier ? "update" : "create"} tier. Please try again.`);
      console.error("Error saving tier:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Edit Tier" : "Create Tier"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Update the tier details below."
              : "Define a new loyalty tier for your program."}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="tier_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tier Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Silver, Gold, Platinum" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="minimum_points"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Points</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="0"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="benefits_description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Benefits Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the benefits for this tier..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                  ? "Save Changes"
                  : "Create Tier"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
