"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Pencil1Icon, ArrowLeftIcon } from "@radix-ui/react-icons";
import { Diamond } from "lucide-react";
import { StarFilledIcon, StarIcon } from "@radix-ui/react-icons";
import { TierDefinition } from "../types";
import { Badge } from "@/components/ui/badge";
import { EditTierDialog } from "./edit-tier-dialog";

interface TierDetailsProps {
  tier: TierDefinition;
}

export function TierDetails({ tier }: TierDetailsProps) {
  const router = useRouter();
  const [showEditDialog, setShowEditDialog] = useState(false);

  // Helper functions for styling based on tier name
  const getTierBackgroundClass = (tierName: string): string => {
    const name = tierName.toLowerCase();
    if (name === "gold") return "bg-gradient-to-br from-yellow-300 to-amber-500 dark:from-yellow-500/60 dark:to-amber-700/60";
    if (name === "silver") return "bg-gradient-to-br from-gray-300 to-slate-400 dark:from-gray-400/60 dark:to-slate-600/60";
    if (name === "platinum") return "bg-gradient-to-br from-indigo-300 via-purple-300 to-pink-300 dark:from-indigo-600/60 dark:via-purple-600/50 dark:to-pink-600/60";
    return "bg-muted";
  };

  const getTierTextColor = (tierName: string): string => {
    const name = tierName.toLowerCase();
    if (name === "gold") return "text-amber-800 dark:text-amber-100";
    if (name === "silver") return "text-slate-800 dark:text-slate-100";
    if (name === "platinum") return "text-indigo-800 dark:text-indigo-100";
    return "text-foreground";
  };

  const getTierIcon = (tierName: string) => {
    const name = tierName.toLowerCase();
    if (name === "gold") return <StarFilledIcon className="h-6 w-6 inline mr-2 text-amber-600 dark:text-amber-300" />;
    if (name === "silver") return <StarIcon className="h-6 w-6 inline mr-2 text-slate-600 dark:text-slate-300" />;
    if (name === "platinum") return <Diamond className="h-6 w-6 inline mr-2 text-indigo-600 dark:text-indigo-300" />;
    return null;
  };

  // Determine tier level based on minimum points
  const getTierLevel = (points: number): string => {
    return points === 0 ? "Basic" : points >= 2000 ? "Premium" : "Standard";
  };

  const formattedCreatedDate = tier.created_at
    ? new Date(tier.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : 'N/A';

  return (
    <Card className="w-full">
      <CardHeader className={`${getTierBackgroundClass(tier.tier_name)} p-8 flex flex-col items-center`}>
        <div className={`text-3xl font-bold ${getTierTextColor(tier.tier_name)} flex items-center`}>
          {getTierIcon(tier.tier_name)}
          {tier.tier_name}
        </div>
        <Badge className="mt-2" variant="outline">
          {getTierLevel(tier.minimum_points)}
        </Badge>
      </CardHeader>

      <CardContent className="pt-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Minimum Points</h3>
            <p className="text-2xl font-bold">{tier.minimum_points.toLocaleString()}</p>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Member Count</h3>
            <p className="text-2xl font-bold">{tier.member_count?.toLocaleString() || '0'}</p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Benefits</h3>
          <div className="bg-muted/30 p-4 rounded-md">
            <p className="whitespace-pre-line">{tier.benefits_description}</p>
          </div>
        </div>

        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-1">Created</h3>
          <p>{formattedCreatedDate}</p>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between border-t pt-6">
        <Button
          variant="outline"
          onClick={() => router.push("/tiers")}
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Tiers
        </Button>
        <Button onClick={() => setShowEditDialog(true)}>
          <Pencil1Icon className="h-4 w-4 mr-2" />
          Edit Tier
        </Button>
      </CardFooter>

      <EditTierDialog
        tier={tier}
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
      />
    </Card>
  );
}