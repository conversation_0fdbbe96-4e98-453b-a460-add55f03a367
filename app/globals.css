@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=IBM+Plex+Mono:wght@400;500&family=Sora:wght@300;400;500;600&display=swap');
@import url('https://api.fontshare.com/v2/css?f[]=satoshi@900,700,500,301,701,300,501,401,901,400&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base Gold */
  --gold-50: 51, 85%, 97%;
  --gold-100: 50, 80%, 90%;
  --gold-200: 49, 90%, 80%;
  --gold-300: 45, 90%, 70%;
  --gold-400: 40, 90%, 60%;
  --gold-500: 41, 75%, 52%;
  --gold-600: 42, 80%, 40%;
  --gold-700: 39, 75%, 30%;
  --gold-800: 36, 70%, 25%;
  --gold-900: 36, 70%, 20%;
  --gold-950: 38, 65%, 12%;

  /* Base Purple */
  --purple-50: 267, 100%, 97%;
  --purple-100: 268, 100%, 92%;
  --purple-200: 268, 95%, 85%;
  --purple-300: 269, 95%, 78%;
  --purple-400: 269, 90%, 70%;
  --purple-500: 267, 83%, 60%;
  --purple-600: 265, 96%, 50%;
  --purple-700: 263, 85%, 40%;
  --purple-800: 263, 70%, 30%;
  --purple-900: 260, 60%, 25%;
  --purple-950: 261, 70%, 18%;

  /* Light Theme Default */
  --background: 220, 20%, 97%;
  --foreground: 225, 25%, 15%;

  --card: 0, 0%, 100%;
  --card-foreground: 225, 25%, 15%;

  --popover: 0, 0%, 100%;
  --popover-foreground: 225, 25%, 15%;

  --primary: 267, 83%, 50%;
  --primary-foreground: 0, 0%, 100%;

  --secondary: 41, 75%, 52%;
  --secondary-foreground: 0, 0%, 100%;

  --muted: 220, 15%, 94%;
  --muted-foreground: 220, 10%, 40%;

  --accent: 220, 15%, 92%;
  --accent-foreground: 220, 25%, 20%;

  --destructive: 0, 84%, 60%;
  --destructive-foreground: 0, 0%, 100%;

  --success: 160, 84%, 39%;
  --warning: 40, 90%, 50%;
  --info: 220, 90%, 60%;

  --border: 220, 13%, 90%;
  --input: 220, 13%, 90%;
  --ring: 267, 75%, 50%;

  --radius: 0.5rem;
}

.dark {
  --background: 225, 20%, 12%;
  --foreground: 220, 20%, 96%;

  --card: 225, 23%, 16%;
  --card-foreground: 220, 20%, 96%;

  --popover: 225, 23%, 14%;
  --popover-foreground: 220, 20%, 96%;

  --primary: 267, 83%, 60%;
  --primary-foreground: 0, 0%, 100%;

  --secondary: 41, 75%, 52%;
  --secondary-foreground: 0, 0%, 100%;

  --muted: 225, 15%, 20%;
  --muted-foreground: 220, 10%, 70%;

  --accent: 225, 15%, 22%;
  --accent-foreground: 220, 20%, 96%;

  --destructive: 0, 70%, 55%;
  --destructive-foreground: 0, 0%, 100%;

  --success: 160, 84%, 35%;
  --warning: 40, 90%, 45%;
  --info: 220, 90%, 55%;

  --border: 225, 15%, 25%;
  --input: 225, 15%, 25%;
  --ring: 267, 75%, 60%;
}

/* Smooth transition between light and dark modes */
:root {
  --transition-duration: 0.3s;
}

.theme-transition * {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--transition-duration);
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    -webkit-tap-highlight-color: transparent;
    /* Smooth scrolling */
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Improved Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-medium tracking-tight;
  }

  h1 {
    @apply text-3xl leading-tight md:text-4xl md:leading-tight lg:text-5xl lg:leading-tight;
  }

  h2 {
    @apply text-2xl leading-tight md:text-3xl md:leading-tight;
  }

  h3 {
    @apply text-xl leading-tight md:text-2xl md:leading-tight;
  }

  h4 {
    @apply text-lg leading-snug md:text-xl md:leading-snug;
  }

  /* Number presentation */
  .numeric {
    @apply font-numeric tabular-nums;
  }

  /* Statistics large numbers */
  .stat-value {
    @apply text-3xl font-numeric font-light tracking-tight md:text-4xl;
  }

  /* Background pattern for dashboard */
  .dashboard-pattern {
    @apply relative overflow-hidden;
    background-image:
      radial-gradient(circle at 100% 0%,
        hsl(var(--primary) / 0.08) 0%,
        transparent 40%),
      radial-gradient(circle at 0% 100%,
        hsl(var(--secondary) / 0.08) 0%,
        transparent 40%);
    background-attachment: fixed;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-muted/50;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full hover:bg-muted-foreground/50 transition-colors;
  }

  /* Better table styles */
  table {
    @apply w-full border-collapse;
  }

  table thead tr {
    @apply border-b border-border;
  }

  table tbody tr {
    @apply border-b border-border/50 last:border-0;
  }

  table th {
    @apply h-12 px-4 text-left align-middle font-medium text-muted-foreground;
  }

  table td {
    @apply p-4 align-middle;
  }

  /* Better form elements */
  input, select, textarea {
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* Premium Card Styles */
  .premium-card {
    @apply relative rounded-lg border bg-card p-6 text-card-foreground shadow-card transition-all duration-300 hover:shadow-card-hover;
  }

  .premium-card-lg {
    @apply premium-card p-8;
  }

  .premium-card-elevated {
    @apply premium-card shadow-card-lg;
  }

  .premium-card-gold {
    @apply premium-card border-secondary/50 bg-gradient-to-b from-card to-card;
  }

  .premium-card-gold:before {
    content: '';
    @apply absolute inset-0 rounded-lg border border-secondary/30 bg-gradient-to-b from-secondary/5 to-transparent opacity-50;
    pointer-events: none;
  }

  /* Card components */
  .card-title {
    @apply text-lg font-medium tracking-tight;
  }

  .card-description {
    @apply text-sm text-muted-foreground;
  }

  /* Stat cards */
  .stats-card {
    @apply premium-card flex flex-col gap-2;
  }

  .stats-value {
    @apply text-3xl font-numeric font-medium tracking-tight;
  }

  .stats-label {
    @apply text-sm font-medium text-muted-foreground;
  }

  .stats-trend {
    @apply inline-flex items-center space-x-1 text-xs font-medium;
  }

  .stats-trend-up {
    @apply text-success;
  }

  .stats-trend-down {
    @apply text-destructive;
  }

  /* Better dashboard grid */
  .dashboard-grid {
    @apply grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  .dashboard-grid-cols-3 {
    @apply grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3;
  }

  /* Badge variations */
  .premium-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors;
  }

  .badge-primary {
    @apply premium-badge bg-primary/10 text-primary dark:bg-primary/20;
  }

  .badge-secondary {
    @apply premium-badge bg-secondary/10 text-secondary dark:bg-secondary/20;
  }

  .badge-outline {
    @apply premium-badge border border-border bg-background/50;
  }

  .badge-gold {
    @apply premium-badge bg-secondary/20 text-secondary-foreground;
  }

  /* Dividers and separators */
  .divider {
    @apply my-6 h-px w-full bg-border;
  }

  .divider-vertical {
    @apply mx-6 h-full w-px bg-border;
  }

  /* Gradient effects */
  .gold-gradient {
    @apply bg-gradient-to-r from-secondary to-secondary-600;
  }

  .gold-gradient-text {
    @apply bg-gradient-to-r from-gold to-gold-600 bg-clip-text text-transparent;
  }

  .purple-gradient {
    @apply bg-gradient-to-r from-primary to-purple-600;
  }

  .purple-gradient-text {
    @apply bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent;
  }

  /* Branded buttons */
  .btn-premium {
    @apply inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply btn-premium bg-primary text-primary-foreground hover:bg-primary/90;
  }

  .btn-secondary {
    @apply btn-premium bg-secondary text-secondary-foreground hover:bg-secondary/90;
  }

  .btn-outline {
    @apply btn-premium border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }

  .btn-ghost {
    @apply btn-premium hover:bg-accent hover:text-accent-foreground;
  }

  /* Improved inputs */
  .input-premium {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  /* Mobile adaptations */
  @media (max-width: 640px) {
    .mobile-hidden {
      @apply hidden;
    }

    .mobile-only {
      @apply block;
    }

    .premium-card {
      @apply p-4;
    }

    .premium-card-lg {
      @apply p-5;
    }
  }

  @media (min-width: 641px) {
    .mobile-only {
      @apply hidden;
    }

    .mobile-hidden {
      @apply block;
    }
  }
}
