'use client'

import { useState, useEffect } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Search, Plus, Edit, History, TrendingUp, TrendingDown, Minus } from 'lucide-react'
import { toast } from 'sonner'

interface BusinessItem {
  id: string;
  name: string;
  category: string;
  current_price: number;
  average_price: number;
  total_quantity: number;
  total_revenue: number;
  popularity_score: number;
  price_trend: 'up' | 'down' | 'stable';
  last_seen: string;
  frequency: number;
}

export default function ItemsPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()

  const [activeTab, setActiveTab] = useState('catalog')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [items, setItems] = useState<BusinessItem[]>([])

  const categories = ['all', 'Beverages', 'Food', 'Desserts', 'Services'];
  // Load items data
  useEffect(() => {
    const mockItems: BusinessItem[] = [
      {
        id: '1',
        name: 'Ethiopian Coffee',
        category: 'Beverages',
        current_price: 45.00,
        average_price: 43.50,
        total_quantity: 247,
        total_revenue: 11115.00,
        popularity_score: 95,
        price_trend: 'up',
        last_seen: '2025-08-12',
        frequency: 247
      },
      {
        id: '2',
        name: 'Injera',
        category: 'Food',
        current_price: 25.00,
        average_price: 25.00,
        total_quantity: 156,
        total_revenue: 3900.00,
        popularity_score: 87,
        price_trend: 'stable',
        last_seen: '2025-08-12',
        frequency: 156
      },
      {
        id: '3',
        name: 'Doro Wot',
        category: 'Food',
        current_price: 120.00,
        average_price: 125.00,
        total_quantity: 89,
        total_revenue: 10680.00,
        popularity_score: 78,
        price_trend: 'down',
        last_seen: '2025-08-11',
        frequency: 89
      }
    ];

    const loadItems = async () => {
      try {
        // TODO: Replace with actual API call
        // const response = await fetch(`/api/items?company_id=${company?.id}`)
        // const data = await response.json()
        // setItems(data.items)

        // Using mock data for now
        setItems(mockItems)
      } catch (error) {
        console.error('Failed to load items:', error)
        toast.error('Failed to load items')
      }
    }

    if (company?.id) {
      loadItems()
    }
  }, [company?.id])

  const filteredItems = items.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  if (authLoading || companyLoading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col gap-4">
          <div className="animate-shimmer w-48 h-8 rounded-lg"></div>
          <div className="animate-shimmer w-full h-64 rounded-lg"></div>
        </div>
      </div>
    )
  }

  if (!user || !company) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Business Items</h1>
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Item
          </Button>
        </div>

        <Tabs defaultValue="catalog" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 md:w-[400px] mb-4">
            <TabsTrigger value="catalog">Item Catalog</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="history">Price History</TabsTrigger>
          </TabsList>

          <TabsContent value="catalog">
            <Card>
              <CardHeader>
                <CardTitle>Item Catalog</CardTitle>
                <CardDescription>
                  Manage your business items, prices, and inventory tracking
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">

                {/* Search and Filter */}
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search items..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {categories.map(category => (
                      <Button
                        key={category}
                        variant={selectedCategory === category ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedCategory(category)}
                      >
                        {category === 'all' ? 'All Categories' : category}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Items Grid */}
                <div className="space-y-4">
                  {filteredItems.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <h3 className="font-semibold text-lg">{item.name}</h3>
                            <Badge variant="secondary">{item.category}</Badge>
                            {item.price_trend === 'up' && (
                              <div className="flex items-center gap-1 text-green-600">
                                <TrendingUp className="h-4 w-4" />
                                <span className="text-sm">Rising</span>
                              </div>
                            )}
                            {item.price_trend === 'down' && (
                              <div className="flex items-center gap-1 text-red-600">
                                <TrendingDown className="h-4 w-4" />
                                <span className="text-sm">Declining</span>
                              </div>
                            )}
                            {item.price_trend === 'stable' && (
                              <div className="flex items-center gap-1 text-gray-600">
                                <Minus className="h-4 w-4" />
                                <span className="text-sm">Stable</span>
                              </div>
                            )}
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3 text-sm">
                            <div>
                              <div className="font-medium">Current Price</div>
                              <div className="text-lg font-bold text-blue-600">
                                {item.current_price.toFixed(2)} ETB
                              </div>
                            </div>
                            <div>
                              <div className="font-medium">Total Sold</div>
                              <div className="text-lg font-bold">
                                {item.total_quantity}
                              </div>
                            </div>
                            <div>
                              <div className="font-medium">Revenue</div>
                              <div className="text-lg font-bold text-green-600">
                                {item.total_revenue.toFixed(2)} ETB
                              </div>
                            </div>
                            <div>
                              <div className="font-medium">Popularity</div>
                              <div className="text-lg font-bold">
                                {item.popularity_score}%
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <History className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {filteredItems.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-gray-500 mb-2">No items found</div>
                    <div className="text-sm text-gray-400">
                      Try adjusting your search or category filter
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Item Analytics</CardTitle>
                <CardDescription>
                  Performance insights and trends for your business items
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {items.length}
                    </div>
                    <div className="text-sm text-gray-600">Total Items</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {items.reduce((sum, item) => sum + item.total_revenue, 0).toFixed(0)} ETB
                    </div>
                    <div className="text-sm text-gray-600">Total Revenue</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {items.reduce((sum, item) => sum + item.total_quantity, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Items Sold</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {Math.round(items.reduce((sum, item) => sum + item.popularity_score, 0) / items.length)}%
                    </div>
                    <div className="text-sm text-gray-600">Avg Popularity</div>
                  </div>
                </div>

                {/* Top Performers */}
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-3">Top Performing Items</h3>
                  <div className="space-y-3">
                    {items
                      .sort((a, b) => b.total_revenue - a.total_revenue)
                      .slice(0, 5)
                      .map((item, index) => (
                        <div key={item.id} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-bold">
                              {index + 1}
                            </div>
                            <div>
                              <div className="font-medium">{item.name}</div>
                              <div className="text-sm text-gray-600">{item.category}</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold text-green-600">
                              {item.total_revenue.toFixed(2)} ETB
                            </div>
                            <div className="text-sm text-gray-600">
                              {item.total_quantity} sold
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>Price History</CardTitle>
                <CardDescription>
                  Track price changes and trends over time
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-center py-12">
                  <div className="text-gray-500 mb-2">Price History Coming Soon</div>
                  <div className="text-sm text-gray-400">
                    Historical pricing data and trend analysis will be available here
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
