'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Resolver, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useRequireAuth } from '@/hooks/use-auth';
import { useCreateCompany } from '@/hooks/use-company';

// Validation schema for company creation
const companySchema = z.object({
  name: z.string().min(2, 'Company name must be at least 2 characters'),
  slug: z.string().min(2, 'Slug must be at least 2 characters'),
  logoUrl: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Please enter a valid hex color code').optional().or(z.literal('')),
  pointsExpirationDays: z.coerce.number().int().positive('Must be a positive number'),
  pointsEarningRatio: z.coerce.number().positive('Must be a positive number'),
});

type CompanyFormValues = z.infer<typeof companySchema>;

export default function CreateCompanyPage() {
  const { user } = useRequireAuth();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  // Use hook for company creation
  const createCompanyMutation = useCreateCompany();

  // Fix hydration issues by ensuring client-side only rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema) as Resolver<CompanyFormValues>,
    defaultValues: {
      name: '',
      slug: '',
      logoUrl: '',
      primaryColor: '#6366f1',
      pointsExpirationDays: 365,
      pointsEarningRatio: 1.0,
    },
  });

  // Auto-generate slug from name
  useEffect(() => {
    const subscription = watch((value, { name }) => {
      if (name === 'name') {
        setValue('slug', generateSlug(value.name || ''));
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, setValue]);

  // Generate slug from business name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special chars
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-'); // Remove consecutive hyphens
  };

  const onSubmit: SubmitHandler<CompanyFormValues> = async (data) => {
    if (!user) {
      toast.error('You must be logged in to create a company');
      return;
    }

    try {
      // Use the mutation hook to create the company
      const newCompany = await createCompanyMutation.mutateAsync({
        name: data.name,
        slug: data.slug,
        logo_url: data.logoUrl || undefined,
        primary_color: data.primaryColor || undefined,
        points_expiration_days: data.pointsExpirationDays,
        points_earning_ratio: data.pointsEarningRatio,
      });

      // Store company ID in localStorage with a proper namespace to avoid conflicts
      // Changed from 'current_company_id' to 'loyal_app_company_id' to avoid any parameter conflicts
      localStorage.setItem('loyal_app_company_id', newCompany.id);

      toast.success('Company created successfully!');
      router.push('/dashboard');
    } catch (error) {
      console.error('Error creating company:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create company';
      toast.error(errorMessage);
    }
  };

  // If not mounted yet, don't render to avoid hydration errors
  if (!mounted) {
    return null;
  }

  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Create Your Company Profile</CardTitle>
          <CardDescription>
            Set up your company details to start your loyalty program
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Company Name</Label>
              <Input
                id="name"
                placeholder="Your Company Name"
                {...register('name')}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">
                Company URL Slug
                <span className="text-sm text-muted-foreground ml-2">
                  (used in your loyalty program URL)
                </span>
              </Label>
              <Input
                id="slug"
                placeholder="your-company-name"
                {...register('slug')}
              />
              {errors.slug && (
                <p className="text-sm text-red-500">{errors.slug.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="logoUrl">Logo URL (optional)</Label>
              <Input
                id="logoUrl"
                placeholder="https://example.com/logo.png"
                {...register('logoUrl')}
              />
              {errors.logoUrl && (
                <p className="text-sm text-red-500">{errors.logoUrl.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="primaryColor">Brand Color (optional)</Label>
              <div className="flex items-center gap-3">
                <Input
                  id="primaryColor"
                  type="color"
                  className="w-12 h-12 p-1"
                  {...register('primaryColor')}
                />
                <Input
                  type="text"
                  placeholder="#6366f1"
                  value={watch('primaryColor')}
                  onChange={(e) => setValue('primaryColor', e.target.value)}
                />
              </div>
              {errors.primaryColor && (
                <p className="text-sm text-red-500">{errors.primaryColor.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="pointsExpirationDays">
                  Points Expiration (days)
                </Label>
                <Input
                  id="pointsExpirationDays"
                  type="number"
                  min="1"
                  {...register('pointsExpirationDays', { valueAsNumber: true })}
                />
                {errors.pointsExpirationDays && (
                  <p className="text-sm text-red-500">{errors.pointsExpirationDays.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="pointsEarningRatio">
                  Points Earning Ratio
                  <span className="text-sm text-muted-foreground ml-2">
                    (ETB to points)
                  </span>
                </Label>
                <Input
                  id="pointsEarningRatio"
                  type="number"
                  min="0.1"
                  step="0.1"
                  {...register('pointsEarningRatio', { valueAsNumber: true })}
                />
                {errors.pointsEarningRatio && (
                  <p className="text-sm text-red-500">{errors.pointsEarningRatio.message}</p>
                )}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              className="w-full"
              disabled={createCompanyMutation.isPending}
            >
              {createCompanyMutation.isPending ? 'Creating...' : 'Create Company Profile'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
