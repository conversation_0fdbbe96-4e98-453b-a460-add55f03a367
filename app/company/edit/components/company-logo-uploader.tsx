'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { FileImage, Upload, X } from 'lucide-react'
import { toast } from 'sonner'
import { uploadImage } from '@/lib/file-upload'
import Image from 'next/image'

interface CompanyLogoUploaderProps {
  currentLogoUrl: string
  onLogoChange: (url: string) => void
  companyId?: string
}

export default function CompanyLogoUploader({
  currentLogoUrl,
  onLogoChange,
  companyId
}: CompanyLogoUploaderProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState(currentLogoUrl)

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !companyId) return

    // Validate file type and size
    if (!['image/jpeg', 'image/png', 'image/svg+xml'].includes(file.type)) {
      toast.error('Please select a valid image file (JPEG, PNG, SVG)')
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB max
      toast.error('Image file is too large. Maximum size is 5MB.')
      return
    }

    setIsUploading(true)

    try {
      // Create a local preview
      const objectUrl = URL.createObjectURL(file)
      setPreviewUrl(objectUrl)

      // Upload to Supabase Storage using the existing upload utility
      const uploadResult = await uploadImage(file, 'fufis', 'company-logos')
      const publicUrl = uploadResult.url

      // Update the parent component with the new URL
      onLogoChange(publicUrl)

      // Clean up the object URL and use the uploaded URL
      URL.revokeObjectURL(objectUrl)
      setPreviewUrl(publicUrl)

      toast.success('Logo uploaded successfully')
    } catch (error: unknown) {
      console.error('Error uploading logo:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      toast.error(`Failed to upload logo: ${errorMessage}`)
      // Reset preview to the previous value
      setPreviewUrl(currentLogoUrl)
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveLogo = () => {
    // Update the parent component and local state
    onLogoChange('')
    setPreviewUrl('')
    toast.success('Logo removed successfully')
  }

  return (
    <div className="space-y-4">
      <div className="border rounded-md p-4 flex flex-col sm:flex-row items-center gap-4">
        <div className="relative h-24 w-24 border rounded-md flex items-center justify-center overflow-hidden bg-muted/30">
          {previewUrl ? (
            <>
              <Image
                src={previewUrl}
                alt="Company Logo"
                className="object-contain"
                fill
                sizes="(max-width: 96px) 100vw, 96px"
                priority
                onError={() => {
                  console.error('Error loading image from URL:', previewUrl);
                  // If image fails to load, show the placeholder
                  setPreviewUrl('');
                }}
              />
              {/* Debug info */}
              <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-[6px] p-1 overflow-hidden">
                {previewUrl.substring(0, 20)}...
              </div>
            </>
          ) : (
            <FileImage className="h-12 w-12 text-muted-foreground" />
          )}

          {isUploading && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        <div className="space-y-2 flex-1 text-center sm:text-left">
          <h4 className="font-medium">Company Logo</h4>
          <p className="text-sm text-muted-foreground">
            Upload your business logo. This will appear in your loyalty portal and dashboard.
          </p>
          <p className="text-xs text-muted-foreground">
            Recommended: PNG or SVG with transparent background. Max 5MB.
          </p>
        </div>

        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => document.getElementById('company-logo-upload')?.click()}
            disabled={isUploading}
            className="flex gap-1 items-center"
          >
            <Upload className="h-4 w-4" /> Upload
          </Button>

          {previewUrl && (
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={handleRemoveLogo}
              disabled={isUploading}
              className="text-destructive hover:bg-destructive/10"
            >
              <X className="h-4 w-4" />
            </Button>
          )}

          <input
            type="file"
            id="company-logo-upload"
            accept=".jpg,.jpeg,.png,.svg"
            className="hidden"
            onChange={handleFileChange}
            disabled={isUploading}
          />
        </div>
      </div>
    </div>
  )
}
