'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Resolver, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { useRequireAuth } from '@/hooks/use-auth';
import { useCompany } from '@/contexts/company-context';
import { useUpdateCompany } from '@/hooks/use-company';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import CompanyLogoUploader from './components/company-logo-uploader';

// Validation schema for company update
const companySchema = z.object({
  name: z.string().min(2, 'Company name must be at least 2 characters'),
  primaryColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Please enter a valid hex color code').optional().or(z.literal('')),
  pointsExpirationDays: z.coerce.number().int().positive('Must be a positive number'),
  pointsEarningRatio: z.coerce.number().positive('Must be a positive number'),
});

type CompanyFormValues = z.infer<typeof companySchema>;

export default function EditCompanyPage() {
  const { user, isLoading } = useRequireAuth();
  const { company, isLoading: companyLoading, refreshCompany } = useCompany();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [logoUrl, setLogoUrl] = useState('');
  const updateCompanyMutation = useUpdateCompany();

  // Fix hydration issues by ensuring client-side only rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  // State to track permission status
  const [hasPermission, setHasPermission] = useState(true);

  // Redirect if not authenticated or check permissions
  useEffect(() => {
    if (mounted && !isLoading) {
      if (!user) {
        router.push('/login');
      } else if (company) {
        // Check if user has permission to edit company
        const checkPermission = async () => {
          try {
            // Call API to check user role
            const response = await fetch(`/api/user-role?companyId=${company.id}`);
            if (response.ok) {
              const { role } = await response.json();
              console.log('User role for company:', role);

              // Only OWNER role can edit company details
              if (role !== 'OWNER') {
                setHasPermission(false);
                toast.error('You need owner permissions to edit company details');
              }
            } else {
              console.error('Failed to check user role');
            }
          } catch (error) {
            console.error('Error checking permissions:', error);
          }
        };

        checkPermission();
      }
    }
  }, [mounted, isLoading, user, router, company]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema) as Resolver<CompanyFormValues>,
    defaultValues: {
      name: '',
      primaryColor: '#6366f1',
      pointsExpirationDays: 365,
      pointsEarningRatio: 1.0,
    },
  });

  // Load company data when available
  useEffect(() => {
    if (company && mounted) {
      const companyLogoUrl = company.logo_url || '';
      console.log('Company data loaded:', {
        companyName: company.name,
        logoUrl: companyLogoUrl,
        hasLogoUrl: !!companyLogoUrl
      });

      // Ensure the logo URL is properly formatted
      if (companyLogoUrl) {
        // Make sure the URL is absolute if it's not already
        const formattedLogoUrl = companyLogoUrl.startsWith('http')
          ? companyLogoUrl
          : `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${companyLogoUrl}`;

        console.log('Setting logo URL:', formattedLogoUrl);
        setLogoUrl(formattedLogoUrl);
      } else {
        setLogoUrl('');
      }

      reset({
        name: company.name,
        primaryColor: company.appearance?.primary_color || '#6366f1',
        pointsExpirationDays: company.points_expiration_days,
        pointsEarningRatio: company.points_earning_ratio,
      });
    }
  }, [company, mounted, reset]);

  const onSubmit: SubmitHandler<CompanyFormValues> = async (data) => {
    if (!user || !company) {
      toast.error('You must be logged in and have a company to update');
      return;
    }

    setIsSubmitting(true);

    try {
      // Update company using the mutation hook
      await updateCompanyMutation.mutateAsync({
        name: data.name,
        logo_url: logoUrl || '',
        primary_color: data.primaryColor || '',
        points_expiration_days: data.pointsExpirationDays,
        points_earning_ratio: data.pointsEarningRatio,
      });

      // Refresh company context
      if (refreshCompany) {
        refreshCompany();
      }

      toast.success('Company updated successfully!');
      router.push('/dashboard');
    } catch (error) {
      console.error('Error updating company:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update company');
    } finally {
      setIsSubmitting(false);
    }
  };

  // If not mounted yet, don't render to avoid hydration errors
  if (!mounted || isLoading || companyLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-background">
        <div className="animate-shimmer w-48 h-12 rounded-lg"></div>
      </div>
    );
  }

  if (!hasPermission && mounted && !isLoading) {
    return (
      <div className="container mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">
                You need owner permissions to edit company details. Please contact an administrator.
              </p>
            </div>
          </div>
        </div>
        <button
          onClick={() => router.push('/dashboard')}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
        >
          Return to Dashboard
        </button>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">No Company Account Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a company profile first.</p>
        <Button onClick={() => router.push('/company/create')}>
          Create Company Profile
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <Button variant="outline" asChild className="mb-4">
          <Link href="/dashboard">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Edit Company Information</CardTitle>
          <CardDescription>
            Update your company details and loyalty program settings
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Company Name</Label>
              <Input
                id="name"
                placeholder="Your Company Name"
                {...register('name')}
              />
              {errors.name && (
                <p className="text-destructive text-sm mt-1">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Company Logo</Label>
              <CompanyLogoUploader
                currentLogoUrl={logoUrl}
                onLogoChange={setLogoUrl}
                companyId={company?.id}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="primaryColor">Brand Color</Label>
              <div className="flex gap-3">
                <Input
                  id="primaryColor"
                  type="color"
                  className="w-16 h-10 p-1"
                  {...register('primaryColor')}
                />
                <Input
                  type="text"
                  placeholder="#6366f1"
                  className="flex-1"
                  {...register('primaryColor')}
                />
              </div>
              {errors.primaryColor && (
                <p className="text-destructive text-sm mt-1">{errors.primaryColor.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="pointsEarningRatio">Points Earning Ratio</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="pointsEarningRatio"
                    type="number"
                    step="0.1"
                    min="0.1"
                    {...register('pointsEarningRatio')}
                  />
                  <span className="text-muted-foreground">points per 1 Birr</span>
                </div>
                {errors.pointsEarningRatio && (
                  <p className="text-destructive text-sm mt-1">{errors.pointsEarningRatio.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="pointsExpirationDays">Points Expiration</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="pointsExpirationDays"
                    type="number"
                    min="1"
                    {...register('pointsExpirationDays')}
                  />
                  <span className="text-muted-foreground">days</span>
                </div>
                {errors.pointsExpirationDays && (
                  <p className="text-destructive text-sm mt-1">{errors.pointsExpirationDays.message}</p>
                )}
              </div>
            </div>

            <div className="p-3 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>Note:</strong> The company slug cannot be changed after creation as it is used in URLs and API endpoints.
              </p>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" type="button" onClick={() => router.push('/dashboard')}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
