import { Providers } from './providers'
import './globals.css'
import { inter, roboto_mono } from './fonts'
import { ConditionalHeader, ConditionalFooter } from '@/components/layout/ConditionalHeaderFooter'
import { ThemeProvider } from 'next-themes'
import type { Metadata } from 'next'
import { RemoveExtensionAttributes } from '@/lib/extension-cleanup'

export const metadata: Metadata = {
  title: 'Loyal - Business Admin',
  description: 'Loyalty program management for your business',
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning className={`${inter.variable} ${roboto_mono.variable}`}>
      <head>
        {/* Preload critical assets */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href={process.env.NEXT_PUBLIC_SUPABASE_URL} />

        {/* Meta tag to help prevent FOUC (Flash of Unstyled Content) */}
        <meta name="color-scheme" content="light dark" />
      </head>
      <body className="min-h-screen font-sans antialiased" suppressHydrationWarning>
        <Providers>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
          >
            {/* Use `suppressHydrationWarning` on wrapper to prevent hydration errors from dev tools */}
            <div className="relative flex min-h-screen flex-col" suppressHydrationWarning>
              <ConditionalHeader />
              <main className="flex-grow">
                {children}
              </main>
              <ConditionalFooter />
            </div>
          </ThemeProvider>
          <RemoveExtensionAttributes />
        </Providers>
      </body>
    </html>
  )
}
