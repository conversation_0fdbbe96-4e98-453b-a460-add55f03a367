'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON>bsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from 'recharts'
import { AlertCircle, RefreshCw, Clock } from 'lucide-react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useRouter } from 'next/navigation'
import { useApiMetrics, TimePeriod } from '@/hooks/use-api-metrics'
import { initApiMonitoring, getApiUsageSummary, getRecentApiCalls } from '@/lib/monitoring/api-usage'

// Period options for the time filter
const PERIOD_OPTIONS: { label: string; value: TimePeriod }[] = [
  { label: 'Last hour', value: '1h' },
  { label: 'Last 6 hours', value: '6h' },
  { label: 'Last 24 hours', value: '24h' },
  { label: 'Last 7 days', value: '7d' },
  { label: 'Last 30 days', value: '30d' },
];

export default function MonitoringPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const [mounted, setMounted] = useState(false)
  const [clientMetricsEnabled, setClientMetricsEnabled] = useState(false)
  const router = useRouter()

  // Use the server-side API metrics
  const {
    metrics,
    timePeriod,
    setTimePeriod,
    isLoading: metricsLoading,
    error: metricsError,
    refetch
  } = useApiMetrics('24h');

  // Initialize client-side API monitoring for current session metrics
  useEffect(() => {
    if (typeof window !== 'undefined') {
      initApiMonitoring({ enablePerformanceTracking: true });
      setClientMetricsEnabled(true);
    }
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !authLoading && !user) {
      router.push('/login')
    }
  }, [mounted, authLoading, user, router])

  // Get client-side API usage data for the current session
  const clientSideMetrics = clientMetricsEnabled ? getApiUsageSummary() : {
    totalCalls: 0,
    uniqueEndpoints: 0,
    averageDuration: 0,
    callsByEndpoint: {},
    slowestEndpoints: []
  };
  const recentCalls = clientMetricsEnabled ? getRecentApiCalls({ limit: 50 }) : [];
  const slowCalls = clientMetricsEnabled ? getRecentApiCalls({ minDuration: 500, limit: 10 }) : [];

  // Combine server metrics with client metrics for display
  const summary = metrics.summary;

  // Prepare chart data for server metrics
  const endpointChartData = Object.entries(summary.callsByEndpoint).map(([endpoint, count]) => ({
    name: endpoint.length > 30 ? endpoint.substring(0, 30) + '...' : endpoint,
    value: count,
    fullName: endpoint
  })).sort((a, b) => b.value - a.value).slice(0, 10)

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#8dd1e1', '#a4de6c', '#d0ed57']

  // Handle refresh
  const handleRefresh = () => {
    refetch();
  }

  if (!mounted || authLoading) {
    return null // Prevent hydration mismatch
  }

  const formatBytes = (bytes: number | undefined): string => {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">API Usage Monitoring</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Track and optimize API calls to prevent excessive usage
          </p>
        </div>
        <div className="flex gap-2 items-center mt-4 sm:mt-0">
          <div className="flex items-center gap-2 rounded-lg border bg-card p-1 pr-2 shadow-sm">
            <Clock className="h-4 w-4 ml-1 text-muted-foreground" />
            <select
              className="bg-transparent text-sm outline-none pr-1"
              value={timePeriod}
              onChange={(e) => setTimePeriod(e.target.value as TimePeriod)}
            >
              {PERIOD_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total API Calls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{summary.totalCalls}</div>
            <p className="text-xs text-muted-foreground mt-1">
              {timePeriod === '24h' ? 'Last 24 hours' : `Last ${timePeriod}`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Unique Endpoints</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{summary.uniqueEndpoints}</div>
            <p className="text-xs text-muted-foreground mt-1">Different API routes</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{summary.averageDuration.toFixed(2)}ms</div>
            <p className="text-xs text-muted-foreground mt-1">Across all endpoints</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Session Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{clientSideMetrics.totalCalls}</div>
            <p className="text-xs text-muted-foreground mt-1">API calls in current session</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="current-session">Current Session</TabsTrigger>
          <TabsTrigger value="slow">Slow Calls</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Top API Endpoints</CardTitle>
                <CardDescription>Most frequently called endpoints</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                {metricsLoading ? (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    Loading metrics...
                  </div>
                ) : endpointChartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={endpointChartData}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis type="category" dataKey="name" width={150} />
                      <Tooltip formatter={(value, name, props) => [value, props.payload.fullName]} />
                      <Bar dataKey="value" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    No API calls recorded yet
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>API Usage Distribution</CardTitle>
                <CardDescription>Percentage of calls by endpoint</CardDescription>
              </CardHeader>
              <CardContent className="h-[400px]">
                {metricsLoading ? (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    Loading metrics...
                  </div>
                ) : endpointChartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={endpointChartData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={150}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
                      >
                        {endpointChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value, name, props) => [value, props.payload.fullName]} />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    {metricsError ? 'Error loading metrics' : 'No API calls recorded yet'}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Endpoints Tab */}
        <TabsContent value="endpoints">
          <Card>
            <CardHeader>
              <CardTitle>Endpoint Usage</CardTitle>
              <CardDescription>All API endpoints and their call frequency</CardDescription>
            </CardHeader>
            <CardContent>
              {metricsLoading ? (
                <div className="flex items-center justify-center p-6 text-muted-foreground">
                  Loading metrics...
                </div>
              ) : metrics.endpoints.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Endpoint</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead className="text-right">Calls</TableHead>
                      <TableHead className="text-right">Avg. Duration (ms)</TableHead>
                      <TableHead>Last Called</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {metrics.endpoints.map((endpoint, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-mono text-xs">{endpoint.endpoint}</TableCell>
                        <TableCell>{endpoint.method}</TableCell>
                        <TableCell className="text-right">{endpoint.total_calls}</TableCell>
                        <TableCell className={`text-right ${endpoint.avg_duration > 500 ? 'text-amber-600' : ''}`}>
                          {endpoint.avg_duration.toFixed(2)}
                        </TableCell>
                        <TableCell className="text-xs">
                          {new Date(endpoint.last_called).toLocaleString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex items-center justify-center p-6 text-muted-foreground">
                  {metricsError ? 'Error loading metrics' : 'No API calls recorded yet'}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Current Session Tab */}
        <TabsContent value="current-session">
          <Card>
            <CardHeader>
              <CardTitle>Current Session API Calls</CardTitle>
              <CardDescription>API calls made since this page was loaded</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Endpoint</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead className="text-right">Duration (ms)</TableHead>
                    <TableHead className="text-right">Response Size</TableHead>
                    <TableHead className="text-right">Status</TableHead>
                    <TableHead>Timestamp</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentCalls.map((call, index) => {
                    const url = new URL(call.url, window.location.origin);
                    return (
                      <TableRow key={index}>
                        <TableCell className="font-mono text-xs max-w-[200px] truncate" title={url.pathname}>
                          {url.pathname}
                        </TableCell>
                        <TableCell>{call.method}</TableCell>
                        <TableCell className={`text-right ${call.duration > 500 ? 'text-amber-600' : ''}`}>
                          {call.duration.toFixed(2)}
                        </TableCell>
                        <TableCell className="px-4 py-2 text-right">
                          {formatBytes(call.responseSize)}
                        </TableCell>
                        <TableCell className={`text-right ${call.status >= 400 ? 'text-red-600' : ''}`}>
                          {call.status}
                        </TableCell>
                        <TableCell className="text-xs">
                          {new Date(call.timestamp).toLocaleTimeString()}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                  {recentCalls.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                        No API calls recorded in this session yet
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Slow Calls Tab */}
        <TabsContent value="slow">
          <Card>
            <CardHeader>
              <CardTitle>Slow API Calls</CardTitle>
              <CardDescription>API calls taking longer than 500ms</CardDescription>
            </CardHeader>
            <CardContent>
              <h3 className="text-md font-semibold mb-2">Server-side slow endpoints</h3>
              {metricsLoading ? (
                <div className="flex items-center justify-center p-6 text-muted-foreground">
                  Loading metrics...
                </div>
              ) : (
                <Table className="mb-6">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Endpoint</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead className="text-right">Avg. Duration (ms)</TableHead>
                      <TableHead className="text-right">Total Calls</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {metrics.endpoints
                      .filter(endpoint => endpoint.avg_duration > 500)
                      .sort((a, b) => b.avg_duration - a.avg_duration)
                      .map((endpoint, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-mono text-xs">{endpoint.endpoint}</TableCell>
                          <TableCell>{endpoint.method}</TableCell>
                          <TableCell className="text-right text-amber-600 font-medium">
                            {endpoint.avg_duration.toFixed(2)}
                          </TableCell>
                          <TableCell className="text-right">
                            {endpoint.total_calls}
                          </TableCell>
                        </TableRow>
                      ))}
                    {metrics.endpoints.filter(endpoint => endpoint.avg_duration > 500).length === 0 && (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-4 text-muted-foreground">
                          No slow API calls detected
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              )}

              <h3 className="text-md font-semibold mb-2 mt-6">Current session slow calls</h3>
              {slowCalls.length === 0 ? (
                <div className="flex items-center justify-center p-6 text-muted-foreground">
                  <AlertCircle className="mr-2 h-4 w-4" />
                  No slow API calls detected in the current session
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Endpoint</TableHead>
                      <TableHead className="text-right">Duration (ms)</TableHead>
                      <TableHead className="text-right">Response Size</TableHead>
                      <TableHead>Timestamp</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {slowCalls.map((call, index) => {
                      const url = new URL(call.url, window.location.origin);
                      return (
                        <TableRow key={index}>
                          <TableCell className="font-mono text-xs max-w-[300px] truncate" title={url.pathname}>
                            {url.pathname}
                          </TableCell>
                          <TableCell className="text-right text-amber-600 font-medium">
                            {call.duration.toFixed(2)}
                          </TableCell>
                          <TableCell className="px-4 py-2 text-right">
                            {formatBytes(call.responseSize)}
                          </TableCell>
                          <TableCell className="text-xs">
                            {new Date(call.timestamp).toLocaleTimeString()}
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
            <CardFooter className="text-xs text-muted-foreground">
              Slow API calls can indicate inefficient queries or excessive data transfer
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
