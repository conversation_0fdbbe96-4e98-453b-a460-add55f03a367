'use client'

import { useEffect, useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { getSupabaseClient } from '@/lib/supabase'

interface DbTestResult {
  authTest: string | null
  companies: Array<{
    id: string
    name: string
    administrator_id: string
  }> | null
  receipts: Array<{
    id: string
    company_id: string
    member_id: string
    amount: number
  }> | null
  userAuth: {
    userId: string
    userEmail: string | undefined
  }
  companyInfo: {
    companyId: string
    companyName: string
    administratorId: string
  }
}

export default function AuthDebugPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const [dbTestResult, setDbTestResult] = useState<DbTestResult | null>(null)
  const [dbError, setDbError] = useState<string | null>(null)

  useEffect(() => {
    const testDatabaseAccess = async () => {
      if (!user || !company) {
        console.log('User or company not available yet')
        return
      }

      try {
        const supabase = getSupabaseClient()

        // Test 1: Check auth.uid() from the database perspective
        const { data: authTest, error: authError } = await supabase
          .rpc('get_current_user_id')

        if (authError) {
          console.log('Auth test error:', authError)
        } else {
          console.log('Auth test result:', authTest)
        }

        // Test 2: Try to fetch companies the user administers
        const { data: companies, error: companiesError } = await supabase
          .from('companies')
          .select('id, name, administrator_id')
          .eq('administrator_id', user.id)

        if (companiesError) {
          console.log('Companies query error:', companiesError)
        } else {
          console.log('Companies user administers:', companies)
        }

        // Test 3: Try to fetch receipts for the company
        const { data: receipts, error: receiptsError } = await supabase
          .from('receipts')
          .select('id, company_id, member_id, amount')
          .eq('company_id', company.id)
          .limit(5)

        if (receiptsError) {
          console.log('Receipts query error:', receiptsError)
          setDbError(receiptsError.message)
        } else {
          console.log('Receipts data:', receipts)
          setDbTestResult({
            authTest,
            companies,
            receipts,
            userAuth: {
              userId: user.id,
              userEmail: user.email
            },
            companyInfo: {
              companyId: company.id,
              companyName: company.name,
              administratorId: company.administrator_id || 'unknown'
            }
          })
        }

      } catch (error) {
        console.error('Database test error:', error)
        setDbError(error instanceof Error ? error.message : 'Unknown error')
      }
    }

    if (!authLoading && !companyLoading && user && company) {
      testDatabaseAccess()
    }
  }, [user, company, authLoading, companyLoading])

  if (authLoading || companyLoading) {
    return <div>Loading authentication...</div>
  }

  if (!user) {
    return <div>Not authenticated</div>
  }

  if (!company) {
    return <div>No company found</div>
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Authentication Debug</h1>

      <div className="space-y-4">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="font-semibold mb-2">User Info</h2>
          <p>ID: {user.id}</p>
          <p>Email: {user.email}</p>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <h2 className="font-semibold mb-2">Company Info</h2>
          <p>ID: {company.id}</p>
          <p>Name: {company.name}</p>
          <p>Administrator ID: {company.administrator_id}</p>
          <p>User is Admin: {user.id === company.administrator_id ? 'Yes' : 'No'}</p>
        </div>

        {dbError && (
          <div className="bg-red-50 p-4 rounded-lg">
            <h2 className="font-semibold mb-2">Database Error</h2>
            <p className="text-red-600">{dbError}</p>
          </div>
        )}

        {dbTestResult && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h2 className="font-semibold mb-2">Database Test Results</h2>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(dbTestResult, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
