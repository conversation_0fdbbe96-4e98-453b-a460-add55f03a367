'use client'

import { useState, useEffect } from 'react'
import { useCompany } from '@/contexts/company-context'
import { PageHeader } from '@/components/page-header'
import { Activity, Filter, AlertCircle, ArrowUpRight, Gift, Plus, Users } from 'lucide-react'
import { Card, CardContent, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'

interface Transaction {
  id: string;
  member_id: string;
  member_name?: string;
  points_change: number;
  transaction_type: string;
  transaction_date?: string;
  created_at?: string;
  description?: string;
}

interface Redemption {
  id: string;
  member_id: string;
  member_name?: string;
  reward_id: string;
  reward_name?: string;
  points_cost: number;
  points_used: number;
  created_at?: string;
  reward?: {
    id: string;
    name: string;
  };
}

interface Member {
  id: string;
  name: string;
  registration_date: string;
  loyalty_tier: string | null;
  loyalty_id: string;
}

interface ActivityItem {
  id: string;
  type: 'member' | 'transaction' | 'redemption' | 'notification';
  title: string;
  description: string;
  timestamp: string;
  icon: React.ReactNode;
  link?: string;
  badge?: {
    text: string;
    variant: 'default' | 'secondary' | 'destructive' | 'outline';
  };
}

export default function ActivityPage() {
  const { company } = useCompany()
  const [mounted, setMounted] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [filterType, setFilterType] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const itemsPerPage = 20

  // Use React Query for data fetching with proper types
  const { data: membersData, isLoading: membersLoading } = useQuery({
    queryKey: ['members', company?.id, 'all-activity'],
    queryFn: async () => {
      if (!company?.id) return { data: [] };
      const response = await fetch(`/api/members?companyId=${company.id}&limit=100`);
      if (!response.ok) throw new Error('Failed to fetch members');
      return response.json();
    },
    enabled: !!company?.id,
    staleTime: 60 * 1000, // 1 minute
  });

  const { data: transactionsData, isLoading: transactionsLoading } = useQuery({
    queryKey: ['transactions', company?.id, 'all-activity'],
    queryFn: async () => {
      if (!company?.id) return { data: [] };
      const response = await fetch(`/api/transactions?companyId=${company.id}&limit=100`);
      if (!response.ok) throw new Error('Failed to fetch transactions');
      return response.json();
    },
    enabled: !!company?.id,
    staleTime: 60 * 1000, // 1 minute
  });

  const { data: redemptionsData, isLoading: redemptionsLoading } = useQuery({
    queryKey: ['redemptions', company?.id, 'all-activity'],
    queryFn: async () => {
      if (!company?.id) return { data: [] };
      const response = await fetch(`/api/redemptions?companyId=${company.id}&limit=100`);
      if (!response.ok) throw new Error('Failed to fetch redemptions');
      return response.json();
    },
    enabled: !!company?.id,
    staleTime: 60 * 1000, // 1 minute
  });

  // Handle hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Show skeleton during client-side render
  if (!mounted) {
    return (
      <div className="container py-6">
        <PageHeader
          heading="Activity Feed"
          subheading="View all member activity, points transactions, and redemptions"
        />
        <ActivitySkeletonLoader />
      </div>
    );
  }

  // Check if data is loading
  const dataLoading = !company || membersLoading || transactionsLoading || redemptionsLoading;

  if (dataLoading) {
    return (
      <div className="container py-6">
        <PageHeader
          heading="Activity Feed"
          subheading="View all member activity, points transactions, and redemptions"
        />
        <ActivitySkeletonLoader />
      </div>
    );
  }

  const transactions: Transaction[] = transactionsData?.data || [];
  const redemptions: Redemption[] = redemptionsData?.data || [];
  const members: Member[] = membersData?.data || [];

  // Create a member lookup map for faster access
  const memberMap = new Map<string, Member>();
  members.forEach(m => memberMap.set(m.id, m));

  // Enhance transactions with member data
  const enhancedTransactions = transactions.map((tx: Transaction) => {
    const member = memberMap.get(tx.member_id);
    return {
      ...tx,
      member_name: member?.name || tx.member_name || 'Unknown Member',
      date: tx.transaction_date || tx.created_at || new Date().toISOString()
    };
  });

  // Enhance redemptions with member data
  const enhancedRedemptions = redemptions.map((r: Redemption) => {
    const member = memberMap.get(r.member_id);
    return {
      ...r,
      member_name: member?.name || r.member_name || 'Unknown Member',
      date: r.created_at || new Date().toISOString()
    };
  });

  // Combine and sort activity
  let activity: ActivityItem[] = [
    ...members.map((member: Member) => ({
      id: `member-${member.id}`,
      type: 'member' as const,
      title: 'New Member',
      description: `${member.name} joined the loyalty program`,
      timestamp: new Date(member.registration_date).toISOString(),
      icon: <Users className="h-4 w-4 text-blue-600 dark:text-blue-400" />,
      link: `/members/${member.id}`,
      badge: {
        text: member.loyalty_id || 'New Member',
        variant: 'secondary' as 'default' | 'secondary' | 'destructive' | 'outline'
      }
    })),
    ...enhancedTransactions.map((tx: Transaction & { date: string }) => ({
      id: `transaction-${tx.id}`,
      type: 'transaction' as const,
      title: 'Points Earned',
      description: `${tx.member_name} earned ${Math.abs(tx.points_change)} points from ${tx.description || 'a purchase'}`,
      timestamp: tx.date,
      icon: <Plus className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />,
      badge: {
        text: `${Math.abs(tx.points_change)} points`,
        variant: 'default' as 'default' | 'secondary' | 'destructive' | 'outline'
      }
    })),
    ...enhancedRedemptions.map((r: Redemption & { date: string }) => ({
      id: `redemption-${r.id}`,
      type: 'redemption' as const,
      title: 'Reward Redeemed',
      description: `${r.member_name} redeemed ${r.reward?.name || 'a reward'}`,
      timestamp: r.date,
      icon: <Gift className="h-4 w-4 text-purple-600 dark:text-purple-400" />,
      badge: {
        text: `${r.points_used} points`,
        variant: 'destructive' as 'default' | 'secondary' | 'destructive' | 'outline'
      }
    }))
  ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  // Apply filters
  if (filterType !== 'all') {
    activity = activity.filter(item => item.type === filterType);
  }

  // Apply search
  if (searchQuery) {
    const query = searchQuery.toLowerCase();
    activity = activity.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query)
    );
  }

  // Calculate pagination
  const totalPages = Math.ceil(activity.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedItems = activity.slice(startIndex, startIndex + itemsPerPage);

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // Always include first page
      pageNumbers.push(1);

      // Calculate start and end of the visible pages
      let start = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
      const end = Math.min(totalPages - 1, start + maxVisiblePages - 3);

      // Adjust start if end is too close to totalPages
      if (end === totalPages - 1) {
        start = Math.max(2, end - (maxVisiblePages - 3));
      }

      // Add ellipsis after first page if needed
      if (start > 2) {
        pageNumbers.push('...');
      }

      // Add visible pages
      for (let i = start; i <= end; i++) {
        pageNumbers.push(i);
      }

      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pageNumbers.push('...');
      }

      // Always include last page
      if (totalPages > 1) {
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers;
  };

  return (
    <div className="container py-6">
      <PageHeader
        heading="Activity Feed"
        subheading="View all member activity, points transactions, and redemptions"
        icon={<Activity className="h-6 w-6" />}
      />

      <div className="bg-card rounded-lg p-4 mb-6 shadow-sm">
        <div className="flex flex-col md:flex-row gap-4 items-end">
          <div className="flex-1">
            <label className="text-sm font-medium mb-1.5 block">Search</label>
            <div className="relative">
              <Input
                placeholder="Search activity..."
                className="pl-9"
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
              />
              <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  className="w-4 h-4"
                >
                  <path
                    fillRule="evenodd"
                    d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
          </div>

          <div className="w-full md:w-48">
            <label className="text-sm font-medium mb-1.5 block">Activity Type</label>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger>
                <SelectValue placeholder="All activity types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Activity</SelectItem>
                <SelectItem value="member">New Members</SelectItem>
                <SelectItem value="transaction">Points Transactions</SelectItem>
                <SelectItem value="redemption">Reward Redemptions</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="w-full md:w-auto">
            <Button
              variant="secondary"
              size="icon"
              className="h-10 w-10"
            >
              <Filter className="h-4 w-4" />
              <span className="sr-only">Filter</span>
            </Button>
          </div>
        </div>
      </div>

      {paginatedItems.length === 0 ? (
        <Card className="p-8 text-center">
          <CardContent className="pt-6 flex flex-col items-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <CardTitle className="text-xl mb-2">No Activity Found</CardTitle>
            <p className="text-muted-foreground max-w-md mx-auto">
              There is no activity matching your search criteria. Try adjusting your filters or check back later.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {paginatedItems.map((item) => (
            <Card key={item.id} className="overflow-hidden hover:bg-muted/50 transition-colors">
              <CardContent className="p-0">
                <div className="p-4 flex items-center gap-4">
                  <div className="rounded-full bg-background p-2">
                    {item.icon}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">
                      {item.title}
                      <span className="text-muted-foreground ml-2 text-sm">
                        {new Date(item.timestamp).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {item.description}
                    </div>
                  </div>

                  {item.badge && (
                    <Badge variant={item.badge.variant}>
                      {item.badge.text}
                    </Badge>
                  )}

                  {item.link && (
                    <Link href={item.link} className="ml-2">
                      <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">View details</span>
                        <ArrowUpRight className="h-4 w-4" />
                      </Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {totalPages > 1 && (
        <div className="mt-8">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    if (currentPage > 1) setCurrentPage(currentPage - 1);
                  }}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>

              {getPageNumbers().map((pageNum, idx) => (
                <PaginationItem key={idx}>
                  {typeof pageNum === 'number' ? (
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        setCurrentPage(pageNum);
                      }}
                      isActive={currentPage === pageNum}
                    >
                      {pageNum}
                    </PaginationLink>
                  ) : (
                    <span className="flex h-9 w-9 items-center justify-center text-sm">
                      {pageNum}
                    </span>
                  )}
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
                  }}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  )
}

// Skeleton loader component
function ActivitySkeletonLoader() {
  return (
    <div className="space-y-4">
      <div className="bg-card rounded-lg p-4 shadow-sm flex flex-col md:flex-row gap-4 items-end">
        <div className="flex-1">
          <Skeleton className="h-4 w-16 mb-2" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="w-full md:w-48">
          <Skeleton className="h-4 w-24 mb-2" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="w-full md:w-auto">
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      <div className="grid gap-4">
        {Array.from({ length: 5 }).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-0">
              <div className="p-4 flex items-center gap-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-32 mb-1" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-6 w-20" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}