'use client';

import { useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { format } from 'date-fns';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { CalendarIcon } from 'lucide-react';

// Import the custom styles
import './style.css';

// Define the form schema with validation rules
const formSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  phoneNumber: z.string().min(10, { message: 'Please enter a valid phone number' }),
  email: z.string().email({ message: 'Please enter a valid email address' }).optional().or(z.literal('')),
  birthday: z.date({ required_error: 'Please select a date' }),
  telegramId: z.string()
});

type FormValues = z.infer<typeof formSchema>;

// Client component that safely uses useSearchParams
function TelegramRegistrationForm() {
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Extract telegram_id from query parameters
  const telegramId = searchParams.get('telegram_id') || '';

  // Initialize the form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      phoneNumber: '',
      email: '',
      telegramId: telegramId,
    }
  });

  // Handle form submission
  async function onSubmit(data: FormValues) {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Send form data to your API endpoint
      const response = await fetch('/api/telegram/members/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: data.name,
          phone_number: data.phoneNumber,
          email: data.email || null,
          birthday: format(data.birthday, 'yyyy-MM-dd'),
          telegram_chat_id: data.telegramId
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to register. Please try again.');
      }

      // Show success message and reset form
      setSubmitSuccess(true);
      form.reset();

      // Redirect the user after successful submission
      setTimeout(() => {
        // Send them back to Telegram or a success page
        window.location.href = `https://t.me/your_bot_username?start=registration_complete`;
      }, 3000);

    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="flex min-h-screen bg-muted/20 items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-lg telegram-form-card">
        <CardHeader className="text-center telegram-form-header">
          <CardTitle className="text-2xl font-bold">Loyalty Program Registration</CardTitle>
          <CardDescription>Please complete your details to join our loyalty program</CardDescription>
        </CardHeader>

        <CardContent>
          {submitSuccess ? (
            <div className="bg-green-50 p-4 rounded-md text-green-700 text-center">
              <p className="font-medium">Registration successful!</p>
              <p className="text-sm mt-2">Redirecting you back to Telegram...</p>
            </div>
          ) : (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 telegram-form">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="required">Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your full name" {...field} className="telegram-form-input" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="required">Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="+251911234567" {...field} className="telegram-form-input" />
                      </FormControl>
                      <FormDescription className="text-xs">
                        Include country code (e.g. +251 for Ethiopia)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          className="telegram-form-input"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="birthday"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="required">Birthday</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal telegram-form-input",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date('1900-01-01')
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="telegramId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="required">Telegram ID</FormLabel>
                      <FormControl>
                        <Input {...field} readOnly className="bg-muted telegram-form-input" />
                      </FormControl>
                      <FormDescription className="text-xs">
                        This is your unique Telegram identifier (auto-filled)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {submitError && (
                  <div className="bg-red-50 p-3 rounded-md text-red-700 text-sm">
                    {submitError}
                  </div>
                )}

                <Button 
                  type="submit" 
                  className="w-full telegram-form-button" 
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Registering...' : 'Complete Registration'}
                </Button>
              </form>
            </Form>
          )}
        </CardContent>

        <CardFooter className="text-center telegram-form-footer">
          <p className="w-full">
            By registering, you agree to our Terms of Service and Privacy Policy.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
}

// Default export that wraps the form in Suspense
export default function TelegramRegistrationPage() {
  return (
    <Suspense fallback={<div className="container mx-auto p-6 flex items-center justify-center min-h-screen">
      <Card className="w-full max-w-md p-8 text-center">
        <CardTitle className="mb-4">Loading Registration Form</CardTitle>
        <CardDescription>Please wait while we prepare your registration form...</CardDescription>
      </Card>
    </div>}>
      <TelegramRegistrationForm />
    </Suspense>
  );
}
