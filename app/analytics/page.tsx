'use client'

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts'
import {
  TrendingUp,
  Users,
  ShoppingCart,
  Target,
  Award,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Eye,
  FileText,
  Receipt,
  ShoppingBag
} from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface ItemAnalytics {
  item_name: string
  total_sales_count: number
  total_revenue: number
  avg_selling_price: number
  category?: string
  item_category?: string
  unique_customers?: number
  last_sold_date: string
}

interface CustomerPreference {
  member_id: string
  loyalty_id: string
  customer_name: string
  favorite_items: Array<{
    item_name: string
    purchase_count: number
    total_spent: number
  }>
  total_visits: number
  total_spent: number
}

interface TemplateMetric {
  template_name: string
  accuracy_rate: number
  processing_time: number
  receipts_processed: number
  error_rate: number
  last_updated: string
}

// API Response Types
interface BusinessPerformanceAPI {
  item_name: string
  item_category: string
  total_sales: number
  total_revenue: number
  avg_selling_price: number
  unique_customers: number
  last_sold_date: string
}

interface CustomerInsightAPI {
  member_id: string
  loyalty_id: string
  customer_name: string
  total_visits: number
  total_spent: number
  favorite_items: Array<{
    item_name: string
    item_category: string
    purchase_count: number
    total_spent_on_item: number
  }>
}

interface TemplateAnalyticsAPI {
  template_name: string
  success_rate_percentage: number
  total_extractions: number
  last_updated: string
}

export default function AnalyticsPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()

  const [activeTab, setActiveTab] = useState('customer-insights')
  const [isClient, setIsClient] = useState(false)

  // Real data states
  const [itemAnalytics, setItemAnalytics] = useState<ItemAnalytics[]>([])
  const [customerPreferences, setCustomerPreferences] = useState<CustomerPreference[]>([])
  const [templateMetrics, setTemplateMetrics] = useState<TemplateMetric[]>([])
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false)

  // Analytics summary stats
  const [totalReceipts, setTotalReceipts] = useState(0)
  const [avgExtractionConfidence, setAvgExtractionConfidence] = useState(0)
  const [templateUsageStats, setTemplateUsageStats] = useState({
    with_template: 0,
    without_template: 0
  })

  // Ensure client-side only rendering
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Memoize companyId to prevent unnecessary re-renders
  const memoizedCompanyId = useMemo(() => company?.id, [company?.id])

  const fetchAnalyticsData = useCallback(async () => {
    if (!memoizedCompanyId || authLoading || companyLoading) {
      console.log('No company ID available or still loading, skipping analytics fetch')
      return
    }

    console.log('Fetching comprehensive analytics data for company:', memoizedCompanyId)

    setIsLoadingAnalytics(true)

    try {
      // Use the new comprehensive analytics API
      const response = await fetch(`/api/analytics/comprehensive?company_id=${memoizedCompanyId}`)

      if (!response.ok) {
        throw new Error(`Analytics API failed: ${response.status} ${response.statusText}`)
      }

      const analyticsData = await response.json()

      if (!analyticsData.success) {
        throw new Error(analyticsData.error || 'Failed to fetch analytics data')
      }

      const { data } = analyticsData

      // Update state with comprehensive data
      console.log('Analytics data received:', data)

      // Transform business performance data for existing component structure
      const transformedItems = (data.businessPerformance || []).map((item: BusinessPerformanceAPI) => ({
        item_name: item.item_name,
        total_sales_count: item.total_sales || 0,
        total_revenue: item.total_revenue || 0,
        avg_selling_price: item.avg_selling_price || 0,
        item_category: item.item_category || 'Other',
        category: item.item_category || 'Other',
        last_sold_date: item.last_sold_date,
        unique_customers: item.unique_customers || 0
      }))
      setItemAnalytics(transformedItems)

      // Transform customer insights data
      const transformedCustomers = (data.customerInsights || []).map((customer: CustomerInsightAPI) => ({
        member_id: customer.member_id,
        loyalty_id: customer.loyalty_id,
        customer_name: customer.customer_name || `Customer ${customer.loyalty_id}`,
        favorite_items: customer.favorite_items || [],
        total_visits: customer.total_visits || 0,
        total_spent: customer.total_spent || 0
      }))
      setCustomerPreferences(transformedCustomers)

      // Transform template data
      const transformedTemplates = (data.templateAnalytics || []).map((template: TemplateAnalyticsAPI) => ({
        template_name: template.template_name,
        accuracy_rate: template.success_rate_percentage || 0,
        processing_time: 1200, // Mock value - would need to track this
        receipts_processed: template.total_extractions || 0,
        error_rate: 100 - (template.success_rate_percentage || 0),
        last_updated: template.last_updated ? new Date(template.last_updated).toLocaleDateString() : 'Unknown'
      }))
      setTemplateMetrics(transformedTemplates)

      // Set summary statistics
      const summary = data.summary || {}
      setTotalReceipts(summary.total_receipts || 0)
      setAvgExtractionConfidence((summary.avg_extraction_confidence || 0) / 100) // Convert to decimal
      setTemplateUsageStats({
        with_template: Math.round((summary.template_usage_percentage || 0) * (summary.total_receipts || 0) / 100),
        without_template: (summary.total_receipts || 0) - Math.round((summary.template_usage_percentage || 0) * (summary.total_receipts || 0) / 100)
      })

      console.log('Analytics data successfully processed and set')

    } catch (error) {
      console.error('Error fetching comprehensive analytics:', error)

      // Fallback to empty states
      setItemAnalytics([])
      setCustomerPreferences([])
      setTemplateMetrics([])
      setTotalReceipts(0)
      setAvgExtractionConfidence(0)
      setTemplateUsageStats({ with_template: 0, without_template: 0 })
    } finally {
      setIsLoadingAnalytics(false)
    }
  }, [memoizedCompanyId, authLoading, companyLoading])

  useEffect(() => {
    if (memoizedCompanyId && isClient) {
      fetchAnalyticsData()
    }
  }, [memoizedCompanyId, fetchAnalyticsData, isClient])

  // Loading states
  if (authLoading || companyLoading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="space-y-4 w-full max-w-3xl">
          <Skeleton className="h-12 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
            <Skeleton className="h-32" />
          </div>
        </div>
      </div>
    )
  }

  if (!user || !company) {
    return null
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Business Analytics</h1>
            <p className="text-muted-foreground">
              Real-time insights into customer preferences, business performance, and template effectiveness
            </p>
            {isLoadingAnalytics && (
              <div className="mt-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                  Loading analytics data...
                </div>
              </div>
            )}
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Export Report
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 w-full max-w-[600px]">
            <TabsTrigger value="customer-insights">Customer Insights</TabsTrigger>
            <TabsTrigger value="business-performance">Business Performance</TabsTrigger>
            <TabsTrigger value="template-effectiveness">Template Analytics</TabsTrigger>
          </TabsList>

          {/* Customer Insights Tab */}
          <TabsContent value="customer-insights" className="space-y-6">

            {/* Summary Cards with Real Data */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <div className="text-2xl font-bold">{customerPreferences.length}</div>
                  </div>
                  <p className="text-xs text-muted-foreground">Active Customers</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <div className="text-2xl font-bold">
                      {customerPreferences.length > 0
                        ? (customerPreferences.reduce((sum, c) => sum + c.total_visits, 0) / customerPreferences.length).toFixed(1)
                        : '0'
                      }
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Avg Visit Frequency</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4 text-yellow-600" />
                    <div className="text-2xl font-bold">
                      ETB {customerPreferences.length > 0
                        ? (customerPreferences.reduce((sum, c) => sum + c.total_spent, 0) / customerPreferences.length).toFixed(0)
                        : '0'
                      }
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Avg Customer Value</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Receipt className="h-4 w-4 text-purple-600" />
                    <div className="text-2xl font-bold">{totalReceipts}</div>
                  </div>
                  <p className="text-xs text-muted-foreground">Total Receipts</p>
                </CardContent>
              </Card>
            </div>

            {/* Customer Preferences Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Top Customer Preferences</CardTitle>
                <CardDescription>Most popular items based on customer favorites and frequency</CardDescription>
              </CardHeader>
              <CardContent>
                {(() => {
                  // Create preference data from customer favorites
                  const preferenceData = customerPreferences.reduce((acc, customer) => {
                    customer.favorite_items.forEach(item => {
                      const existing = acc.find(p => p.item_name === item.item_name)
                      if (existing) {
                        existing.customer_count += 1
                        existing.total_spent += item.total_spent
                      } else {
                        acc.push({
                          item_name: item.item_name,
                          customer_count: 1,
                          total_spent: item.total_spent,
                          preference_score: 1
                        })
                      }
                    })
                    return acc
                  }, [] as Array<{item_name: string, customer_count: number, total_spent: number, preference_score: number}>)

                  // Calculate preference scores and sort
                  preferenceData.forEach(item => {
                    item.preference_score = (item.customer_count * 20) + (item.total_spent / 10)
                  })

                  const sortedPreferences = preferenceData
                    .sort((a, b) => b.preference_score - a.preference_score)
                    .slice(0, 8)

                  return sortedPreferences.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={sortedPreferences}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="item_name" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: number, name: string) => [
                            name === 'customer_count' ? `${value} customers` : value.toFixed(1),
                            name === 'customer_count' ? 'Customers who prefer this' : 'Preference Score'
                          ]}
                        />
                        <Bar dataKey="preference_score" fill="#3B82F6" name="preference_score" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                      No customer preference data available. Customers need to have favorite items to see preferences.
                    </div>
                  )
                })()}
              </CardContent>
            </Card>

            {/* Top Customers */}
            <Card>
              <CardHeader>
                <CardTitle>High-Value Customers</CardTitle>
                <CardDescription>Customers with highest spending and frequency</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {customerPreferences.slice(0, 5).map((customer, index) => (
                    <div key={customer.member_id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="font-bold text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium">{customer.customer_name}</p>
                          <p className="text-sm text-muted-foreground">
                            {customer.total_visits} visits • {customer.favorite_items.length} favorite items
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-green-600">ETB {customer.total_spent.toFixed(2)}</p>
                        <p className="text-sm text-muted-foreground">
                          Top item: {customer.favorite_items[0]?.item_name || 'N/A'}
                        </p>
                      </div>
                    </div>
                  ))}
                  {customerPreferences.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No customer data available yet. Upload some receipts to see customer insights!
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Business Performance Tab */}
          <TabsContent value="business-performance" className="space-y-6">

            {/* Performance Summary */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <ShoppingCart className="h-4 w-4 text-blue-600" />
                    <div className="text-2xl font-bold">
                      ETB {itemAnalytics.reduce((sum, item) => sum + item.total_revenue, 0).toFixed(2)}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Total Revenue</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Target className="h-4 w-4 text-green-600" />
                    <div className="text-2xl font-bold">
                      {itemAnalytics.reduce((sum, item) => sum + item.total_sales_count, 0)}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Items Sold</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <ShoppingBag className="h-4 w-4 text-yellow-600" />
                    <div className="text-2xl font-bold">{itemAnalytics.length}</div>
                  </div>
                  <p className="text-xs text-muted-foreground">Business Items</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4 text-purple-600" />
                    <div className="text-2xl font-bold">{(avgExtractionConfidence * 100).toFixed(1)}%</div>
                  </div>
                  <p className="text-xs text-muted-foreground">OCR Accuracy</p>
                </CardContent>
              </Card>
            </div>

            {/* Top 10 Revenue by Item */}
            <Card>
              <CardHeader>
                <CardTitle>Top 10 Revenue by Item</CardTitle>
                <CardDescription>Highest revenue-generating items in your business</CardDescription>
              </CardHeader>
              <CardContent>
                {itemAnalytics.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart
                      data={itemAnalytics
                        .sort((a, b) => b.total_revenue - a.total_revenue)
                        .slice(0, 10)
                      }
                      layout="horizontal"
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="item_name" type="category" width={120} />
                      <Tooltip formatter={(value: number) => [`ETB ${value.toFixed(2)}`, 'Revenue']} />
                      <Bar dataKey="total_revenue" fill="#3B82F6" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                    No revenue data available
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Item Performance Table */}
            <Card>
              <CardHeader>
                <CardTitle>Item Performance</CardTitle>
                <CardDescription>Detailed performance metrics for each item</CardDescription>
              </CardHeader>
              <CardContent>
                {itemAnalytics.length > 0 ? (
                  <div className="space-y-4">
                    {itemAnalytics.map((item, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <h3 className="font-semibold">{item.item_name}</h3>
                            <Badge variant="secondary">{item.category}</Badge>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-green-600">ETB {item.total_revenue.toFixed(2)}</p>
                            <p className="text-sm text-muted-foreground">{item.total_sales_count} sold</p>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Avg Price:</span>
                            <span className="ml-2 font-medium">ETB {item.avg_selling_price?.toFixed(2) || '0.00'}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Sales Count:</span>
                            <span className="ml-2 font-medium">{item.total_sales_count}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Last Sold:</span>
                            <span className="ml-2 font-medium">
                              {item.last_sold_date ? new Date(item.last_sold_date).toLocaleDateString() : 'Never'}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No business items data available. Add some business items to see performance metrics!
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Template Effectiveness Tab */}
          <TabsContent value="template-effectiveness" className="space-y-6">

            {/* Template Summary */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-blue-600" />
                    <div className="text-2xl font-bold">{templateMetrics.length}</div>
                  </div>
                  <p className="text-xs text-muted-foreground">Active Templates</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <div className="text-2xl font-bold">
                      {templateMetrics.length > 0
                        ? (templateMetrics.reduce((sum, t) => sum + t.accuracy_rate, 0) / templateMetrics.length).toFixed(1)
                        : '0'
                      }%
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Avg Accuracy Rate</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Eye className="h-4 w-4 text-yellow-600" />
                    <div className="text-2xl font-bold">
                      {templateUsageStats.with_template + templateUsageStats.without_template > 0
                        ? ((templateUsageStats.with_template / (templateUsageStats.with_template + templateUsageStats.without_template)) * 100).toFixed(0)
                        : 0
                      }%
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Template Usage Rate</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Receipt className="h-4 w-4 text-purple-600" />
                    <div className="text-2xl font-bold">
                      {templateMetrics.reduce((sum, t) => sum + t.receipts_processed, 0)}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">Total Receipts Processed</p>
                </CardContent>
              </Card>
            </div>

            {/* Template Performance Chart */}
            {templateMetrics.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Template Accuracy Comparison</CardTitle>
                  <CardDescription>Accuracy rates across different templates</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={templateMetrics}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="template_name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="accuracy_rate" fill="#10B981" name="Accuracy %" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            )}

            {/* Template Details */}
            <Card>
              <CardHeader>
                <CardTitle>Template Performance Details</CardTitle>
                <CardDescription>Detailed metrics for each template</CardDescription>
              </CardHeader>
              <CardContent>
                {templateMetrics.length > 0 ? (
                  <div className="space-y-4">
                    {templateMetrics.map((template, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <h3 className="font-semibold">{template.template_name}</h3>
                            {template.accuracy_rate > 85 ? (
                              <Badge className="bg-green-100 text-green-800">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                High Performance
                              </Badge>
                            ) : template.accuracy_rate > 70 ? (
                              <Badge className="bg-yellow-100 text-yellow-800">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Medium Performance
                              </Badge>
                            ) : (
                              <Badge className="bg-red-100 text-red-800">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                Needs Improvement
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Updated: {template.last_updated}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          <div>
                            <p className="text-sm font-medium">Accuracy Rate</p>
                            <p className="text-2xl font-bold text-green-600">{template.accuracy_rate.toFixed(1)}%</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Receipts Processed</p>
                            <p className="text-2xl font-bold">{template.receipts_processed.toLocaleString()}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Error Rate</p>
                            <p className="text-2xl font-bold text-red-600">{template.error_rate.toFixed(1)}%</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Processing Time</p>
                            <p className="text-2xl font-bold">{template.processing_time}ms</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No template data available. Create some receipt templates to see analytics!
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
