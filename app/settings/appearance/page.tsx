'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  CheckCircle,
  Palette,
  FileImage,
  Layout,
  Sun,
  Moon,
  Monitor
} from 'lucide-react'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Input } from '@/components/ui/input'
import ThemeColorSelector from './components/theme-color-selector'
import LogoUploader from './components/logo-uploader'
import RoundedCornerDemo from './components/rounded-corner-demo'
import { getSupabaseClient } from '@/lib/supabase'

export default function AppearancePage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading, refreshCompany } = useCompany()
  const [activeTab, setActiveTab] = useState('theme')
  const router = useRouter()
  const [isSaving, setIsSaving] = useState(false)

  // Theme settings
  const [themeMode, setThemeMode] = useState<'light' | 'dark' | 'system'>('system')
  const [primaryColor, setPrimaryColor] = useState('#0070f3')
  const [secondaryColor, setSecondaryColor] = useState('#f5a623')
  const [accentColor, setAccentColor] = useState('#10b981')
  const [cornerRadius, setCornerRadius] = useState(8)
  const [fontFamily, setFontFamily] = useState('system-ui')
  const [enableAnimations, setEnableAnimations] = useState(true)
  const [customCssEnabled, setCustomCssEnabled] = useState(false)
  const [customCss, setCustomCss] = useState('')

  // Branding settings
  const [logoUrl, setLogoUrl] = useState('')
  const [favicon, setFavicon] = useState('')
  const [memberPortalCustomization, setMemberPortalCustomization] = useState(true)
  const [businessName, setBusinessName] = useState('')
  const [brandDescription, setBrandDescription] = useState('')

  // Layout settings
  const [navbarStyle, setNavbarStyle] = useState('default')
  const [sidebarCompact, setSidebarCompact] = useState(false)
  const [dashboardLayout, setDashboardLayout] = useState('default')

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login')
    }
  }, [authLoading, user, router])

  // Load company appearance settings
  useEffect(() => {
    if (company) {
      // Load theme settings
      setThemeMode((company.appearance?.theme as 'light' | 'dark' | 'system') || 'system')
      setPrimaryColor(company.appearance?.primary_color || '#0070f3')
      setSecondaryColor(company.appearance?.secondary_color || '#f5a623')
      setAccentColor(company.appearance?.accent_color || '#10b981')
      setCornerRadius(company.appearance?.border_radius ? parseInt(company.appearance.border_radius) : 8)
      setFontFamily(company.appearance?.font_family || 'system-ui')
      // These fields aren't in our interface yet, use safe defaults
      setEnableAnimations(true)
      setCustomCssEnabled(false)
      setCustomCss('')

      // Load branding settings
      setLogoUrl(company.logo_url || '')
      setFavicon('') // Not in our interface yet
      setBusinessName(company.name || '')
      setBrandDescription(company.description || '')
      setMemberPortalCustomization(true) // Not in our interface yet

      // Load layout settings
      setNavbarStyle(company.appearance?.card_style || 'default')
      setSidebarCompact(false) // Not in our interface yet
      setDashboardLayout(company.appearance?.button_style || 'default')
    }
  }, [company])

  // Loading state
  const isLoading = authLoading || companyLoading

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col gap-4">
          <div className="animate-shimmer w-48 h-8 rounded-lg"></div>
          <div className="animate-shimmer w-full h-64 rounded-lg"></div>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect in the effect
  }

  const handleSaveAppearance = async () => {
    setIsSaving(true)

    try {
      const appearanceSettings = {
        themeMode,
        primaryColor,
        secondaryColor,
        accentColor,
        cornerRadius,
        fontFamily,
        enableAnimations,
        customCss: customCssEnabled ? customCss : '',
        favicon,
        memberPortalCustomization,
        navbarStyle,
        sidebarCompact,
        dashboardLayout
      }

      // Update company appearance settings in Supabase
      const supabase = getSupabaseClient()
      const { error } = await supabase
        .from('companies')
        .update({
          appearance: appearanceSettings,
          name: businessName,
          description: brandDescription
        })
        .eq('id', company?.id)

      if (error) {
        throw error
      }

      // Refresh company data in context
      await refreshCompany()

      toast.success('Appearance settings saved successfully')
    } catch (error) {
      console.error('Error saving appearance settings:', error)
      toast.error('Failed to save appearance settings')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <div className="flex flex-col gap-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Appearance Settings</h1>
            <p className="text-muted-foreground">Customize the look and feel of your loyalty program</p>
          </div>
          <Button
            onClick={handleSaveAppearance}
            disabled={isSaving}
            className="gap-2"
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>

        <Tabs defaultValue="theme" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 md:w-[400px] mb-4">
            <TabsTrigger value="theme" className="gap-2">
              <Palette className="h-4 w-4" /> Theme
            </TabsTrigger>
            <TabsTrigger value="branding" className="gap-2">
              <FileImage className="h-4 w-4" /> Branding
            </TabsTrigger>
            <TabsTrigger value="layout" className="gap-2">
              <Layout className="h-4 w-4" /> Layout
            </TabsTrigger>
          </TabsList>

          <TabsContent value="theme">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Theme Settings</CardTitle>
                    <CardDescription>
                      Customize the colors, fonts, and other visual elements
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium mb-2">Theme Mode</h3>
                        <RadioGroup
                          value={themeMode}
                          onValueChange={(value) => setThemeMode(value as 'light' | 'dark' | 'system')}
                          className="flex flex-wrap gap-4"
                        >
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="light" id="light" />
                            <Label htmlFor="light" className="flex items-center gap-2">
                              <Sun className="h-4 w-4" /> Light
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="dark" id="dark" />
                            <Label htmlFor="dark" className="flex items-center gap-2">
                              <Moon className="h-4 w-4" /> Dark
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="system" id="system" />
                            <Label htmlFor="system" className="flex items-center gap-2">
                              <Monitor className="h-4 w-4" /> System
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="font-medium mb-4">Brand Colors</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <ThemeColorSelector
                            label="Primary Color"
                            value={primaryColor}
                            onChange={setPrimaryColor}
                            description="Used for primary buttons and key UI elements"
                          />
                          <ThemeColorSelector
                            label="Secondary Color"
                            value={secondaryColor}
                            onChange={setSecondaryColor}
                            description="Used for secondary buttons and accents"
                          />
                          <ThemeColorSelector
                            label="Accent Color"
                            value={accentColor}
                            onChange={setAccentColor}
                            description="Used for highlights and special elements"
                          />
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <Label htmlFor="corner-radius">Corner Radius: {cornerRadius}px</Label>
                          </div>
                          <div className="flex gap-4 items-center">
                            <Slider
                              id="corner-radius"
                              min={0}
                              max={20}
                              step={1}
                              value={[cornerRadius]}
                              onValueChange={(value) => setCornerRadius(value[0])}
                              className="flex-1"
                            />
                            <RoundedCornerDemo radius={cornerRadius} />
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="font-family" className="mb-2 block">Font Family</Label>
                          <Select value={fontFamily} onValueChange={setFontFamily}>
                            <SelectTrigger id="font-family">
                              <SelectValue placeholder="Select a font" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="system-ui">System Default</SelectItem>
                              <SelectItem value="'Inter', sans-serif">Inter</SelectItem>
                              <SelectItem value="'Roboto', sans-serif">Roboto</SelectItem>
                              <SelectItem value="'Open Sans', sans-serif">Open Sans</SelectItem>
                              <SelectItem value="'Poppins', sans-serif">Poppins</SelectItem>
                              <SelectItem value="'Montserrat', sans-serif">Montserrat</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">Enable Animations</h3>
                            <p className="text-sm text-muted-foreground">Page transitions and UI effects</p>
                          </div>
                          <Switch
                            checked={enableAnimations}
                            onCheckedChange={setEnableAnimations}
                          />
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">Custom CSS</h3>
                            <p className="text-sm text-muted-foreground">Add your own CSS styles</p>
                          </div>
                          <Switch
                            checked={customCssEnabled}
                            onCheckedChange={setCustomCssEnabled}
                          />
                        </div>

                        {customCssEnabled && (
                          <div>
                            <textarea
                              className="w-full min-h-[200px] p-2 font-mono text-sm border rounded-md"
                              value={customCss}
                              onChange={(e) => setCustomCss(e.target.value)}
                              placeholder=".your-class { /* custom styles */ }"
                            />
                            <p className="text-xs text-muted-foreground mt-2">
                              Custom CSS will be applied to your loyalty portal and member-facing pages
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Theme Preview</CardTitle>
                    <CardDescription>
                      See how your changes look
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div
                      className="border rounded-md p-4 aspect-video"
                      style={{
                        fontFamily: fontFamily,
                        '--primary-color': primaryColor,
                        '--secondary-color': secondaryColor,
                        '--accent-color': accentColor,
                        '--corner-radius': `${cornerRadius}px`,
                      } as React.CSSProperties}
                    >
                      <div className="preview-window border rounded-md overflow-hidden"
                        style={{ borderRadius: `${cornerRadius}px` }}>
                        <div className="preview-header flex items-center justify-between p-2"
                          style={{ backgroundColor: primaryColor, color: 'white' }}>
                          <div className="flex items-center gap-2">
                            <div className="h-3 w-3 rounded-full bg-white"></div>
                            <div className="text-xs">Your Loyalty Program</div>
                          </div>
                        </div>
                        <div className="preview-content bg-white dark:bg-gray-800 p-2 flex flex-col gap-2">
                          <div className="w-full h-6 rounded animate-pulse"
                            style={{ backgroundColor: `${primaryColor}20` }}></div>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="h-10 rounded flex items-center justify-center text-xs text-white"
                              style={{ backgroundColor: primaryColor }}>
                              Primary
                            </div>
                            <div className="h-10 rounded flex items-center justify-center text-xs text-white"
                              style={{ backgroundColor: secondaryColor }}>
                              Secondary
                            </div>
                          </div>
                          <div className="text-center text-xs p-1 rounded"
                            style={{ backgroundColor: `${accentColor}40`, color: accentColor }}>
                            Accent Element
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 text-xs text-center text-muted-foreground">
                      This is a simplified preview. Actual appearance may vary.
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="branding">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Branding</CardTitle>
                    <CardDescription>
                      Upload your logo and customize your brand appearance
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium mb-4">Company Details</h3>
                        <div className="grid grid-cols-1 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="business-name">Business Name</Label>
                            <Input
                              id="business-name"
                              value={businessName}
                              onChange={(e) => setBusinessName(e.target.value)}
                              placeholder="Your Business Name"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="brand-description">Brand Description</Label>
                            <textarea
                              id="brand-description"
                              className="w-full p-2 min-h-[100px] border rounded-md"
                              value={brandDescription}
                              onChange={(e) => setBrandDescription(e.target.value)}
                              placeholder="Brief description of your business"
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="font-medium mb-4">Logo</h3>
                        <LogoUploader
                          currentLogoUrl={logoUrl}
                          onLogoChange={setLogoUrl}
                          companyId={company?.id}
                        />
                      </div>

                      <Separator />

                      <div>
                        <h3 className="font-medium mb-4">Favicon</h3>
                        <div>
                          <div className="font-medium">{businessName || "Your Business Name"}</div>
                          <div className="text-xs text-muted-foreground">Loyalty Program</div>
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        {brandDescription || "Your brand description will appear here. Add a short description that represents your business."}
                      </div>

                      <div className="grid grid-cols-2 gap-2">
                        <div className="border rounded p-2 text-center text-xs flex flex-col items-center justify-center">
                          <div className="font-semibold mb-1">Members Portal</div>
                          <div className="text-muted-foreground">
                            {memberPortalCustomization ? "✓ Customized" : "Default Style"}
                          </div>
                        </div>
                        <div className="border rounded p-2 text-center text-xs flex flex-col items-center justify-center">
                          <div className="font-semibold mb-1">Favicon</div>
                          <div className="text-muted-foreground">
                            {favicon ? "✓ Set" : "Not Set"}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="layout">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Layout Settings</CardTitle>
                    <CardDescription>
                      Configure how the dashboard and pages are structured
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="font-medium mb-4">Navigation Style</h3>
                        <RadioGroup
                          value={navbarStyle}
                          onValueChange={setNavbarStyle}
                          className="grid grid-cols-1 md:grid-cols-3 gap-4"
                        >
                          <div className="border rounded-md p-4 flex items-center gap-2 cursor-pointer data-[state=checked]:border-primary">
                            <RadioGroupItem value="default" id="navbar-default" />
                            <Label htmlFor="navbar-default" className="cursor-pointer">
                              <div className="flex flex-col gap-2">
                                <span>Default</span>
                                <div className="h-10 w-full bg-muted rounded flex items-center px-2 justify-between">
                                  <div className="bg-primary/20 h-4 w-12 rounded"></div>
                                  <div className="flex gap-1">
                                    <div className="h-4 w-4 rounded-full bg-muted-foreground/30"></div>
                                    <div className="h-4 w-4 rounded-full bg-muted-foreground/30"></div>
                                  </div>
                                </div>
                              </div>
                            </Label>
                          </div>
                          <div className="border rounded-md p-4 flex items-center gap-2 cursor-pointer data-[state=checked]:border-primary">
                            <RadioGroupItem value="centered" id="navbar-centered" />
                            <Label htmlFor="navbar-centered" className="cursor-pointer">
                              <div className="flex flex-col gap-2">
                                <span>Centered</span>
                                <div className="h-10 w-full bg-muted rounded flex items-center justify-center px-2">
                                  <div className="bg-primary/20 h-4 w-12 rounded"></div>
                                </div>
                              </div>
                            </Label>
                          </div>
                          <div className="border rounded-md p-4 flex items-center gap-2 cursor-pointer data-[state=checked]:border-primary">
                            <RadioGroupItem value="minimal" id="navbar-minimal" />
                            <Label htmlFor="navbar-minimal" className="cursor-pointer">
                              <div className="flex flex-col gap-2">
                                <span>Minimal</span>
                                <div className="h-10 w-full bg-muted rounded flex items-center justify-end px-2">
                                  <div className="flex gap-1">
                                    <div className="h-4 w-4 rounded-full bg-muted-foreground/30"></div>
                                    <div className="h-4 w-4 rounded-full bg-muted-foreground/30"></div>
                                  </div>
                                </div>
                              </div>
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>

                      <Separator />

                      <div>
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">Compact Sidebar</h3>
                            <p className="text-sm text-muted-foreground">
                              Use smaller icons and less text in the sidebar
                            </p>
                          </div>
                          <Switch
                            checked={sidebarCompact}
                            onCheckedChange={setSidebarCompact}
                          />
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="font-medium mb-4">Dashboard Layout</h3>
                        <RadioGroup
                          value={dashboardLayout}
                          onValueChange={setDashboardLayout}
                          className="grid grid-cols-1 md:grid-cols-2 gap-4"
                        >
                          <div className="border rounded-md p-4 flex items-center gap-2 cursor-pointer data-[state=checked]:border-primary">
                            <RadioGroupItem value="default" id="layout-default" />
                            <Label htmlFor="layout-default" className="cursor-pointer">
                              <div className="flex flex-col gap-2">
                                <span>Default</span>
                                <div className="aspect-video bg-muted rounded p-2">
                                  <div className="grid grid-cols-2 gap-2 h-full">
                                    <div className="bg-muted-foreground/20 rounded"></div>
                                    <div className="grid grid-rows-2 gap-2">
                                      <div className="bg-muted-foreground/20 rounded"></div>
                                      <div className="bg-muted-foreground/20 rounded"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </Label>
                          </div>

                          <div className="border rounded-md p-4 flex items-center gap-2 cursor-pointer data-[state=checked]:border-primary">
                            <RadioGroupItem value="cards" id="layout-cards" />
                            <Label htmlFor="layout-cards" className="cursor-pointer">
                              <div className="flex flex-col gap-2">
                                <span>Cards</span>
                                <div className="aspect-video bg-muted rounded p-2">
                                  <div className="grid grid-cols-3 gap-2 h-full">
                                    <div className="bg-muted-foreground/20 rounded"></div>
                                    <div className="bg-muted-foreground/20 rounded"></div>
                                    <div className="bg-muted-foreground/20 rounded"></div>
                                    <div className="bg-muted-foreground/20 rounded"></div>
                                    <div className="bg-muted-foreground/20 rounded"></div>
                                    <div className="bg-muted-foreground/20 rounded"></div>
                                  </div>
                                </div>
                              </div>
                            </Label>
                          </div>
                        </RadioGroup>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Layout Preview</CardTitle>
                    <CardDescription>
                      Visualization of your layout settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="border rounded-md overflow-hidden">
                      {/* Navbar preview */}
                      <div className={`h-8 border-b flex items-center px-3 ${
                        navbarStyle === 'centered' ? 'justify-center' :
                        navbarStyle === 'minimal' ? 'justify-end' : 'justify-between'
                      }`}>
                        {(navbarStyle === 'default' || navbarStyle === 'centered') && (
                          <div className="h-3 w-10 bg-primary/30 rounded"></div>
                        )}
                        {(navbarStyle === 'default' || navbarStyle === 'minimal') && (
                          <div className="flex gap-1">
                            <div className="h-3 w-3 rounded-full bg-muted-foreground/30"></div>
                            <div className="h-3 w-3 rounded-full bg-muted-foreground/30"></div>
                          </div>
                        )}
                      </div>

                      {/* Main layout preview */}
                      <div className="flex h-32">
                        {/* Sidebar */}
                        <div className={`border-r bg-muted/30 ${sidebarCompact ? 'w-10' : 'w-24'} flex flex-col items-center pt-2 gap-2`}>
                          <div className="w-5 h-5 rounded-full bg-primary/30"></div>
                          <div className="w-5 h-5 rounded-full bg-muted-foreground/30"></div>
                          <div className="w-5 h-5 rounded-full bg-muted-foreground/30"></div>
                          {!sidebarCompact && (
                            <>
                              <div className="w-16 h-2 bg-muted-foreground/20 rounded mt-1"></div>
                              <div className="w-16 h-2 bg-muted-foreground/20 rounded"></div>
                              <div className="w-16 h-2 bg-muted-foreground/20 rounded"></div>
                            </>
                          )}
                        </div>

                        {/* Dashboard content */}
                        <div className="flex-1 p-2">
                          {dashboardLayout === 'default' ? (
                            <div className="grid grid-cols-3 gap-2 h-full">
                              <div className="col-span-2 bg-muted rounded"></div>
                              <div className="grid grid-rows-2 gap-2">
                                <div className="bg-muted rounded"></div>
                                <div className="bg-muted rounded"></div>
                              </div>
                            </div>
                          ) : (
                            <div className="grid grid-cols-2 grid-rows-2 gap-2 h-full">
                              <div className="bg-muted rounded"></div>
                              <div className="bg-muted rounded"></div>
                              <div className="bg-muted rounded"></div>
                              <div className="bg-muted rounded"></div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 text-xs text-center text-muted-foreground">
                      This is a simplified preview of your layout settings
                    </div>
                  </CardContent>
                </Card>

                <Card className="mt-6">
                  <CardHeader className="pb-2">
                    <CardTitle>Applied Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-2">
                    <ul className="space-y-2 text-sm">
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        {navbarStyle.charAt(0).toUpperCase() + navbarStyle.slice(1)} navigation style
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        {sidebarCompact ? 'Compact' : 'Standard'} sidebar
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        {dashboardLayout.charAt(0).toUpperCase() + dashboardLayout.slice(1)} dashboard layout
                      </li>
                      <li className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        {memberPortalCustomization ? 'Customized' : 'Standard'} member portal
                      </li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}