'use client'

import { useState } from 'react'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'

interface ThemeColorSelectorProps {
  label: string
  value: string
  onChange: (value: string) => void
  description?: string
}

export default function ThemeColorSelector({
  label,
  value,
  onChange,
  description
}: ThemeColorSelectorProps) {
  const [localValue, setLocalValue] = useState(value)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setLocalValue(newValue)
  }

  const handleBlur = () => {
    // Validate color hex format
    const isValidHex = /^#([0-9A-F]{3}){1,2}$/i.test(localValue)
    if (isValidHex) {
      onChange(localValue)
    } else {
      // Reset to previous valid value
      setLocalValue(value)
    }
  }

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value
    setLocalValue(newColor)
    onChange(newColor)
  }

  const predefinedColors = [
    '#0070f3', // Blue
    '#10b981', // Green
    '#f97316', // Orange
    '#f5a623', // Gold
    '#8b5cf6', // Purple
    '#ef4444', // Red
    '#ec4899', // Pink
    '#06b6d4', // Cyan
    '#14b8a6', // Teal
    '#84cc16', // Lime
  ]

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <Label htmlFor={`color-${label}`}>{label}</Label>
        <Popover>
          <PopoverTrigger asChild>
            <button
              id={`color-${label}`}
              type="button"
              className={cn(
                "w-8 h-8 rounded-md border border-input flex items-center justify-center",
                "hover:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
              )}
              style={{ backgroundColor: localValue }}
              aria-label={`Select ${label}`}
            >
              <span className="sr-only">Select color</span>
            </button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-3">
            <div className="space-y-3">
              <div className="flex flex-wrap gap-2">
                {predefinedColors.map((color) => (
                  <button
                    key={color}
                    type="button"
                    className={cn(
                      "w-6 h-6 rounded-md border",
                      localValue === color ? "border-2 border-primary" : "border-input"
                    )}
                    style={{ backgroundColor: color }}
                    onClick={() => {
                      setLocalValue(color)
                      onChange(color)
                    }}
                    aria-label={`Select color ${color}`}
                  />
                ))}
              </div>

              <div className="flex gap-2 items-center">
                <div className="flex-1 space-y-1">
                  <Label htmlFor={`${label}-hex-input`}>Hex</Label>
                  <Input
                    id={`${label}-hex-input`}
                    value={localValue}
                    onChange={handleInputChange}
                    onBlur={handleBlur}
                    className="h-8 font-mono"
                  />
                </div>
                <div>
                  <input
                    type="color"
                    value={localValue}
                    onChange={handleColorChange}
                    className="w-8 h-8 rounded-md border border-input cursor-pointer"
                    aria-label="Select color"
                  />
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}

      <div className="h-8 w-full rounded-md flex items-center justify-center text-xs text-white" style={{ backgroundColor: localValue }}>
        {localValue.toUpperCase()}
      </div>
    </div>
  )
}