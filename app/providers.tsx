'use client'

import { useState, useEffect } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { ThemeProvider } from '@/components/theme-provider'
import { CompanyProvider } from '@/contexts/company-context'
import { initApiMonitoring } from '@/lib/monitoring/api-usage'
import { CACHE_TIMES, COMMON_QUERY_OPTIONS } from '@/lib/query-config'
import { Toaster } from 'sonner'

// Create a query client factory for consistent configuration
function createQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: CACHE_TIMES.NORMAL,
        gcTime: CACHE_TIMES.NORMAL * 2,
        ...COMMON_QUERY_OPTIONS,
        refetchOnMount: false, // Changed from 'always' to prevent unnecessary refetches when navigating back
      },
      mutations: {
        retry: 1,
        networkMode: 'online',
      },
    },
  })
}

export function Providers({ children }: { children: React.ReactNode }) {
  // Create query client with improved configuration
  const [queryClient] = useState(createQueryClient)

  // Initialize API monitoring safely on the client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        initApiMonitoring({
          enablePerformanceTracking: true,
          slowRequestThreshold: 500,
        });
      } catch (error) {
        console.error('Failed to initialize API monitoring:', error);
      }
    }
  }, [])

  // Reset query client in development to avoid stale data issues
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const originalConsoleError = console.error

      // Suppress specific React Query error messages during development
      console.error = (...args: unknown[]) => {
        if (
          typeof args[0] === 'string' &&
          args[0].includes('QueryClient') &&
          args[0].includes('already dehydrated')
        ) {
          return
        }
        originalConsoleError(...args)
      }

      return () => {
        console.error = originalConsoleError
      }
    }
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
      >
        <CompanyProvider>
          {children}
          <Toaster position="top-right" richColors closeButton />
        </CompanyProvider>
      </ThemeProvider>
      {/* Only show React Query Devtools in development */}
      {process.env.NODE_ENV === 'development' && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  )
}
