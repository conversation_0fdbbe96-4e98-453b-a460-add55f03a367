'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSimpleAuth } from '@/hooks/use-simple-auth'
import { useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { motion } from 'framer-motion'
import { useTheme } from 'next-themes'
import { MoonIcon, SunIcon, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import { LoadingSpinner, LoadingOverlay } from '@/components/ui/loading-spinner'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

// Component that uses useSearchParams
function LoginForm() {
  const { user, isLoading } = useSimpleAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirectTo') || '/dashboard'
  const message = searchParams.get('message')
  const verified = searchParams.get('verified')
  const { theme, setTheme } = useTheme()

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [mounted, setMounted] = useState(false)
  const [forceShowForm, setForceShowForm] = useState(false) // Force show form after timeout

  // Handle hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Force show login form after 3 seconds if still loading
  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        console.warn('⚠️ Auth loading timeout - forcing login form display')
        setForceShowForm(true)
      }, 3000)

      return () => clearTimeout(timeout)
    }
  }, [isLoading])

  // Redirect if already authenticated - simplified logic
  useEffect(() => {
    // Only redirect if we have a confirmed user and we're not from logout
    if (mounted && !isLoading && user && !searchParams.get('fromLogout')) {
      console.log('User already authenticated, redirecting to:', redirectTo)
      router.replace(redirectTo) // Use replace instead of push to avoid back button issues
    }
  }, [mounted, isLoading, user, redirectTo, searchParams, router])

  // Show loading while auth is still determining state (unless forced to show form)
  if (!mounted || (isLoading && !forceShowForm)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <div>Loading authentication...</div>
          <div className="text-sm text-gray-500 mt-2">
            Mounted: {mounted ? 'Yes' : 'No'} | Loading: {isLoading ? 'Yes' : 'No'} | User: {user ? 'Yes' : 'No'}
          </div>
          {isLoading && (
            <div className="text-xs text-gray-400 mt-2">
              If this takes too long, the form will appear automatically
            </div>
          )}
        </div>
      </div>
    )
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        throw error
      }

      // After successful login, redirect to dashboard or intended page
      console.log('Login successful, redirecting to:', redirectTo)
      router.replace(redirectTo) // Use replace instead of push
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to sign in'
      setError(errorMessage)
      setLoading(false)
    }
  }

  // Show loading while mounting or auth is loading
  if (!mounted) {
    return <LoadingOverlay />
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
        <span className="ml-2">Checking authentication...</span>
      </div>
    )
  }

  // Don't show login form if already authenticated and not from logout
  if (user && !searchParams.get('fromLogout')) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
        <span className="ml-2">Redirecting...</span>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300 relative overflow-hidden">
      {/* Abstract SVG art background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Top-left geometric shapes */}
        <svg className="absolute top-0 left-0 w-64 h-64 text-purple-200 dark:text-purple-900/20 opacity-40 transform -translate-x-1/4 -translate-y-1/4" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 20 L80 20 L80 80 L20 80 Z" stroke="currentColor" strokeWidth="2" />
          <path d="M35 35 L65 65" stroke="currentColor" strokeWidth="2" />
          <path d="M35 65 L65 35" stroke="currentColor" strokeWidth="2" />
          <path d="M50 20 L50 35" stroke="currentColor" strokeWidth="2" />
          <path d="M50 65 L50 80" stroke="currentColor" strokeWidth="2" />
          <path d="M20 50 L35 50" stroke="currentColor" strokeWidth="2" />
          <path d="M65 50 L80 50" stroke="currentColor" strokeWidth="2" />
        </svg>

        {/* Bottom-right geometric shapes */}
        <svg className="absolute bottom-0 right-0 w-80 h-80 text-amber-200 dark:text-amber-900/20 opacity-40 transform translate-x-1/4 translate-y-1/4" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="20" y="20" width="60" height="60" rx="4" stroke="currentColor" strokeWidth="2" />
          <path d="M20 50 L80 50" stroke="currentColor" strokeWidth="2" />
          <path d="M50 20 L50 80" stroke="currentColor" strokeWidth="2" />
        </svg>

        {/* Middle decorative elements */}
        <svg className="absolute top-1/3 right-1/4 w-32 h-32 text-gray-300 dark:text-gray-700 opacity-30" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M25 25 L75 25 L75 75 L25 75 Z" stroke="currentColor" strokeWidth="2" />
          <path d="M40 40 L60 60" stroke="currentColor" strokeWidth="2" />
          <path d="M40 60 L60 40" stroke="currentColor" strokeWidth="2" />
        </svg>
      </div>

      {/* Theme toggle */}
      <button
        onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
        className="absolute top-4 right-4 p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 z-10"
        aria-label="Toggle dark mode"
      >
        {theme === 'dark' ? <SunIcon className="h-5 w-5" /> : <MoonIcon className="h-5 w-5" />}
      </button>

      {/* Back button */}
      <Link href="/" className="absolute top-4 left-4 p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 z-50 hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors">
        <ArrowLeft className="h-5 w-5" />
      </Link>

      {/* Left side - decorative */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-600 to-amber-500 dark:from-purple-900 dark:to-amber-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-pattern opacity-10"
          style={{
            backgroundImage: 'linear-gradient(to right, white 1px, transparent 1px), linear-gradient(to bottom, white 1px, transparent 1px)',
            backgroundSize: '20px 20px'
          }}>
        </div>

        {/* Decorative SVG elements */}
        <svg className="absolute top-1/4 left-1/4 w-32 h-32 text-white opacity-20" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M25 25 L75 25 L75 75 L25 75 Z" stroke="currentColor" strokeWidth="2" />
          <path d="M40 40 L60 60" stroke="currentColor" strokeWidth="2" />
          <path d="M40 60 L60 40" stroke="currentColor" strokeWidth="2" />
        </svg>

        <svg className="absolute bottom-1/4 right-1/4 w-40 h-40 text-white opacity-20" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="30" y="30" width="40" height="40" rx="8" stroke="currentColor" strokeWidth="2" />
          <circle cx="50" cy="50" r="12" stroke="currentColor" strokeWidth="2" />
        </svg>

        <div className="relative z-10 flex flex-col justify-center items-center w-full p-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6 text-white">Loyal</h1>
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-white/90 text-xl text-center max-w-md mb-12"
          >
            Elevate your business with our premium loyalty program management solution
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="grid grid-cols-2 gap-6 w-full max-w-md"
          >
            <div className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="text-white mb-2 text-sm font-medium">Engage Customers</div>
              <p className="text-white/80 text-sm">Build lasting relationships with your loyal customers</p>
            </div>

            <div className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="text-white mb-2 text-sm font-medium">Boost Revenue</div>
              <p className="text-white/80 text-sm">Increase repeat business and customer lifetime value</p>
            </div>

            <div className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="text-white mb-2 text-sm font-medium">Track Analytics</div>
              <p className="text-white/80 text-sm">Gain insights into customer behavior and preferences</p>
            </div>

            <div className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="text-white mb-2 text-sm font-medium">Reward Loyalty</div>
              <p className="text-white/80 text-sm">Create compelling rewards that drive engagement</p>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Right side - login form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-6 md:p-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="border-gray-300 dark:border-gray-700 shadow-xl bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">Welcome back</CardTitle>
              <CardDescription className="text-gray-700 dark:text-gray-300">
                Sign in to your account to continue
              </CardDescription>
              {/* Email verification messages */}
              {verified === 'true' && (
                <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-3 rounded-md text-sm mt-4">
                  ✅ Email verified successfully! You can now login to your account.
                </div>
              )}
              {message === 'verification-sent' && (
                <div className="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 p-3 rounded-md text-sm mt-4">
                  📧 Please check your email and click the verification link, then return here to login.
                </div>
              )}
            </CardHeader>
            <CardContent>
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-900 dark:text-gray-100">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label htmlFor="password" className="text-gray-900 dark:text-gray-100">Password</Label>
                    <Link href="#" className="text-sm text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300">
                      Forgot password?
                    </Link>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>

                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                <Button
                  type="submit"
                  disabled={loading || !email || !password}
                  className="w-full min-h-10 bg-gradient-to-r from-purple-600 to-amber-500 hover:from-purple-700 hover:to-amber-600 text-white"
                >
                  {loading ? (
                    <span className="flex items-center justify-center">
                      <LoadingSpinner size="sm" color="white" className="mr-2" />
                      <span>Signing in...</span>
                    </span>
                  ) : (
                    'Sign In'
                  )}
                </Button>
              </form>
            </CardContent>
            <div className="px-6 pb-6 text-center">
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Don&apos;t have an account?{' '}
                <Link href="/signup" className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 font-medium">
                  Sign up
                </Link>
              </p>
            </div>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

// Loading fallback for Suspense
function LoginLoading() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
      <div className="w-full max-w-md space-y-8 rounded-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-md p-8 shadow-xl">
        <div className="text-center">
          <div className="h-8 w-48 mx-auto bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
          <div className="h-4 w-36 mx-auto bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
        <div className="space-y-6">
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>
    </div>
  )
}

// Main page component with Suspense boundary
export default function LoginPage() {
  return (
    <Suspense fallback={<LoginLoading />}>
      <LoginForm />
    </Suspense>
  )
}
