# Database Security Audit Analysis

## Executive Summary

This document provides a comprehensive analysis of security issues in the Supabase/PostgreSQL database and related data-fetching code, based on the Supabase linter output and code review. The audit focuses on Row Level Security (RLS), security definer functions/views, and mutable function search paths.

## Key Findings

1. **Row Level Security (RLS) Issues**: Multiple tables have RLS policies defined but RLS is not enabled on those tables, rendering the policies ineffective.
2. **Security Definer Functions**: Several functions are defined with `SECURITY DEFINER`, which could bypass RLS if not properly secured.
3. **Mutable Search Path**: Many PostgreSQL functions have mutable `search_path` settings, creating potential security vulnerabilities.
4. **Authentication Configuration**: Auth settings have potential security issues, including long OTP expiry and disabled leaked password protection.
5. **Exposed Credentials**: Full Supabase anon and service role keys are exposed in the repository.
6. **Inconsistent Parameter Naming**: Inconsistency between PostgreSQL session variables used in policies (`app.current_company_id` vs `app.current_tenant`).
7. **Excessive RLS Bypass**: API routes frequently bypass RLS using service role client, potentially undermining security controls.

## Detailed Analysis & Recommendations

### 1. RLS Not Enabled on Tables with Policies

#### Issue Description
Multiple tables in the `public` schema have RLS policies defined but RLS is not enabled on those tables. This means the policies are not being enforced, leaving data exposed.

#### Affected Tables
- `companies`
- `loyalty_members`
- `member_notifications`
- `administrators`
- `program_rules`
- `rewards`
- `company_administrators`
- `reward_tier_eligibility`
- `reward_redemptions`
- `tier_definitions`

#### Impact
High. Without RLS enabled, any client with database access can read or modify data across all tenants, bypassing multi-tenancy isolation.

#### Recommended Fix
Run the `improved-rls-fix.sql` script which enables RLS on all tables and creates appropriate policies:

```sql
-- Enable RLS on all tables that need it
DO $$
DECLARE
  table_record RECORD;
BEGIN
  FOR table_record IN 
    SELECT tablename 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename IN (
      'companies', 'loyalty_members', 'member_notifications', 'administrators',
      'program_rules', 'rewards', 'company_administrators', 'reward_tier_eligibility',
      'reward_redemptions', 'tier_definitions'
    )
  LOOP
    EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', table_record.tablename);
    RAISE NOTICE 'Enabled RLS on table: %', table_record.tablename;
  END LOOP;
END;
$$;
```

### 2. Security Definer Functions

#### Issue Description
The points transaction functions (`add_points_transaction`, `deduct_points_transaction`) are defined with `SECURITY DEFINER`, which means they execute with the privileges of the function owner, potentially bypassing RLS.

#### Impact
Medium. While these functions include explicit tenant checks (`p_company_id`), they bypass RLS controls and could lead to data leakage if not properly implemented.

#### Recommended Fix
Convert these functions to `SECURITY INVOKER` (the default) and rely on RLS for access control:

```sql
-- Update add_points_transaction to use SECURITY INVOKER
CREATE OR REPLACE FUNCTION add_points_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_points INTEGER,
  p_description TEXT,
  p_transaction_type TEXT DEFAULT 'EARN'
) RETURNS VOID AS $$
-- Function body remains the same
$$ LANGUAGE plpgsql; -- Remove SECURITY DEFINER

-- Update deduct_points_transaction to use SECURITY INVOKER
CREATE OR REPLACE FUNCTION deduct_points_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_points INTEGER,
  p_description TEXT,
  p_transaction_type TEXT DEFAULT 'REDEEM'
) RETURNS VOID AS $$
-- Function body remains the same
$$ LANGUAGE plpgsql; -- Remove SECURITY DEFINER
```

### 3. Mutable Search Path in Functions

#### Issue Description
Many PostgreSQL functions have mutable `search_path` settings, which could lead to function hijacking if a malicious schema is added to the search path.

#### Impact
Medium. Could lead to SQL injection or privilege escalation if exploited.

#### Affected Functions
- `get_active_inactive_ratio`
- `get_member_engagement`
- `get_location_distribution`
- `update_member_tier`
- `update_modified_timestamp`
- `update_notification_status`
- `update_points_on_expiration`
- `update_timestamp`
- And potentially others

#### Recommended Fix
Explicitly set the search path in each function and use schema-qualified references:

```sql
-- Example fix for one function
CREATE OR REPLACE FUNCTION public.get_active_inactive_ratio(p_days integer DEFAULT 30,
  p_company_id uuid DEFAULT NULL)
  RETURNS json
  LANGUAGE plpgsql
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  -- Rest of function body with schema-qualified references
  -- e.g., public.loyalty_members instead of just loyalty_members
  
  -- Function body...
END;
$function$;
```

### 4. Authentication Configuration Issues

#### Issue Description
The Supabase linter flagged two auth-related warnings:
1. OTP expiry is set too long
2. Leaked password protection is disabled

#### Impact
Medium. Could lead to security vulnerabilities in the authentication system.

#### Recommended Fix
Update the auth settings in the Supabase dashboard or via API:

```javascript
// Update in lib/supabase-auth-config.ts or via Supabase dashboard
const authConfig = {
  // Other settings...
  otpExpirySeconds: 300, // Reduce to 5 minutes (from default 24 hours)
  enableLeakedPasswordProtection: true
};
```

### 5. Exposed Credentials

#### Issue Description
Full Supabase anon and service role keys are exposed in the `.env.local` file which appears to be committed to the repository.

#### Impact
Critical. Exposed service role keys grant full database access, bypassing all security controls.

#### Recommended Fix
1. Immediately rotate the compromised keys in the Supabase dashboard
2. Remove the keys from version control
3. Add `.env.local` to `.gitignore`
4. Use environment variables in deployment environments

```bash
# .gitignore
.env.local
.env.*.local
```

### 6. Inconsistent Parameter Naming

#### Issue Description
There's inconsistency between PostgreSQL session variables used in policies (`app.current_company_id` vs `app.current_tenant`).

#### Impact
Medium. Could lead to policies not being applied correctly, resulting in data leakage.

#### Recommended Fix
Standardize on `app.current_company_id` across all policies:

```sql
-- Update any policies using app.current_tenant
DO $$
DECLARE
  policy_record RECORD;
BEGIN
  FOR policy_record IN 
    SELECT schemaname, tablename, policyname
    FROM pg_policies
    WHERE polqual::text LIKE '%app.current_tenant%'
  LOOP
    EXECUTE format('DROP POLICY %I ON %I.%I', 
      policy_record.policyname, 
      policy_record.schemaname, 
      policy_record.tablename
    );
    
    -- Recreate with app.current_company_id
    -- (This is a simplified example - actual recreation will depend on the specific policy)
    EXECUTE format('CREATE POLICY %I ON %I.%I FOR ALL TO authenticated USING (company_id::text = current_setting(''app.current_company_id'')::text)', 
      policy_record.policyname, 
      policy_record.schemaname, 
      policy_record.tablename
    );
  END LOOP;
END;
$$;
```

### 7. Excessive RLS Bypass in API Routes

#### Issue Description
API routes frequently bypass RLS using the service role client (`getSupabaseClient(true)`), potentially undermining security controls.

#### Impact
Medium. Bypassing RLS should be done selectively and with caution.

#### Recommended Fix
1. Audit all instances of service role client usage
2. Replace with anon client where possible
3. Document justification for remaining service role usage

Example refactoring for an API route:

```typescript
// Before
const supabase = getSupabaseClient(true); // Service role client

// After
// Use anon client with RLS where possible
const supabase = getSupabaseClient(false); 

// Only use service role for operations that truly need it
async function adminOperation() {
  const supabaseAdmin = getSupabaseClient(true);
  // Operation that requires admin privileges
}
```

## Implementation Priority

1. **Critical (Immediate Action)**
   - Rotate exposed Supabase credentials
   - Enable RLS on all tables with policies

2. **High (Within 1 Week)**
   - Fix mutable search path in functions
   - Convert SECURITY DEFINER functions to INVOKER where appropriate
   - Update authentication configuration

3. **Medium (Within 2 Weeks)**
   - Standardize parameter naming in policies
   - Refactor API routes to minimize RLS bypass

## Conclusion

The database security audit has identified several critical issues that need immediate attention, particularly around RLS enablement and exposed credentials. Implementing the recommended fixes will significantly improve the security posture of the application and ensure proper multi-tenant data isolation.

The existing scripts (`enable-rls.sql` and `improved-rls-fix.sql`) provide a good foundation for addressing many of these issues, but they need to be executed and potentially updated to cover all affected tables and policies.
