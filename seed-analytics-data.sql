-- COMPREHENSIVE SEED DATA FOR LOYAL ANALYTICS TESTING
-- This script provides sample data to test all analytics functionality
-- Run this after setting up your database schema

-- First, let's ensure we have a test company
INSERT INTO companies (id, name, business_type, slug, logo_url, primary_color, points_expiration_days, points_earning_ratio, is_active, onboarding_completed, setup_wizard_step, created_at)
VALUES (
  '7c4b5389-b630-4d2b-b9b1-9f6460117371',
  'FUFIS Beauty Services',
  'Beauty & Wellness',
  'fufis-beauty-services',
  'https://example.com/logo.png',
  '#FF6B6B',
  365,
  0.1,
  true,
  true,
  5,
  NOW() - INTERVAL '30 days'
) ON CONFLICT (id) DO NOTHING;

-- Create additional test companies for variety
INSERT INTO companies (id, name, business_type, slug, logo_url, primary_color, points_expiration_days, points_earning_ratio, is_active, onboarding_completed, setup_wizard_step, created_at)
VALUES
  (
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'Maraki Coffee House',
    'Food & Beverage',
    'maraki-coffee-house',
    'https://example.com/coffee-logo.png',
    '#8B4513',
    180,
    0.08,
    true,
    true,
    5,
    NOW() - INTERVAL '45 days'
  ),
  (
    'e15bfe8f-4227-514d-c683-d27bc981e482',
    'Addis Electronics',
    'Electronics & Tech',
    'addis-electronics',
    'https://example.com/electronics-logo.png',
    '#4169E1',
    90,
    0.05,
    true,
    true,
    5,
    NOW() - INTERVAL '60 days'
  )
ON CONFLICT (id) DO NOTHING;

-- Insert comprehensive business items for FUFIS Beauty Services
INSERT INTO business_items (
  id, company_id, item_name, item_code, standard_price, item_category, item_subcategory,
  description, is_active, total_sales_count, total_revenue, avg_selling_price,
  last_sold_date, ai_recognition_patterns, common_variations, created_at, updated_at
) VALUES
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Gel Manicure',
    'GM001',
    450.00,
    'Nail Services',
    'Manicure',
    'Professional gel manicure with base coat, color, and top coat',
    true,
    156,
    70200.00,
    450.00,
    NOW() - INTERVAL '2 days',
    ARRAY['gel mani', 'gel manicure', 'nail gel', 'mani gel'],
    ARRAY['gel mani', 'gelish mani', 'gel polish'],
    NOW() - INTERVAL '30 days',
    NOW() - INTERVAL '2 days'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Shellac Polish',
    'SP001',
    875.00,
    'Nail Services',
    'Polish',
    'Premium shellac nail polish application',
    true,
    89,
    77875.00,
    875.00,
    NOW() - INTERVAL '1 day',
    ARRAY['shellac', 'shell lac', 'nail polish', 'polish'],
    ARRAY['shellac polish', 'shell lac', 'nail lacquer'],
    NOW() - INTERVAL '25 days',
    NOW() - INTERVAL '1 day'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Eyebrow Threading',
    'ET001',
    200.00,
    'Facial Services',
    'Eyebrow',
    'Professional eyebrow shaping using threading technique',
    true,
    234,
    46800.00,
    200.00,
    NOW() - INTERVAL '3 hours',
    ARRAY['eyebrow thread', 'brow threading', 'thread brow', 'eyebrow'],
    ARRAY['brow thread', 'eyebrow shaping', 'threading'],
    NOW() - INTERVAL '35 days',
    NOW() - INTERVAL '3 hours'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Facial Treatment',
    'FT001',
    1200.00,
    'Facial Services',
    'Treatment',
    'Deep cleansing facial with extraction and moisturizing',
    true,
    67,
    80400.00,
    1200.00,
    NOW() - INTERVAL '5 days',
    ARRAY['facial', 'face treatment', 'skin treatment', 'facial care'],
    ARRAY['facial treatment', 'face care', 'skin facial'],
    NOW() - INTERVAL '40 days',
    NOW() - INTERVAL '5 days'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Hair Wash & Blow Dry',
    'HW001',
    350.00,
    'Hair Services',
    'Styling',
    'Professional hair washing and blow drying service',
    true,
    123,
    43050.00,
    350.00,
    NOW() - INTERVAL '4 hours',
    ARRAY['hair wash', 'blow dry', 'hair styling', 'wash blow'],
    ARRAY['hair washing', 'blow drying', 'wash and style'],
    NOW() - INTERVAL '28 days',
    NOW() - INTERVAL '4 hours'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Pedicure',
    'PD001',
    600.00,
    'Nail Services',
    'Pedicure',
    'Complete foot care with nail trimming, filing, and polish',
    true,
    98,
    58800.00,
    600.00,
    NOW() - INTERVAL '6 hours',
    ARRAY['pedicure', 'foot care', 'toe nails', 'foot treatment'],
    ARRAY['pedi', 'foot pedicure', 'toe care'],
    NOW() - INTERVAL '32 days',
    NOW() - INTERVAL '6 hours'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Massage Therapy',
    'MT001',
    1500.00,
    'Body Services',
    'Massage',
    'Relaxing full body massage therapy session',
    true,
    45,
    67500.00,
    1500.00,
    NOW() - INTERVAL '2 days',
    ARRAY['massage', 'body massage', 'therapy massage', 'relaxation'],
    ARRAY['massage therapy', 'body therapy', 'therapeutic massage'],
    NOW() - INTERVAL '20 days',
    NOW() - INTERVAL '2 days'
  );

-- Insert business items for Maraki Coffee House
INSERT INTO business_items (
  id, company_id, item_name, item_code, standard_price, item_category, item_subcategory,
  description, is_active, total_sales_count, total_revenue, avg_selling_price,
  last_sold_date, ai_recognition_patterns, common_variations, created_at, updated_at
) VALUES
  (
    gen_random_uuid(),
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'Ethiopian Coffee',
    'EC001',
    45.00,
    'Beverages',
    'Coffee',
    'Traditional Ethiopian coffee ceremony brew',
    true,
    345,
    15525.00,
    45.00,
    NOW() - INTERVAL '1 hour',
    ARRAY['ethiopian coffee', 'coffee', 'traditional coffee', 'ceremony coffee'],
    ARRAY['eth coffee', 'local coffee', 'traditional brew'],
    NOW() - INTERVAL '45 days',
    NOW() - INTERVAL '1 hour'
  ),
  (
    gen_random_uuid(),
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'Macchiato',
    'MC001',
    55.00,
    'Beverages',
    'Coffee',
    'Espresso with steamed milk foam',
    true,
    234,
    12870.00,
    55.00,
    NOW() - INTERVAL '2 hours',
    ARRAY['macchiato', 'machiato', 'espresso milk', 'coffee milk'],
    ARRAY['machiatto', 'macchiatto', 'milk coffee'],
    NOW() - INTERVAL '40 days',
    NOW() - INTERVAL '2 hours'
  ),
  (
    gen_random_uuid(),
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'Injera with Doro Wot',
    'IW001',
    125.00,
    'Food',
    'Traditional',
    'Traditional Ethiopian dish with spicy chicken stew',
    true,
    167,
    20875.00,
    125.00,
    NOW() - INTERVAL '3 hours',
    ARRAY['injera', 'doro wot', 'doro wet', 'ethiopian food', 'traditional food'],
    ARRAY['injera doro', 'doro wat', 'chicken stew'],
    NOW() - INTERVAL '42 days',
    NOW() - INTERVAL '3 hours'
  );

-- Create loyalty members
INSERT INTO loyalty_members (
  id, company_id, loyalty_id, first_name, last_name, email, phone,
  registration_date, total_points, tier_id, is_active, created_at
) VALUES
  (
    'f1513243-d4e6-4b89-9c7a-123456789001',
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'FUFIS001',
    'Sarah',
    'Johnson',
    '<EMAIL>',
    '+************',
    NOW() - INTERVAL '60 days',
    2580,
    1,
    true,
    NOW() - INTERVAL '60 days'
  ),
  (
    'f1513243-d4e6-4b89-9c7a-123456789002',
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'FUFIS002',
    'Almaz',
    'Tadesse',
    '<EMAIL>',
    '+251922345678',
    NOW() - INTERVAL '45 days',
    1950,
    1,
    true,
    NOW() - INTERVAL '45 days'
  ),
  (
    'f1513243-d4e6-4b89-9c7a-123456789003',
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'FUFIS003',
    'Michelle',
    'Anderson',
    '<EMAIL>',
    '+251933456789',
    NOW() - INTERVAL '30 days',
    1425,
    1,
    true,
    NOW() - INTERVAL '30 days'
  ),
  (
    'f1513243-d4e6-4b89-9c7a-123456789004',
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'FUFIS004',
    'Hanan',
    'Mohammed',
    '<EMAIL>',
    '+251944567890',
    NOW() - INTERVAL '20 days',
    875,
    1,
    true,
    NOW() - INTERVAL '20 days'
  ),
  (
    'f1513243-d4e6-4b89-9c7a-123456789005',
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'MARAKI001',
    'David',
    'Williams',
    '<EMAIL>',
    '+************',
    NOW() - INTERVAL '35 days',
    1680,
    1,
    true,
    NOW() - INTERVAL '35 days'
  );

-- Create receipt templates
INSERT INTO receipt_templates (
  id, company_id, template_name, template_image_url, template_metadata,
  ai_prompt_context, validation_rules, confidence_threshold, is_active,
  total_extractions, successful_extractions, avg_confidence_score,
  created_at, updated_at
) VALUES
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'FUFIS Beauty Services Standard',
    'https://example.com/template1.jpg',
    '{"layout": "standard", "fields": ["service", "price", "date", "total"]}',
    'This is a beauty salon receipt template with services listed vertically, prices on the right, and total at bottom',
    '{"required_fields": ["total_amount", "service_description"], "date_format": "DD/MM/YYYY"}',
    0.85,
    true,
    234,
    203,
    0.867,
    NOW() - INTERVAL '25 days',
    NOW() - INTERVAL '3 days'
  ),
  (
    gen_random_uuid(),
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'Maraki Coffee House Receipt',
    'https://example.com/template2.jpg',
    '{"layout": "compact", "fields": ["item", "quantity", "price", "total"]}',
    'Coffee shop receipt template with items and quantities, compact layout',
    '{"required_fields": ["total_amount", "items"], "currency": "ETB"}',
    0.90,
    true,
    156,
    142,
    0.910,
    NOW() - INTERVAL '40 days',
    NOW() - INTERVAL '5 days'
  );

-- Create receipts with realistic data
INSERT INTO receipts (
  id, company_id, member_id, loyalty_id, receipt_image_url, total_amount,
  service_description, transaction_date, points_earned, extraction_confidence,
  template_id, ai_extracted_data, manual_verification_status, created_at
) VALUES
  -- FUFIS Beauty Services receipts
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'f1513243-d4e6-4b89-9c7a-123456789001',
    'FUFIS001',
    'https://example.com/receipt1.jpg',
    875.00,
    'Shellac Polish',
    NOW() - INTERVAL '1 day',
    88,
    0.92,
    (SELECT id FROM receipt_templates WHERE template_name = 'FUFIS Beauty Services Standard' LIMIT 1),
    '{"items": [{"name": "Shellac Polish", "price": 875.00}], "total": 875.00}',
    'verified',
    NOW() - INTERVAL '1 day'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'f1513243-d4e6-4b89-9c7a-123456789002',
    'FUFIS002',
    'https://example.com/receipt2.jpg',
    450.00,
    'Gel Manicure',
    NOW() - INTERVAL '2 days',
    45,
    0.89,
    (SELECT id FROM receipt_templates WHERE template_name = 'FUFIS Beauty Services Standard' LIMIT 1),
    '{"items": [{"name": "Gel Manicure", "price": 450.00}], "total": 450.00}',
    'verified',
    NOW() - INTERVAL '2 days'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'f1513243-d4e6-4b89-9c7a-123456789003',
    'FUFIS003',
    'https://example.com/receipt3.jpg',
    1200.00,
    'Facial Treatment',
    NOW() - INTERVAL '3 days',
    120,
    0.95,
    (SELECT id FROM receipt_templates WHERE template_name = 'FUFIS Beauty Services Standard' LIMIT 1),
    '{"items": [{"name": "Facial Treatment", "price": 1200.00}], "total": 1200.00}',
    'verified',
    NOW() - INTERVAL '3 days'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'f1513243-d4e6-4b89-9c7a-123456789001',
    'FUFIS001',
    'https://example.com/receipt4.jpg',
    950.00,
    'Gel Manicure + Eyebrow Threading',
    NOW() - INTERVAL '5 days',
    95,
    0.87,
    (SELECT id FROM receipt_templates WHERE template_name = 'FUFIS Beauty Services Standard' LIMIT 1),
    '{"items": [{"name": "Gel Manicure", "price": 450.00}, {"name": "Eyebrow Threading", "price": 200.00}], "total": 650.00}',
    'verified',
    NOW() - INTERVAL '5 days'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'f1513243-d4e6-4b89-9c7a-123456789004',
    'FUFIS004',
    'https://example.com/receipt5.jpg',
    600.00,
    'Pedicure',
    NOW() - INTERVAL '7 days',
    60,
    0.91,
    (SELECT id FROM receipt_templates WHERE template_name = 'FUFIS Beauty Services Standard' LIMIT 1),
    '{"items": [{"name": "Pedicure", "price": 600.00}], "total": 600.00}',
    'verified',
    NOW() - INTERVAL '7 days'
  ),
  -- Maraki Coffee House receipts
  (
    gen_random_uuid(),
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'f1513243-d4e6-4b89-9c7a-123456789005',
    'MARAKI001',
    'https://example.com/receipt6.jpg',
    135.00,
    'Ethiopian Coffee + Injera with Doro Wot',
    NOW() - INTERVAL '1 day',
    14,
    0.94,
    (SELECT id FROM receipt_templates WHERE template_name = 'Maraki Coffee House Receipt' LIMIT 1),
    '{"items": [{"name": "Ethiopian Coffee", "price": 45.00, "qty": 1}, {"name": "Injera with Doro Wot", "price": 125.00, "qty": 1}], "total": 170.00}',
    'verified',
    NOW() - INTERVAL '1 day'
  ),
  (
    gen_random_uuid(),
    'd10aed7e-3116-403c-a572-c16ab870d761',
    'f1513243-d4e6-4b89-9c7a-123456789005',
    'MARAKI001',
    'https://example.com/receipt7.jpg',
    100.00,
    'Macchiato',
    NOW() - INTERVAL '4 days',
    10,
    0.88,
    (SELECT id FROM receipt_templates WHERE template_name = 'Maraki Coffee House Receipt' LIMIT 1),
    '{"items": [{"name": "Macchiato", "price": 55.00, "qty": 2}], "total": 110.00}',
    'verified',
    NOW() - INTERVAL '4 days'
  ),
  -- Some receipts without templates (for testing template usage stats)
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'f1513243-d4e6-4b89-9c7a-123456789002',
    'FUFIS002',
    'https://example.com/receipt8.jpg',
    350.00,
    'Hair Wash & Blow Dry',
    NOW() - INTERVAL '8 days',
    35,
    0.72,
    NULL,
    '{"items": [{"name": "Hair Wash & Blow Dry", "price": 350.00}], "total": 350.00}',
    'needs_review',
    NOW() - INTERVAL '8 days'
  ),
  (
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'f1513243-d4e6-4b89-9c7a-123456789003',
    'FUFIS003',
    'https://example.com/receipt9.jpg',
    1500.00,
    'Massage Therapy',
    NOW() - INTERVAL '10 days',
    150,
    0.68,
    NULL,
    '{"items": [{"name": "Massage Therapy", "price": 1500.00}], "total": 1500.00}',
    'needs_review',
    NOW() - INTERVAL '10 days'
  );

-- Create receipt items for detailed analytics
INSERT INTO receipt_items (
  id, receipt_id, item_name, quantity, unit_price, total_price,
  matched_business_item_id, ai_confidence_score, created_at
) VALUES
  -- Items for receipt 1 (Shellac Polish)
  (
    gen_random_uuid(),
    (SELECT id FROM receipts WHERE service_description = 'Shellac Polish' LIMIT 1),
    'Shellac Polish',
    1,
    875.00,
    875.00,
    (SELECT id FROM business_items WHERE item_name = 'Shellac Polish' LIMIT 1),
    0.92,
    NOW() - INTERVAL '1 day'
  ),
  -- Items for receipt 2 (Gel Manicure)
  (
    gen_random_uuid(),
    (SELECT id FROM receipts WHERE service_description = 'Gel Manicure' LIMIT 1),
    'Gel Manicure',
    1,
    450.00,
    450.00,
    (SELECT id FROM business_items WHERE item_name = 'Gel Manicure' LIMIT 1),
    0.89,
    NOW() - INTERVAL '2 days'
  ),
  -- Items for receipt 3 (Facial Treatment)
  (
    gen_random_uuid(),
    (SELECT id FROM receipts WHERE service_description = 'Facial Treatment' LIMIT 1),
    'Facial Treatment',
    1,
    1200.00,
    1200.00,
    (SELECT id FROM business_items WHERE item_name = 'Facial Treatment' LIMIT 1),
    0.95,
    NOW() - INTERVAL '3 days'
  ),
  -- Items for receipt 4 (Combo service)
  (
    gen_random_uuid(),
    (SELECT id FROM receipts WHERE service_description = 'Gel Manicure + Eyebrow Threading' LIMIT 1),
    'Gel Manicure',
    1,
    450.00,
    450.00,
    (SELECT id FROM business_items WHERE item_name = 'Gel Manicure' LIMIT 1),
    0.87,
    NOW() - INTERVAL '5 days'
  ),
  (
    gen_random_uuid(),
    (SELECT id FROM receipts WHERE service_description = 'Gel Manicure + Eyebrow Threading' LIMIT 1),
    'Eyebrow Threading',
    1,
    200.00,
    200.00,
    (SELECT id FROM business_items WHERE item_name = 'Eyebrow Threading' LIMIT 1),
    0.87,
    NOW() - INTERVAL '5 days'
  );

-- Update template extraction statistics
UPDATE receipt_templates
SET
  total_extractions = (
    SELECT COUNT(*) FROM receipts
    WHERE template_id = receipt_templates.id
  ),
  successful_extractions = (
    SELECT COUNT(*) FROM receipts
    WHERE template_id = receipt_templates.id
    AND extraction_confidence > 0.8
  ),
  avg_confidence_score = (
    SELECT AVG(extraction_confidence) FROM receipts
    WHERE template_id = receipt_templates.id
    AND extraction_confidence IS NOT NULL
  ),
  updated_at = NOW()
WHERE id IN (
  SELECT DISTINCT template_id FROM receipts WHERE template_id IS NOT NULL
);

-- Update business items statistics
UPDATE business_items SET
  total_sales_count = COALESCE((
    SELECT SUM(quantity) FROM receipt_items
    WHERE matched_business_item_id = business_items.id
  ), 0),
  total_revenue = COALESCE((
    SELECT SUM(total_price) FROM receipt_items
    WHERE matched_business_item_id = business_items.id
  ), 0),
  avg_selling_price = COALESCE((
    SELECT AVG(unit_price) FROM receipt_items
    WHERE matched_business_item_id = business_items.id
  ), standard_price),
  last_sold_date = (
    SELECT MAX(created_at) FROM receipt_items
    WHERE matched_business_item_id = business_items.id
  ),
  updated_at = NOW()
WHERE id IN (
  SELECT DISTINCT matched_business_item_id FROM receipt_items
  WHERE matched_business_item_id IS NOT NULL
);

-- Insert points transactions
INSERT INTO points_transactions (
  id, member_id, transaction_type, points, description,
  receipt_id, created_at
)
SELECT
  gen_random_uuid(),
  member_id,
  'earned',
  points_earned,
  'Points earned from purchase: ' || service_description,
  id,
  created_at
FROM receipts
WHERE points_earned > 0;

-- Create some sample administrators for testing
INSERT INTO administrators (id, email, full_name, is_super_admin, is_active, created_at)
VALUES
  (
    gen_random_uuid(),
    '<EMAIL>',
    'Beauty Admin',
    false,
    true,
    NOW() - INTERVAL '30 days'
  ),
  (
    gen_random_uuid(),
    '<EMAIL>',
    'Coffee Shop Owner',
    false,
    true,
    NOW() - INTERVAL '45 days'
  )
ON CONFLICT (email) DO NOTHING;

-- Link administrators to companies
INSERT INTO company_administrators (company_id, administrator_id, role, created_at)
VALUES
  (
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    (SELECT id FROM administrators WHERE email = '<EMAIL>' LIMIT 1),
    'admin',
    NOW() - INTERVAL '30 days'
  ),
  (
    'd10aed7e-3116-403c-a572-c16ab870d761',
    (SELECT id FROM administrators WHERE email = '<EMAIL>' LIMIT 1),
    'owner',
    NOW() - INTERVAL '45 days'
  )
ON CONFLICT (company_id, administrator_id) DO NOTHING;

-- Insert some sample tier definitions
INSERT INTO tier_definitions (id, company_id, tier_name, min_points, max_points, benefits, created_at)
VALUES
  (
    1,
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Bronze',
    0,
    999,
    '{"discount_percentage": 5, "special_offers": ["birthday_reward"]}',
    NOW() - INTERVAL '30 days'
  ),
  (
    2,
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Silver',
    1000,
    2499,
    '{"discount_percentage": 10, "special_offers": ["birthday_reward", "priority_booking"]}',
    NOW() - INTERVAL '30 days'
  ),
  (
    3,
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    'Gold',
    2500,
    999999,
    '{"discount_percentage": 15, "special_offers": ["birthday_reward", "priority_booking", "free_upgrade"]}',
    NOW() - INTERVAL '30 days'
  )
ON CONFLICT (id, company_id) DO NOTHING;

-- Print summary of inserted data
DO $$
BEGIN
  RAISE NOTICE 'SEED DATA INSERTION COMPLETE!';
  RAISE NOTICE '=================================';
  RAISE NOTICE 'Companies: %', (SELECT COUNT(*) FROM companies);
  RAISE NOTICE 'Business Items: %', (SELECT COUNT(*) FROM business_items);
  RAISE NOTICE 'Loyalty Members: %', (SELECT COUNT(*) FROM loyalty_members);
  RAISE NOTICE 'Receipt Templates: %', (SELECT COUNT(*) FROM receipt_templates);
  RAISE NOTICE 'Receipts: %', (SELECT COUNT(*) FROM receipts);
  RAISE NOTICE 'Receipt Items: %', (SELECT COUNT(*) FROM receipt_items);
  RAISE NOTICE 'Points Transactions: %', (SELECT COUNT(*) FROM points_transactions);
  RAISE NOTICE 'Administrators: %', (SELECT COUNT(*) FROM administrators);
  RAISE NOTICE '=================================';
  RAISE NOTICE 'Analytics should now show real data!';
END $$;
