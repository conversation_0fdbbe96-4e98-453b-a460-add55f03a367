# AI Redemption Flow Testing Guide

This document provides step-by-step instructions for testing the AI-enhanced reward redemption flow.

## Prerequisites

1. Ensure you have the following environment variables set in `.env.local`:
   - `GOOGLE_GENERATIVE_AI_API_KEY`
   - All Supabase environment variables

2. Ensure the development server is running:
   ```bash
   npm run dev
   ```

## Testing the Debug Endpoint

1. Use the provided test script:
   ```bash
   ./test-debug-api.sh
   ```

2. This will make a GET request to `/api/debug/lookup-member?id=ce4ada4a-3d7b-4082-bac3-5f4bbcb5abb1`

3. Check the response for:
   - Member found in which table (`members` or `loyalty_members`)
   - Member data structure
   - Any errors encountered

4. Try with different member IDs as needed by editing the script

## Testing the AI Validation Endpoint

1. Use the provided test script:
   ```bash
   ./test-ai-validation.sh
   ```

2. This will make a POST request to `/api/redemptions/validate-ai` with:
   - `memberId`: "ce4ada4a-3d7b-4082-bac3-5f4bbcb5abb1"
   - `rewardId`: "dc541e17-5016-4e7a-9aa9-0c5f06003b88"
   - `receiptTotal`: 100.50

3. Check the response for:
   - Eligibility determination
   - AI insights and reasoning
   - Alternative recommendations
   - Any warnings or errors

4. Try different member IDs, reward IDs, and receipt totals by editing the script

## End-to-End UI Testing

1. Log in to the admin interface
2. Navigate to the redemption page at `/transactions/redeem`
3. Test the following scenarios:

   **Scenario 1: Basic redemption**
   - Select a member
   - Select a reward
   - Enter a receipt total
   - Observe the AI validation results
   - Submit the redemption
   - Verify the member's points are updated

   **Scenario 2: Receipt upload and OCR**
   - Select a member
   - Select a reward
   - Upload a receipt image
   - Observe OCR results and extracted values
   - Verify AI recommendations based on receipt data

   **Scenario 3: Ineligible redemption**
   - Select a member with insufficient points
   - Select a reward
   - Verify that AI validation shows ineligible status
   - Check that alternative recommendations are provided

4. Check error handling by:
   - Temporarily removing the API key to test fallback logic
   - Using invalid member or reward IDs
   - Uploading malformed receipt images

## Troubleshooting Common Issues

- **"Member not found"**: Use the debug endpoint to verify member existence in the database
- **AI validation timeout**: Check the AI service configuration and API key validity
- **OCR extraction failures**: Ensure the receipt image is clear and properly formatted
- **Incorrect point calculations**: Verify the member's available points in the database

## Final Verification

1. After successful testing, remove or disable the debug endpoints for production
2. Remove any console.log statements used for debugging
3. Update the documentation with any findings or adjustments made during testing
