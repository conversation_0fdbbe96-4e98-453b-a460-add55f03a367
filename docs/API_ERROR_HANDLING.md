# API Error Handling Best Practices

## Overview

This document outlines the standardized approach for handling API errors in the Loyal application to prevent infinite loops, improve error reporting, and enhance application stability.

## Common Issues

### Infinite Loops in API Calls

One of the most critical issues we've encountered is infinite loops in API calls, which can occur when:

1. An API call fails but the error isn't properly handled
2. The component continues to retry the failed call without proper backoff or limits
3. Error states aren't properly managed, leading to continuous re-rendering and re-fetching

These issues can cause:
- Excessive server load
- Poor user experience with UI flickering
- Potential rate limiting or IP blocking
- Browser performance degradation

## Standardized Solution

We've implemented a systematic approach to prevent these issues:

### 1. Error Handling Utility

The `error-handling.ts` utility provides standardized functions for handling API errors:

```typescript
// Import in your component
import { handleSupabaseError, safeQuery } from '@/lib/error-handling'
```

Key functions:

- **handleSupabaseError**: Standardized error logging and user notification
- **safeQuery**: A wrapper for Supabase queries that handles errors and provides fallback values

### 2. Best Practices for API Calls

#### Using the Safe Query Pattern

```typescript
// Before
try {
  const { data, error } = await supabase
    .from('table_name')
    .select('*')
    .eq('id', id)
  
  if (error) throw error
  
  setData(data)
} catch (error) {
  console.error('Error:', error)
}

// After
try {
  const data = await safeQuery(
    () => supabase
      .from('table_name')
      .select('*')
      .eq('id', id),
    [], // Fallback value (empty array)
    'Fetching data' // Context for error messages
  )
  
  setData(data)
} catch (error) {
  handleSupabaseError(error, 'Fetching data')
  setData([]) // Always set a default value to prevent undefined states
}
```

#### Always Set Default State Values

```typescript
// Always set a default state value after catching an error
try {
  // API call
} catch (error) {
  handleSupabaseError(error, 'Operation name')
  setData([]) // Set empty array or appropriate default value
}
```

#### Implement Proper Loading States

```typescript
const [loading, setLoading] = useState(true)

const fetchData = async () => {
  try {
    setLoading(true)
    // API call
  } catch (error) {
    // Error handling
  } finally {
    setLoading(false) // Always ensure loading state is updated
  }
}
```

### 3. API Health Check

We've implemented an API health check script (`scripts/api-health-check.ts`) to systematically test all endpoints for potential issues. This helps identify configuration problems or other issues that could lead to infinite loops.

Run it with:

```bash
npx ts-node scripts/api-health-check.ts
```

## Implementation Checklist

When implementing API calls, ensure:

- [ ] Use the `safeQuery` utility for Supabase queries
- [ ] Always set default state values after errors
- [ ] Implement proper loading states
- [ ] Add early returns after error handling to prevent further execution
- [ ] Use the `handleSupabaseError` utility for consistent error reporting
- [ ] Test your implementation with the API health check script

## Common Error Patterns to Watch For

1. **Database Configuration Errors**: `unrecognized configuration parameter` errors often indicate issues with database settings or RLS policies
2. **Authentication Issues**: Check that service role keys are properly used for bypassing RLS when needed
3. **Missing Error Handling**: Always handle both Supabase errors and general exceptions
4. **Infinite Loops**: Watch for components that continuously re-render and trigger API calls

## Row Level Security (RLS) Considerations

When dealing with Supabase and RLS:

1. For client-side components, fetch data through API routes rather than directly using the service role key
2. Server-side API routes should use the service role key to bypass RLS when needed
3. Use the `getSupabaseClient(true)` helper to get a client with the service role key on the server

```typescript
// In API routes
import { getSupabaseClient } from '@/lib/supabase';

// Use the service role client to bypass RLS
const supabase = getSupabaseClient(true);
```

By following these guidelines, we can ensure our application remains stable, responsive, and free from infinite loop issues.
