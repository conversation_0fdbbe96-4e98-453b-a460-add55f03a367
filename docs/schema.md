# Loyal Database Schema Documentation

## Tables and Relationships

### companies
- Primary key: `id` (UUID)
- Fields:
  - `name`: Company name
  - `slug`: URL-friendly identifier
  - `logo_url`: Optional URL to company logo
  - `primary_color`: Hex color code for branding
  - `points_earning_ratio`: Points earned per currency unit (default: 1.0)
  - `points_expiration_days`: Days until points expire (default: 365)
  - `administrator_id`: Foreign key to user who administers the company
- Relationships:
  - One-to-many with `loyalty_members`
  - One-to-many with `rewards`
  - One-to-many with `tier_definitions`

### loyalty_members
- Primary key: `id` (UUID)
- Fields:
  - `company_id`: Foreign key to companies table
  - `name`: Member's name
  - `email`: Optional email address
  - `phone_number`: Contact phone number
  - `birthday`: Optional date of birth
  - `loyalty_id`: Unique identifier for member within company
  - `telegram_chat_id`: Optional Telegram chat ID for bot integration
  - `loyalty_tier`: Current tier level (references tier_definitions)
  - `lifetime_points`: Total points earned (never decreases)
  - `available_points`: Current points balance available for redemption
  - `redeemed_points`: Total points redeemed
  - `expired_points`: Total points expired
  - `registration_date`: When member joined
  - `notes`: Optional additional information
- Relationships:
  - Many-to-one with `companies`
  - One-to-many with `points_transactions`
  - One-to-many with `reward_redemptions`

### points_transactions
- Primary key: `id` (UUID)
- Fields:
  - `member_id`: Foreign key to loyalty_members
  - `transaction_type`: Enum ('earn', 'redeem', 'expire', 'adjust')
  - `points`: Number of points (positive or negative)
  - `transaction_date`: When transaction occurred
  - `receipt_amount`: Optional amount spent (for earn transactions)
  - `description`: Optional transaction details
  - `expiration_date`: When these points will expire (for earn transactions)
- Relationships:
  - Many-to-one with `loyalty_members`

### rewards
- Primary key: `id` (UUID)
- Fields:
  - `company_id`: Foreign key to companies
  - `title`: Reward name
  - `description`: Detailed description
  - `reward_type`: Type of reward
  - `reward_value_type`: Enum ('percentage', 'fixed_amount')
  - `reward_value`: Discount percentage or fixed amount
  - `code`: Unique 4-character redemption code (2 uppercase letters + 2 digits)
  - `points_required`: Points cost to redeem
  - `start_date`: When reward becomes available
  - `expiration_date`: Optional end date
  - `is_active`: Whether reward is currently available
  - `reward_image_url`: Optional image URL
- Relationships:
  - Many-to-one with `companies`
  - One-to-many with `reward_redemptions`

### reward_redemptions
- Primary key: `id` (UUID)
- Fields:
  - `member_id`: Foreign key to loyalty_members
  - `reward_id`: Foreign key to rewards
  - `redemption_date`: When redeemed
  - `points_spent`: Points deducted from member balance
  - `status`: Enum ('pending', 'completed', 'cancelled')
- Relationships:
  - Many-to-one with `loyalty_members`
  - Many-to-one with `rewards`

### tier_definitions
- Primary key: `id` (UUID)
- Fields:
  - `company_id`: Foreign key to companies
  - `tier_name`: Name of the tier (e.g., Silver, Gold, Platinum)
  - `points_threshold`: Minimum lifetime points required
  - `multiplier`: Optional points earning multiplier for this tier
  - `benefits`: Optional JSON with additional tier benefits
- Relationships:
  - Many-to-one with `companies`

## Database Triggers

1. **points_transaction_trigger**
   - Updates member's available_points, lifetime_points, redeemed_points, or expired_points
   - Runs after insert on points_transactions

2. **update_member_tier_trigger**
   - Automatically updates member's tier based on lifetime_points
   - Runs after update on loyalty_members

3. **expire_points_job**
   - Scheduled job that creates 'expire' transactions for points past expiration date
   - Runs on a schedule via pg_cron

## Row Level Security (RLS)

All tables have RLS policies ensuring that users can only access data for companies they administer:

- `companies`: Users can only view/edit their own companies
- `loyalty_members`: Users can only view/edit members of their companies
- `rewards`, `tier_definitions`, etc.: Users can only view/edit records for their companies

This ensures proper multi-tenancy isolation between different businesses using the platform.
