# Auth Re-enablement Testing Plan

## Overview
This document outlines the comprehensive testing strategy for safely re-enabling authentication in the Loyal app after it was temporarily disabled for debugging.

## Current State
- Authentication is **DISABLED** (`DISABLE_AUTH_FOR_DEBUG = true`)
- All protected routes are accessible without login
- Cypress tests are configured to work with disabled auth

## Tools Created
1. **`scripts/toggle-auth.sh`** - Safely enable/disable auth
2. **`scripts/test-auth-system.sh`** - Comprehensive auth validation
3. **`cypress/support/auth-commands.ts`** - Enhanced Cypress auth commands
4. **`scripts/auth-reactivation-checklist.md`** - Step-by-step checklist

## Pre-Enablement Tests

### 1. Environment Validation
```bash
# Check environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY

# Test Supabase connection
node debug-auth.js
```

### 2. Current System Test
```bash
# Test current system (auth disabled)
npm run dev
./scripts/test-auth-system.sh

# Run existing tests
npm run test:e2e
```

### 3. Database Verification
- Ensure test user exists in Supabase Auth
- Verify RLS policies are properly configured
- Check company data exists and is accessible

## Auth Enablement Process

### Step 1: Enable Auth
```bash
# Enable authentication
./scripts/toggle-auth.sh enable

# Verify status
./scripts/toggle-auth.sh status
```

### Step 2: Immediate Testing
```bash
# Test the system immediately
./scripts/test-auth-system.sh

# Manual browser tests:
# 1. Clear cookies/localStorage
# 2. Try accessing /dashboard (should redirect to login)
# 3. Test login flow
```

### Step 3: Update Tests
```bash
# Update Cypress tests to use real auth
# Tests will now use cy.smartLogin() which detects auth status
npm run test:e2e
```

## Test Scenarios

### Happy Path Tests
1. **Login Flow**
   - Visit `/login`
   - Enter valid credentials
   - Redirected to `/dashboard`
   - Session persists across page refreshes

2. **Protected Routes**
   - All routes in `protectedRoutes` array redirect to login when not authenticated
   - After login, can access all protected routes

3. **Logout Flow**
   - Logout clears session
   - Redirected to login page
   - Cannot access protected routes after logout

### Edge Case Tests
1. **Direct URL Access**
   - Accessing `/dashboard` directly when not logged in
   - Should redirect to `/login?redirectTo=/dashboard`
   - After login, should redirect back to originally requested page

2. **Session Expiry**
   - Token expires naturally
   - User gets redirected to login
   - Can login again successfully

3. **Multiple Tabs**
   - Login in one tab
   - Other tabs should recognize the session
   - Logout in one tab should affect all tabs

4. **Browser Refresh**
   - Session persists across refreshes
   - User stays logged in

### Error Scenarios
1. **Invalid Credentials**
   - Wrong password shows error
   - Wrong email shows error
   - Form validation works

2. **Network Errors**
   - App handles network failures gracefully
   - Shows appropriate error messages

3. **Redirect Loops**
   - Middleware prevents infinite redirects
   - Shows error after too many redirects

## Automated Testing

### Cypress Test Updates
The new `auth-commands.ts` provides:
- `cy.smartLogin()` - Adapts to auth enabled/disabled
- `cy.loginReal()` - Real login for when auth is enabled
- `cy.isAuthEnabled()` - Detects current auth status
- `cy.checkAuthStatus()` - Checks current session

### Example Updated Test
```typescript
describe('Dashboard (Auth Enabled)', () => {
  beforeEach(() => {
    cy.smartLogin(); // Automatically adapts to auth status
  });

  it('should require authentication', () => {
    cy.isAuthEnabled().then((authEnabled) => {
      if (authEnabled) {
        // Test protected route behavior
        cy.clearCookies();
        cy.visit('/dashboard');
        cy.url().should('include', '/login');
      }
    });
  });
});
```

## Rollback Plan

If issues are encountered:

### Immediate Rollback
```bash
# Disable auth immediately
./scripts/toggle-auth.sh disable
```

### Debug and Fix
1. Check logs for specific errors
2. Test individual components
3. Verify Supabase configuration
4. Check middleware logic

### Re-attempt
```bash
# After fixes, try again
./scripts/toggle-auth.sh enable
./scripts/test-auth-system.sh
```

## Success Criteria

### Technical Requirements
- [ ] All protected routes require authentication
- [ ] Login/logout flow works seamlessly
- [ ] No infinite redirect loops
- [ ] Session persistence works correctly
- [ ] All Cypress tests pass
- [ ] No console errors related to auth

### User Experience Requirements
- [ ] Smooth login experience
- [ ] Proper error messages for failed login
- [ ] Redirects work as expected
- [ ] Multiple tab behavior is correct
- [ ] Performance is not degraded

## Monitoring

After enabling auth, monitor:
1. **Error Rates** - Watch for auth-related errors
2. **Performance** - Ensure auth checks don't slow down the app
3. **User Feedback** - Any UX issues with login flow
4. **Logs** - Check for redirect loops or auth failures

## Quick Reference Commands

```bash
# Check auth status
./scripts/toggle-auth.sh status

# Enable auth
./scripts/toggle-auth.sh enable

# Test system
./scripts/test-auth-system.sh

# Disable auth (if needed)
./scripts/toggle-auth.sh disable

# Run tests
npm run test:e2e

# Create test user (if needed)
# Use Supabase dashboard or auth flow test
```

## Notes

- The middleware includes loop detection to prevent infinite redirects
- Cypress tests detect auth status automatically
- All scripts create backups before making changes
- Environment variables must be properly set
- Test user credentials should be available for testing
