# Authentication Hook Migration Guide

## Overview

This document provides guidance for migrating from the legacy `useAuth` hook in `auth.ts` to the new `useEnhancedAuth` hook in `use-enhanced-auth.ts`.

## Why Migrate?

The new `useEnhancedAuth` hook offers several advantages:

1. **React Query Integration**: Uses React Query for efficient caching and state management
2. **Improved Performance**: Reduces redundant network calls with optimized caching
3. **Better Error Handling**: More consistent error handling and retry policies
4. **Enhanced Session Persistence**: Better handling of auth state changes
5. **TypeScript Support**: Fully typed with proper interfaces
6. **Simplified API**: Cleaner, more consistent API for auth operations

## Migration Steps

### 1. Import Change

Replace:
```typescript
import { useAuth } from '@/hooks/auth'
```

With:
```typescript
import { useEnhancedAuth } from '@/hooks/use-enhanced-auth'
```

### 2. Hook Usage

The basic usage remains similar:

```typescript
// Before
const { user, session, isLoading, error } = useAuth()

// After
const { user, session, isLoading, error } = useEnhancedAuth()
```

### 3. Auth Operations

Authentication operations have the same signature but return a structured response:

```typescript
// Before
const result = await signIn(email, password)

// After
const { success, error } = await signIn(email, password)
if (success) {
  // Handle successful sign-in
} else {
  // Handle error with error message
}
```

### 4. Redirects

For redirects, use the hook parameters:

```typescript
// Before
useAuth({ redirectTo: '/dashboard' })

// After
useEnhancedAuth({ redirectTo: '/dashboard' })
```

### 5. Company Metadata

The enhanced hook automatically handles company metadata processing on sign-in and sign-up:

```typescript
// Before (manual company creation)
await signUp(email, password)
await createCompany(companyName)

// After (automatic company handling)
await signUp(email, password, companyName, businessType)
```

## Common Patterns

### Protected Routes

```typescript
const { user, isLoading } = useEnhancedAuth({ redirectTo: '/login' })

if (isLoading) {
  return <LoadingSpinner />
}

// User is guaranteed to be available here
return <ProtectedContent user={user} />
```

### Login Form

```typescript
const { signIn } = useEnhancedAuth({ redirectTo: '/dashboard', redirectIfFound: true })

const handleSubmit = async (e) => {
  e.preventDefault()
  const { success, error } = await signIn(email, password)
  
  if (!success) {
    setErrorMessage(error)
  }
}
```

## Testing

After migration, test the following flows:

1. Sign-in
2. Sign-up
3. Sign-out
4. Protected route access
5. Role-based access control
6. Session persistence across page refreshes

## Need Help?

If you encounter any issues during migration, please refer to the enhanced auth hook implementation or contact the development team.
