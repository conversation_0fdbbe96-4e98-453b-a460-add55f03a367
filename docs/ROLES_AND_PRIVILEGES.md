# Loyal Rewards System - Roles and Privileges

This document outlines the different user roles in the Loyal Rewards System and their respective privileges.

## Role Hierarchy

The system has the following roles in descending order of privileges:

1. **App Admin** - System-level administrators (Loyal team)
2. **Business Admin** - Business owners and managers
3. **Cashier** - Staff responsible for day-to-day operations
4. **Member** - Loyalty program members

## Role Definitions and Privileges

### 1. App Admin

App Administrators have full system access and can manage all aspects of the platform.

**Privileges:**
- Full access to all system features and data
- Create, update, and delete companies
- Manage system-wide configurations
- Access to system analytics and metrics
- User management across all companies
- Database administration
- API access management

### 2. Business Admin

Business Administrators manage their specific company's loyalty program.

**Privileges:**
- Dashboard access with company analytics
- Member management (view, add, edit, delete)
- Transaction management (view, add, process)
- Reward management (create, edit, delete)
- Tier management (create, edit, delete)
- Company settings configuration
- Staff management (add/remove cashiers)
- View reports and analytics
- Access to company API keys

### 3. Cashier

Cashiers handle day-to-day operations with customers, including processing transactions and managing member information.

**Privileges:**
- Limited dashboard access (daily metrics only)
- Member management:
  - View member information
  - Add new members
  - Edit basic member information
  - Search for members
- Transaction management:
  - Process new transactions
  - View transaction history
  - Apply rewards during transactions
  - Process receipt uploads
- Reward redemption:
  - View available rewards
  - Process reward redemptions
- No access to:
  - Company settings
  - Reward/tier configuration
  - Staff management
  - Financial reports

### 4. Member

Members are the end-users of the loyalty program.

**Privileges:**
- View personal profile
- View point balance and history
- View available rewards
- View tier status and benefits
- Redeem rewards
- View transaction history
- Update personal information

## Implementation Details

### Database Structure

The role-based access control is implemented through the `company_administrators` table with the following structure:

```sql
CREATE TABLE company_administrators (
  id UUID PRIMARY KEY,
  administrator_id UUID REFERENCES administrators(id),
  company_id UUID REFERENCES companies(id),
  role TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID
);
```

### Available Roles

The following role values are recognized by the system:

- `OWNER` - Business Admin with full company access
- `CASHIER` - Limited access for day-to-day operations
- `VIEWER` - Read-only access to company data (optional role)

### Access Control Implementation

Role-based access control is enforced through:

1. **Frontend UI restrictions** - Hiding UI elements based on user role
2. **API middleware** - Validating user permissions before processing requests
3. **Database RLS policies** - Row-level security in Supabase

## Recommended Security Practices

1. Always assign the minimum required privileges for each role
2. Regularly audit user access and permissions
3. Implement proper logging for sensitive operations
4. Rotate API keys and credentials regularly
5. Review and update this document as the system evolves
