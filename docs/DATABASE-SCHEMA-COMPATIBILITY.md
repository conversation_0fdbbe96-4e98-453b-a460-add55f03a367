# Database Schema Compatibility Issue

## Issue

We encountered an error when trying to validate members for reward redemption:

```
Member lookup failed: column loyalty_members.available_points does not exist
```

This occurs because the database schema has different column names across multiple tables:

1. In the `members` table, points are stored in `available_points`
2. In the `loyalty_members` table, points are stored in `points` (or another field name)

## Solution

We implemented a two-part solution to ensure robust operation:

### 1. Schema-Agnostic Member Lookup

- Modified the AI validation endpoint to use flexible field mapping
- First tries to find the member in the `members` table
- If not found, tries the `loyalty_members` table with a generic `select *` query
- Maps different field names to a consistent structure

### 2. Client-Side Validation Fallback

- Created a new endpoint `/api/redemptions/validate-client` that:
  - Uses simpler field detection logic
  - Dynamically detects available fields in the result
  - Works with any column naming convention
  - Provides deterministic eligibility calculation

- The React Query hook automatically tries this fallback endpoint if the AI validation fails

## Testing

A comprehensive test script (`test-validation-system.sh`) has been created to:
- Test the debug endpoint to understand table schemas
- Test both the AI and client-side validation endpoints
- Compare results between the two approaches

## Next Steps

1. Long-term solution: Normalize the database schema to use consistent column names
2. Update database migrations to reflect the new schema
3. Update all code to use the normalized field names

## How to Run Tests

```bash
./test-validation-system.sh
```
