# Enhanced Authentication System Documentation

## Overview

The Loyal application uses an enhanced authentication system built on Supabase Auth with React Query for efficient caching, improved error handling, and optimized session persistence. This document provides a comprehensive guide to the authentication system for developers.

## Key Components

### 1. Enhanced Auth Hook (`use-enhanced-auth.ts`)

The central authentication hook that provides user data, session information, and authentication methods throughout the application.

```typescript
// Example usage
import { useEnhancedAuth } from '@/hooks/use-enhanced-auth'

function MyComponent() {
  const { user, isLoading, signIn, signOut } = useEnhancedAuth()
  
  // Use authentication data and methods
}
```

#### Features:

- **React Query Integration**: Uses React Query for efficient caching and automatic refetching
- **Optimized Session Retrieval**: Prioritizes `getSession()` for speed (local cache) before `getUser()` for validation
- **Persistent User ID Storage**: Stores user ID in localStorage/sessionStorage for quick access by dependent hooks
- **Improved Error Handling**: Comprehensive error handling with retry logic
- **Session Persistence**: Enhanced session persistence across page reloads and navigation
- **Cache Invalidation**: Intelligent cache invalidation on auth state changes

### 2. Server-Side Authentication (`auth-server.ts`)

Handles server-side authentication for API routes and server components using the Supabase SSR package.

```typescript
// Example usage in API route
import { getServerSession } from '@/lib/auth-server'

export async function GET(request: Request) {
  const session = await getServerSession()
  // Use session data
}
```

#### Features:

- **Cookie-Based Authentication**: Parses session token from cookies for user identification
- **Service Role Access**: Uses service role client to bypass RLS for privileged endpoints
- **Standardized Session Access**: Consistent method for retrieving session data

### 3. User Role API

Optimized API for retrieving and validating user roles.

```typescript
// Example usage
const { data: userRole } = useUserRoleQuery()
```

#### Features:

- **Cached Role Data**: Efficiently caches role information
- **Automatic Invalidation**: Invalidates cache when authentication state changes
- **Role-Based Access Control**: Simplifies implementing permission checks

## Authentication Flow

1. **Initialization**:
   - On application load, the enhanced auth hook initializes
   - First checks for cached session using `getSession()`
   - If session exists, stores user ID in localStorage/sessionStorage
   - Then validates session with `getUser()` network call
   - Sets up auth state change listener

2. **Sign In**:
   - User submits credentials
   - Credentials validated with Supabase Auth
   - On success:
     - Auth state updated
     - React Query cache invalidated for auth-dependent queries
     - User redirected to appropriate page

3. **Session Persistence**:
   - Session maintained via Supabase's cookie-based auth
   - User ID stored in localStorage/sessionStorage for quick access
   - Session validated on page loads and navigation

4. **Sign Out**:
   - User session terminated with Supabase Auth
   - Local storage cleared
   - React Query cache cleared
   - User redirected to login page

## Caching Strategy

The authentication system uses a multi-layered caching approach:

1. **Fast Local Access**:
   - User ID stored in localStorage/sessionStorage
   - Provides immediate access for dependent hooks before full auth initialization

2. **React Query Cache**:
   - Auth data cached with React Query
   - Configurable staleTime and cacheTime
   - Automatic background refetching

3. **Cache Invalidation**:
   - Auth cache invalidated on sign in/out
   - Related queries (user role, company data) invalidated when auth state changes
   - Optimistic updates for better UX

## Error Handling

The enhanced authentication system includes robust error handling:

1. **Retry Logic**:
   - Automatic retries for transient network failures
   - Exponential backoff to prevent overwhelming the server

2. **Error States**:
   - Detailed error states for different failure scenarios
   - User-friendly error messages

3. **Fallback Mechanisms**:
   - Graceful degradation when authentication services are unavailable
   - Clear error boundaries to prevent cascading failures

## Best Practices

1. **Always use the enhanced auth hook**:
   ```typescript
   import { useEnhancedAuth } from '@/hooks/use-enhanced-auth'
   ```

2. **Handle loading states**:
   ```typescript
   const { user, isLoading } = useEnhancedAuth()
   
   if (isLoading) {
     return <LoadingSpinner />
   }
   ```

3. **Check authentication before accessing protected resources**:
   ```typescript
   const { user } = useEnhancedAuth()
   
   if (!user) {
     return <NotAuthorizedMessage />
   }
   ```

4. **Use React Query's built-in features**:
   - Take advantage of `isLoading`, `isError`, and `error` properties
   - Use `enabled` option to control dependent queries

5. **Server-side authentication**:
   - Always use the standardized `getServerSession()` method
   - Handle cases where session might be null

## Security Considerations

1. **Token Security**:
   - Never expose Supabase service role key in client-side code
   - Use appropriate token refresh mechanisms

2. **Role-Based Access Control**:
   - Implement both client-side and server-side permission checks
   - Never rely solely on UI hiding for security

3. **Session Management**:
   - Use appropriate session timeouts
   - Implement secure cookie handling

## Troubleshooting

Common issues and their solutions:

1. **Authentication state not persisting**:
   - Check browser cookie settings
   - Verify Supabase configuration

2. **Slow authentication initialization**:
   - Ensure dependent hooks use the stored user ID when possible
   - Check for unnecessary network calls

3. **Permission issues**:
   - Verify user role assignments
   - Check RLS policies in Supabase

## Migration from Legacy Auth

If you encounter code still using the legacy auth hook, follow the [Auth Migration Guide](./auth-migration-guide.md) to update it to the enhanced authentication system.
