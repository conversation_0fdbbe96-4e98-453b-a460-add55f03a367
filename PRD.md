# Loyal MVP PRD

---

## 1. Purpose & Goals
**Purpose:**
Enable Ethiopian businesses to run loyalty programs via web‑admin and Telegram bots—no member signup friction, track spend→points→tiers→rewards.

**Business Goals:**
- Acquire first 5 salon clients in 8 weeks
- Onboard ≥200 loyalty members
- Achieve 20% reward‐redemption rate
- Demonstrate payback ROI to salons

---

## 2. User Personas
1. **Business Admin**
   - Uses web UI to configure program, view dashboard, manage members/rewards.
   - Tech level: basic computer skills.

2. **Cashier/User on Telegram**
   - Uploads receipts via Telegram bot.
   - Tech level: familiar with Telegram.

3. **Loyalty Member**
   - Signs up & interacts entirely in Telegram.
   - Views points, redeems rewards via bot.

---

## 3. Key Use‑Cases & User Stories

### A. Member Onboarding
- As a member, I fill an n8n form in Telegram → I’m created/updated in the system via my `telegram_chat_id`.
- As a member, I can supply a loyalty code if registering outside Telegram.

### B. Earning Points
- As a cashier, I send receipt total + loyalty ID → member earns X points (1 ETB = 1 point).

### C. Points Expiry & Tiers
- Points auto‑expire per rules (e.g. 12 months).
- Member’s tier (Silver/Gold/Platinum) updates on lifetime points.

### D. Rewards Management
- As an admin, I create %‑off or fixed‑amount rewards with 4‑char codes (e.g. HA12).
- As a member, I redeem via Telegram, decreasing my available points.

### E. Dashboard & Reporting
- As an admin, I view KPIs: memberships, points issued/redeemed, upcoming expirations.

---

## 4. MVP Feature List & Priority

| Priority | Feature                                      |
|:--------:|----------------------------------------------|
| **P0**   | Admin auth (Supabase), multi‑tenant RLS      |
| **P0**   | Telegram bot → `/api/members` upsert         |
| **P0**   | Receipt upload → points_transactions triggers |
| **P0**   | `loyalty_members` schema (UUID PK + loyalty_id + nullable chat_id) |
| **P1**   | Rewards CRUD (UUID PK + 4‑char `code`)       |
| **P1**   | Redemption flow with `check_same_company` & `redeem_points` |
| **P2**   | Tier definitions & auto‑tier trigger         |
| **P2**   | Points‑expiry job (`expire_points_with_logging`) |
| **P3**   | Basic dashboard: members, points graph       |
| **P4**   | Member notifications (via Telegram)          |

---

## 5. Acceptance Criteria

- **Member upsert**
  - Given valid `telegram_chat_id` or `loyalty_id`, record created/updated in `loyalty_members`.
- **Earning points**
  - POST `/receipts` → creates `points_transactions`; triggers update of `lifetime_points`, `available_points`.
- **Reward code**
  - Only accepts two uppercase letters + two digits, unique.
- **Redemption**
  - Member can’t redeem > available_points; redemption logged in `reward_redemptions`.
- **Dashboard**
  - Displays real‑time counts for members, points issued/redeemed, expirations.

---

## 6. UI Wireframes (High‑Level)

1. **Login** (admin email/password)
2. **Dashboard**
   - KPI cards: Members, Points Issued, Points Redeemed, Expiring Soon
   - Line chart: points over time
3. **Members**
   - Table: Name, loyalty_id, tier, available_points
   - Search & filter
4. **Receipts**
   - Form: loyalty_id, amount, date
5. **Rewards**
   - List: code, type, value, expiry
   - “New Reward” modal
6. **Tiers**
   - List/edit thresholds
7. **Settings**
   - Company profile, program rules

---

## 7. Tech & Integration Stack

- **Full-stack framework**: Next.js (App Router & API Routes/Edge Functions)
- **UI components**: shadcn + Tailwind CSS
- **DB**: Supabase (Postgres + RLS + triggers + pg_cron)
  - Direct database access via postgres MCP tool or SQL queries
  - Client access via `supabase` client from `lib/supabase.ts`
- **Auth**: Supabase Auth (admin only via Next.js middleware)
- **Data access**: Prisma ORM or direct Supabase client
- **Bots & Automation**: n8n + Telegram Bot API
- **Jobs & Scheduling**: pg_cron + Supabase Edge Functions
- **Monitoring**: Sentry + Supabase logs
- **State management**: TanStack Query for server-state, optional Zustand for global UI-state
- **Deployment & CI/CD**: Vercel (with preview deployments) & GitHub Actions
- **Testing & QA**: Jest + React Testing Library, Cypress E2E
- **Code quality**: ESLint & Prettier

---

## 8. Timeline & Milestones

| Sprint | Duration  | Goals                                     |
|:------:|:---------:|-------------------------------------------|
| **1**  | 2 weeks   | Schema + RLS + Admin auth + Members API   |
| **2**  | 2 weeks   | Receipt → points flow + triggers + tests  |
| **3**  | 2 weeks   | Rewards CRUD + redemption + bot workflows |
| **4**  | 2 weeks   | Tiers, expiry job, dashboard UI & polish  |

_Total: 8 weeks to MVP ready._

---

## 9. Success Metrics

- Onboarded salons (target ≥5)
- Active loyalty members (≥200)
- Points issued vs redeemed ratio
- Monthly active users (Telegram interactions)
- Feature usage (bot flows vs web UI)

---

## 10. Data Fetching & Performance Requirements

### API Efficiency Requirements
- **Query Deduplication**: All API requests must use React Query or equivalent caching mechanism to prevent redundant requests
- **Database Credits**: Keep Supabase database operations within free tier limits (500M RPS/month)
- **Resource Optimization**: No component should make duplicate calls to the same endpoint

### Data Fetching Patterns
1. **Use React Query Hooks**:
   - All data fetching must happen through centralized React Query hooks
   - Hooks should be organized by entity (members, transactions, rewards)
   - Common staleTime of 60s minimum for all queries

2. **Lookup Tables for Related Data**:
   - Create lookup maps for related entities instead of separate API calls (e.g., member info for each transaction)
   - Example: `const memberMap = new Map(); members.forEach(m => memberMap.set(m.id, m));`

3. **Pagination & Limits**:
   - Always use pagination or limits when fetching large datasets
   - Dashboard queries should limit to 5-10 most recent items
   - Total counts should use direct count endpoints, not fetching all records

4. **Response Shaping**:
   - API endpoints should support field selection to reduce payload size
   - Consider implementing GraphQL for complex data requirements

### Performance Monitoring
- Monitor API usage with Supabase dashboard
- Set up alerts for abnormal usage patterns
- Implement performance measurement with `performance.now()` for critical paths

---
