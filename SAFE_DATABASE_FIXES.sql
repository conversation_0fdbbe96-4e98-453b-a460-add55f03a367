-- =====================================================
-- SAFE DATABASE FIXES - HANDLES EXISTING CONSTRAINTS
-- =====================================================
-- This version safely handles existing constraints and objects
-- Run these sections one by one, checking for errors

-- =====================================================
-- STEP 1: BACKUP YOUR DATA (CRITICAL - ALWAYS RUN FIRST!)
-- =====================================================
DO $$
BEGIN
    -- Only create backup if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'loyalty_members_backup') THEN
        CREATE TABLE loyalty_members_backup AS SELECT * FROM loyalty_members;
        RAISE NOTICE 'Created loyalty_members_backup';
    ELSE
        RAISE NOTICE 'loyalty_members_backup already exists, skipping';
    END IF;

    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'points_transactions_backup') THEN
        CREATE TABLE points_transactions_backup AS SELECT * FROM points_transactions;
        RAISE NOTICE 'Created points_transactions_backup';
    ELSE
        RAISE NOTICE 'points_transactions_backup already exists, skipping';
    END IF;

    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'reward_redemptions_backup') THEN
        CREATE TABLE reward_redemptions_backup AS SELECT * FROM reward_redemptions;
        RAISE NOTICE 'Created reward_redemptions_backup';
    ELSE
        RAISE NOTICE 'reward_redemptions_backup already exists, skipping';
    END IF;
END $$;

-- =====================================================
-- STEP 2: ANALYZE CURRENT DATA ISSUES (OPTIONAL - FOR INFORMATION)
-- =====================================================
-- Check for duplicate members
SELECT
    'DUPLICATE MEMBERS CHECK' as check_type,
    name, email, phone_number, company_id, COUNT(*) as duplicate_count,
    ARRAY_AGG(id) as member_ids, ARRAY_AGG(loyalty_id) as loyalty_ids
FROM loyalty_members
GROUP BY name, email, phone_number, company_id
HAVING COUNT(*) > 1;

-- Check points consistency
SELECT
    'POINTS CONSISTENCY CHECK' as check_type,
    lm.loyalty_id, lm.name,
    lm.lifetime_points as stored_lifetime,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0) as calculated_lifetime,
    lm.redeemed_points as stored_redeemed,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0) as calculated_redeemed
FROM loyalty_members lm
LEFT JOIN points_transactions pt ON lm.id = pt.member_id
GROUP BY lm.id, lm.loyalty_id, lm.name, lm.lifetime_points, lm.redeemed_points
HAVING lm.lifetime_points != COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0)
    OR lm.redeemed_points != COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0);

-- =====================================================
-- STEP 3: CREATE NEW CLEAN VIEWS (MAIN FIX)
-- =====================================================
-- Create the new member points view (single source of truth)
CREATE OR REPLACE VIEW member_points_live AS
SELECT
    lm.id, lm.loyalty_id, lm.name, lm.email, lm.phone_number, lm.company_id,
    lm.loyalty_tier, lm.registration_date, lm.profile_image_url,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0) as lifetime_points,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0) as redeemed_points,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EXPIRE' THEN ABS(pt.points_change) ELSE 0 END), 0) as expired_points,
    COALESCE(SUM(pt.points_change), 0) as available_points,
    MAX(pt.created_at) as last_transaction_date,
    COUNT(pt.id) as total_transactions
FROM loyalty_members lm
LEFT JOIN points_transactions pt ON lm.id = pt.member_id
GROUP BY lm.id, lm.loyalty_id, lm.name, lm.email, lm.phone_number, lm.company_id, lm.loyalty_tier, lm.registration_date, lm.profile_image_url;

-- Create the new dashboard metrics view (consolidated)
CREATE OR REPLACE VIEW dashboard_metrics_live AS
WITH transaction_stats AS (
    SELECT
        pt.company_id,
        COUNT(DISTINCT pt.member_id) as unique_members,
        SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END) as total_lifetime_points,
        SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END) as total_redeemed_points,
        SUM(CASE WHEN pt.transaction_type = 'EXPIRE' THEN ABS(pt.points_change) ELSE 0 END) as total_expired_points,
        SUM(pt.points_change) as total_available_points,
        COUNT(*) as total_transactions,
        COUNT(CASE WHEN pt.transaction_type = 'EARN' THEN 1 END) as earn_transactions,
        COUNT(CASE WHEN pt.transaction_type = 'REDEEM' THEN 1 END) as redeem_transactions,
        COUNT(DISTINCT CASE WHEN pt.created_at >= NOW() - INTERVAL '30 days' THEN pt.member_id END) as active_members_30d,
        MAX(pt.created_at) as last_transaction_date
    FROM points_transactions pt
    GROUP BY pt.company_id
)
SELECT
    c.id as company_id, c.name as company_name,
    COUNT(DISTINCT lm.id) as total_members,
    COALESCE(ts.active_members_30d, 0) as active_members_30d,
    COALESCE(ts.total_lifetime_points, 0) as total_lifetime_points,
    COALESCE(ts.total_redeemed_points, 0) as total_redeemed_points,
    COALESCE(ts.total_expired_points, 0) as total_expired_points,
    COALESCE(ts.total_available_points, 0) as total_available_points,
    COALESCE(ts.total_transactions, 0) as total_transactions,
    COALESCE(ts.earn_transactions, 0) as earn_transactions,
    COALESCE(ts.redeem_transactions, 0) as redeem_transactions,
    ts.last_transaction_date, NOW() as last_updated,
    CASE WHEN ts.total_lifetime_points > 0 THEN
        ROUND((ts.total_redeemed_points::NUMERIC / ts.total_lifetime_points::NUMERIC) * 100, 2)
    ELSE 0 END as redemption_rate_percentage
FROM companies c
LEFT JOIN loyalty_members lm ON lm.company_id = c.id
LEFT JOIN transaction_stats ts ON ts.company_id = c.id
GROUP BY c.id, c.name, ts.active_members_30d, ts.total_lifetime_points, ts.total_redeemed_points,
         ts.total_expired_points, ts.total_available_points, ts.total_transactions,
         ts.earn_transactions, ts.redeem_transactions, ts.last_transaction_date;

-- =====================================================
-- STEP 3.5: UPDATE DATABASE FUNCTIONS TO USE NEW VIEWS
-- =====================================================
-- Update the get_top_members function to use member_points_live for consistency
CREATE OR REPLACE FUNCTION public.get_top_members(p_company_id uuid, p_metric text, p_limit integer)
RETURNS json
LANGUAGE plpgsql
SET search_path TO 'public'
AS $function$
DECLARE
  result JSON;
BEGIN
  -- Validate metric parameter
  IF p_metric NOT IN ('lifetime_points', 'available_points', 'redeemed_points', 'redemption_count') THEN
    RAISE EXCEPTION 'Invalid metric parameter. Valid options are: lifetime_points, available_points, redeemed_points, redemption_count';
  END IF;

  -- Use member_points_live for consistent calculations
  IF p_metric = 'redemption_count' THEN
    WITH member_redemptions AS (
      SELECT
        mpl.id,
        mpl.name,
        mpl.email,
        mpl.loyalty_tier,
        mpl.redeemed_points,
        mpl.lifetime_points,
        mpl.profile_image_url
      FROM member_points_live mpl
      WHERE mpl.company_id = p_company_id
      ORDER BY mpl.redeemed_points DESC
      LIMIT p_limit
    )
    SELECT json_agg(
      json_build_object(
        'id', id,
        'name', name,
        'email', email,
        'loyalty_tier', loyalty_tier,
        'value', redeemed_points,
        'metric', 'redemption_count',
        'profile_image_url', profile_image_url,
        'lifetime_points', lifetime_points
      )
    ) INTO result
    FROM member_redemptions;
  ELSE
    -- For points-based metrics using member_points_live
    WITH member_points_data AS (
      SELECT
        mpl.id,
        mpl.name,
        mpl.email,
        mpl.loyalty_tier,
        mpl.lifetime_points,
        mpl.available_points,
        mpl.redeemed_points,
        mpl.profile_image_url
      FROM member_points_live mpl
      WHERE mpl.company_id = p_company_id
      ORDER BY
        CASE
          WHEN p_metric = 'lifetime_points' THEN mpl.lifetime_points
          WHEN p_metric = 'available_points' THEN mpl.available_points
          WHEN p_metric = 'redeemed_points' THEN mpl.redeemed_points
        END DESC NULLS LAST
      LIMIT p_limit
    )
    SELECT json_agg(
      json_build_object(
        'id', id,
        'name', name,
        'email', email,
        'loyalty_tier', loyalty_tier,
        'value',
          CASE
            WHEN p_metric = 'lifetime_points' THEN lifetime_points
            WHEN p_metric = 'available_points' THEN available_points
            WHEN p_metric = 'redeemed_points' THEN redeemed_points
          END,
        'metric', p_metric,
        'profile_image_url', profile_image_url,
        'lifetime_points', lifetime_points
      )
    ) INTO result
    FROM member_points_data;
  END IF;

  RETURN result;
END;
$function$;

-- =====================================================
-- STEP 4: ADD PERFORMANCE INDEXES (SAFE)
-- =====================================================
-- Note: These indexes are created WITHOUT CONCURRENTLY to avoid transaction block issues
-- If you need CONCURRENTLY (for production with heavy traffic), run these separately:

-- Create indexes (will skip if they already exist)
DROP INDEX IF EXISTS idx_points_transactions_member_created;
CREATE INDEX IF NOT EXISTS idx_points_transactions_member_created
ON points_transactions(member_id, created_at DESC);

DROP INDEX IF EXISTS idx_points_transactions_company_type;
CREATE INDEX IF NOT EXISTS idx_points_transactions_company_type
ON points_transactions(company_id, transaction_type);

DROP INDEX IF EXISTS idx_loyalty_members_company_reg;
CREATE INDEX IF NOT EXISTS idx_loyalty_members_company_reg
ON loyalty_members(company_id, registration_date DESC);

-- =====================================================
-- ALTERNATIVE: FOR PRODUCTION - RUN THESE SEPARATELY IF NEEDED
-- =====================================================
-- If you're running this on a production database with active traffic,
-- you may want to run these CONCURRENTLY commands separately, one by one:

/*
-- Run these individually in separate transactions if needed:
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_points_transactions_member_created_v2
ON points_transactions(member_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_points_transactions_company_type_v2
ON points_transactions(company_id, transaction_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_loyalty_members_company_reg_v2
ON loyalty_members(company_id, registration_date DESC);
*/

-- =====================================================
-- STEP 5: ADD CONSTRAINTS (SAFE - HANDLES EXISTING)
-- =====================================================
DO $$
BEGIN
    -- Check for unique_loyalty_id_per_company (might exist as constraint or index)
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'unique_loyalty_id_per_company'
        UNION
        SELECT 1 FROM pg_indexes WHERE indexname = 'unique_loyalty_id_per_company'
    ) THEN
        BEGIN
            ALTER TABLE loyalty_members
            ADD CONSTRAINT unique_loyalty_id_per_company
            UNIQUE (loyalty_id, company_id);
            RAISE NOTICE 'Added unique_loyalty_id_per_company constraint';
        EXCEPTION
            WHEN unique_violation THEN
                RAISE NOTICE 'Cannot add unique_loyalty_id_per_company - duplicate data exists. Clean up duplicates first.';
        END;
    ELSE
        RAISE NOTICE 'unique_loyalty_id_per_company already exists (as constraint or index), skipping';
    END IF;

    -- Check for unique_email_per_company (might exist as constraint or index)
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'unique_email_per_company'
        UNION
        SELECT 1 FROM pg_indexes WHERE indexname = 'unique_email_per_company'
    ) THEN
        BEGIN
            ALTER TABLE loyalty_members
            ADD CONSTRAINT unique_email_per_company
            UNIQUE (email, company_id);
            RAISE NOTICE 'Added unique_email_per_company constraint';
        EXCEPTION
            WHEN unique_violation THEN
                RAISE NOTICE 'Cannot add unique_email_per_company - duplicate data exists. Clean up duplicates first.';
        END;
    ELSE
        RAISE NOTICE 'unique_email_per_company already exists (as constraint or index), skipping';
    END IF;

    -- Check for valid_transaction_type constraint
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'valid_transaction_type'
        AND conrelid = 'points_transactions'::regclass
    ) THEN
        -- Check if similar constraint already exists
        IF NOT EXISTS (
            SELECT 1 FROM pg_constraint
            WHERE conrelid = 'points_transactions'::regclass
            AND contype = 'c'
            AND pg_get_constraintdef(oid) LIKE '%transaction_type%'
        ) THEN
            BEGIN
                ALTER TABLE points_transactions
                ADD CONSTRAINT valid_transaction_type
                CHECK (transaction_type IN ('EARN', 'REDEEM', 'EXPIRE', 'ADJUST'));
                RAISE NOTICE 'Added valid_transaction_type constraint';
            EXCEPTION
                WHEN check_violation THEN
                    RAISE NOTICE 'Cannot add valid_transaction_type - invalid data exists. Clean up data first.';
            END;
        ELSE
            RAISE NOTICE 'Transaction type validation constraint already exists with different name, skipping';
        END IF;
    ELSE
        RAISE NOTICE 'valid_transaction_type constraint already exists, skipping';
    END IF;

    -- Check for non_zero_points_change constraint
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'non_zero_points_change'
        AND conrelid = 'points_transactions'::regclass
    ) THEN
        BEGIN
            ALTER TABLE points_transactions
            ADD CONSTRAINT non_zero_points_change
            CHECK (points_change != 0);
            RAISE NOTICE 'Added non_zero_points_change constraint';
        EXCEPTION
            WHEN check_violation THEN
                RAISE NOTICE 'Cannot add non_zero_points_change - zero point transactions exist. Review data first.';
        END;
    ELSE
        RAISE NOTICE 'non_zero_points_change constraint already exists, skipping';
    END IF;
END $$;

-- =====================================================
-- STEP 6: VALIDATION QUERIES (VERIFY EVERYTHING WORKS)
-- =====================================================
-- Test the new views
SELECT 'VIEW TEST: member_points_live' as test_name, COUNT(*) as record_count FROM member_points_live;
SELECT 'VIEW TEST: dashboard_metrics_live' as test_name, COUNT(*) as record_count FROM dashboard_metrics_live;

-- Verify consistency between views and tables
SELECT
    'CONSISTENCY CHECK' as test_name,
    'member_points_live' as source,
    COUNT(*) as member_count,
    SUM(lifetime_points) as total_lifetime,
    SUM(available_points) as total_available
FROM member_points_live
UNION ALL
SELECT
    'CONSISTENCY CHECK' as test_name,
    'points_transactions' as source,
    COUNT(DISTINCT member_id) as member_count,
    SUM(CASE WHEN transaction_type = 'EARN' THEN points_change ELSE 0 END) as total_lifetime,
    SUM(points_change) as total_available
FROM points_transactions;

-- Check for duplicates
SELECT
    'DUPLICATE CHECK' as test_name,
    COUNT(*) as total_members,
    COUNT(DISTINCT loyalty_id) as unique_loyalty_ids,
    COUNT(DISTINCT email) as unique_emails,
    CASE WHEN COUNT(*) = COUNT(DISTINCT loyalty_id) THEN 'PASS' ELSE 'FAIL' END as loyalty_id_test,
    CASE WHEN COUNT(*) = COUNT(DISTINCT email) THEN 'PASS' ELSE 'FAIL' END as email_test
FROM loyalty_members;

-- =====================================================
-- STEP 7: SUCCESS CONFIRMATION
-- =====================================================
SELECT
    '✅ DATABASE FIXES COMPLETED SUCCESSFULLY!' as status,
    COUNT(*) as total_members,
    SUM(available_points) as total_available_points,
    AVG(available_points)::INTEGER as avg_member_points,
    SUM(lifetime_points) as total_lifetime_points
FROM member_points_live;

-- Show a sample of the new data to verify it looks correct
SELECT
    '📊 SAMPLE DATA FROM NEW VIEW' as info,
    loyalty_id, name, available_points, lifetime_points, redeemed_points, expired_points
FROM member_points_live
WHERE available_points > 0
ORDER BY available_points DESC
LIMIT 5;

-- =====================================================
-- STEP 8: UPDATE GET_TOP_MEMBERS FUNCTION TO USE NEW VIEW
-- =====================================================
-- Create updated version of get_top_members function that uses member_points_live
CREATE OR REPLACE FUNCTION get_top_members_live(p_company_id uuid, p_metric text, p_limit integer)
RETURNS json
LANGUAGE plpgsql
AS $function$
DECLARE
  result json;
BEGIN
  -- Validate metric parameter
  IF p_metric NOT IN ('lifetime_points', 'available_points', 'redeemed_points', 'redemption_count') THEN
    RAISE EXCEPTION 'Invalid metric parameter. Valid options are: lifetime_points, available_points, redeemed_points, redemption_count';
  END IF;

  IF p_metric = 'redemption_count' THEN
    -- Handle redemption count metric using actual transaction counts
    SELECT json_agg(
      json_build_object(
        'id', mpl.id,
        'name', mpl.name,
        'email', mpl.email,
        'loyalty_tier', COALESCE(td.tier_name, mpl.loyalty_tier),
        'profile_image_url', mpl.profile_image_url,
        'lifetime_points', mpl.lifetime_points,
        'value', COALESCE(redemption_stats.redemption_count, 0),
        'metric', 'redemption_count'
      )
    ) INTO result
    FROM member_points_live mpl
    LEFT JOIN (
      SELECT
        member_id,
        COUNT(*) as redemption_count
      FROM points_transactions
      WHERE transaction_type = 'REDEEM'
        AND company_id = p_company_id
      GROUP BY member_id
    ) redemption_stats ON mpl.id = redemption_stats.member_id
    LEFT JOIN tier_definitions td ON td.company_id = p_company_id
      AND mpl.lifetime_points >= td.minimum_points
      AND td.id = (
        SELECT id FROM tier_definitions
        WHERE company_id = p_company_id
          AND mpl.lifetime_points >= minimum_points
        ORDER BY minimum_points DESC
        LIMIT 1
      )
    WHERE mpl.company_id = p_company_id
    ORDER BY COALESCE(redemption_stats.redemption_count, 0) DESC
    LIMIT p_limit;
  ELSE
    -- Handle points-based metrics using member_points_live view
    SELECT json_agg(
      json_build_object(
        'id', member_data.id,
        'name', member_data.name,
        'email', member_data.email,
        'loyalty_tier', member_data.loyalty_tier,
        'profile_image_url', member_data.profile_image_url,
        'lifetime_points', member_data.lifetime_points,
        'value', member_data.metric_value,
        'metric', p_metric
      )
    ) INTO result
    FROM (
      SELECT
        mpl.id,
        mpl.name,
        mpl.email,
        COALESCE(td.tier_name, mpl.loyalty_tier) as loyalty_tier,
        mpl.profile_image_url,
        mpl.lifetime_points,
        CASE
          WHEN p_metric = 'lifetime_points' THEN mpl.lifetime_points
          WHEN p_metric = 'available_points' THEN mpl.available_points
          WHEN p_metric = 'redeemed_points' THEN mpl.redeemed_points
          ELSE mpl.lifetime_points
        END as metric_value
      FROM member_points_live mpl
      LEFT JOIN tier_definitions td ON td.company_id = p_company_id
        AND mpl.lifetime_points >= td.minimum_points
        AND td.id = (
          SELECT id FROM tier_definitions
          WHERE company_id = p_company_id
            AND mpl.lifetime_points >= minimum_points
          ORDER BY minimum_points DESC
          LIMIT 1
        )
      WHERE mpl.company_id = p_company_id
      ORDER BY
        CASE
          WHEN p_metric = 'lifetime_points' THEN mpl.lifetime_points
          WHEN p_metric = 'available_points' THEN mpl.available_points
          WHEN p_metric = 'redeemed_points' THEN mpl.redeemed_points
          ELSE mpl.lifetime_points
        END DESC
      LIMIT p_limit
    ) member_data;
  END IF;

  RETURN COALESCE(result, '[]'::json);
END;
$function$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_top_members_live(uuid, text, integer) TO authenticated;
GRANT EXECUTE ON FUNCTION get_top_members_live(uuid, text, integer) TO service_role;

-- Add comment
COMMENT ON FUNCTION get_top_members_live(uuid, text, integer) IS
'Gets top members using member_points_live view for accurate, real-time points data. Supports lifetime_points, available_points, redeemed_points, and redemption_count metrics.';
