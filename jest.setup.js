import '@testing-library/jest-dom'
import { expect } from '@jest/globals'
import { server } from './test-utils/msw-handlers'

// Set up MSW handlers before tests
beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
// Clean up after tests
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

// Global timeout increase
jest.setTimeout(30000)

// Polyfills for TextEncoder/TextDecoder which are required by MSW
global.TextEncoder = require('util').TextEncoder
global.TextDecoder = require('util').TextDecoder

// Mock Next.js Request/Response for API route tests
global.Request = class Request {
  constructor(url, options = {}) {
    this.url = url
    this.method = options.method || 'GET'
    this.body = options.body || null
    this.headers = new Headers(options.headers || {})
  }
}

global.Response = class Response {
  constructor(body, options = {}) {
    this.body = body
    this.status = options.status || 200
    this.statusText = options.statusText || ''
    this.headers = new Headers(options.headers || {})
    this._bodyInit = body
  }

  json() {
    return Promise.resolve(JSON.parse(this.body))
  }
}

global.Headers = class Headers {
  constructor(init = {}) {
    this.headers = init
  }

  get(name) {
    return this.headers[name] || null
  }

  set(name, value) {
    this.headers[name] = value
  }
}

// Mock the NextRequest and NextResponse
jest.mock('next/server', () => {
  return {
    NextResponse: {
      json: (body, options) => new Response(JSON.stringify(body), options),
      redirect: (url) => new Response(null, { status: 302, headers: { Location: url } }),
    },
  }
})

// Mock fetch for testing with MSW
global.fetch = global.fetch || ((...args) => {});

// Mock window.matchMedia for next-themes
if (typeof window !== 'undefined') {
  window.matchMedia = window.matchMedia || function() {
    return {
      matches: false,
      addListener: function() {},
      removeListener: function() {},
      addEventListener: function() {},
      removeEventListener: function() {},
      dispatchEvent: function() {
        return true
      },
    }
  }
  
  // Mock ResizeObserver
  window.ResizeObserver = window.ResizeObserver || class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  }
  
  // Mock EventSource
  window.EventSource = window.EventSource || class EventSource {
    constructor() {
      this.addEventListener = jest.fn()
      this.removeEventListener = jest.fn()
      this.close = jest.fn()
    }
  }
  
  // Mock requestAnimationFrame
  window.requestAnimationFrame = 
    window.requestAnimationFrame || (callback => setTimeout(callback, 0))
    
  // Mock scrollTo
  window.scrollTo = window.scrollTo || jest.fn()
  
  // Mock document functions for shadcn
  document.createRange = document.createRange || (() => {
    return {
      setStart: () => {},
      setEnd: () => {},
      commonAncestorContainer: {
        nodeName: 'BODY',
        ownerDocument: document,
      },
      getBoundingClientRect: () => ({ 
        left: 0, 
        top: 0, 
        right: 0, 
        bottom: 0, 
        width: 0, 
        height: 0 
      }),
    }
  })
}

// Mock for IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor(callback) {
    this.callback = callback
  }
  
  observe() {
    // Trigger with 100% visibility immediately for tests
    this.callback([{ isIntersecting: true, intersectionRatio: 1 }], this)
  }
  
  unobserve() {}
  disconnect() {}
}

// Add global console.error override to prevent React Testing Library warnings
// from failing tests while still logging them to the console
const originalConsoleError = console.error
console.error = (...args) => {
  // Suppress React act() warnings - they're expected with React Query
  if (
    args[0]?.includes?.('Warning:') && 
    (
      /Warning.*not wrapped in act/.test(args[0] || '') ||
      args[0].includes('An update to TestComponent inside a test was not wrapped in act')
    )
  ) {
    return
  }
  
  // Otherwise, pass through to the original console.error
  originalConsoleError(...args)
}
