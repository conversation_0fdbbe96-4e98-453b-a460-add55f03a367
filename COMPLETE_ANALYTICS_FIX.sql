-- ===============================================================================
-- FINAL ANALYTICS FIX - Complete solution for all analytics issues
-- ===============================================================================

-- First, drop existing functions with incompatible signatures
DROP FUNCTION IF EXISTS get_business_performance(uuid);
DROP FUNCTION IF EXISTS get_template_analytics(uuid);
DROP FUNCTION IF EXISTS get_recent_activity(uuid, integer);
DROP FUNCTION IF EXISTS get_recent_activity(uuid);

-- 1. Fix get_business_performance function (removes ambiguous column error)
CREATE OR REPLACE FUNCTION get_business_performance(input_company_id UUID)
RETURNS TABLE (
    item_id UUID,
    item_name TEXT,
    item_category TEXT,
    standard_price NUMERIC,
    total_sales BIGINT,
    total_quantity_sold NUMERIC,
    total_revenue NUMERIC,
    avg_selling_price NUMERIC,
    unique_customers BIGINT,
    last_sold_date TIMESTAMPTZ,
    popularity_score NUMERIC,
    revenue_share_in_category NUMERIC,
    pricing_strategy TEXT
)
LANGUAGE SQL
SECURITY DEFINER
AS $$
    WITH item_stats AS (
        SELECT
            bi.id as item_id,
            bi.item_name,
            bi.item_category,
            bi.standard_price,
            COUNT(ri.id) as total_sales,
            COALESCE(SUM(ri.quantity), 0) as total_quantity_sold,
            COALESCE(SUM(ri.total_price), 0) as total_revenue,
            COALESCE(AVG(ri.unit_price), bi.standard_price) as avg_selling_price,
            COUNT(DISTINCT r.member_id) as unique_customers,
            MAX(r.created_at) as last_sold_date,
            (COUNT(ri.id)::NUMERIC * 0.6 + COUNT(DISTINCT r.member_id)::NUMERIC * 0.4) as popularity_score
        FROM business_items bi
        LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
        LEFT JOIN receipts r ON ri.receipt_id = r.id
        WHERE bi.company_id = input_company_id AND bi.is_active = true
        GROUP BY bi.id, bi.item_name, bi.item_category, bi.standard_price
    ),
    category_stats AS (
        SELECT
            item_category,
            SUM(total_revenue) as category_revenue
        FROM item_stats
        GROUP BY item_category
    )
    SELECT
        ist.item_id,
        ist.item_name,
        ist.item_category,
        ist.standard_price,
        ist.total_sales,
        ist.total_quantity_sold,
        ist.total_revenue,
        ist.avg_selling_price,
        ist.unique_customers,
        ist.last_sold_date,
        ist.popularity_score,
        CASE
            WHEN ist.total_revenue > 0 AND cs.category_revenue > 0 THEN
                ROUND((ist.total_revenue / cs.category_revenue * 100)::NUMERIC, 2)
            ELSE 0
        END as revenue_share_in_category,
        CASE
            WHEN ist.avg_selling_price > ist.standard_price * 1.1 THEN 'Premium Pricing'
            WHEN ist.avg_selling_price < ist.standard_price * 0.9 THEN 'Discounted'
            ELSE 'Standard Pricing'
        END as pricing_strategy
    FROM item_stats ist
    LEFT JOIN category_stats cs ON ist.item_category = cs.item_category
    ORDER BY ist.popularity_score DESC, ist.total_revenue DESC;
$$;

-- 2. Fix get_template_analytics function (fixes return type mismatch)
CREATE OR REPLACE FUNCTION get_template_analytics(input_company_id UUID)
RETURNS TABLE (
    template_id UUID,
    template_name TEXT,
    total_extractions BIGINT,
    successful_extractions BIGINT,
    success_rate NUMERIC,
    avg_confidence NUMERIC,
    recent_extractions BIGINT,
    effectiveness_score NUMERIC
)
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT
        rt.id as template_id,
        rt.template_name::TEXT,
        COUNT(r.id) as total_extractions,
        COUNT(r.id) FILTER (WHERE r.extraction_confidence >= COALESCE(rt.confidence_threshold, 0.8)) as successful_extractions,
        CASE
            WHEN COUNT(r.id) > 0 THEN
                ROUND((COUNT(r.id) FILTER (WHERE r.extraction_confidence >= COALESCE(rt.confidence_threshold, 0.8))::NUMERIC / COUNT(r.id) * 100)::NUMERIC, 2)
            ELSE 0
        END as success_rate,
        COALESCE(AVG(r.extraction_confidence), 0) as avg_confidence,
        COUNT(r.id) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_extractions,
        CASE
            WHEN COUNT(r.id) > 0 AND AVG(r.extraction_confidence) IS NOT NULL THEN
                ((COUNT(r.id) FILTER (WHERE r.extraction_confidence >= COALESCE(rt.confidence_threshold, 0.8))::NUMERIC / COUNT(r.id) * 0.7) + (AVG(r.extraction_confidence) * 0.3))
            ELSE 0
        END as effectiveness_score
    FROM receipt_templates rt
    LEFT JOIN receipts r ON rt.id = r.template_id
    WHERE rt.company_id = input_company_id AND rt.is_active = true
    GROUP BY rt.id, rt.template_name, rt.confidence_threshold
    ORDER BY effectiveness_score DESC;
$$;

-- 3. Create get_recent_activity function (fixes the relationship ambiguity issue)
CREATE OR REPLACE FUNCTION get_recent_activity(input_company_id UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    activity_id UUID,
    activity_type TEXT,
    member_name TEXT,
    member_loyalty_id TEXT,
    amount NUMERIC,
    description TEXT,
    activity_date TIMESTAMPTZ
)
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT
        r.id as activity_id,
        'Receipt' as activity_type,
        lm.name as member_name,
        lm.loyalty_id as member_loyalty_id,
        r.total_amount as amount,
        r.service_description as description,
        r.created_at as activity_date
    FROM receipts r
    JOIN loyalty_members lm ON r.member_id = lm.id  -- Use member_id relationship only
    WHERE r.company_id = input_company_id
    ORDER BY r.created_at DESC
    LIMIT limit_count;
$$;

-- ===============================================================================
-- VERIFICATION - Test all functions work correctly
-- ===============================================================================

-- Test 1: Business Performance (should return 1 item with sales data)
SELECT 'BUSINESS PERFORMANCE TEST' as test, COUNT(*) as result_count
FROM get_business_performance('d10aed7e-3116-403c-a572-c16ab870d761');

-- Test 2: Template Analytics (will return 0 - no templates for this company)
SELECT 'TEMPLATE ANALYTICS TEST' as test, COUNT(*) as result_count
FROM get_template_analytics('d10aed7e-3116-403c-a572-c16ab870d761');

-- Test 3: Customer Insights (should return 3 customers)
SELECT 'CUSTOMER INSIGHTS TEST' as test, COUNT(*) as result_count
FROM get_customer_insights('d10aed7e-3116-403c-a572-c16ab870d761');

-- Test 4: Recent Activity (should return recent receipts)
SELECT 'RECENT ACTIVITY TEST' as test, COUNT(*) as result_count
FROM get_recent_activity('d10aed7e-3116-403c-a572-c16ab870d761', 5);
