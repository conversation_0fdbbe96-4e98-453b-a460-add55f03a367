-- Create comprehensive analytics summary view
CREATE OR REPLACE VIEW analytics_summary_dashboard AS
SELECT
    c.id as company_id,
    c.name as company_name,
    c.business_type,
    -- Customer Metrics
    COUNT(DISTINCT lm.id) as total_customers,
    COUNT(DISTINCT lm.id) FILTER (WHERE lm.registration_date >= NOW() - INTERVAL '30 days') as new_customers_30d,
    COUNT(DISTINCT lm.id) FILTER (WHERE lm.lifetime_points > 0) as active_customers,
    -- Transaction Metrics
    COUNT(DISTINCT r.id) as total_receipts,
    COUNT(DISTINCT r.id) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as receipts_30d,
    COALESCE(SUM(r.total_amount), 0) as total_revenue,
    COALESCE(SUM(r.total_amount) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days'), 0) as revenue_30d,
    CASE
        WHEN COUNT(DISTINCT r.id) > 0 THEN
            ROUND((SUM(r.total_amount) / COUNT(DISTINCT r.id))::NUMERIC, 2)
        ELSE 0
    END as avg_transaction_value,
    -- Business Items
    COUNT(DISTINCT bi.id) as total_business_items,
    COUNT(DISTINCT bi.id) FILTER (WHERE bi.is_active = true) as active_business_items,
    -- Template Metrics
    COUNT(DISTINCT rt.id) as total_templates,
    COUNT(DISTINCT r.id) FILTER (WHERE r.template_id IS NOT NULL) as template_enhanced_receipts,
    CASE
        WHEN COUNT(DISTINCT r.id) > 0 THEN
            ROUND((COUNT(DISTINCT r.id) FILTER (WHERE r.template_id IS NOT NULL)::NUMERIC / COUNT(DISTINCT r.id) * 100)::NUMERIC, 2)
        ELSE 0
    END as template_usage_percentage,
    AVG(r.extraction_confidence) as avg_extraction_confidence,
    -- Calculated at timestamp
    NOW() as calculated_at
FROM companies c
LEFT JOIN loyalty_members lm ON c.id = lm.company_id
LEFT JOIN receipts r ON c.id = r.company_id
LEFT JOIN business_items bi ON c.id = bi.company_id
LEFT JOIN receipt_templates rt ON c.id = rt.company_id AND rt.is_active = true
GROUP BY c.id, c.name, c.business_type;
