#!/bin/bash

# Setup Telegram Bot Menu
# This script sets up the command menu for the Telegram bot

echo "🤖 Setting up Telegram Bot Menu..."

# Check if TELEGRAM_BOT_TOKEN is set
if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
    echo "❌ Error: TELEGRAM_BOT_TOKEN environment variable is not set"
    echo "Please set your Telegram bot token:"
    echo "export TELEGRAM_BOT_TOKEN=your_bot_token_here"
    exit 1
fi

# Setup the bot menu using Telegram API directly
curl -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/setMyCommands" \
     -H "Content-Type: application/json" \
     -d '{
       "commands": [
         {"command": "start", "description": "Initialize bot and link account"},
         {"command": "link", "description": "Get account linking instructions"},
         {"command": "balance", "description": "Check your points balance"},
         {"command": "rewards", "description": "Browse available rewards"},
         {"command": "history", "description": "View transaction history"},
         {"command": "profile", "description": "View your profile information"},
         {"command": "settings", "description": "Bot preferences"},
         {"command": "help", "description": "Show help message"},
         {"command": "unlink", "description": "Unlink your account"}
       ]
     }'

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Bot menu setup completed successfully!"
    echo ""
    echo "Users can now:"
    echo "• Tap the menu button (☰) next to the message input"
    echo "• Type '/' to see available commands"
    echo "• Use the commands directly"
    echo ""
    echo "Available commands:"
    echo "  /start - Initialize bot and link account"
    echo "  /link - Get account linking instructions"
    echo "  /balance - Check your points balance"
    echo "  /rewards - Browse available rewards"
    echo "  /history - View transaction history"
    echo "  /profile - View your profile information"
    echo "  /settings - Bot preferences"
    echo "  /help - Show help message"
    echo "  /unlink - Unlink your account"
else
    echo "❌ Failed to setup bot menu"
    exit 1
fi
