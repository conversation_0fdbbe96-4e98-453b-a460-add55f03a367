-- QUIC<PERSON> ANALYTICS DATA ENHANCEMENT
-- This script enhances existing data to make analytics work properly

-- First, let's see what we have
SELECT 'Current Data Status:' as status;
SELECT
  'Companies: ' || COUNT(*) as info FROM companies
UNION ALL
SELECT
  'Business Items: ' || COUNT(*) as info FROM business_items
UNION ALL
SELECT
  'Receipts: ' || COUNT(*) as info FROM receipts
UNION ALL
SELECT
  'Templates: ' || COUNT(*) as info FROM receipt_templates
UNION ALL
SELECT
  'Members: ' || COUNT(*) as info FROM loyalty_members;

-- Check if we have template data
SELECT template_name, is_active, total_extractions FROM receipt_templates;

-- Check receipt data
SELECT company_id, total_amount, extraction_confidence, template_id FROM receipts LIMIT 5;

-- Check business items data
SELECT company_id, item_name, total_sales_count, total_revenue FROM business_items LIMIT 5;
