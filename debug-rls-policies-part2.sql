-- Part 2: Get the critical RLS policy information
-- Please run these queries one by one and share the results

-- 1. Check RLS status on tables
SELECT
    c.relname as tablename,
    c.relrowsecurity as rls_enabled,
    c.relforcerowsecurity as rls_forced
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'public'
AND c.relname IN ('companies', 'company_administrators')
AND c.relkind = 'r'
ORDER BY c.relname;

-- 2. Get all RLS policies for companies table
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd as command,
    qual as using_expression,
    with_check as with_check_expression
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'companies'
ORDER BY policyname;

-- 3. Get all RLS policies for company_administrators table
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd as command,
    qual as using_expression,
    with_check as with_check_expression
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'company_administrators'
ORDER BY policyname;

-- 4. Check what roles exist and their RLS bypass capability
SELECT
    rolname,
    rolsuper,
    rolbypassrls
FROM pg_roles
WHERE rolname IN ('anon', 'authenticated', 'service_role', 'postgres')
ORDER BY rolname;

-- 5. Test direct access to companies table as service_role
-- (This should work if service_role bypasses RLS)
SELECT
    id,
    name,
    administrator_id
FROM companies
WHERE administrator_id = '0557e2e2-75dd-4b23-a6fb-ad5ac0211b00'
LIMIT 1;
