-- Improve Customer Preferences Analytics
-- This script enhances the analytics to show more useful customer preference data

-- Create a function to get multiple favorite items per customer
CREATE OR REPLACE FUNCTION get_detailed_customer_preferences(p_company_id UUID)
RETURNS TABLE(
    member_id UUID,
    loyalty_id TEXT,
    customer_name TEXT,
    total_visits BIGINT,
    total_spent NUMERIC,
    favorite_items JSONB
) AS $$
BEGIN
    RETURN QUERY
    WITH customer_base AS (
        SELECT
            lm.id as member_id,
            lm.loyalty_id,
            lm.name as customer_name,
            COUNT(DISTINCT r.id) as total_visits,
            COALESCE(SUM(r.total_amount), 0) as total_spent
        FROM loyalty_members lm
        LEFT JOIN receipts r ON lm.id = r.member_id
        WHERE lm.company_id = p_company_id
        GROUP BY lm.id, lm.loyalty_id, lm.name
    ),
    member_preferences AS (
        SELECT
            cb.member_id,
            jsonb_agg(
                jsonb_build_object(
                    'item_name', bi.item_name,
                    'item_category', bi.item_category,
                    'purchase_count', purchase_count,
                    'total_spent_on_item', total_spent_on_item
                ) ORDER BY purchase_count DESC
            ) FILTER (WHERE bi.item_name IS NOT NULL) as favorite_items
        FROM customer_base cb
        LEFT JOIN (
            SELECT
                r.member_id,
                bi.item_name,
                bi.item_category,
                COUNT(*) as purchase_count,
                SUM(ri.total_price) as total_spent_on_item,
                ROW_NUMBER() OVER (PARTITION BY r.member_id ORDER BY COUNT(*) DESC) as item_rank
            FROM receipts r
            JOIN receipt_items ri ON r.id = ri.receipt_id
            JOIN business_items bi ON ri.business_item_id = bi.id
            WHERE bi.company_id = p_company_id
            GROUP BY r.member_id, bi.item_name, bi.item_category
        ) item_stats ON cb.member_id = item_stats.member_id AND item_stats.item_rank <= 3
        LEFT JOIN business_items bi ON bi.item_name = item_stats.item_name AND bi.company_id = p_company_id
        GROUP BY cb.member_id
    )
    SELECT
        cb.member_id,
        cb.loyalty_id,
        cb.customer_name,
        cb.total_visits,
        cb.total_spent,
        COALESCE(mp.favorite_items, '[]'::jsonb) as favorite_items
    FROM customer_base cb
    LEFT JOIN member_preferences mp ON cb.member_id = mp.member_id
    WHERE cb.total_visits > 0  -- Only customers with transactions
    ORDER BY cb.total_spent DESC, cb.total_visits DESC;
END;
$$ LANGUAGE plpgsql;

-- Test the function
SELECT * FROM get_detailed_customer_preferences('d10aed7e-3116-403c-a572-c16ab870d761');
