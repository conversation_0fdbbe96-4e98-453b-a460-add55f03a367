-- Comprehensive RLS and Table Configuration Diagnostics

-- 1. Check RLS status for key tables
SELECT
    schemaname,
    tablename,
    (SELECT relrowsecurity FROM pg_class
     WHERE relname = tablename AND relnamespace =
         (SELECT oid FROM pg_namespace WHERE nspname = schemaname)
    ) as rls_enabled
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('companies', 'company_administrators', 'profiles')
ORDER BY tablename;

-- 2. Get all RLS policies for companies table
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd as command,
    qual as using_expression,
    with_check as with_check_expression
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'companies'
ORDER BY policyname;

-- 3. Get all RLS policies for company_administrators table
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd as command,
    qual as using_expression,
    with_check as with_check_expression
FROM pg_policies
WHERE schemaname = 'public'
AND tablename = 'company_administrators'
ORDER BY policyname;

-- 4. Check what roles exist and their permissions
SELECT
    rolname,
    rolsuper,
    rolinherit,
    rolcreaterole,
    rolcreatedb,
    rolcanlogin,
    rolreplication,
    rolbypassrls
FROM pg_roles
WHERE rolname IN ('anon', 'authenticated', 'service_role', 'postgres')
ORDER BY rolname;

-- 5. Check current user and role
SELECT
    current_user as current_user,
    current_role as current_role,
    session_user as session_user;

-- 6. Check for app-related and company-related settings
SELECT
    name,
    setting
FROM pg_settings
WHERE name LIKE '%app%' OR name LIKE '%company%'
ORDER BY name;

-- 7. Detailed table access information
SELECT
    pc.relname AS table_name,
    pc.relkind AS table_type,
    CASE
        WHEN has_table_privilege(current_user, pc.oid, 'SELECT') THEN 'Yes'
        ELSE 'No'
    END AS can_select,
    CASE
        WHEN has_table_privilege(current_user, pc.oid, 'INSERT') THEN 'Yes'
        ELSE 'No'
    END AS can_insert,
    CASE
        WHEN has_table_privilege(current_user, pc.oid, 'UPDATE') THEN 'Yes'
        ELSE 'No'
    END AS can_update,
    CASE
        WHEN has_table_privilege(current_user, pc.oid, 'DELETE') THEN 'Yes'
        ELSE 'No'
    END AS can_delete,
    (SELECT relrowsecurity FROM pg_class WHERE oid = pc.oid) AS rls_enabled
FROM pg_class pc
JOIN pg_namespace pn ON pc.relnamespace = pn.oid
WHERE pn.nspname = 'public'
AND pc.relname IN ('companies', 'company_administrators', 'profiles');

-- 8. Test table access with a specific administrator_id
-- Note: Adjust the ID if needed
SELECT
    id,
    name,
    administrator_id,
    created_at
FROM companies
WHERE administrator_id = '0557e2e2-75dd-4b23-a6fb-ad5ac0211b00'
LIMIT 5;

-- 9. Test company administrators table access
SELECT
    company_id,
    administrator_id,
    role,
    created_at
FROM company_administrators
WHERE administrator_id = '0557e2e2-75dd-4b23-a6fb-ad5ac0211b00'
LIMIT 5;

-- 10. Check functions related to company context
SELECT
    n.nspname as schema_name,
    p.proname as function_name,
    pg_catalog.pg_get_function_result(p.oid) as result_type,
    pg_catalog.pg_get_function_arguments(p.oid) as arguments,
    CASE p.provolatile
        WHEN 'i' THEN 'IMMUTABLE'
        WHEN 's' THEN 'STABLE'
        WHEN 'v' THEN 'VOLATILE'
    END as volatility,
    p.prosecdef as security_definer
FROM pg_catalog.pg_proc p
LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
WHERE n.nspname = 'public'
AND (p.proname ILIKE '%company%' OR p.proname ILIKE '%admin%' OR p.proname ILIKE '%app%')
ORDER BY p.proname;

-- 11. Show table permissions for key roles
SELECT
    grantee,
    table_schema,
    table_name,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges
WHERE table_schema = 'public'
AND table_name IN ('companies', 'company_administrators')
AND grantee IN ('anon', 'authenticated', 'service_role', 'postgres')
ORDER BY table_name, grantee, privilege_type;