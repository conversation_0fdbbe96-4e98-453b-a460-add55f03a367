-- Migration: Add Telegram notification trigger for transaction points
-- This creates a trigger to send Telegram notifications when points transactions occur

-- Step 1: Create function to send telegram notification for points transactions
CREATE OR REPLACE FUNCTION send_telegram_notification_for_transaction()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  member_data RECORD;
  notification_message TEXT;
  notification_title TEXT;
BEGIN
  -- Only process INSERT operations for earning points (positive points_change)
  IF TG_OP = 'INSERT' AND NEW.points_change > 0 THEN

    -- Get member data including telegram_chat_id
    SELECT
      m.id,
      m.name,
      m.telegram_chat_id,
      m.notification_preferences
    INTO member_data
    FROM loyalty_members m
    WHERE m.id = NEW.member_id;

    -- Only send notification if member has telegram_chat_id and notification preferences allow it
    IF member_data.telegram_chat_id IS NOT NULL
       AND (member_data.notification_preferences->>'points_earned')::boolean = true THEN

      -- Create notification message
      notification_title := 'Points Earned!';
      notification_message := format(
        'Congratulations %s! You earned %s points. %s',
        member_data.name,
        NEW.points_change,
        NEW.description
      );

      -- Insert into telegram_notifications table
      INSERT INTO telegram_notifications (
        member_id,
        chat_id,
        notification_type,
        title,
        message,
        related_transaction_id
      ) VALUES (
        member_data.id,
        member_data.telegram_chat_id,
        'points_earned',
        notification_title,
        notification_message,
        NEW.id
      );

    END IF;

  END IF;

  RETURN NEW;
END;
$$;

-- Step 2: Create trigger on points_transactions table
DROP TRIGGER IF EXISTS tr_telegram_notification_for_points ON points_transactions;

CREATE TRIGGER tr_telegram_notification_for_points
  AFTER INSERT ON points_transactions
  FOR EACH ROW
  EXECUTE FUNCTION send_telegram_notification_for_transaction();

-- Step 3: Also handle redemption notifications (negative points_change)
CREATE OR REPLACE FUNCTION send_telegram_notification_for_transaction()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  member_data RECORD;
  notification_message TEXT;
  notification_title TEXT;
BEGIN
  -- Process both earning (positive) and redemption (negative) points
  IF TG_OP = 'INSERT' THEN

    -- Get member data including telegram_chat_id
    SELECT
      m.id,
      m.name,
      m.telegram_chat_id,
      m.notification_preferences
    INTO member_data
    FROM loyalty_members m
    WHERE m.id = NEW.member_id;

    -- Only send notification if member has telegram_chat_id and notification preferences allow it
    IF member_data.telegram_chat_id IS NOT NULL
       AND (member_data.notification_preferences->>'points_earned')::boolean = true THEN

      -- Create notification message based on transaction type
      IF NEW.points_change > 0 THEN
        -- Points earned
        notification_title := 'Points Earned!';
        notification_message := format(
          'Congratulations %s! You earned %s points. %s',
          member_data.name,
          NEW.points_change,
          NEW.description
        );
      ELSE
        -- Points redeemed
        notification_title := 'Points Redeemed!';
        notification_message := format(
          '%s, you redeemed %s points. %s',
          member_data.name,
          ABS(NEW.points_change),
          NEW.description
        );
      END IF;

      -- Insert into telegram_notifications table
      INSERT INTO telegram_notifications (
        member_id,
        chat_id,
        notification_type,
        title,
        message,
        related_transaction_id
      ) VALUES (
        member_data.id,
        member_data.telegram_chat_id,
        CASE
          WHEN NEW.points_change > 0 THEN 'points_earned'
          ELSE 'points_redeemed'
        END,
        notification_title,
        notification_message,
        NEW.id
      );

    END IF;

  END IF;

  RETURN NEW;
END;
$$;
