-- Test Script: Telegram Chat ID Uniqueness Enforcement
-- This script tests the uniqueness constraint to ensure it's working properly

-- Test 1: Try to insert duplicate telegram_chat_id (should fail)
DO $$
BEGIN
    BEGIN
        -- Try to create a temporary test member with existing chat_id
        INSERT INTO loyalty_members (
            name,
            phone_number,
            email,
            telegram_chat_id,
            company_id,
            loyalty_id
        ) VALUES (
            'Test Duplicate',
            '+1234567890',
            '<EMAIL>',
            '1057291201',  -- This is the existing chat_id for F1513243
            (SELECT id FROM companies LIMIT 1),
            'TEST001'
        );

        RAISE NOTICE 'ERROR: Duplicate insertion succeeded - constraint is not working!';

        -- Clean up if somehow it succeeded
        DELETE FROM loyalty_members WHERE loyalty_id = 'TEST001';

    EXCEPTION
        WHEN unique_violation THEN
            RAISE NOTICE 'SUCCESS: Unique constraint prevented duplicate telegram_chat_id';
    END;
END $$;

-- Test 2: Verify we can insert a member with a unique telegram_chat_id
DO $$
BEGIN
    BEGIN
        -- Insert test member with unique chat_id
        INSERT INTO loyalty_members (
            name,
            phone_number,
            telegram_chat_id,
            company_id,
            loyalty_id
        ) VALUES (
            'Test Unique',
            '+1234567890',
            '9999999999',  -- Unique chat_id
            (SELECT id FROM companies LIMIT 1),
            'TEST002'
        );

        RAISE NOTICE 'SUCCESS: Unique telegram_chat_id insertion succeeded';

        -- Clean up
        DELETE FROM loyalty_members WHERE loyalty_id = 'TEST002';

    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'ERROR: Failed to insert member with unique telegram_chat_id: %', SQLERRM;
    END;
END $$;

-- Test 3: Verify constraint details
SELECT
    'Constraint verified: ' || constraint_name as status,
    constraint_type,
    column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'loyalty_members'
AND kcu.column_name = 'telegram_chat_id'
AND tc.constraint_type = 'UNIQUE';

-- Test 4: Show current telegram connections
SELECT
    'Current connections:' as status,
    loyalty_id,
    name,
    telegram_chat_id,
    telegram_username
FROM loyalty_members
WHERE telegram_chat_id IS NOT NULL;
