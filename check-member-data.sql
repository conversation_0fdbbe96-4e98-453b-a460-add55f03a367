-- Check what members actually exist in the database
SELECT 
  id, 
  name, 
  company_id, 
  loyalty_id,
  lifetime_points,
  redeemed_points,
  expired_points,
  (lifetime_points - redeemed_points - expired_points) as available_points
FROM loyalty_members 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
ORDER BY name;

-- Check if there are any members at all
SELECT COUNT(*) as total_members FROM loyalty_members;

-- Check if the specific member exists anywhere
SELECT 
  id, 
  name, 
  company_id
FROM loyalty_members 
WHERE id = '7075cd9e-c6fb-46b6-974a-ab65d77317da';

-- Check what companies exist
SELECT id, name FROM companies ORDER BY name;
