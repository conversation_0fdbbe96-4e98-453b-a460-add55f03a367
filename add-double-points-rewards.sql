-- Add Double Points Reward Type Support
-- This migration adds support for DOUBLE_POINTS reward type

-- Step 1: Add DOUBLE_POINTS to the enum (must be in separate transaction)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_enum
        WHERE enumlabel = 'DOUBLE_POINTS'
        AND enumtypid = (
            SELECT oid FROM pg_type WHERE typname = 'reward_value_type_enum'
        )
    ) THEN
        ALTER TYPE reward_value_type_enum ADD VALUE 'DOUBLE_POINTS';
    END IF;
END $$;

-- COMMIT the enum change before using it
COMMIT;

-- Step 2: Now add sample rewards (in new transaction)
BEGIN;

-- Add some sample double points rewards for testing
INSERT INTO rewards (
    id,
    reward_code,
    title,
    description,
    reward_type,
    reward_value_type,
    reward_value,
    points_required,
    is_active,
    start_date,
    expiration_date,
    code,
    company_id,
    created_at,
    updated_at
) VALUES
-- Double Points Weekend Special
(
    gen_random_uuid(),
    'DP01',
    'Double Points Weekend',
    'Get 2x points on all purchases during weekends! Perfect for boosting your loyalty status.',
    'GENERAL',
    'DOUBLE_POINTS',
    2.0,
    0, -- No points required, auto-applies
    true,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '30 days',
    'DP01',
    (SELECT id FROM companies LIMIT 1), -- Replace with your company ID
    NOW(),
    NOW()
),
-- Triple Points for VIP
(
    gen_random_uuid(),
    'TP01',
    'VIP Triple Points',
    'Exclusive triple points multiplier for our VIP members with 1000+ lifetime points.',
    'GENERAL',
    'DOUBLE_POINTS',
    3.0,
    1000, -- Requires 1000 points to be eligible
    true,
    CURRENT_DATE,
    CURRENT_DATE + INTERVAL '90 days',
    'TP01',
    (SELECT id FROM companies LIMIT 1), -- Replace with your company ID
    NOW(),
    NOW()
);

-- Add index for better performance on double points queries
CREATE INDEX IF NOT EXISTS idx_rewards_double_points
ON rewards (company_id, reward_value_type, is_active, expiration_date)
WHERE reward_value_type = 'DOUBLE_POINTS';

COMMIT;
