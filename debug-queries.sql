-- DEBUG QUERIES FOR TRANSACTION PROCESSING ISSUES
-- Run these queries in your Supabase SQL editor to diagnose the problems

-- 1. Check if the member exists and belongs to the correct company
SELECT 
  id, 
  name, 
  company_id, 
  loyalty_id,
  lifetime_points,
  redeemed_points,
  expired_points,
  (lifetime_points - redeemed_points - expired_points) as available_points
FROM loyalty_members 
WHERE id = '7075cd9e-c6fb-46b6-974a-ab65d77317da';

-- 2. Check if the company exists and has proper configuration
SELECT 
  id, 
  name, 
  points_earning_ratio,
  points_expiration_days,
  created_at
FROM companies 
WHERE id = 'd10aed7e-3116-403c-a572-c16ab870d761';

-- 3. Check if there are any receipts for this member (for purchase analytics)
SELECT 
  id,
  member_id,
  company_id,
  total_amount,
  subtotal,
  created_at,
  business_name,
  extracted_items
FROM receipts 
WHERE member_id = '7075cd9e-c6fb-46b6-974a-ab65d77317da' 
  AND company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
ORDER BY created_at DESC
LIMIT 10;

-- 4. Check RLS policies on loyalty_members table
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM pg_policies 
WHERE tablename = 'loyalty_members';

-- 5. Check RLS policies on receipts table
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual, 
  with_check
FROM pg_policies 
WHERE tablename = 'receipts';

-- 6. Check if the create_points_transaction function exists
SELECT 
  p.proname as function_name,
  pg_get_function_result(p.oid) as return_type,
  pg_get_function_arguments(p.oid) as arguments
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'create_points_transaction'
  AND n.nspname = 'public';

-- 7. Check recent transactions for this member
SELECT 
  id,
  member_id,
  transaction_type,
  points_change,
  description,
  total_amount,
  business_name,
  created_at
FROM loyalty_transactions 
WHERE member_id = '7075cd9e-c6fb-46b6-974a-ab65d77317da'
ORDER BY created_at DESC
LIMIT 10;

-- 8. Check if there are any rewards for this company
SELECT 
  id,
  title,
  points_required,
  reward_value_type,
  reward_value,
  is_active,
  expiration_date,
  company_id
FROM rewards 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
  AND is_active = true
ORDER BY points_required ASC;

-- 9. Test if service role can access the member (RLS bypass test)
-- This should be run with service role credentials
SET role postgres; -- or your service role
SELECT 
  id, 
  name, 
  company_id 
FROM loyalty_members 
WHERE id = '7075cd9e-c6fb-46b6-974a-ab65d77317da';

-- 10. Check if the get_company_config function exists
SELECT 
  p.proname as function_name,
  pg_get_function_result(p.oid) as return_type,
  pg_get_function_arguments(p.oid) as arguments
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE p.proname = 'get_company_config'
  AND n.nspname = 'public';
