-- ===============================================================================
-- ANALYTICS VIEWS FIX - Run this to create the missing analytics views
-- ===============================================================================

-- Drop existing views if they exist
DROP VIEW IF EXISTS analytics_customer_insights CASCADE;
DROP VIEW IF EXISTS analytics_business_performance CASCADE;
DROP VIEW IF EXISTS analytics_template_metrics CASCADE;

-- 1. Customer Insights View
CREATE OR REPLACE VIEW analytics_customer_insights AS
WITH customer_base AS (
    SELECT
        lm.id as member_id,
        lm.company_id,
        lm.loyalty_id,
        lm.name,
        lm.lifetime_points,
        lm.loyalty_tier,
        lm.registration_date,
        COUNT(DISTINCT r.id) as total_visits,
        COALESCE(SUM(r.total_amount), 0) as total_spent,
        COALESCE(AVG(r.total_amount), 0) as avg_order_value,
        MAX(r.created_at) as last_visit_date
    FROM loyalty_members lm
    LEFT JOIN receipts r ON lm.id = r.member_id
    WHERE lm.company_id IS NOT NULL
    GROUP BY lm.id, lm.company_id, lm.loyalty_id, lm.name, lm.lifetime_points, lm.loyalty_tier, lm.registration_date
),
member_preferences AS (
    SELECT
        lm.id as member_id,
        bi.item_name,
        bi.item_category,
        COUNT(*) as purchase_count,
        SUM(ri.total_price) as total_spent_on_item,
        ROW_NUMBER() OVER (PARTITION BY lm.id ORDER BY COUNT(*) DESC, SUM(ri.total_price) DESC) as preference_rank
    FROM loyalty_members lm
    JOIN receipts r ON lm.id = r.member_id
    JOIN receipt_items ri ON r.id = ri.receipt_id
    JOIN business_items bi ON ri.business_item_id = bi.id
    GROUP BY lm.id, bi.item_name, bi.item_category
)
SELECT
    cb.*,
    mp.item_name as favorite_item,
    mp.item_category as favorite_category,
    mp.purchase_count as favorite_item_purchases,
    mp.total_spent_on_item as favorite_item_spent
FROM customer_base cb
LEFT JOIN member_preferences mp ON cb.member_id = mp.member_id AND mp.preference_rank = 1;

-- 2. Business Performance View (Fixed ambiguous column reference)
CREATE OR REPLACE VIEW analytics_business_performance AS
WITH item_stats AS (
    SELECT
        bi.id,
        bi.company_id,
        bi.item_name,
        bi.item_category,
        bi.standard_price,
        COUNT(ri.id) as total_sales,
        COALESCE(SUM(ri.quantity), 0) as total_quantity_sold,
        COALESCE(SUM(ri.total_price), 0) as total_revenue,
        COALESCE(AVG(ri.unit_price), bi.standard_price) as avg_selling_price,
        COUNT(DISTINCT r.member_id) as unique_customers,
        MAX(r.created_at) as last_sold_date,
        -- Calculate popularity score: 60% sales weight + 40% customer diversity
        (COUNT(ri.id)::NUMERIC * 0.6 + COUNT(DISTINCT r.member_id)::NUMERIC * 0.4) as popularity_score
    FROM business_items bi
    LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
    LEFT JOIN receipts r ON ri.receipt_id = r.id
    WHERE bi.is_active = true
    GROUP BY bi.id, bi.company_id, bi.item_name, bi.item_category, bi.standard_price
),
category_stats AS (
    SELECT
        company_id,
        item_category,
        COUNT(*) as items_in_category,
        SUM(total_revenue) as category_revenue,
        SUM(total_sales) as category_sales,
        AVG(avg_selling_price) as category_avg_price
    FROM item_stats
    GROUP BY company_id, item_category
)
SELECT
    ist.id,
    ist.company_id,
    ist.item_name,
    ist.item_category,
    ist.standard_price,
    ist.total_sales,
    ist.total_quantity_sold,
    ist.total_revenue,
    ist.avg_selling_price,
    ist.unique_customers,
    ist.last_sold_date,
    ist.popularity_score,
    cs.category_revenue,
    cs.category_sales,
    cs.items_in_category,
    CASE
        WHEN ist.total_revenue > 0 AND cs.category_revenue > 0 THEN
            ROUND((ist.total_revenue / cs.category_revenue * 100)::NUMERIC, 2)
        ELSE 0
    END as revenue_share_in_category,
    CASE
        WHEN ist.avg_selling_price > ist.standard_price * 1.1 THEN 'Premium Pricing'
        WHEN ist.avg_selling_price < ist.standard_price * 0.9 THEN 'Discounted'
        ELSE 'Standard Pricing'
    END as pricing_strategy
FROM item_stats ist
LEFT JOIN category_stats cs ON ist.company_id = cs.company_id AND ist.item_category = cs.item_category;

-- 3. Template Analytics View
CREATE OR REPLACE VIEW analytics_template_metrics AS
SELECT
    rt.id,
    rt.company_id,
    rt.template_name,
    COALESCE(rt.confidence_threshold, 0.8) as confidence_threshold,
    COALESCE(rt.total_extractions, 0) as total_extractions,
    COALESCE(rt.successful_extractions, 0) as successful_extractions,
    COALESCE(rt.avg_confidence_score, 0) as avg_confidence_score,
    CASE
        WHEN COALESCE(rt.total_extractions, 0) > 0 THEN
            ROUND((COALESCE(rt.successful_extractions, 0)::NUMERIC / rt.total_extractions * 100)::NUMERIC, 2)
        ELSE 0
    END as success_rate_percentage,
    COUNT(r.id) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_extractions,
    AVG(r.extraction_confidence) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_avg_confidence,
    CASE
        WHEN COALESCE(rt.total_extractions, 0) > 0 AND rt.avg_confidence_score IS NOT NULL THEN
            ((COALESCE(rt.successful_extractions, 0)::NUMERIC / rt.total_extractions * 0.7) + (rt.avg_confidence_score * 0.3))
        ELSE 0
    END as effectiveness_score,
    rt.created_at,
    rt.updated_at
FROM receipt_templates rt
LEFT JOIN receipts r ON rt.id = r.template_id
WHERE rt.is_active = true
GROUP BY rt.id, rt.company_id, rt.template_name, rt.confidence_threshold,
         rt.total_extractions, rt.successful_extractions, rt.avg_confidence_score,
         rt.created_at, rt.updated_at;

-- ===============================================================================
-- VERIFICATION QUERIES - Check that everything is working
-- ===============================================================================

-- Test 1: Check views exist
SELECT 'Analytics Views Created' as status,
       COUNT(*) as view_count
FROM information_schema.views
WHERE table_schema = 'public'
AND table_name IN ('analytics_customer_insights', 'analytics_business_performance', 'analytics_template_metrics');

-- Test 2: Check customer insights
SELECT 'Customer Insights Test' as test_name,
       COUNT(*) as customer_count
FROM analytics_customer_insights
WHERE total_visits > 0;

-- Test 3: Check business performance
SELECT 'Business Performance Test' as test_name,
       COUNT(*) as item_count
FROM analytics_business_performance
WHERE total_sales > 0;

-- Test 4: Check template metrics
SELECT 'Template Metrics Test' as test_name,
       COUNT(*) as template_count
FROM analytics_template_metrics;
