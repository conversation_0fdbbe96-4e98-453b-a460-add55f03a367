# Database Security Implementation Task Tracker

## Overview
This document tracks the implementation of security fixes identified in the database security audit (`db_audit_analysis.md`). Tasks are organized by priority level and include implementation status.

## Critical Priority (Immediate Action)

### 1. Rotate Exposed Supabase Credentials
- [ ] Rotate anon key in Supabase dashboard
- [ ] Rotate service role key in Supabase dashboard
- [ ] Update `.env.local` with new keys
- [ ] Add `.env.local` to `.gitignore`
- [ ] Verify application works with new keys

### 2. Enable RLS on All Tables with Policies
- [ ] Execute improved RLS fix script
```sql
-- Script to run
DO $$
DECLARE
  table_record RECORD;
BEGIN
  FOR table_record IN 
    SELECT tablename 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename IN (
      'companies', 'loyalty_members', 'member_notifications', 'administrators',
      'program_rules', 'rewards', 'company_administrators', 'reward_tier_eligibility',
      'reward_redemptions', 'tier_definitions'
    )
  LOOP
    EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', table_record.tablename);
    RAISE NOTICE 'Enabled RLS on table: %', table_record.tablename;
  END LOOP;
END;
$$;
```
- [ ] Verify RLS is enabled on all tables
```sql
-- Verification query
SELECT
  schemaname,
  tablename,
  CASE WHEN relrowsecurity THEN 'RLS enabled' ELSE 'RLS disabled' END AS rls_status
FROM
  pg_tables t
JOIN
  pg_class c ON t.tablename = c.relname AND t.schemaname = c.relnamespace::regnamespace::text
WHERE
  schemaname = 'public'
  AND tablename IN (
    'companies', 'loyalty_members', 'member_notifications', 'administrators',
    'program_rules', 'rewards', 'company_administrators', 'reward_tier_eligibility',
    'reward_redemptions', 'tier_definitions'
  )
ORDER BY
  tablename;
```

## High Priority (Within 1 Week)

### 3. Fix Mutable Search Path in Functions
- [ ] Update `get_active_inactive_ratio` function
- [ ] Update `get_member_engagement` function
- [ ] Update `get_location_distribution` function
- [ ] Update `update_member_tier` function
- [ ] Update `update_modified_timestamp` function
- [ ] Update `update_notification_status` function
- [ ] Update `update_points_on_expiration` function
- [ ] Update `update_timestamp` function
- [ ] Verify all functions have explicit search paths

Example fix template:
```sql
CREATE OR REPLACE FUNCTION public.function_name(...)
  RETURNS ...
  LANGUAGE plpgsql
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  -- Rest of function body with schema-qualified references
  -- e.g., public.table_name instead of just table_name
  
  -- Function body...
END;
$function$;
```

### 4. Convert SECURITY DEFINER Functions to INVOKER
- [ ] Update `add_points_transaction` function
```sql
CREATE OR REPLACE FUNCTION add_points_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_points INTEGER,
  p_description TEXT,
  p_transaction_type TEXT DEFAULT 'EARN'
) RETURNS VOID AS $$
-- Function body remains the same
$$ LANGUAGE plpgsql; -- Remove SECURITY DEFINER
```
- [ ] Update `deduct_points_transaction` function
```sql
CREATE OR REPLACE FUNCTION deduct_points_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_points INTEGER,
  p_description TEXT,
  p_transaction_type TEXT DEFAULT 'REDEEM'
) RETURNS VOID AS $$
-- Function body remains the same
$$ LANGUAGE plpgsql; -- Remove SECURITY DEFINER
```
- [ ] Verify functions work correctly with RLS

### 5. Update Authentication Configuration
- [ ] Update OTP expiry to 5 minutes
- [ ] Enable leaked password protection
- [ ] Verify auth settings in Supabase dashboard

## Medium Priority (Within 2 Weeks)

### 6. Standardize Parameter Naming in Policies
- [ ] Identify all policies using `app.current_tenant`
```sql
-- Query to find inconsistent policies
SELECT 
  schemaname, 
  tablename, 
  policyname, 
  polqual::text AS policy_using_clause
FROM 
  pg_policies 
WHERE 
  polqual::text LIKE '%app.current_tenant%';
```
- [ ] Update policies to use `app.current_company_id` consistently
- [ ] Verify all policies use consistent parameter naming

### 7. Refactor API Routes to Minimize RLS Bypass
- [ ] Audit all instances of `getSupabaseClient(true)` usage
- [ ] Create list of justified vs. unjustified service role usage
- [ ] Refactor unjustified service role usage to use anon client
- [ ] Document justification for remaining service role usage
- [ ] Verify application works correctly after refactoring

## Implementation Progress

| Task | Status | Date Completed | Notes |
|------|--------|----------------|-------|
| 1. Rotate Exposed Supabase Credentials | Not Started | | |
| 2. Enable RLS on All Tables | Not Started | | |
| 3. Fix Mutable Search Path | Not Started | | |
| 4. Convert SECURITY DEFINER Functions | Not Started | | |
| 5. Update Authentication Configuration | Not Started | | |
| 6. Standardize Parameter Naming | Not Started | | |
| 7. Refactor API Routes | Not Started | | |

## Testing Checklist

- [ ] Verify multi-tenant isolation (users can only see their own company's data)
- [ ] Verify points transactions work correctly
- [ ] Verify member creation and updates work correctly
- [ ] Verify reward redemptions work correctly
- [ ] Verify admin functions work correctly
- [ ] Test performance impact of RLS policies
