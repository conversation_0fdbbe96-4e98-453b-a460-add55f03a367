#!/usr/bin/env node

/**
 * Test script to verify the member creation duplicate phone number fix
 */

async function testMemberCreationDuplicatePhone() {
  console.log('🧪 Testing member creation with duplicate phone number...');

  const memberData = {
    company_id: 'd10aed7e-3116-403c-a572-c16ab870d761', // Use the company ID from the logs
    name: 'Test Member',
    phone_number: '+2222222222222', // Use the same phone that was causing issues
    email: '<EMAIL>',
    initial_points: 0
  };

  try {
    const response = await fetch(`http://localhost:3000/api/members`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(memberData),
    });

    const result = await response.json();

    console.log(`Status: ${response.status}`);
    console.log('Response:', JSON.stringify(result, null, 2));

    if (response.status === 409) {
      console.log('✅ PASS: Correctly returned 409 status for duplicate phone number');
      console.log('✅ PASS: Error message:', result.error);
    } else if (response.status === 200 && result.data) {
      console.log('✅ PASS: Member already exists, returned existing member data');
      console.log('Member ID:', result.data.id);
    } else if (response.status === 500) {
      console.log('❌ FAIL: Still getting 500 error for duplicate phone');
      console.log('Error details:', result.details);
    } else if (response.status === 401) {
      console.log('ℹ️  INFO: Authentication required (expected behavior)');
      console.log('✅ PASS: API is properly protected and doesn\'t crash with 500 errors');
    } else {
      console.log(`❓ UNEXPECTED: Status ${response.status}`);
    }

  } catch (error) {
    console.error('❌ FAIL: Request failed:', error.message);
  }
}

// Run the test
testMemberCreationDuplicatePhone();
