-- Comprehensive seed data for the loyal app database (CORRECTED VERSION)
-- This will populate the database with realistic test data to demonstrate analytics functionality

-- Insert test companies (using correct column names)
INSERT INTO companies (id, name, slug, business_type, logo_url, primary_color, points_expiration_days, points_earning_ratio, is_active, onboarding_completed, created_at, administrator_id)
VALUES
  ('7c4b5389-b630-4d2b-b9b1-9f6460117371', 'FUFIS Beauty Services P.L.C', 'fufis-beauty', 'beauty_wellness', 'https://example.com/logo.png', '#3B82F6', 365, 1.0, true, true, NOW() - (format('%s days', 180)::text)::interval, '65208ae0-0a45-4f0a-a8a6-f6c1c0e4b949')
ON CONFLICT (id) DO NOTHING;

-- Insert additional business items with realistic data (using correct column names)
INSERT INTO business_items (id, company_id, item_name, item_code, standard_price, item_category, item_subcategory, description, is_active, total_sales_count, total_revenue, avg_selling_price, last_sold_date, ai_recognition_patterns, common_variations, created_at, updated_at)
VALUES
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Full Manicure', 'MAN001', 450.00, 'Nail Care', 'Manicure', 'Complete nail care with polish', true, 35, 15750.00, 450.00, NOW() - (format('%s days', 2)::text)::interval, ARRAY['manicure', 'nail care', 'polish'], ARRAY['full mani', 'manicure service', 'nail polish'], NOW() - (format('%s days', 90)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Hair Cut & Style', 'HAI001', 800.00, 'Hair Care', 'Styling', 'Professional haircut with styling', true, 42, 33600.00, 800.00, NOW() - (format('%s days', 1)::text)::interval, ARRAY['haircut', 'styling', 'hair'], ARRAY['hair cut', 'style', 'haircut styling'], NOW() - (format('%s days', 90)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Facial Treatment', 'FAC001', 1200.00, 'Skin Care', 'Facial', 'Deep cleansing facial treatment', true, 28, 33600.00, 1200.00, NOW() - (format('%s days', 3)::text)::interval, ARRAY['facial', 'skin care', 'treatment'], ARRAY['face treatment', 'facial care', 'skin facial'], NOW() - (format('%s days', 90)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Pedicure Deluxe', 'PED001', 650.00, 'Nail Care', 'Pedicure', 'Luxury pedicure with massage', true, 31, 20150.00, 650.00, NOW() - (format('%s days', 1)::text)::interval, ARRAY['pedicure', 'foot care', 'deluxe'], ARRAY['foot treatment', 'luxury pedicure', 'deluxe pedi'], NOW() - (format('%s days', 90)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Hair Coloring', 'COL001', 1500.00, 'Hair Care', 'Coloring', 'Professional hair coloring service', true, 18, 27000.00, 1500.00, NOW() - (format('%s days', 5)::text)::interval, ARRAY['hair color', 'coloring', 'dye'], ARRAY['hair dye', 'color treatment', 'hair painting'], NOW() - (format('%s days', 90)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Eyebrow Threading', 'EYE001', 300.00, 'Beauty Service', 'Eyebrows', 'Precision eyebrow shaping', true, 55, 16500.00, 300.00, NOW() - (format('%s days', 1)::text)::interval, ARRAY['eyebrow', 'threading', 'shaping'], ARRAY['brow threading', 'eyebrow shape', 'brow shaping'], NOW() - (format('%s days', 90)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Deep Conditioning', 'TRT001', 600.00, 'Hair Care', 'Treatment', 'Intensive hair conditioning treatment', true, 22, 13200.00, 600.00, NOW() - (format('%s days', 4)::text)::interval, ARRAY['conditioning', 'hair treatment', 'deep'], ARRAY['hair mask', 'conditioning treatment', 'deep condition'], NOW() - (format('%s days', 90)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Massage Therapy', 'MAS001', 1000.00, 'Wellness', 'Massage', 'Relaxing full body massage', true, 25, 25000.00, 1000.00, NOW() - (format('%s days', 2)::text)::interval, ARRAY['massage', 'therapy', 'relaxation'], ARRAY['body massage', 'massage therapy', 'relaxing massage'], NOW() - (format('%s days', 90)::text)::interval, NOW());

-- Insert test loyalty members with realistic data (using correct column names and format)
INSERT INTO loyalty_members (id, company_id, loyalty_id, name, email, phone_number, birthday, registration_date, lifetime_points, loyalty_tier, redeemed_points, expired_points)
VALUES
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'F1234567', 'Amara Johnson', '<EMAIL>', '+************', '1990-05-15', NOW() - (format('%s days', 150)::text)::interval, 3200, 'Gold', 750, 0),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'F2345678', 'Bethlehem Tadesse', '<EMAIL>', '+************', '1985-11-22', NOW() - (format('%s days', 120)::text)::interval, 2890, 'Silver', 1000, 0),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'F3456789', 'Hanan Mohammed', '<EMAIL>', '+************', '1992-08-10', NOW() - (format('%s days', 180)::text)::interval, 4500, 'Gold', 1950, 0),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'F4567890', 'Meron Assefa', '<EMAIL>', '+************', '1988-03-18', NOW() - (format('%s days', 90)::text)::interval, 2100, 'Silver', 540, 0),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'F5678901', 'Sara Ahmed', '<EMAIL>', '+************', '1995-12-05', NOW() - (format('%s days', 60)::text)::interval, 1250, 'Bronze', 270, 0);

-- Insert receipt templates with more realistic data
INSERT INTO receipt_templates (id, company_id, template_name, template_image_url, template_metadata, ai_prompt_context, validation_rules, confidence_threshold, is_active, total_extractions, successful_extractions, avg_confidence_score, created_at, updated_at)
VALUES
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'FUFIS Standard Receipt v2.1', 'https://example.com/template1.jpg', '{"version": "2.1", "type": "service_receipt", "layout": "standard"}', 'Extract service items, prices, and customer information from FUFIS Beauty Services receipts. Look for service names, individual prices, total amount, customer ID, and date.', '{"required_fields": ["total_amount", "service_items"], "optional_fields": ["customer_id", "date"]}', 0.85, true, 45, 41, 0.91, NOW() - (format('%s days', 60)::text)::interval, NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Mobile Payment Receipt', 'https://example.com/template2.jpg', '{"version": "1.0", "type": "mobile_receipt", "layout": "compact"}', 'Process mobile payment receipts for beauty services. Focus on transaction details, service codes, and payment confirmation.', '{"required_fields": ["total_amount", "transaction_id"], "optional_fields": ["service_description"]}', 0.80, true, 12, 10, 0.83, NOW() - (format('%s days', 30)::text)::interval, NOW());

-- Insert receipts with realistic service data
DO $$
DECLARE
    member_ids uuid[] := ARRAY(SELECT id FROM loyalty_members WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371' LIMIT 5);
    template_ids uuid[] := ARRAY(SELECT id FROM receipt_templates WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371' LIMIT 2);
    service_descriptions text[] := ARRAY['Hair Cut & Style', 'Full Manicure', 'Facial Treatment', 'Pedicure Deluxe', 'Hair Coloring', 'Eyebrow Threading', 'Deep Conditioning', 'Massage Therapy'];
    amounts numeric[] := ARRAY[800.00, 450.00, 1200.00, 650.00, 1500.00, 300.00, 600.00, 1000.00];
    i int;
    receipt_id uuid;
    member_id uuid;
    template_id uuid;
    service_desc text;
    amount numeric;
    confidence_score numeric;
BEGIN
    FOR i IN 1..25 LOOP
        receipt_id := gen_random_uuid();
        member_id := member_ids[1 + (i % array_length(member_ids, 1))];
        template_id := CASE WHEN i % 3 = 0 THEN template_ids[1 + (i % array_length(template_ids, 1))] ELSE NULL END;
        service_desc := service_descriptions[1 + (i % array_length(service_descriptions, 1))];
        amount := amounts[1 + (i % array_length(amounts, 1))] + (random() * 200 - 100); -- Add some variation
        confidence_score := CASE WHEN template_id IS NOT NULL THEN 0.85 + (random() * 0.15) ELSE 0.65 + (random() * 0.25) END;

        INSERT INTO receipts (
            id, company_id, member_id, loyalty_id, receipt_image_url,
            total_amount, subtotal, service_description, purchase_date, extraction_confidence,
            template_id, points_awarded, created_at, receipt_number, uploader_telegram_id
        ) VALUES (
            receipt_id,
            '7c4b5389-b630-4d2b-b9b1-9f6460117371',
            member_id,
            (SELECT loyalty_id FROM loyalty_members WHERE id = member_id),
            'https://example.com/receipts/' || receipt_id || '.jpg',
            amount,
            amount, -- subtotal equals total for simplicity
            service_desc,
            NOW() - (format('%s days', floor(random() * 90))::text)::interval,
            confidence_score,
            template_id,
            floor(amount / 10), -- 1 point per 10 ETB
            NOW() - (format('%s days', floor(random() * 90))::text)::interval,
            'RCP' || lpad(i::text, 6, '0'), -- Generate receipt number like RCP000001
            'test_telegram_user' -- Required field for uploader
        );

        -- Insert receipt items for some receipts (using correct column names)
        IF i % 2 = 0 THEN
            INSERT INTO receipt_items (
                id, receipt_id, business_item_id, quantity, unit_price, total_price,
                extracted_description, confidence_score, created_at
            ) VALUES (
                gen_random_uuid(),
                receipt_id,
                (SELECT id FROM business_items WHERE item_name = service_desc AND company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371' LIMIT 1),
                1,
                amount,
                amount,
                service_desc,
                confidence_score,
                NOW() - (format('%s days', floor(random() * 90))::text)::interval
            );
        END IF;
    END LOOP;
END $$;

-- Insert points transactions for the receipts (using correct column names)
INSERT INTO points_transactions (id, member_id, company_id, loyalty_id, receipt_id, transaction_type, points_change, description, expiration_date, transaction_date, total_amount)
SELECT
    gen_random_uuid(),
    member_id,
    company_id,
    loyalty_id,
    id,
    'earned',
    points_awarded,
    'Points earned from purchase: ' || service_description,
    (created_at + interval '1 year')::date,
    created_at,
    total_amount
FROM receipts
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'
AND points_awarded > 0;

-- Update template extraction statistics
UPDATE receipt_templates SET
    total_extractions = (
        SELECT COUNT(*) FROM receipts
        WHERE template_id = receipt_templates.id
    ),
    successful_extractions = (
        SELECT COUNT(*) FROM receipts
        WHERE template_id = receipt_templates.id
        AND extraction_confidence > confidence_threshold
    ),
    avg_confidence_score = (
        SELECT COALESCE(AVG(extraction_confidence), 0) FROM receipts
        WHERE template_id = receipt_templates.id
    )
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';

-- Update business items sales statistics
UPDATE business_items SET
    total_sales_count = COALESCE((
        SELECT SUM(quantity) FROM receipt_items
        WHERE business_item_id = business_items.id
    ), 0),
    total_revenue = COALESCE((
        SELECT SUM(total_price) FROM receipt_items
        WHERE business_item_id = business_items.id
    ), 0),
    avg_selling_price = COALESCE((
        SELECT AVG(unit_price) FROM receipt_items
        WHERE business_item_id = business_items.id
    ), standard_price),
    last_sold_date = (
        SELECT MAX(created_at) FROM receipt_items
        WHERE business_item_id = business_items.id
    )
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';

-- Insert some reward redemptions for testing (using correct column names)
INSERT INTO reward_redemptions (id, member_id, company_id, points_used, redemption_date, status, created_at)
SELECT
    gen_random_uuid(),
    id,
    company_id,
    500,
    NOW() - (format('%s days', floor(random() * 30))::text)::interval,
    'completed',
    NOW() - (format('%s days', floor(random() * 30))::text)::interval
FROM loyalty_members
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'
AND lifetime_points >= 500
LIMIT 3;

-- Final verification queries to check data integrity
SELECT
    'Business Items' as table_name,
    COUNT(*) as record_count,
    SUM(total_revenue) as total_revenue,
    SUM(total_sales_count) as total_sales
FROM business_items
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'

UNION ALL

SELECT
    'Receipts',
    COUNT(*),
    SUM(total_amount),
    NULL
FROM receipts
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'

UNION ALL

SELECT
    'Loyalty Members',
    COUNT(*),
    AVG(lifetime_points),
    SUM(lifetime_points)
FROM loyalty_members
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'

UNION ALL

SELECT
    'Receipt Templates',
    COUNT(*),
    AVG(avg_confidence_score),
    SUM(total_extractions)
FROM receipt_templates
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';
