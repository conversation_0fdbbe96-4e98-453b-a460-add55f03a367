import { useState } from 'react'

export interface Toast {
  title: string
  description?: string
  variant?: 'default' | 'destructive'
}

export const useToast = () => {
  const [toasts, setToasts] = useState<Toast[]>([])

  const toast = (toastData: Toast) => {
    console.log('Toast:', toastData.title, toastData.description)
    setToasts(prev => [...prev, toastData])

    // Simple alert for now - in a real app this would show a proper toast
    alert(`${toastData.title}: ${toastData.description || ''}`)

    // Auto remove after 3 seconds
    setTimeout(() => {
      setToasts(prev => prev.filter(t => t !== toastData))
    }, 3000)
  }

  return {
    toast,
    toasts
  }
}
