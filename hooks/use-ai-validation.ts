import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

export interface AIValidationRequest {
  memberId: string
  rewardId: string
  receiptTotal?: number
  receiptData?: {
    total_amount: number
    business_name?: string
    receipt_date?: string
    service_description?: string
    financial_system_number?: string
  }
}

export interface AIValidationResponse {
  eligible: boolean
  warnings: Array<{
    type: string
    message: string
    severity: 'error' | 'warning' | 'info'
  }>
  calculation?: {
    originalAmount: number
    discountAmount: number
    finalAmount: number
    pointsUsed: number
    pointsRemaining: number
    savingsPercentage: number
    confidence: number
  }
  recommendations: Array<{
    rewardId: string
    rewardTitle: string
    score: number
    reasoning: string
  }>
  transactionDescription?: string
  aiInsights: {
    confidence: number
    reasoning: string[]
    alternatives: Array<{
      rewardId: string
      rewardTitle: string
      score: number
      reasoning: string
    }>
  }
}

/**
 * Hook for AI-powered redemption validation
 */
export function useAIValidation() {
  return useMutation<AIValidationResponse, Error, AIValidationRequest>({
    mutationFn: async (data: AIValidationRequest) => {
      console.log('Making AI validation request:', data)

      try {
        // Try AI validation first
        const response = await fetch('/api/redemptions/validate-ai', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data),
        })

        console.log('Response status:', response.status)

        // Get response as text first so we can log it
        const responseText = await response.text()

        if (!response.ok) {
          console.error('AI Validation API Error:', response.status, responseText)
          console.log('Trying fallback client-side validation...')

          // Try fallback client-side validation instead
          const fallbackResponse = await fetch('/api/redemptions/validate-client', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data),
          })

          if (fallbackResponse.ok) {
            console.log('Fallback validation successful')
            const fallbackData = await fallbackResponse.json()

            // Add AI insights placeholder since this is client-side
            if (!fallbackData.aiInsights) {
              fallbackData.aiInsights = {
                confidence: 1.0,
                reasoning: ['Using client-side validation due to AI service unavailability'],
                alternatives: []
              }
            }

            return fallbackData
          }

          // If fallback also fails, handle the original error
          try {
            const errorData = JSON.parse(responseText)
            const errorMessage = errorData.error || `Validation failed: ${response.status}`
            console.error('Parsed error data:', errorData)
            throw new Error(errorMessage)
          } catch (parseError) {
            console.error('Could not parse error response as JSON:', parseError)
            throw new Error(`Validation failed: ${response.status} - ${responseText}`)
          }
        }

        // Parse successful response
        try {
          const result = JSON.parse(responseText)
          console.log('AI validation result:', result)
          return result
        } catch (parseError) {
          console.error('Could not parse success response as JSON:', parseError)
          throw new Error('Failed to parse validation response')
        }
      } catch (error) {
        console.error('AI validation request failed:', error)
        throw error
      }
    },
    onSuccess: (data) => {
      console.log('AI validation successful:', data)
      // Optionally invalidate related queries
      // queryClient.invalidateQueries({ queryKey: ['rewards'] })
    },
    onError: (error) => {
      console.error('AI validation failed:', error)
      toast.error(`Failed to validate redemption: ${error.message}`)
    },
  })
}

export interface RedemptionRequest {
  member_id: string
  reward_id: string
  description: string
  company_id: string // Add missing company_id field
  calculatedOutcome?: {
    originalAmount: number
    discountAmount: number
    finalAmount: number
    pointsUsed: number
    pointsRemaining: number
    savingsPercentage: number
    confidence: number
  }
}

export interface RedemptionResponse {
  success: boolean
  data?: {
    id: string
    member_id: string
    reward_id: string
    created_at: string
    updated_at: string
    [key: string]: string | number | boolean | null | undefined  // Allow additional fields if needed
  }
  error?: string
}

/**
 * Hook for processing redemption after AI validation
 */
export function useProcessRedemption() {
  const queryClient = useQueryClient()

  return useMutation<RedemptionResponse, Error, RedemptionRequest>({
    mutationFn: async (data: RedemptionRequest) => {
      const response = await fetch('/api/redemptions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to process redemption: ${response.status} - ${errorText}`)
      }

      return response.json()
    },
    onSuccess: () => {
      toast.success('Reward redeemed successfully!')
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['redemptions'] })
      queryClient.invalidateQueries({ queryKey: ['members'] })
    },
    onError: (error: Error) => {
      console.error('Redemption failed:', error)
      toast.error(`Failed to process redemption: ${error.message}`)
    },
  })
}
