import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { QUERY_KEYS } from '@/lib/query-config'
import { toast } from 'sonner'

// Custom error type for duplicate receipts
export class DuplicateReceiptError extends Error {
  isDuplicateReceipt = true
  errorData: { message?: string; fsNumber?: string }

  constructor(message: string, errorData: { message?: string; fsNumber?: string }) {
    super(message)
    this.name = 'DuplicateReceiptError'
    this.errorData = errorData
  }
}

export class RewardAlreadyRedeemedError extends Error {
  isRewardAlreadyRedeemed = true
  errorData: { message?: string; rewardTitle?: string }

  constructor(message: string, errorData: { message?: string; rewardTitle?: string }) {
    super(message)
    this.name = 'RewardAlreadyRedeemedError'
    this.errorData = errorData
  }
}

interface TransactionData {
  id: string
  member_id: string
  company_id: string
  transaction_type: 'EARN' | 'REDEEM'
  points_change: number
  description: string
  transaction_date: string
  total_amount?: number
  business_name?: string
  financial_system_number?: string
  reward_id?: string
  created_at?: string
}

interface RewardData {
  id: string
  title: string
  description?: string
  points_required: number
  reward_value_type: string
  reward_value: number
  is_active: boolean
  company_id: string
}

interface ReceiptData {
  business_name?: string
  total_amount?: number
  receipt_date?: string
  service_description?: string
  confidence?: number
}

interface UnifiedTransactionData {
  member_id: string
  company_id?: string // Optional since it will be added by the hook
  total_amount: number
  business_name?: string
  financial_system_number?: string
  transaction_date: string
  description?: string
  points_earned?: number
  receipt_image_url?: string
  applied_rewards?: Array<{
    reward_id: string
    points_used: number
  }>
}

interface UnifiedTransactionResult {
  success: boolean
  data: {
    transactions: Array<{
      type: 'EARN' | 'REDEEM'
      transaction: TransactionData
      reward?: RewardData
      points_change: number
    }>
    member_stats: {
      available_points: number
      lifetime_points: number
    }
    summary: {
      total_transactions: number
      points_earned: number
      total_points_used: number
      rewards_applied: number
    }
  }
}

/**
 * Hook for creating unified transactions (earnings + redemptions in single flow)
 */
export function useCreateUnifiedTransaction() {
  const queryClient = useQueryClient()
  const { company } = useCompany()

  return useMutation({
    mutationFn: async (data: UnifiedTransactionData): Promise<UnifiedTransactionResult> => {
      if (!company?.id) {
        console.error('Company not found in context:', company)
        throw new Error('Company not found')
      }

      // Add company_id to the request data
      const requestData = {
        ...data,
        company_id: company.id
      }

      console.log('Sending unified transaction request with company_id:', company.id)
      console.log('Full request data:', requestData)

      const response = await fetch('/api/transactions/unified', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      if (!response.ok) {
        const errorData = await response.json()

        // Handle specific error cases
        if (response.status === 409) {
          // Check if it's a duplicate receipt or reward already redeemed error
          if (errorData.code === 'REWARD_ALREADY_REDEEMED') {
            const rewardError = new Error(errorData.message || 'Reward already redeemed') as RewardAlreadyRedeemedError
            rewardError.isRewardAlreadyRedeemed = true
            rewardError.errorData = errorData
            throw rewardError
          } else {
            // Create a special error for duplicate receipts that can be handled differently
            const duplicateError = new Error('Duplicate receipt') as DuplicateReceiptError
            duplicateError.isDuplicateReceipt = true
            duplicateError.errorData = errorData
            throw duplicateError
          }
        }

        throw new Error(errorData.error || 'Failed to process transaction')
      }

      return response.json()
    },
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.transactions(company?.id || '') })
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.members(company?.id || '') })
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.rewards(company?.id || '') })
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.redemptions(company?.id || '') })

      // Invalidate specific member query
      queryClient.invalidateQueries({ queryKey: ['member', variables.member_id] })

      // Invalidate member redemptions cache to ensure reward filtering is up-to-date
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.memberRedemptions(variables.member_id, company?.id || '') })

      // Invalidate redemption count queries to update reward cards
      queryClient.invalidateQueries({ queryKey: ['redemptions', 'count'] })

      // Show success message
      const { summary } = data.data
      if (summary.rewards_applied > 0) {
        toast.success(
          `🎉 Transaction successful! Earned ${summary.points_earned} points and applied ${summary.rewards_applied} reward(s)`
        )
      } else {
        toast.success(`✅ Transaction successful! Earned ${summary.points_earned} points`)
      }
    },
    onError: (error: Error | DuplicateReceiptError | RewardAlreadyRedeemedError) => {
      console.error('Unified transaction error:', error)

      // Don't show toast for duplicate receipts or reward already redeemed - let the component handle it with a dialog
      if ('isDuplicateReceipt' in error && error.isDuplicateReceipt) {
        return
      }

      if ('isRewardAlreadyRedeemed' in error && error.isRewardAlreadyRedeemed) {
        return
      }

      toast.error(error.message || 'Failed to process transaction')
    },
  })
}

/**
 * Hook to get AI-powered reward recommendations based on transaction data
 */
export function useGetRewardRecommendations() {
  return useMutation({
    mutationFn: async (data: {
      member_id: string
      total_amount: number
      business_name?: string
      receipt_data?: ReceiptData
    }) => {
      const response = await fetch('/api/rewards/recommend', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to get recommendations')
      }

      return response.json()
    },
    onError: (error: Error) => {
      console.error('Reward recommendation error:', error)
      // Don't show error toast for recommendations as it's not critical
    },
  })
}
