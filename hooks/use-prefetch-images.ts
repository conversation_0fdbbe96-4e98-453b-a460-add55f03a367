import { useQueries } from '@tanstack/react-query';
import { useEffect } from 'react';

/**
 * Custom hook to prefetch and cache images using React Query
 * This improves performance by loading images in parallel and caching them
 */
export function usePrefetchImages(imageUrls: (string | undefined)[]) {
  
  // Filter out undefined URLs
  const validImageUrls = imageUrls.filter(Boolean) as string[];
  
  // Use React Query's useQueries to fetch multiple images in parallel
  const imageQueries = useQueries({
    queries: validImageUrls.map(url => ({
      queryKey: ['image', url],
      queryFn: async () => {
        // Create a promise that resolves when the image is loaded
        return new Promise<string>((resolve, reject) => {
          const img = new Image();
          img.onload = () => resolve(url);
          img.onerror = (error) => reject(error);
          img.src = url;
        });
      },
      staleTime: 1000 * 60 * 60, // 1 hour
      cacheTime: 1000 * 60 * 60 * 24, // 24 hours
      retry: 1,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }))
  });

  // Prefetch images when URLs change
  useEffect(() => {
    validImageUrls.forEach(url => {
      // Prefetch the image and store in browser cache
      const img = new Image();
      img.src = url;
    });
  }, [validImageUrls]);

  // Return loading status for all images
  const isLoading = imageQueries.some(query => query.isLoading);
  const isError = imageQueries.some(query => query.isError);
  
  return {
    isLoading,
    isError,
    // Individual statuses if needed
    statuses: imageQueries.map((query, index) => ({
      url: validImageUrls[index],
      isLoading: query.isLoading,
      isError: query.isError,
    })),
  };
}
