import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS, CACHE_TIMES } from '@/lib/query-config'
import { useEffect, useState, useRef, useCallback } from 'react'

interface OnboardingResponse {
  hasCompany: boolean
  isOnboardingComplete: boolean
  company?: {
    id: string
    name: string
    memberCount: number
    transactionCount: number
    rewardCount: number
    tierCount: number
  }
  nextSteps: string[]
}

// Constants for retry strategy
// (Retry count and max retries are defined inside the hook)

export function useOnboardingStatus(initialUserId: string | undefined, authLoading = false) {
  // IMPORTANT: Get userId from all possible sources immediately
  const getUserId = useCallback(() => {
    // First priority: passed in userId
    if (initialUserId) return initialUserId;

    // Only access browser globals on the client side
    if (typeof window === 'undefined') {
      return undefined;
    }

    // Second priority: global variable (fastest, no async)
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const globalUserId = (window as any).__LOYAL_USER_ID;
      if (globalUserId) return globalUserId;
    } catch (e) {
      console.debug('Failed to get global user ID:', e);
    }

    // Third priority: localStorage
    try {
      const localUserId = localStorage.getItem('loyal_app_user_id');
      if (localUserId) return localUserId;
    } catch (e) {
      console.debug('Failed to get localStorage user ID:', e);
    }

    // Fourth priority: sessionStorage
    try {
      const sessionUserId = sessionStorage.getItem('loyal_app_user_id');
      if (sessionUserId) return sessionUserId;
    } catch (e) {
      console.debug('Failed to get sessionStorage user ID:', e);
    }

    // No userId found
    return undefined;
  }, [initialUserId]);

  // Get userId from all possible sources (will be undefined during SSR)
  const [userId, setUserId] = useState<string | undefined>(getUserId())
  // const [retryCount, setRetryCount] = useState(0)
  const pollingActiveRef = useRef(false) // Prevent duplicate polling

  // Re-check for userId on client-side after hydration
  useEffect(() => {
    console.log('useOnboardingStatus hydration check - userId:', userId, 'window available:', typeof window !== 'undefined');
    if (!userId && typeof window !== 'undefined') {
      const clientUserId = getUserId();
      console.log('useOnboardingStatus hydration - found clientUserId:', clientUserId);
      if (clientUserId) {
        console.log('useOnboardingStatus: Found userId after hydration:', clientUserId);
        setUserId(clientUserId);
      }
    }
  }, [userId, getUserId]); // Include dependencies

  // Log initial state
  useEffect(() => {
    console.log('useOnboardingStatus initialized with userId:', userId || 'undefined', 'authLoading:', authLoading);

    // If we already have a userId, we're good to go
    if (userId) {
      console.log('useOnboardingStatus: userId available immediately:', userId);
      return;
    }

    // Don't start polling if auth is still loading
    if (authLoading) {
      console.log('useOnboardingStatus: waiting for auth to complete before polling');
      return;
    }

    // Don't start polling if already active
    if (pollingActiveRef.current) {
      console.log('useOnboardingStatus: polling already active, skipping');
      return;
    }

    // Set polling as active
    pollingActiveRef.current = true;

    // Otherwise, set up polling for userId with reduced frequency
    const intervalId = setInterval(() => {
      const foundUserId = getUserId();
      if (foundUserId) {
        console.log('useOnboardingStatus: userId found during polling:', foundUserId);
        setUserId(foundUserId);
        pollingActiveRef.current = false;
        clearInterval(intervalId);
      } else {
        // setRetryCount(prev => {
        //   const newCount = prev + 1;
        //   console.log(`useOnboardingStatus: userId not found, attempt ${newCount}/${maxRetries}`);
        //   if (newCount >= maxRetries) {
        //     console.error('useOnboardingStatus: all attempts exhausted, giving up');
        //     pollingActiveRef.current = false;
        //     clearInterval(intervalId);
        //   }
        //   return newCount;
        // });
      }
    }, 500); // Poll every 500ms (reduced frequency)

    return () => {
      pollingActiveRef.current = false;
      clearInterval(intervalId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authLoading /* getUserId and userId are intentionally omitted to prevent re-polling */]);

  // Update userId if initialUserId changes and we don't already have one
  useEffect(() => {
    if (initialUserId && !userId) {
      console.log('useOnboardingStatus: userId provided by parent:', initialUserId);
      setUserId(initialUserId);
    }
  }, [initialUserId, userId]);

  // Log whenever userId changes
  useEffect(() => {
    console.log('useOnboardingStatus using userId:', userId)
  }, [userId])

  return useQuery<OnboardingResponse>({
    queryKey: QUERY_KEYS.onboardingStatus(userId || ''),
    queryFn: async () => {
      if (!userId) {
        throw new Error('User ID is required')
      }

      console.log('Fetching onboarding status for userId:', userId)
      const response = await fetch('/api/onboarding/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch onboarding status')
      }

      return response.json()
    },
    enabled: !!userId && !authLoading, // Don't fetch if auth is still loading
    staleTime: CACHE_TIMES.NORMAL, // 5 minutes
    retry: 3, // Increase retries
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff with max 10s
  })
}

export function useUpdateOnboardingStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ userId, step }: {
      userId: string
      step: number
    }) => {
      const response = await fetch('/api/onboarding/status', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, step })
      })

      if (!response.ok) {
        throw new Error('Failed to update onboarding status')
      }

      return response.json()
    },
    onSuccess: (_, variables) => {
      // Invalidate the specific user's onboarding status
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.onboardingStatus(variables.userId) })
      // Also invalidate company data as it might have changed
      queryClient.invalidateQueries({ queryKey: ['company'] })
    }
  })
}
