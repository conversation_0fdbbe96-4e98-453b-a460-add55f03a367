import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { refreshSupabaseClient } from '@/lib/supabase';
import { CACHE_TIMES, QUERY_KEYS, COMMON_QUERY_OPTIONS } from '@/lib/query-config';

// Define the Company type based on the database schema
export interface Company {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  contact_email?: string;
  address?: string;
  points_expiration_days: number;
  points_earning_ratio: number;
  business_type?: string;
  created_at?: string;
  administrator_id?: string;
  onboarding_completed?: boolean;
  setup_wizard_step?: number;
  appearance?: {
    primary_color?: string;
    secondary_color?: string;
    accent_color?: string;
    theme?: string;
    font_family?: string;
    border_radius?: string;
    card_style?: string;
    button_style?: string;
  };
}

/**
 * Custom hook for fetching company data using React Query
 */
export function useCompanyQuery() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  // Fetch company data
  const {
    data: company,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: QUERY_KEYS.company(user?.id || ''),
    queryFn: async () => {
      if (!user) {
        console.log('useCompanyQuery: No user available, skipping fetch');
        return null;
      }

      console.log('useCompanyQuery: Fetching company data for user:', user.id);
      
      // Check if user has company metadata first
      const companyName = user.user_metadata?.company_name;
      const businessType = user.user_metadata?.business_type;

      console.log('User metadata check:', {
        companyName,
        businessType,
        hasMetadata: !!companyName,
        fullMetadata: user.user_metadata
      });

      if (companyName) {
        console.log('User has company metadata, attempting auto-creation/fetch:', { companyName, businessType });
        try {
          const autoCreateResponse = await fetch('/api/companies/auto-create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: user.id,
              companyName,
              businessType: businessType || 'other',
            }),
          });

          if (autoCreateResponse.ok) {
            const autoCreateResult = await autoCreateResponse.json();
            console.log('Company auto-create/fetch result:', autoCreateResult);

            // Set the company (whether newly created or existing)
            if (autoCreateResult.company) {
              // Store company ID in localStorage
              try {
                localStorage.setItem('loyal_app_company_id', autoCreateResult.company.id);
                localStorage.setItem('current_company_id', autoCreateResult.company.id);
                refreshSupabaseClient();
                console.log(`Company set in context: ${autoCreateResult.company.id}`);
              } catch (e) {
                console.error('Could not set company ID in localStorage:', e);
              }
              
              return autoCreateResult.company as Company;
            }
          } else {
            console.error('Failed to auto-create/fetch company:', autoCreateResponse.status, await autoCreateResponse.text());
          }
        } catch (autoCreateError) {
          console.error('Error during company auto-creation/fetch:', autoCreateError);
          throw autoCreateError;
        }
      }

      // Fallback: Try the current API endpoint if auto-create didn't work
      const response = await fetch('/api/companies/current');

      if (!response.ok) {
        if (response.status === 401) {
          console.log('API authentication failed, setting company to null');
          return null;
        }
        throw new Error(`Failed to fetch company: ${response.status}`);
      }

      const result = await response.json();
      console.log('Company fetch result:', result);

      if (result.company) {
        // Store company ID in localStorage
        try {
          localStorage.setItem('loyal_app_company_id', result.company.id);
          localStorage.setItem('current_company_id', result.company.id);
          refreshSupabaseClient();
          console.log(`Company context set: ${result.company.id}`);
        } catch (e) {
          console.error('Could not set company ID in localStorage:', e);
        }
        
        return result.company as Company;
      }
      
      return null;
    },
    enabled: !!user,
    staleTime: CACHE_TIMES.NORMAL,
    gcTime: CACHE_TIMES.NORMAL * 2,
    ...COMMON_QUERY_OPTIONS,
    retry: 1,
  });

  // Function to refresh company data
  const refreshCompany = () => {
    if (user?.id) {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.company(user.id) });
    }
  };

  return {
    company,
    isLoading,
    error,
    refetch,
    refreshCompany,
  };
}
