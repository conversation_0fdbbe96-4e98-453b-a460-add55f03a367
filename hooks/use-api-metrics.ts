import { useQuery } from '@tanstack/react-query';
import { useState } from 'react';

export type ApiMetricsSummary = {
  totalCalls: number;
  uniqueEndpoints: number;
  totalResponseSize: number;
  averageDuration: number;
  callsByEndpoint: Record<string, number>;
  slowestEndpoints: Array<{endpoint: string, duration: number}>;
};

export type ApiEndpointMetric = {
  endpoint: string;
  method: string;
  total_calls: number;
  avg_duration: number;
  last_called: string;
};

export type ApiMetricsResponse = {
  summary: ApiMetricsSummary;
  endpoints: ApiEndpointMetric[];
};

export type TimePeriod = '1h' | '6h' | '24h' | '7d' | '30d';

/**
 * Hook for fetching API metrics from the server
 */
export function useApiMetrics(initialPeriod: TimePeriod = '24h') {
  const [timePeriod, setTimePeriod] = useState<TimePeriod>(initialPeriod);

  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery<ApiMetricsResponse>({
    queryKey: ['api-metrics', timePeriod],
    queryFn: async () => {
      const response = await fetch(`/api/admin/metrics?timePeriod=${timePeriod}`);
      if (!response.ok) {
        throw new Error('Failed to fetch API metrics');
      }
      return response.json();
    }
  });

  // If there is no server-side data yet, provide a default empty response
  const metrics = data || {
    summary: {
      totalCalls: 0,
      uniqueEndpoints: 0,
      totalResponseSize: 0,
      averageDuration: 0,
      callsByEndpoint: {},
      slowestEndpoints: []
    },
    endpoints: []
  };

  return {
    metrics,
    timePeriod,
    setTimePeriod,
    isLoading,
    error,
    refetch
  };
}