import { useMutation } from "@tanstack/react-query";
import { uploadImage, uploadProfileImage } from "@/lib/file-upload";
import type { ReceiptData } from "@/lib/receipt-ocr";
import type { EnhancedReceiptData, MatchedItem } from "@/lib/receipt-ocr-enhanced";

interface OCRProcessingResult {
  success: boolean;
  data: {
    raw_ocr_data: ReceiptData;
    enhanced_ocr_data?: EnhancedReceiptData | null; // Include enhanced data with items
    transaction_data: {
      description: string;
      total_amount: number;
      business_name: string;
      financial_system_number: string;
      receipt_date: string;
      suggested_points: number;
    };
    matched_items?: MatchedItem[];
    confidence: number;
    processing_status: string;
  };
}

// Note: useCreateReceipt hook removed - we now store receipt data directly in transactions

/**
 * Hook to upload image file
 */
export function useUploadImage() {
  return useMutation({
    mutationFn: async (file: File) => {
      return uploadImage(file);
    },
  });
}

/**
 * Hook to upload profile image file
 */
export function useUploadProfileImage() {
  return useMutation({
    mutationFn: async (file: File) => {
      return uploadProfileImage(file);
    },
  });
}

/**
 * Hook to process receipt with OCR
 */
export function useProcessReceiptOCR() {
  return useMutation({
    mutationFn: async ({ file, companyId }: { file: File; companyId?: string }): Promise<OCRProcessingResult> => {
      const formData = new FormData();
      formData.append('image', file);

      // Include company ID for enhanced processing if available
      if (companyId) {
        formData.append('company_id', companyId);
      }

      const response = await fetch('/api/receipts/ocr', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'OCR processing failed');
      }

      return response.json();
    },
  });
}
