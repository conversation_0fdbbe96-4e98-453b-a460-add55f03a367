import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCompany } from "@/contexts/company-context";
import { QUERY_KEYS } from "@/lib/query-config";
import { useAuth } from "@/hooks/use-auth";

export interface CompanyUpdateData {
  name: string;
  logo_url?: string;
  primary_color?: string;
  points_expiration_days: number;
  points_earning_ratio: number;
}

/**
 * Hook to update company information
 */
export function useUpdateCompany() {
  const { company } = useCompany();
  const { user } = useAuth();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (companyData: CompanyUpdateData) => {
      console.log('[Frontend] useUpdateCompany called with data:', companyData);
      console.log('[Frontend] Current user state:', { user: user ? { id: user.id, email: user.email } : null });
      console.log('[Frontend] Current company state:', { company: company ? { id: company.id, name: company.name } : null });

      if (!companyId) {
        console.error('[Frontend] No company ID available');
        throw new Error("Company ID is required");
      }

      if (!user?.id) {
        console.error('[Frontend] No user ID available - user not authenticated');
        console.error('[Frontend] User object:', user);
        throw new Error("User authentication is required - please log in");
      }

      console.log('[Frontend] Making PUT request with userId:', user.id);

      const requestBody = {
        ...companyData,
        userId: user.id, // Include user ID in the request
      };

      console.log('[Frontend] Request body being sent:', JSON.stringify(requestBody, null, 2));

      const response = await fetch("/api/companies", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log('[Frontend] Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.text();
        console.error('[Frontend] API Error response:', errorData);
        throw new Error(`Failed to update company: ${errorData}`);
      }

      const result = await response.json();
      console.log('[Frontend] Success response:', result);
      return result;
    },
    onSuccess: () => {
      // Invalidate company query to refetch data
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.company(companyId || '') });
      // Trigger company context refresh
      queryClient.invalidateQueries({ queryKey: ['company-context'] });
    },
  });
}

export interface CompanyCreateData {
  name: string;
  slug: string;
  logo_url?: string;
  primary_color?: string;
  points_expiration_days: number;
  points_earning_ratio: number;
}

/**
 * Hook to create a new company
 */
export function useCreateCompany() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (companyData: CompanyCreateData) => {
      if (!user?.id) {
        throw new Error("User authentication is required");
      }

      const response = await fetch("/api/companies", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...companyData,
          userId: user.id, // Include user ID in the request
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Unknown error" }));
        console.error('[Client] API Error:', errorData);
        throw new Error(errorData.error || "Failed to create company");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate all company-related queries
      queryClient.invalidateQueries({ queryKey: ['company'] });
      queryClient.invalidateQueries({ queryKey: ['company-context'] });
    },
  });
}
