'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import type { User, Session } from '@supabase/supabase-js'

interface SimpleAuthState {
  user: User | null
  session: Session | null
  isLoading: boolean
  isAuthenticated: boolean
  error?: string
}

/**
 * Simple auth hook without React Query for pages that need immediate auth checking
 * Use this for login/logout pages where React Query might cause loading issues
 */
export function useSimpleAuth(): SimpleAuthState {
  const [authState, setAuthState] = useState<SimpleAuthState>({
    user: null,
    session: null,
    isLoading: true,
    isAuthenticated: false,
  })

  useEffect(() => {
    let mounted = true

    const checkAuth = async () => {
      try {
        console.log('🔍 Simple auth check starting...')
        const supabase = createClient()

        const { data: { session }, error } = await supabase.auth.getSession()

        if (!mounted) return // Component unmounted

        console.log('📄 Simple auth result:', { session: !!session, error })

        if (error) {
          setAuthState({
            user: null,
            session: null,
            isLoading: false,
            isAuthenticated: false,
            error: error.message,
          })
          return
        }

        setAuthState({
          user: session?.user || null,
          session: session,
          isLoading: false,
          isAuthenticated: !!session?.user,
        })
      } catch (err) {
        if (!mounted) return

        console.error('🚨 Simple auth error:', err)
        setAuthState({
          user: null,
          session: null,
          isLoading: false,
          isAuthenticated: false,
          error: err instanceof Error ? err.message : 'Unknown error',
        })
      }
    }

    // Set up auth state listener
    const supabase = createClient()
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (!mounted) return

      console.log('🔄 Simple auth state changed:', { event, hasSession: !!session })

      setAuthState({
        user: session?.user || null,
        session: session,
        isLoading: false,
        isAuthenticated: !!session?.user,
      })
    })

    checkAuth()

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  return authState
}
