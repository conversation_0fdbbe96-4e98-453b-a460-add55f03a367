import { useQuery } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { useAuth } from '@/hooks/use-auth'

interface BirthdayEligibilityResponse {
  isEligible: boolean
  birthdayRewards: BirthdayReward[]
}

export interface BirthdayReward {
  id: string
  title: string
  description: string
  points_required: number
  reward_value: string
  reward_value_type: string
}

/**
 * Hook to check if the current member is eligible for birthday rewards
 * and fetch available birthday rewards
 */
export function useBirthdayEligibility(memberId?: string) {
  const { company } = useCompany()
  const { user } = useAuth()

  return useQuery({
    queryKey: ['birthdayEligibility', memberId, company?.id],
    queryFn: async (): Promise<BirthdayEligibilityResponse> => {
      if (!memberId || !company?.id) {
        return { isEligible: false, birthdayRewards: [] }
      }

      // First check if member is birthday eligible
      const eligibilityRes = await fetch(`/api/birthday-rewards/eligibility?memberId=${memberId}&companyId=${company.id}`)

      if (!eligibilityRes.ok) {
        throw new Error('Failed to check birthday eligibility')
      }

      const eligibilityData = await eligibilityRes.json()

      // If not eligible, return early
      if (!eligibilityData.isEligible) {
        return { isEligible: false, birthdayRewards: [] }
      }

      // If eligible, fetch birthday rewards
      const rewardsRes = await fetch(`/api/rewards?companyId=${company.id}&rewardType=BIRTHDAY`)

      if (!rewardsRes.ok) {
        throw new Error('Failed to fetch birthday rewards')
      }

      const rewardsData = await rewardsRes.json()

      return {
        isEligible: true,
        birthdayRewards: rewardsData.data || []
      }
    },
    enabled: !!memberId && !!company?.id && !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
