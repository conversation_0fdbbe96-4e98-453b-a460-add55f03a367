import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useCompany } from "@/contexts/company-context";
import { CACHE_TIMES, QUERY_KEYS, COMMON_QUERY_OPTIONS } from "@/lib/query-config";
import { toast } from "sonner";

export interface Transaction {
  id: string;
  member_id: string;
  points_change: number;
  transaction_type: string;
  description?: string;
  transaction_date?: string;
  receipt_id?: string;
  created_at?: string;
  company_id: string;
  // OCR-related fields
  receipt_ocr_data?: string; // JSON string of extracted data
  receipt_ocr_confidence?: number;
  receipt_processing_status?: string;
  // Receipt image and extracted data fields
  receipt_image_url?: string;
  receipt_number?: string;
  business_name?: string;
  total_amount?: number;
}

/**
 * Hook to fetch all transactions
 */
export function useTransactions(limit?: number, dateRange?: { from?: Date; to?: Date }) {
  const { company } = useCompany();
  const companyId = company?.id;

  return useQuery({
    queryKey: QUERY_KEYS.transactions(companyId || '', limit, dateRange),
    queryFn: async () => {
      if (!companyId) {
        return { data: [] };
      }

      let url = `/api/transactions?companyId=${companyId}`;

      if (limit) {
        url += `&limit=${limit}`;
      }

      // Add date range parameters if provided
      if (dateRange?.from) {
        url += `&startDate=${dateRange.from.toISOString()}`;
      }

      if (dateRange?.to) {
        url += `&endDate=${dateRange.to.toISOString()}`;
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Failed to fetch transactions");
      }
      return response.json();
    },
    enabled: !!companyId,
    staleTime: CACHE_TIMES.DYNAMIC,
    gcTime: CACHE_TIMES.DYNAMIC * 2,
    ...COMMON_QUERY_OPTIONS,
  });
}

/**
 * Hook to fetch a single transaction by ID
 */
export function useTransaction(transactionId: string) {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: ["transaction", companyId, transactionId],
    queryFn: async () => {
      if (!companyId || !transactionId) {
        return null;
      }

      // First try to use the existing transactions query cache
      const transactionsData = queryClient.getQueryData<{ data: Transaction[] }>(["transactions", companyId]);

      if (transactionsData) {
        const cachedTransaction = transactionsData.data.find(t => t.id === transactionId);
        if (cachedTransaction) {
          return cachedTransaction;
        }
      }

      // If not found in cache, fetch directly
      const response = await fetch(`/api/transactions/${transactionId}?companyId=${companyId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch transaction");
      }
      return response.json();
    },
    enabled: !!companyId && !!transactionId,
    staleTime: 60 * 1000, // 1 minute
  });
}

/**
 * Hook to create a new transaction
 */
export function useCreateTransaction() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (transactionData: Omit<Transaction, "id" | "created_at" | "company_id">) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch("/api/transactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...transactionData,
          company_id: companyId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create transaction");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.transactions(companyId || '') });
      // Also invalidate member data as points may have changed
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.members(companyId || '') });
    },
  });
}

/**
 * Hook to delete a transaction (Admin/Owner only)
 */
export function useDeleteTransaction() {
  const { company } = useCompany();
  const queryClient = useQueryClient();
  const companyId = company?.id;

  return useMutation({
    mutationFn: async (transactionId: string) => {
      const response = await fetch(`/api/transactions/${transactionId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete transaction");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Transaction deleted successfully");
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.transactions(companyId || '') });
      // Also invalidate member data as points may have changed
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.members(companyId || '') });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete transaction");
    },
  });
}
