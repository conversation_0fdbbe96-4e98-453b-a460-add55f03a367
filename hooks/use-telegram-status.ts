import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { CACHE_TIMES } from '@/lib/query-config'

export interface TelegramStatus {
  isLinked: boolean
  telegramChatId?: string | null
  telegramUsername?: string | null
  linkedAt?: string | null
  linkingToken?: string | null
}

export interface TelegramLinkData {
  link: string
  linkingToken: string
  member: {
    name: string
    phone_number: string
  }
}

interface MemberWithTelegram {
  id: string
  name: string
  email?: string
  phone_number?: string
  telegram_chat_id?: string | null
  telegram_username?: string | null
  linked_at?: string | null
}

/**
 * Hook to fetch real-time Telegram status for a member
 * Uses optimistic updates and automatic invalidation
 */
export function useTelegramStatus(memberId: string) {
  const { company } = useCompany()

  return useQuery<TelegramStatus>({
    queryKey: ['telegramStatus', memberId, company?.id],
    queryFn: async () => {
      const response = await fetch(`/api/telegram/status/${memberId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch Telegram status')
      }
      return response.json()
    },
    enabled: !!memberId && !!company?.id,
    staleTime: CACHE_TIMES.DYNAMIC, // 1 minute for real-time updates
    refetchInterval: 30000, // Poll every 30 seconds for status changes
    refetchOnWindowFocus: true,
  })
}

/**
 * Hook to generate Telegram links with optimistic updates
 */
export function useGenerateTelegramLink() {
  const queryClient = useQueryClient()

  return useMutation<TelegramLinkData, Error, { memberId: string }, { previousStatus: unknown }>({
    mutationFn: async ({ memberId }) => {
      const response = await fetch('/api/telegram/generate-link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ memberId }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to generate link')
      }

      return response.json()
    },
    onMutate: async ({ memberId }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['telegramStatus', memberId] })

      // Optimistically update status to "generating"
      const previousStatus = queryClient.getQueryData(['telegramStatus', memberId])
      queryClient.setQueryData(['telegramStatus', memberId], (old: TelegramStatus | undefined) => ({
        ...old,
        isGenerating: true
      }))

      return { previousStatus }
    },
    onError: (err, { memberId }, context) => {
      // Rollback on error
      if (context?.previousStatus) {
        queryClient.setQueryData(['telegramStatus', memberId], context.previousStatus)
      }
    },
    onSettled: (data, error, { memberId }) => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: ['telegramStatus', memberId] })
      // Also invalidate member data to sync
      queryClient.invalidateQueries({ queryKey: ['member'] })
    },
  })
}

/**
 * Hook to unlink Telegram with optimistic updates
 */
export function useUnlinkTelegram() {
  const queryClient = useQueryClient()

  return useMutation<{ success: boolean }, Error, { memberId: string }, { previousStatus: unknown }>({
    mutationFn: async ({ memberId }) => {
      const response = await fetch('/api/telegram/unlink', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ memberId }),
      })

      if (!response.ok) {
        const error = await response.json()

        // Handle specific error cases
        if (response.status === 400 && error.error?.includes('not linked')) {
          throw new Error('This member is not currently linked to Telegram')
        }

        throw new Error(error.error || 'Failed to unlink Telegram account')
      }

      return response.json()
    },
    onMutate: async ({ memberId }) => {
      await queryClient.cancelQueries({ queryKey: ['telegramStatus', memberId] })

      const previousStatus = queryClient.getQueryData(['telegramStatus', memberId])

      // Optimistically update to unlinked state
      queryClient.setQueryData(['telegramStatus', memberId], {
        isLinked: false,
        telegramChatId: null,
        telegramUsername: null,
        linkedAt: null,
        linkingToken: null
      })

      return { previousStatus }
    },
    onError: (err, { memberId }, context) => {
      if (context?.previousStatus) {
        queryClient.setQueryData(['telegramStatus', memberId], context.previousStatus)
      }
    },
    onSettled: (data, error, { memberId }) => {
      queryClient.invalidateQueries({ queryKey: ['telegramStatus', memberId] })
      queryClient.invalidateQueries({ queryKey: ['member'] })
      queryClient.invalidateQueries({ queryKey: ['members'] }) // Update member list
      queryClient.invalidateQueries({ queryKey: ['membersWithTelegram'] }) // Update Telegram members list
    },
  })
}

/**
 * Hook to get all members with Telegram status for bulk management
 */
export function useMembersWithTelegramStatus() {
  const { company } = useCompany()

  return useQuery({
    queryKey: ['membersWithTelegram', company?.id],
    queryFn: async () => {
      const response = await fetch(`/api/members/telegram-status?companyId=${company?.id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch members with Telegram status')
      }
      return response.json()
    },
    enabled: !!company?.id,
    staleTime: CACHE_TIMES.NORMAL, // 5 minutes
    select: (data) => {
      // Transform data for easier consumption
      return data.members?.map((member: MemberWithTelegram) => ({
        ...member,
        telegramStatus: {
          isLinked: !!member.telegram_chat_id,
          telegramChatId: member.telegram_chat_id,
          telegramUsername: member.telegram_username,
          linkedAt: member.linked_at
        }
      })) || []
    }
  })
}

interface TelegramAvailabilityResult {
  available: boolean
  chatId: string
  linkedTo?: {
    memberId: string
    memberName: string
    loyaltyId: string
    telegramUsername?: string
    linkedAt: string
  }
}

/**
 * Hook to check if a Telegram chat ID is available for linking
 */
export function useCheckTelegramAvailability() {
  return useMutation<TelegramAvailabilityResult, Error, { chatId: string }>({
    mutationFn: async ({ chatId }) => {
      const response = await fetch('/api/telegram/check-availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chatId }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to check availability')
      }

      return response.json()
    },
  })
}
