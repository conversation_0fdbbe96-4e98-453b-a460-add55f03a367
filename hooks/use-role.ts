import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useAuth } from './use-auth'
import { useCompany } from '@/contexts/company-context'
import { useEffect, useMemo } from 'react'

// Define role types for better type safety
type UserRole = 'OWNER' | 'ADMIN' | 'CASHIER' | 'VIEWER' | null

/**
 * Enhanced role hook with React Query for optimized caching and error handling
 */
export function useRole() {
  const { user, isLoading: authLoading } = useAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const queryClient = useQueryClient()

  // Cache key for consistent reference using useMemo to avoid dependency array changes
  const roleQueryKey = useMemo(() => ['userRole', user?.id, company?.id], [user?.id, company?.id])

  // Set up cache invalidation on user or company changes
  useEffect(() => {
    // When user or company changes, invalidate the role cache
    if (user?.id && company?.id) {
      // Prefetch the role to improve perceived performance
      queryClient.prefetchQuery({
        queryKey: roleQueryKey,
        queryFn: () => fetchUserRole(user.id, company.id),
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
    }

    return () => {
      // Clean up any pending queries when dependencies change
      queryClient.cancelQueries({ queryKey: roleQueryKey })
    }
  }, [user?.id, company?.id, queryClient, roleQueryKey])

  // Separate the fetch function for reusability and testing
  const fetchUserRole = async (userId: string, companyId: string): Promise<UserRole> => {
    try {
      console.log('Fetching role for user:', userId, 'company:', companyId)
      const response = await fetch(`/api/user-role?companyId=${companyId}`, {
        // Add cache control headers
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        const errorMessage = errorData.error || `Failed to fetch role: ${response.status}`

        // Categorize errors for better handling
        if (response.status === 401 || response.status === 403) {
          throw new Error(`Authentication error: ${errorMessage}`)
        } else if (response.status === 404) {
          throw new Error(`Resource not found: ${errorMessage}`)
        } else if (response.status >= 500) {
          throw new Error(`Server error: ${errorMessage}`)
        } else {
          throw new Error(`Request failed: ${errorMessage}`)
        }
      }

      const data = await response.json()
      console.log('Role data:', data)
      return data.role as UserRole
    } catch (error) {
      console.error('Error fetching role:', error)
      throw error // Let React Query handle the error
    }
  }

  const {
    data: role,
    isLoading: roleLoading,
    error,
    refetch,
    isError,
  } = useQuery<UserRole, Error>({
    queryKey: roleQueryKey,
    queryFn: async () => {
      if (!user?.id || !company?.id) {
        return null
      }

      return fetchUserRole(user.id, company.id)
    },
    enabled: !!user?.id && !!company?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication errors
      if (
        error instanceof Error &&
        (error.message.includes('401') ||
         error.message.includes('403') ||
         error.message.includes('Authentication error'))
      ) {
        return false
      }
      // Retry server errors more times
      if (error instanceof Error && error.message.includes('Server error')) {
        return failureCount < 5
      }
      return failureCount < 3 // Retry up to 3 times for other errors
    },
  })

  return {
    role,
    isOwner: role === 'OWNER',
    isAdmin: role === 'ADMIN',
    isCashier: role === 'CASHIER',
    isViewer: role === 'VIEWER',
    hasRole: role !== null,
    isLoading: authLoading || companyLoading || roleLoading,
    isError,
    error,
    refetchRole: refetch,
  }
}
