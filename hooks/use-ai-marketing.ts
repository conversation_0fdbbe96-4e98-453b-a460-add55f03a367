import { useState } from 'react'
import { toast } from 'sonner'

interface GenerateMessageParams {
  campaignType: string
  targetAudience: string
  businessName: string
  businessType: string
  tone: string
  keyMessage: string
  callToAction: string
  memberData?: {
    totalMembers: number
    averageTier: string
    topSpenders: string
    engagementRate: number
  }
}

interface OptimizeCampaignParams {
  message: string
  targetAudience: string
  businessType: string
  campaignGoal: string
  memberInsights?: {
    totalMembers: number
    averageTier: string
    topSpenders: string
    engagementRate: number
  }
}

interface GeneratedMessage {
  message: string
  subjectLines: string[]
  metadata: {
    campaignType: string
    targetAudience: string
    tone: string
    generatedAt: string
  }
}

interface CampaignOptimization {
  overallScore: number
  improvements: Array<{
    category: string
    suggestion: string
    impact: string
    reason: string
  }>
  optimizedMessage: string
  keyStrengths: string[]
  potentialIssues: string[]
}

export function useAIMarketing() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isOptimizing, setIsOptimizing] = useState(false)

  const generateMessage = async (params: GenerateMessageParams): Promise<GeneratedMessage | null> => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/ai/generate-marketing-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      if (!response.ok) {
        throw new Error('Failed to generate message')
      }

      const data = await response.json()
      toast.success('Marketing message generated successfully!')
      return data
    } catch (error) {
      console.error('Error generating message:', error)
      toast.error('Failed to generate marketing message')
      return null
    } finally {
      setIsGenerating(false)
    }
  }

  const optimizeCampaign = async (params: OptimizeCampaignParams): Promise<CampaignOptimization | null> => {
    setIsOptimizing(true)
    try {
      const response = await fetch('/api/ai/optimize-campaign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      if (!response.ok) {
        throw new Error('Failed to optimize campaign')
      }

      const data = await response.json()
      toast.success('Campaign analysis completed!')
      return data
    } catch (error) {
      console.error('Error optimizing campaign:', error)
      toast.error('Failed to analyze campaign')
      return null
    } finally {
      setIsOptimizing(false)
    }
  }

  return {
    generateMessage,
    optimizeCampaign,
    isGenerating,
    isOptimizing,
  }
}
