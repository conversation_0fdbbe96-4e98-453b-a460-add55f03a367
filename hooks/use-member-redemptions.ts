import { useQuery } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { CACHE_TIMES, QUERY_KEYS } from '@/lib/query-config'

interface MemberRedemptionsData {
  redeemed_reward_ids: string[]
}

interface MemberRedemptionsResult {
  success: boolean
  data: MemberRedemptionsData
}

/**
 * Hook to fetch which rewards a member has already redeemed
 */
export function useMemberRedemptions(memberId: string | null) {
  const { company } = useCompany()

  return useQuery({
    queryKey: QUERY_KEYS.memberRedemptions(memberId || '', company?.id || ''),
    queryFn: async (): Promise<MemberRedemptionsResult> => {
      if (!memberId || !company?.id) {
        return { success: true, data: { redeemed_reward_ids: [] } }
      }

      const response = await fetch('/api/rewards/member-redemptions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          member_id: memberId,
          company_id: company.id
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to fetch member redemptions')
      }

      return response.json()
    },
    enabled: !!memberId && !!company?.id,
    staleTime: CACHE_TIMES.DYNAMIC, // Cache for 1 minute
    gcTime: CACHE_TIMES.NORMAL,
  })
}
