-- Marketing System Database Schema Migration
-- Sprint 1: Database Foundation
-- Date: August 23, 2025

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create marketing_campaigns table
CREATE TABLE IF NOT EXISTS marketing_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  message_title VARCHAR(255),
  message_content TEXT NOT NULL,
  target_type VARCHAR(50) NOT NULL CHECK (target_type IN ('all', 'tier', 'individual', 'custom')),
  target_criteria JSONB, -- Store targeting rules as JSON
  status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'failed')),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE,
  total_recipients INTEGER DEFAULT 0,
  successful_sends INTEGER DEFAULT 0,
  failed_sends INTEGER DEFAULT 0,
  company_id UUID NOT NULL REFERENCES companies(id),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create campaign_recipients tracking table
CREATE TABLE IF NOT EXISTS campaign_recipients (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID NOT NULL REFERENCES marketing_campaigns(id) ON DELETE CASCADE,
  member_id UUID NOT NULL REFERENCES loyalty_members(id),
  notification_id UUID REFERENCES telegram_notifications(id),
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'skipped')),
  sent_at TIMESTAMP WITH TIME ZONE,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Extend telegram_notifications table to link to campaigns
ALTER TABLE telegram_notifications
ADD COLUMN IF NOT EXISTS campaign_id UUID REFERENCES marketing_campaigns(id);

-- Add performance indexes
CREATE INDEX IF NOT EXISTS idx_marketing_campaigns_company_id ON marketing_campaigns(company_id);
CREATE INDEX IF NOT EXISTS idx_marketing_campaigns_status ON marketing_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_marketing_campaigns_created_at ON marketing_campaigns(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_marketing_campaigns_scheduled_at ON marketing_campaigns(scheduled_at) WHERE scheduled_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_campaign_recipients_campaign_id ON campaign_recipients(campaign_id);
CREATE INDEX IF NOT EXISTS idx_campaign_recipients_member_id ON campaign_recipients(member_id);
CREATE INDEX IF NOT EXISTS idx_campaign_recipients_status ON campaign_recipients(status);
CREATE INDEX IF NOT EXISTS idx_campaign_recipients_sent_at ON campaign_recipients(sent_at) WHERE sent_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_telegram_notifications_campaign_id ON telegram_notifications(campaign_id) WHERE campaign_id IS NOT NULL;

-- Create updated_at trigger function if not exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at trigger for marketing_campaigns
DROP TRIGGER IF EXISTS update_marketing_campaigns_updated_at ON marketing_campaigns;
CREATE TRIGGER update_marketing_campaigns_updated_at
    BEFORE UPDATE ON marketing_campaigns
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create member_segments view for targeting
CREATE OR REPLACE VIEW member_segments AS
SELECT
  lm.id,
  lm.name,
  lm.email,
  lm.phone_number,
  lm.telegram_chat_id,
  lm.loyalty_tier,
  lm.lifetime_points,
  lm.company_id,
  lm.registration_date,
  lm.notification_preferences,
  mp.available_points,
  CASE
    WHEN mp.available_points >= 1000 THEN 'high_balance'
    WHEN mp.available_points >= 500 THEN 'medium_balance'
    ELSE 'low_balance'
  END as balance_segment,
  CASE
    WHEN lm.registration_date >= NOW() - INTERVAL '30 days' THEN 'new'
    WHEN lm.registration_date >= NOW() - INTERVAL '90 days' THEN 'recent'
    ELSE 'established'
  END as member_age_segment,
  -- notification_preferences is a JSONB column (see telegram bot spec). Derive a boolean
  -- for "notifications enabled" from the preferences (fall back to TRUE).
  CASE
    WHEN lm.telegram_chat_id IS NOT NULL AND COALESCE((lm.notification_preferences->>'points_earned')::boolean, true) = true THEN true
    ELSE false
  END as has_telegram,
  CASE
    WHEN lm.telegram_chat_id IS NOT NULL AND COALESCE((lm.notification_preferences->>'points_earned')::boolean, true) = true THEN 'eligible'
    WHEN lm.telegram_chat_id IS NULL THEN 'no_telegram'
    WHEN COALESCE((lm.notification_preferences->>'points_earned')::boolean, true) = false THEN 'notifications_disabled'
    ELSE 'ineligible'
  END as telegram_eligibility
FROM loyalty_members lm
LEFT JOIN member_points_live mp ON lm.id = mp.id;

-- Create campaign analytics view
CREATE OR REPLACE VIEW campaign_analytics AS
SELECT
  mc.id,
  mc.name,
  mc.status,
  mc.total_recipients,
  mc.successful_sends,
  mc.failed_sends,
  ROUND(
    CASE
      WHEN mc.total_recipients > 0
      THEN (mc.successful_sends::numeric / mc.total_recipients) * 100
      ELSE 0
    END, 2
  ) as delivery_rate_percentage,
  mc.sent_at,
  mc.created_at,
  mc.company_id,
  COUNT(cr.id) as tracked_recipients,
  COUNT(CASE WHEN cr.status = 'sent' THEN 1 END) as confirmed_delivered,
  COUNT(CASE WHEN cr.status = 'failed' THEN 1 END) as confirmed_failed,
  COUNT(CASE WHEN cr.status = 'pending' THEN 1 END) as pending_delivery
FROM marketing_campaigns mc
LEFT JOIN campaign_recipients cr ON mc.id = cr.campaign_id
GROUP BY mc.id, mc.name, mc.status, mc.total_recipients, mc.successful_sends, mc.failed_sends, mc.sent_at, mc.created_at, mc.company_id;

-- Create function to get eligible members for campaigns
CREATE OR REPLACE FUNCTION get_campaign_eligible_members(
  p_company_id UUID,
  p_target_type VARCHAR(50),
  p_target_criteria JSONB DEFAULT '{}'::JSONB
)
RETURNS TABLE (
  member_id UUID,
  member_name VARCHAR(255),
  member_email VARCHAR(255),
  telegram_chat_id BIGINT,
  loyalty_tier VARCHAR(50),
  available_points INTEGER,
  balance_segment VARCHAR(20),
  member_age_segment VARCHAR(20)
) AS $$
BEGIN
  -- Base query for all eligible members (has telegram and notifications enabled)
  IF p_target_type = 'all' THEN
    RETURN QUERY
    SELECT
      ms.id, ms.name, ms.email, ms.telegram_chat_id,
      ms.loyalty_tier, ms.available_points, ms.balance_segment, ms.member_age_segment
    FROM member_segments ms
    WHERE ms.company_id = p_company_id
      AND ms.has_telegram = true;

  -- Target by loyalty tier
  ELSIF p_target_type = 'tier' THEN
    RETURN QUERY
    SELECT
      ms.id, ms.name, ms.email, ms.telegram_chat_id,
      ms.loyalty_tier, ms.available_points, ms.balance_segment, ms.member_age_segment
    FROM member_segments ms
    WHERE ms.company_id = p_company_id
      AND ms.has_telegram = true
      AND ms.loyalty_tier = ANY(
        SELECT jsonb_array_elements_text(p_target_criteria->'tier_names')
      );

  -- Target individual members
  ELSIF p_target_type = 'individual' THEN
    RETURN QUERY
    SELECT
      ms.id, ms.name, ms.email, ms.telegram_chat_id,
      ms.loyalty_tier, ms.available_points, ms.balance_segment, ms.member_age_segment
    FROM member_segments ms
    WHERE ms.company_id = p_company_id
      AND ms.has_telegram = true
      AND ms.id = ANY(
        SELECT (jsonb_array_elements_text(p_target_criteria->'member_ids'))::UUID
      );

  -- Custom segment targeting
  ELSIF p_target_type = 'custom' THEN
    RETURN QUERY
    SELECT
      ms.id, ms.name, ms.email, ms.telegram_chat_id,
      ms.loyalty_tier, ms.available_points, ms.balance_segment, ms.member_age_segment
    FROM member_segments ms
    WHERE ms.company_id = p_company_id
      AND ms.has_telegram = true
      AND (
        -- Points filter
        (p_target_criteria ? 'min_points' AND ms.available_points >= (p_target_criteria->>'min_points')::INTEGER) OR
        (NOT p_target_criteria ? 'min_points')
      )
      AND (
        (p_target_criteria ? 'max_points' AND ms.available_points <= (p_target_criteria->>'max_points')::INTEGER) OR
        (NOT p_target_criteria ? 'max_points')
      )
      AND (
        -- Balance segment filter
        (p_target_criteria ? 'balance_segments' AND ms.balance_segment = ANY(
          SELECT jsonb_array_elements_text(p_target_criteria->'balance_segments')
        )) OR
        (NOT p_target_criteria ? 'balance_segments')
      )
      AND (
        -- Member age segment filter
        (p_target_criteria ? 'member_age_segments' AND ms.member_age_segment = ANY(
          SELECT jsonb_array_elements_text(p_target_criteria->'member_age_segments')
        )) OR
        (NOT p_target_criteria ? 'member_age_segments')
      );
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT SELECT ON member_segments TO authenticated;
GRANT SELECT ON campaign_analytics TO authenticated;
GRANT ALL ON marketing_campaigns TO authenticated;
GRANT ALL ON campaign_recipients TO authenticated;

-- Add RLS (Row Level Security) policies for marketing tables
ALTER TABLE marketing_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaign_recipients ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access campaigns for their company
CREATE POLICY marketing_campaigns_company_access ON marketing_campaigns
  FOR ALL
  USING (
    company_id IN (
      SELECT id FROM companies
      WHERE administrator_id = auth.uid()
    )
  );

-- Policy: Users can only access campaign recipients for their company's campaigns
CREATE POLICY campaign_recipients_company_access ON campaign_recipients
  FOR ALL
  USING (
    campaign_id IN (
      SELECT id FROM marketing_campaigns
      WHERE company_id IN (
        SELECT id FROM companies
        WHERE administrator_id = auth.uid()
      )
    )
  );

-- Create notification for successful migration
DO $$
BEGIN
  RAISE NOTICE 'Marketing system database schema created successfully!';
  RAISE NOTICE 'Tables created: marketing_campaigns, campaign_recipients';
  RAISE NOTICE 'Views created: member_segments, campaign_analytics';
  RAISE NOTICE 'Function created: get_campaign_eligible_members()';
  RAISE NOTICE 'Indexes and RLS policies applied successfully';
END $$;
