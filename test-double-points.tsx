// Test file to debug double points rewards flow
// Run this with: npx tsx test-double-points.tsx

interface TransactionSummaryData {
  transactions: Array<{
    id: string
    type: 'EARN' | 'REDEEM' | 'DOUBLE_POINTS'
    points_change: number
    description: string
    reward?: {
      id: string
      title: string
      reward_value_type: string
      applied_value?: number
    }
  }>
  summary: {
    points_earned: number
    total_points_used: number
    rewards_applied: number
    net_points_change: number
  }
}

// Test function to fetch and analyze transaction data
async function testDoublePointsFlow() {
  const transactionId = '1b84ee1c-7488-45ef-bf77-589bf459e8f3'; // Latest test transaction

  try {
    const response = await fetch(`http://localhost:3000/api/transactions/summary/${transactionId}`);
    const data: TransactionSummaryData = await response.json();

    console.log('=== DOUBLE POINTS FLOW TEST ===\n');

    console.log('📊 SUMMARY STATISTICS:');
    console.log(`   Points Earned: ${data.summary.points_earned}`);
    console.log(`   Points Used: ${data.summary.total_points_used}`);
    console.log(`   Rewards Applied: ${data.summary.rewards_applied}`);
    console.log(`   Net Change: ${data.summary.net_points_change}\n`);

    console.log('📝 TRANSACTIONS:');
    data.transactions.forEach((tx, index) => {
      console.log(`   ${index + 1}. ${tx.type}: ${tx.points_change > 0 ? '+' : ''}${tx.points_change} pts`);
      console.log(`      Description: ${tx.description}`);
      if (tx.reward) {
        console.log(`      Reward: ${tx.reward.title} (${tx.reward.reward_value_type})`);
        console.log(`      Applied Value: ${tx.reward.applied_value}`);
      }
      console.log('');
    });

    // Check for issues
    const doublePointsTransactions = data.transactions.filter(tx => tx.type === 'DOUBLE_POINTS');
    const rewardWithValue = data.transactions.find(tx => tx.reward?.applied_value);

    console.log('🔍 ANALYSIS:');
    console.log(`   Double Points Transactions: ${doublePointsTransactions.length}`);
    console.log(`   Reward with Applied Value: ${rewardWithValue ? 'YES' : 'NO'}`);

    if (rewardWithValue) {
      console.log(`   Reward Value Type: ${rewardWithValue.reward?.reward_value_type}`);
      console.log(`   Should Show Discount: ${rewardWithValue.reward?.reward_value_type !== 'DOUBLE_POINTS' ? 'YES' : 'NO'}`);
    }

    // Expected vs Actual
    console.log('\n✅ EXPECTED BEHAVIOR:');
    console.log('   - Points Earned: 1034 (517 base + 517 bonus)');
    console.log('   - Points Used: 0 (double points don\'t consume points)');
    console.log('   - Rewards Applied: 1 (the double points reward)');
    console.log('   - No discount section should show');
    console.log('   - Should show "+517 pts" bonus in purple');

  } catch (error) {
    console.error('Error testing double points flow:', error);
  }
}

// Run the test
testDoublePointsFlow();
