## Profile Image Debug & Fix (June 23, 2025)

### Problem Summary
Profile images were not displaying despite being properly uploaded and stored in Supabase. Investigation revealed two core issues:

1. URL path mismatch: Database records pointed to `/receipts/` folder while actual images were stored in `/profile-images/`
2. Filename mismatch: The filenames in database URLs didn't match actual file names in storage

### Solution
1. Created diagnostic script `scripts/fix-profile-urls.js` to:
   - List actual files in storage
   - Update database records with correct URLs pointing to actual files
   - Verify updated URLs are accessible with HTTP 200 response

2. API Routes Fix:
   - Updated member & tier API routes to accept `companyId` parameter as fallback when session auth fails
   - Added better error messages and debugging to image components
   - Added `unoptimized={true}` to Next.js Image components to bypass optimization for Supabase storage URLs

3. Image Component Updates:
   - Enhanced error handling to show initials if image fails to load
   - Added logging to track image load success/failure
   - Added automatic URL path correction for any URLs still using wrong path

### Verification
- Confirmed via HTTP requests that image URLs are now returning 200 OK
- Confirmed all members have been updated with valid image URLs
- Tested on both member list and member details pages
- Initials will show as fallback if image loading fails

### Root Cause
The issue was caused by a combination of:
1. RLS policies that were initially too restrictive (fixed)
2. URL mismatch between database entries and actual storage paths
3. Authentication issues in API routes when not passing `companyId` in query params
4. Stale data caching in TanStack Query

### Additional Improvements (June 23, 2025)
1. Enhanced TanStack Query Configuration:
   - Modified `useMember` hook to always fetch fresh data
   - Reduced staleTime for member queries to ensure up-to-date data
   - Added bidirectional cache updates to keep list and detail views in sync

2. Optimized Image Loading:
   - Added `priority={true}` to Next.js Image components to preload images
   - Improved fallback mechanism to show initials only when truly needed
   - Added more robust error handling and automatic recovery
   - Forced an immediate refetch when member detail page mounts

3. Fixed List View Avatars:
   - Updated the member list component to properly show images
   - Added better error handling in list view
   - Ensured data-attribute selectors work correctly for fallbacks
   - Applied URL correction to any problematic image paths
