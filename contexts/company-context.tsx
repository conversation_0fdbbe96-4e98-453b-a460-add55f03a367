'use client';

import { createContext, useContext, useMemo } from 'react';
import { useCompanyQuery, Company } from '@/hooks/use-company-query';

// Company type is now imported from use-company-query.ts

interface CompanyContextType {
  company: Company | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  refreshCompany: () => void;
}


const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export function CompanyProvider({ children }: { children: React.ReactNode }) {
  // Use the React Query hook instead of direct fetch calls
  const { company, isLoading, error, refetch, refreshCompany } = useCompanyQuery();

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    company: company ?? null, // Ensure company is never undefined
    isLoading,
    error,
    refetch,
    refreshCompany,
  }), [company, isLoading, error, refetch, refreshCompany]);

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
}

export const useCompany = () => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};
