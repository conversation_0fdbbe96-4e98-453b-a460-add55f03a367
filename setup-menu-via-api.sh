#!/bin/bash

# Simple script to setup Telegram Bot Menu via API
# This can be run without environment variables

echo "🤖 Setting up Telegram Bot Menu via API..."

# Try to setup the menu using the local API
curl -X POST http://localhost:3000/api/telegram/setup-menu \
  -H "Content-Type: application/json" \
  -d '{"adminSetup": true}' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "If this fails, you can also run:"
echo "  node setup-telegram-bot-menu.js"
echo ""
echo "Or manually setup via Telegram API with your bot token:"
echo "  ./setup-telegram-bot-menu.sh"
