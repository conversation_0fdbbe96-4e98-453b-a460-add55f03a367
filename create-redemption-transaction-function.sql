-- Create a function to insert redemption transactions that bypasses <PERSON><PERSON> and triggers
CREATE OR REPLACE FUNCTION create_redemption_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_points_change INTEGER,
  p_description TEXT,
  p_transaction_date TIMESTAMPTZ,
  p_reward_id UUID
)
RETURNS SETOF points_transactions
LANGUAGE sql
SECURITY DEFINER
AS $$
    INSERT INTO points_transactions (
      member_id,
      company_id,
      transaction_type,
      points_change,
      description,
      transaction_date,
      expiration_date
    )
    VALUES (
      p_member_id,
      p_company_id,
      'REDEEM',
      p_points_change,
      p_description,
      p_transaction_date,
      CURRENT_DATE  -- Redemptions don't expire, use current date
    )
    RETURNING *;
$$;

-- Grant execution permissions
GRANT EXECUTE ON FUNCTION create_redemption_transaction(UUI<PERSON>, UUID, INTEGER, TEXT, TIMESTAMPTZ, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION create_redemption_transaction(U<PERSON><PERSON>, UUI<PERSON>, INTEGER, TEXT, TIMES<PERSON>MPTZ, UUID) TO service_role;
