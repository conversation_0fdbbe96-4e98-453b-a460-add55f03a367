-- =============================================================================
-- CRITICAL ANALYTICS FIXES - RUN THESE COMMANDS IN YOUR DATABASE
-- =============================================================================

-- 1. CREATE MISSING BUSINESS ITEMS FOR UNLINKED RECEIPT ITEMS
INSERT INTO business_items (
    id,
    company_id,
    item_name,
    item_category,
    standard_price,
    total_sales_count,
    total_revenue,
    avg_selling_price,
    last_sold_date,
    is_active,
    created_at,
    updated_at
)
SELECT
    gen_random_uuid() as id,
    r.company_id,
    COALESCE(ri.extracted_description, 'Unspecified Service') as item_name,
    'General' as item_category,
    ri.unit_price as standard_price,
    COUNT(*)::INTEGER as total_sales_count,
    SUM(ri.total_price) as total_revenue,
    AVG(ri.unit_price) as avg_selling_price,
    MAX(r.created_at) as last_sold_date,
    true as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM receipt_items ri
JOIN receipts r ON ri.receipt_id = r.id
WHERE ri.business_item_id IS NULL
    AND ri.extracted_description IS NOT NULL
    AND ri.extracted_description != ''
    AND NOT EXISTS (
        SELECT 1 FROM business_items bi
        WHERE bi.company_id = r.company_id
        AND LOWER(TRIM(bi.item_name)) = LOWER(TRIM(ri.extracted_description))
    )
GROUP BY r.company_id, ri.extracted_description, ri.unit_price
ON CONFLICT DO NOTHING;

-- 2. LINK RECEIPT ITEMS TO BUSINESS ITEMS
WITH item_matches AS (
    SELECT DISTINCT
        ri.id as receipt_item_id,
        bi.id as business_item_id
    FROM receipt_items ri
    JOIN receipts r ON ri.receipt_id = r.id
    JOIN business_items bi ON (
        bi.company_id = r.company_id
        AND LOWER(TRIM(bi.item_name)) = LOWER(TRIM(ri.extracted_description))
    )
    WHERE ri.business_item_id IS NULL
        AND ri.extracted_description IS NOT NULL
        AND ri.extracted_description != ''
)
UPDATE receipt_items
SET business_item_id = im.business_item_id
FROM item_matches im
WHERE receipt_items.id = im.receipt_item_id;

-- 3. UPDATE BUSINESS ITEMS STATS WITH ACTUAL SALES DATA
UPDATE business_items
SET
    total_sales_count = stats.total_count,
    total_revenue = stats.total_revenue,
    avg_selling_price = stats.avg_price,
    last_sold_date = stats.last_date,
    updated_at = NOW()
FROM (
    SELECT
        bi.id,
        COUNT(ri.id)::INTEGER as total_count,
        COALESCE(SUM(ri.total_price), 0) as total_revenue,
        COALESCE(AVG(ri.unit_price), bi.standard_price) as avg_price,
        MAX(r.created_at) as last_date
    FROM business_items bi
    LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
    LEFT JOIN receipts r ON ri.receipt_id = r.id
    GROUP BY bi.id, bi.standard_price
) stats
WHERE business_items.id = stats.id;

-- =============================================================================
-- VERIFICATION QUERIES - RUN THESE TO CHECK THE FIXES WORKED
-- =============================================================================

-- 1. Check how many receipt_items are now linked
SELECT
    'Receipt Items Linked' as check_name,
    COUNT(*) as total_receipt_items,
    COUNT(business_item_id) as linked_items,
    COUNT(*) - COUNT(business_item_id) as unlinked_items,
    ROUND((COUNT(business_item_id)::NUMERIC / COUNT(*) * 100), 2) as link_percentage
FROM receipt_items;

-- 2. Check business_items have proper sales data
SELECT
    'Business Items with Sales' as check_name,
    COUNT(*) as total_business_items,
    COUNT(*) FILTER (WHERE total_sales_count > 0) as items_with_sales,
    SUM(total_sales_count) as total_sales_across_all_items,
    SUM(total_revenue) as total_revenue_across_all_items
FROM business_items
WHERE is_active = true;

-- 3. Check data by company
SELECT
    c.name as company_name,
    COUNT(DISTINCT r.id) as total_receipts,
    COUNT(DISTINCT ri.id) as total_receipt_items,
    COUNT(DISTINCT bi.id) as total_business_items,
    COUNT(DISTINCT lm.id) as total_members,
    SUM(r.total_amount) as total_revenue
FROM companies c
LEFT JOIN receipts r ON c.id = r.company_id
LEFT JOIN receipt_items ri ON r.id = ri.receipt_id
LEFT JOIN business_items bi ON c.id = bi.company_id AND bi.is_active = true
LEFT JOIN loyalty_members lm ON c.id = lm.company_id
GROUP BY c.id, c.name
ORDER BY total_receipts DESC;
