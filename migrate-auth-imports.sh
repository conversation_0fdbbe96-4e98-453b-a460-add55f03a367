#!/bin/bash

# Migration script to update auth hook imports
# This script replaces all imports of the old auth hooks with the new consolidated one

echo "🔄 Migrating auth hook imports..."

# Files that need to be updated based on grep search results
files=(
  "components/layout/Navbar.tsx"
  "components/layout/header.tsx"
  "app/debug-admin/page.tsx"
  "app/company/create/page.tsx"
  "app/analytics/page.tsx"
  "app/company/edit/page.tsx"
  "app/(auth)/login/page.tsx"
  "app/(auth)/signup/page.tsx"
  "app/items/page.tsx"
  "app/members/telegram-links/page.tsx"
  "app/members/page.tsx"
  "app/members/add/page.tsx"
  "app/transactions/page.tsx"
  "app/dashboard/page.tsx"
  "app/dashboard/components/business-stats.tsx"
  "app/dashboard/components/welcome-state.tsx"
  "app/members/[id]/page.tsx"
  "app/members/[id]/deduct-points/page.tsx"
  "app/members/[id]/edit/page.tsx"
  "app/members/[id]/add-points/page.tsx"
  "app/transactions/add/page.tsx"
)

count=0
for file in "${files[@]}"; do
  if [[ -f "$file" ]]; then
    # Replace the import statement
    sed -i.bak "s|import { useEnhancedAuth } from '@/hooks/use-enhanced-auth'|import { useConsolidatedAuth as useEnhancedAuth } from '@/hooks/use-consolidated-auth'|g" "$file"
    sed -i.bak "s|import { useEnhancedAuth } from \"@/hooks/use-enhanced-auth\"|import { useConsolidatedAuth as useEnhancedAuth } from '@/hooks/use-consolidated-auth'|g" "$file"

    # Clean up backup files
    rm -f "$file.bak"

    echo "✅ Updated $file"
    ((count++))
  else
    echo "⚠️  File not found: $file"
  fi
done

echo ""
echo "🎉 Migration completed! Updated $count files."
echo ""
echo "📋 Summary of changes:"
echo "   • Replaced 'use-enhanced-auth' imports with 'use-consolidated-auth'"
echo "   • Used alias 'useConsolidatedAuth as useEnhancedAuth' for backward compatibility"
echo ""
echo "🚀 Next steps:"
echo "   1. Test the application to ensure auth still works"
echo "   2. Check console for any remaining 'Multiple GoTrueClient instances' warnings"
echo "   3. Consider updating Supabase client imports to use the optimized version"
