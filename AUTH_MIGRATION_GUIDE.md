# 🔄 Auth System Migration Guide
## Transitioning from Multiple Auth Hooks to Consolidated Pattern

### 📋 Overview

This guide helps you migrate components from the old auth system to the new consolidated auth pattern. Follow these steps to ensure a smooth transition.

---

## 🔧 Quick Migration Steps

### Step 1: Update Imports

**Before (Old Patterns):**
```typescript
// ❌ Remove these imports
import { useEnhancedAuth } from '@/hooks/use-enhanced-auth'
import { useAuthQuery } from '@/hooks/use-auth-query'
import { useConsolidatedAuth } from '@/hooks/use-consolidated-auth'
import { useConsolidatedAuthFixed } from '@/hooks/use-consolidated-auth-fixed'
```

**After (New Pattern):**
```typescript
// ✅ Use this single import
import { useAuth, useRequireAuth, signOut } from '@/hooks/use-auth'
```

### Step 2: Update Hook Usage

**Before:**
```typescript
// Old pattern 1
const { user, session, isLoading } = useEnhancedAuth()

// Old pattern 2
const { data: authData, isLoading } = useAuthQuery()
const user = authData?.user

// Old pattern 3
const { user, session, isAuthenticated, isLoading } = useConsolidatedAuth()
```

**After:**
```typescript
// ✅ New consolidated pattern
const { user, session, isLoading, isAuthenticated } = useAuth()

// Or for components that require authentication
const { user, session, isLoading, isAuthenticated } = useRequireAuth()
```

### Step 3: Update Logout Functionality

**Before:**
```typescript
// Old patterns
const supabase = getSupabaseClient()
await supabase.auth.signOut()

// Or manual session clearing
localStorage.removeItem('loyal_app_user_id')
```

**After:**
```typescript
// ✅ New pattern with error handling
import { signOut } from '@/hooks/use-auth'

const handleLogout = async () => {
  try {
    await signOut()
    // Automatically redirects to login
  } catch (error) {
    console.error('Logout failed:', error)
  }
}
```

---

## 📝 Component Migration Examples

### Example 1: Dashboard Component

**Before:**
```typescript
import { useEnhancedAuth } from '@/hooks/use-enhanced-auth'

export default function Dashboard() {
  const { user, session, isLoading } = useEnhancedAuth({
    redirectTo: '/login',
    redirectIfFound: false
  })

  if (isLoading) return <div>Loading...</div>
  if (!user) return <div>Please log in</div>

  return <div>Welcome {user.email}</div>
}
```

**After:**
```typescript
import { useRequireAuth } from '@/hooks/use-auth'

export default function Dashboard() {
  const { user, isLoading } = useRequireAuth()

  if (isLoading) return <div>Loading...</div>

  return <div>Welcome {user?.email}</div>
}
```

### Example 2: Navigation Component

**Before:**
```typescript
import { useAuthQuery } from '@/hooks/use-auth-query'
import { getSupabaseClient } from '@/lib/supabase'

export default function Navigation() {
  const { data: authData } = useAuthQuery()
  const user = authData?.user

  const handleLogout = async () => {
    const supabase = getSupabaseClient()
    await supabase.auth.signOut()
    window.location.href = '/login'
  }

  return (
    <nav>
      {user ? (
        <>
          <span>{user.email}</span>
          <button onClick={handleLogout}>Logout</button>
        </>
      ) : (
        <a href="/login">Login</a>
      )}
    </nav>
  )
}
```

**After:**
```typescript
import { useAuth, signOut } from '@/hooks/use-auth'

export default function Navigation() {
  const { user, isAuthenticated } = useAuth()

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <nav>
      {isAuthenticated ? (
        <>
          <span>{user?.email}</span>
          <button onClick={handleLogout}>Logout</button>
        </>
      ) : (
        <a href="/login">Login</a>
      )}
    </nav>
  )
}
```

### Example 3: Protected Route Component

**Before:**
```typescript
import { useConsolidatedAuth } from '@/hooks/use-consolidated-auth'
import { useRouter } from 'next/navigation'

export default function ProtectedPage() {
  const { isAuthenticated, isLoading } = useConsolidatedAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) return <div>Loading...</div>
  if (!isAuthenticated) return null

  return <div>Protected content</div>
}
```

**After:**
```typescript
import { useRequireAuth } from '@/hooks/use-auth'

export default function ProtectedPage() {
  const { user, isLoading } = useRequireAuth()

  if (isLoading) return <div>Loading...</div>

  return <div>Protected content for {user?.email}</div>
}
```

---

## 🔄 Migration Checklist

### Components to Update
- [ ] All components using `useEnhancedAuth`
- [ ] All components using `useAuthQuery`
- [ ] All components using `useConsolidatedAuth`
- [ ] All components using `useConsolidatedAuthFixed`
- [ ] Any components with manual auth state management

### Files to Check
- [ ] `app/(dashboard)/**/*.tsx` - Dashboard components
- [ ] `components/ui/**/*.tsx` - UI components
- [ ] `components/dashboard/**/*.tsx` - Dashboard-specific components
- [ ] `app/(auth)/**/*.tsx` - Auth-related pages
- [ ] Any other component files with auth logic

### Common Patterns to Replace

1. **Manual localStorage management:**
```typescript
// ❌ Remove this
localStorage.setItem('loyal_app_user_id', user.id)
localStorage.removeItem('loyal_app_user_id')
```

2. **Direct Supabase client auth calls:**
```typescript
// ❌ Replace this
const supabase = getSupabaseClient()
const { data: { user } } = await supabase.auth.getUser()

// ✅ With this
const { user } = useAuth()
```

3. **Multiple auth listeners:**
```typescript
// ❌ Remove additional listeners
useEffect(() => {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(...)
  return () => subscription.unsubscribe()
}, [])
```

---

## ⚠️ Important Notes

### Breaking Changes
1. **Auto-redirect behavior:** `useRequireAuth()` automatically redirects to `/login`
2. **No more manual localStorage:** Auth state is managed automatically
3. **Single auth listener:** Only one listener per app instance
4. **Consistent API:** All components use the same auth interface

### Performance Improvements
- **React Query caching:** Auth state is cached for 5 minutes
- **Single source of truth:** No conflicting auth states
- **Optimized re-renders:** Better dependency management

### Error Handling
- **Better error states:** Consistent error handling across app
- **Logout error handling:** Graceful failure handling
- **Network resilience:** Automatic retries with React Query

---

## 🧪 Testing Your Migration

After migrating each component, test these scenarios:

1. **Login Flow:**
   - User can log in successfully
   - Redirected to correct page after login
   - Auth state updates immediately

2. **Session Persistence:**
   - Refresh page maintains auth state
   - No "Multiple GoTrueClient" warnings in console

3. **Logout Flow:**
   - Logout clears auth state
   - Redirects to login page
   - Cannot access protected routes

4. **Protected Routes:**
   - Unauthenticated users redirected to login
   - Authenticated users can access content

---

## 🎉 Post-Migration Cleanup

Once all components are migrated:

1. **Remove old auth hook files:**
   - `hooks/use-enhanced-auth.ts`
   - `hooks/use-auth-query.ts`
   - `hooks/use-consolidated-auth.ts`
   - `hooks/use-consolidated-auth-fixed.ts`

2. **Remove legacy auth utilities:**
   - Any manual session management code
   - Redundant auth helper functions

3. **Update documentation:**
   - Component documentation
   - API documentation
   - Developer guides

---

**Last Updated:** $(date)
**Migration Status:** Ready for implementation
**Contact:** Development Team for questions
