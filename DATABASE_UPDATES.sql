-- ============================================================================
-- LOYAL PLATFORM - DATABASE SCHEMA IMPROVEMENTS
-- Dashboard Data Accuracy & Business Metrics Enhancement
--
-- Date: June 25, 2025
-- Purpose: Add constraints, materialized views, and business intelligence
-- Target: Supabase PostgreSQL Database
--
-- INSTRUCTIONS:
-- 1. Open Supabase SQL Editor
-- 2. Copy and paste this entire file
-- 3. Execute all commands (they are safe and backwards-compatible)
-- 4. Verify completion with the final SELECT statements
-- ============================================================================

-- =====================================================
-- STEP 1: Add Data Integrity Constraints
-- =====================================================

-- Ensure points cannot be negative
ALTER TABLE loyalty_members
ADD CONSTRAINT lifetime_points_non_negative
CHECK (lifetime_points >= 0);

ALTER TABLE loyalty_members
ADD CONSTRAINT redeemed_points_non_negative
CHECK (redeemed_points >= 0);

ALTER TABLE loyalty_members
ADD CONSTRAINT expired_points_non_negative
CHECK (expired_points >= 0);

-- Ensure redeemed points don't exceed lifetime points
ALTER TABLE loyalty_members
ADD CONSTRAINT redeemed_not_exceed_lifetime
CHECK (redeemed_points <= lifetime_points);

-- =====================================================
-- STEP 2: Create Dashboard Metrics View
-- =====================================================

-- Drop existing view if it exists
DROP MATERIALIZED VIEW IF EXISTS dashboard_metrics;

-- Create materialized view for consistent dashboard calculations
CREATE MATERIALIZED VIEW dashboard_metrics AS
WITH company_stats AS (
  SELECT
    c.id as company_id,
    c.name as company_name,

    -- Member counts
    COUNT(DISTINCT lm.id) as total_members,
    COUNT(DISTINCT CASE
      WHEN pt.created_at >= NOW() - INTERVAL '30 days'
      THEN pt.member_id
      END) as active_members_30d,

    -- Points totals from member table (source of truth)
    COALESCE(SUM(lm.lifetime_points), 0) as total_lifetime_points,
    COALESCE(SUM(lm.redeemed_points), 0) as total_redeemed_points,
    COALESCE(SUM(lm.expired_points), 0) as total_expired_points,

    -- Available points calculation
    COALESCE(SUM(lm.lifetime_points - COALESCE(lm.redeemed_points, 0) - COALESCE(lm.expired_points, 0)), 0) as total_available_points,

    -- Reward counts
    COUNT(DISTINCT r.id) as total_rewards,
    COUNT(DISTINCT CASE
      WHEN r.is_active = true AND r.expiration_date > NOW()
      THEN r.id
      END) as active_rewards,

    -- Transaction counts
    COUNT(DISTINCT pt.id) as total_transactions,
    COUNT(DISTINCT CASE
      WHEN pt.transaction_type = 'EARN'
      THEN pt.id
      END) as earn_transactions,
    COUNT(DISTINCT CASE
      WHEN pt.transaction_type = 'REDEEM'
      THEN pt.id
      END) as redeem_transactions,

    -- Date calculations
    NOW() as last_updated,
    MIN(lm.registration_date) as first_member_date,
    MAX(pt.created_at) as last_transaction_date

  FROM companies c
  LEFT JOIN loyalty_members lm ON lm.company_id = c.id
  LEFT JOIN points_transactions pt ON pt.company_id = c.id
  LEFT JOIN rewards r ON r.company_id = c.id
  GROUP BY c.id, c.name
)
SELECT
  *,
  -- Calculate redemption rate
  CASE
    WHEN total_lifetime_points > 0
    THEN ROUND((total_redeemed_points::decimal / total_lifetime_points::decimal) * 100, 2)
    ELSE 0
  END as redemption_rate_percentage,

  -- Calculate member growth (if we have historical data)
  CASE
    WHEN first_member_date IS NOT NULL
    THEN EXTRACT(days FROM NOW() - first_member_date)
    ELSE 0
  END as days_since_first_member

FROM company_stats;

-- Create index for faster queries
CREATE UNIQUE INDEX idx_dashboard_metrics_company_id ON dashboard_metrics(company_id);

-- =====================================================
-- STEP 3: Create Historical Metrics Table
-- =====================================================

-- Table to track daily snapshots for trend calculations
CREATE TABLE IF NOT EXISTS dashboard_metrics_history (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  snapshot_date DATE NOT NULL DEFAULT CURRENT_DATE,

  -- Snapshot of key metrics
  total_members INTEGER NOT NULL DEFAULT 0,
  active_members_30d INTEGER NOT NULL DEFAULT 0,
  total_lifetime_points INTEGER NOT NULL DEFAULT 0,
  total_redeemed_points INTEGER NOT NULL DEFAULT 0,
  total_available_points INTEGER NOT NULL DEFAULT 0,
  total_rewards INTEGER NOT NULL DEFAULT 0,
  active_rewards INTEGER NOT NULL DEFAULT 0,
  redemption_rate_percentage DECIMAL(5,2) NOT NULL DEFAULT 0,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure one snapshot per company per day
  UNIQUE(company_id, snapshot_date)
);

-- Create index for faster historical queries
CREATE INDEX idx_dashboard_metrics_history_company_date ON dashboard_metrics_history(company_id, snapshot_date);

-- =====================================================
-- STEP 4: Create Function to Calculate Growth Rates
-- =====================================================

-- Function to calculate growth percentage between two values
CREATE OR REPLACE FUNCTION calculate_growth_rate(current_value INTEGER, previous_value INTEGER)
RETURNS DECIMAL(5,2) AS $$
BEGIN
  IF previous_value = 0 OR previous_value IS NULL THEN
    RETURN CASE WHEN current_value > 0 THEN 100.0 ELSE 0.0 END;
  END IF;

  RETURN ROUND(((current_value - previous_value)::decimal / previous_value::decimal) * 100, 2);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 5: Create Function for Dashboard Data
-- =====================================================

-- Function to get complete dashboard data with growth rates
CREATE OR REPLACE FUNCTION get_dashboard_data(p_company_id UUID)
RETURNS TABLE(
  -- Current metrics
  total_members INTEGER,
  active_members_30d INTEGER,
  total_lifetime_points INTEGER,
  total_redeemed_points INTEGER,
  total_available_points INTEGER,
  total_rewards INTEGER,
  active_rewards INTEGER,
  redemption_rate_percentage DECIMAL(5,2),

  -- Growth rates (compared to 30 days ago)
  members_growth_rate DECIMAL(5,2),
  points_growth_rate DECIMAL(5,2),
  rewards_growth_rate DECIMAL(5,2),
  redemption_growth_rate DECIMAL(5,2),

  -- Metadata
  last_updated TIMESTAMP WITH TIME ZONE,
  has_historical_data BOOLEAN
) AS $$
DECLARE
  current_data dashboard_metrics%ROWTYPE;
  historical_data dashboard_metrics_history%ROWTYPE;
BEGIN
  -- Get current metrics
  SELECT * INTO current_data
  FROM dashboard_metrics
  WHERE company_id = p_company_id;

  -- Get historical data from 30 days ago
  SELECT * INTO historical_data
  FROM dashboard_metrics_history
  WHERE company_id = p_company_id
    AND snapshot_date = CURRENT_DATE - INTERVAL '30 days'
  ORDER BY snapshot_date DESC
  LIMIT 1;

  -- Return results
  RETURN QUERY
  SELECT
    current_data.total_members,
    current_data.active_members_30d,
    current_data.total_lifetime_points,
    current_data.total_redeemed_points,
    current_data.total_available_points,
    current_data.total_rewards,
    current_data.active_rewards,
    current_data.redemption_rate_percentage,

    -- Growth calculations
    calculate_growth_rate(current_data.total_members, historical_data.total_members),
    calculate_growth_rate(current_data.total_lifetime_points, historical_data.total_lifetime_points),
    calculate_growth_rate(current_data.total_rewards, historical_data.total_rewards),
    calculate_growth_rate(current_data.redemption_rate_percentage::INTEGER, historical_data.redemption_rate_percentage::INTEGER),

    current_data.last_updated,
    (historical_data.id IS NOT NULL) as has_historical_data;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 6: Create Trigger to Update Materialized View
-- =====================================================

-- Function to refresh dashboard metrics
CREATE OR REPLACE FUNCTION refresh_dashboard_metrics()
RETURNS TRIGGER AS $$
BEGIN
  REFRESH MATERIALIZED VIEW dashboard_metrics;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Triggers to auto-refresh the materialized view
DROP TRIGGER IF EXISTS trigger_refresh_dashboard_metrics_members ON loyalty_members;
DROP TRIGGER IF EXISTS trigger_refresh_dashboard_metrics_transactions ON points_transactions;
DROP TRIGGER IF EXISTS trigger_refresh_dashboard_metrics_rewards ON rewards;

CREATE TRIGGER trigger_refresh_dashboard_metrics_members
  AFTER INSERT OR UPDATE OR DELETE ON loyalty_members
  FOR EACH STATEMENT
  EXECUTE FUNCTION refresh_dashboard_metrics();

CREATE TRIGGER trigger_refresh_dashboard_metrics_transactions
  AFTER INSERT OR UPDATE OR DELETE ON points_transactions
  FOR EACH STATEMENT
  EXECUTE FUNCTION refresh_dashboard_metrics();

CREATE TRIGGER trigger_refresh_dashboard_metrics_rewards
  AFTER INSERT OR UPDATE OR DELETE ON rewards
  FOR EACH STATEMENT
  EXECUTE FUNCTION refresh_dashboard_metrics();

-- =====================================================
-- STEP 7: Create Daily Snapshot Function
-- =====================================================

-- Function to create daily snapshot for historical tracking
CREATE OR REPLACE FUNCTION create_daily_snapshot(p_company_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  snapshot_count INTEGER := 0;
  company_record UUID;
BEGIN
  -- If company_id provided, process just that company, otherwise all companies
  FOR company_record IN
    SELECT id FROM companies
    WHERE (p_company_id IS NULL OR id = p_company_id)
  LOOP
    INSERT INTO dashboard_metrics_history (
      company_id,
      snapshot_date,
      total_members,
      active_members_30d,
      total_lifetime_points,
      total_redeemed_points,
      total_available_points,
      total_rewards,
      active_rewards,
      redemption_rate_percentage
    )
    SELECT
      company_id,
      CURRENT_DATE,
      total_members,
      active_members_30d,
      total_lifetime_points,
      total_redeemed_points,
      total_available_points,
      total_rewards,
      active_rewards,
      redemption_rate_percentage
    FROM dashboard_metrics
    WHERE company_id = company_record
    ON CONFLICT (company_id, snapshot_date)
    DO UPDATE SET
      total_members = EXCLUDED.total_members,
      active_members_30d = EXCLUDED.active_members_30d,
      total_lifetime_points = EXCLUDED.total_lifetime_points,
      total_redeemed_points = EXCLUDED.total_redeemed_points,
      total_available_points = EXCLUDED.total_available_points,
      total_rewards = EXCLUDED.total_rewards,
      active_rewards = EXCLUDED.active_rewards,
      redemption_rate_percentage = EXCLUDED.redemption_rate_percentage;

    snapshot_count := snapshot_count + 1;
  END LOOP;

  RETURN snapshot_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 8: Initialize Data
-- =====================================================

-- Refresh the materialized view with current data
REFRESH MATERIALIZED VIEW dashboard_metrics;

-- Create initial snapshot for all companies
SELECT create_daily_snapshot();

-- =====================================================
-- STEP 9: Verify Installation
-- =====================================================

-- Query to verify everything is working
-- Run this after all above commands complete successfully
SELECT
  'Dashboard Metrics View' as component,
  COUNT(*) as record_count,
  'SUCCESS' as status
FROM dashboard_metrics
UNION ALL
SELECT
  'Historical Snapshots' as component,
  COUNT(*) as record_count,
  'SUCCESS' as status
FROM dashboard_metrics_history
UNION ALL
SELECT
  'Data Validation' as component,
  CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END as record_count,
  CASE WHEN COUNT(*) > 0 THEN 'SUCCESS' ELSE 'NO DATA' END as status
FROM loyalty_members;

-- =====================================================
-- STEP 10: Sample Query for Dashboard API
-- =====================================================

-- This is how your API should query the dashboard data:
/*
SELECT * FROM get_dashboard_data('your-company-uuid-here');

-- Or query the materialized view directly:
SELECT * FROM dashboard_metrics WHERE company_id = 'your-company-uuid-here';

-- For historical trends:
SELECT
  snapshot_date,
  total_members,
  total_lifetime_points,
  redemption_rate_percentage
FROM dashboard_metrics_history
WHERE company_id = 'your-company-uuid-here'
  AND snapshot_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY snapshot_date;
*/

-- =====================================================
-- STEP 11: Schedule Daily Snapshots (Optional)
-- =====================================================

-- Note: You'll need to set up a cron job or scheduled function in Supabase
-- to run this daily. This can be done through:
-- 1. Supabase Edge Functions with cron
-- 2. External cron job calling an API endpoint
-- 3. Database scheduler if available

-- Example for daily execution:
-- SELECT create_daily_snapshot();

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Database updates completed successfully!
Dashboard metrics view created and initialized.
Historical tracking enabled.
Run the verification query above to confirm.' as message;
