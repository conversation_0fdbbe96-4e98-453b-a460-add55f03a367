-- STEP 1: Create business_items from existing receipt_items
-- Run this in Supabase SQL Editor to populate business_items table

-- First, let's see what we're working with
SELECT
    ri.extracted_description,
    AVG(ri.unit_price) as avg_price,
    SUM(ri.quantity) as total_quantity,
    SUM(ri.total_price) as total_revenue,
    COUNT(*) as sales_count,
    MAX(r.created_at) as last_sold_date,
    r.company_id
FROM receipt_items ri
JOIN receipts r ON ri.receipt_id = r.id
WHERE ri.business_item_id IS NULL
  AND ri.extracted_description IS NOT NULL
  AND ri.extracted_description != ''
GROUP BY ri.extracted_description, r.company_id
ORDER BY sales_count DESC;

-- STEP 2: Insert business_items for the main company (d10aed7e-3116-403c-a572-c16ab870d761)
INSERT INTO business_items (
    id,
    company_id,
    item_name,
    standard_price,
    item_category,
    item_subcategory,
    description,
    total_sales_count,
    total_revenue,
    avg_selling_price,
    last_sold_date,
    is_active,
    created_at,
    updated_at
)
SELECT
    gen_random_uuid() as id,
    'd10aed7e-3116-403c-a572-c16ab870d761'::uuid as company_id,
    ri.extracted_description as item_name,
    AVG(ri.unit_price) as standard_price,
    'General' as item_category,
    NULL as item_subcategory,
    'Auto-generated from receipt data' as description,
    COUNT(*)::integer as total_sales_count,
    SUM(ri.total_price) as total_revenue,
    AVG(ri.unit_price) as avg_selling_price,
    MAX(r.created_at) as last_sold_date,
    true as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM receipt_items ri
JOIN receipts r ON ri.receipt_id = r.id
WHERE r.company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
  AND ri.business_item_id IS NULL
  AND ri.extracted_description IS NOT NULL
  AND ri.extracted_description != ''
GROUP BY ri.extracted_description;

-- STEP 3: Update receipt_items to link to the newly created business_items
UPDATE receipt_items
SET business_item_id = bi.id
FROM business_items bi, receipts r
WHERE receipt_items.receipt_id = r.id
  AND r.company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
  AND receipt_items.extracted_description = bi.item_name
  AND receipt_items.business_item_id IS NULL
  AND bi.company_id = 'd10aed7e-3116-403c-a572-c16ab870d761';
