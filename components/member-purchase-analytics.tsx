'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { ShoppingBag, TrendingUp, DollarSign, Package, ChevronDown, ChevronUp } from 'lucide-react'
import { useCompany } from '@/contexts/company-context'

interface PurchaseAnalytics {
  member_id: string
  member_name: string
  total_purchases: number
  total_spent: number
  most_purchased_items: Array<{
    item_name: string
    category?: string
    purchase_count: number
    total_spent: number
    average_price: number
  }>
  purchase_frequency: {
    daily_average: number
    monthly_total: number
    last_purchase_date: string
  }
}

interface MemberPurchaseAnalyticsProps {
  memberId: string
  memberName: string
  className?: string
}

export function MemberPurchaseAnalytics({ memberId, memberName, className }: MemberPurchaseAnalyticsProps) {
  const { company } = useCompany()
  const [analytics, setAnalytics] = useState<PurchaseAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expanded, setExpanded] = useState(false)

  useEffect(() => {
    if (!company?.id || !memberId) return

    const fetchAnalytics = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/members/${memberId}/purchase-analytics?companyId=${company.id}`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch purchase analytics')
        }

        const data = await response.json()
        setAnalytics(data)
      } catch (err) {
        console.error('Error fetching purchase analytics:', err)
        setError(err instanceof Error ? err.message : 'Failed to load analytics')
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [company?.id, memberId])

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Purchase Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <Skeleton className="h-16" />
            <Skeleton className="h-16" />
            <Skeleton className="h-16" />
          </div>
          <Skeleton className="h-32" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Purchase Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Unable to load purchase analytics</p>
            <p className="text-sm">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!analytics || analytics.total_purchases === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Purchase Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No purchase history available</p>
            <p>This member hasn&apos;t made any purchases yet</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const topItems = analytics.most_purchased_items.slice(0, expanded ? 10 : 5)

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingBag className="h-5 w-5" />
          Purchase Analytics - {memberName}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-600">Total Purchases</span>
            </div>
            <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
              {analytics.total_purchases}
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-600">Total Spent</span>
            </div>
            <div className="text-2xl font-bold text-green-700 dark:text-green-300">
              ETB {analytics.total_spent.toLocaleString()}
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-600">Avg per Purchase</span>
            </div>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
              ETB {Math.round(analytics.total_spent / analytics.total_purchases).toLocaleString()}
            </div>
          </div>
        </div>

        {/* Most Purchased Items */}
        <div>
          <h4 className="font-semibold mb-3 flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Most Purchased Items
          </h4>
          <div className="space-y-3">
            {topItems.map((item, index) => (
              <div key={item.item_name} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-full">
                    <span className="text-sm font-bold text-primary">#{index + 1}</span>
                  </div>
                  <div>
                    <div className="font-medium">{item.item_name}</div>
                    {item.category && (
                      <Badge variant="outline" className="text-xs">
                        {item.category}
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{item.purchase_count}x purchased</div>
                  <div className="text-sm text-muted-foreground">
                    ETB {item.total_spent.toLocaleString()} total
                  </div>
                  <div className="text-xs text-muted-foreground">
                    ETB {item.average_price.toLocaleString()} avg
                  </div>
                </div>
              </div>
            ))}
          </div>

          {analytics.most_purchased_items.length > 5 && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full mt-3"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-2" />
                  Show Less
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-2" />
                  Show More ({analytics.most_purchased_items.length - 5} more items)
                </>
              )}
            </Button>
          )}
        </div>

        {/* Purchase Frequency */}
        <div className="pt-4 border-t">
          <h4 className="font-semibold mb-3">Purchase Frequency</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Monthly Average:</span>
              <div className="font-semibold">{analytics.purchase_frequency.monthly_total} purchases</div>
            </div>
            <div>
              <span className="text-muted-foreground">Daily Average:</span>
              <div className="font-semibold">{analytics.purchase_frequency.daily_average.toFixed(1)} purchases</div>
            </div>
            <div>
              <span className="text-muted-foreground">Last Purchase:</span>
              <div className="font-semibold">
                {new Date(analytics.purchase_frequency.last_purchase_date).toLocaleDateString()}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
