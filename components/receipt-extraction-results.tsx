'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import {
  CheckCircle,
  AlertCircle,
  Edit,
  Save,
  X,
  TrendingUp,
  DollarSign,
  Calendar,
  Building,
  Receipt,
  Eye,
  EyeOff
} from 'lucide-react'

interface ReceiptItem {
  id: string
  name: string
  quantity: number
  unit_price: number
  total_price: number
  category?: string
  confidence: number
}

interface EnhancedReceiptData {
  id: string
  business_name: string
  receipt_date: string
  total_amount: number
  financial_system_number?: string
  items: ReceiptItem[]
  confidence: {
    overall: number
    business_name: number
    total_amount: number
    items: number
  }
  metadata: {
    processing_time: number
    template_used: string
    extraction_method: string
  }
}

interface ReceiptExtractionResultsProps {
  receiptData: EnhancedReceiptData
  onItemEdit: (itemId: string, updatedItem: ReceiptItem) => void
  onConfirm: () => void
  onReject: () => void
}

export function ReceiptExtractionResults({
  receiptData,
  onItemEdit,
  onConfirm,
  onReject
}: ReceiptExtractionResultsProps) {
  const [editingItemId, setEditingItemId] = useState<string | null>(null)
  const [showLowConfidenceOnly, setShowLowConfidenceOnly] = useState(false)
  const [editingItem, setEditingItem] = useState<ReceiptItem | null>(null)

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'text-green-600 bg-green-100'
    if (confidence >= 0.7) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.9) return 'High'
    if (confidence >= 0.7) return 'Medium'
    return 'Low'
  }

  const handleItemEdit = (item: ReceiptItem) => {
    setEditingItemId(item.id)
    setEditingItem({ ...item })
  }

  const handleSaveItem = () => {
    if (editingItem) {
      onItemEdit(editingItem.id, editingItem)
      setEditingItemId(null)
      setEditingItem(null)
    }
  }

  const handleCancelEdit = () => {
    setEditingItemId(null)
    setEditingItem(null)
  }

  const filteredItems = showLowConfidenceOnly
    ? receiptData.items.filter(item => item.confidence < 0.7)
    : receiptData.items

  const lowConfidenceItemsCount = receiptData.items.filter(item => item.confidence < 0.7).length

  return (
    <div className="space-y-6">
      {/* Header with Overall Confidence */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Receipt className="h-5 w-5" />
              <div>
                <CardTitle>Receipt Processing Complete</CardTitle>
                <CardDescription>
                  Extracted {receiptData.items.length} items using {receiptData.metadata.template_used}
                </CardDescription>
              </div>
            </div>
            <Badge className={`${getConfidenceColor(receiptData.confidence.overall)} font-medium`}>
              {Math.round(receiptData.confidence.overall * 100)}% Overall
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Business Information */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-gray-500" />
                <Label className="text-sm font-medium">Business</Label>
                <Badge className={getConfidenceColor(receiptData.confidence.business_name)}>
                  {getConfidenceLabel(receiptData.confidence.business_name)}
                </Badge>
              </div>
              <p className="text-sm">{receiptData.business_name}</p>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-gray-500" />
                <Label className="text-sm font-medium">Total</Label>
                <Badge className={getConfidenceColor(receiptData.confidence.total_amount)}>
                  {getConfidenceLabel(receiptData.confidence.total_amount)}
                </Badge>
              </div>
              <p className="text-sm font-mono">{receiptData.total_amount.toFixed(2)} ETB</p>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <Label className="text-sm font-medium">Date</Label>
              </div>
              <p className="text-sm">{receiptData.receipt_date}</p>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-gray-500" />
                <Label className="text-sm font-medium">FS Number</Label>
              </div>
              <p className="text-sm font-mono">{receiptData.financial_system_number || 'Not found'}</p>
            </div>
          </div>

          {/* Processing Metadata */}
          <div className="flex items-center justify-between text-xs text-gray-500 border-t pt-3">
            <span>Processing time: {receiptData.metadata.processing_time}ms</span>
            <span>Method: {receiptData.metadata.extraction_method}</span>
          </div>
        </CardContent>
      </Card>

      {/* Item-Level Extraction Results */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Extracted Items</CardTitle>
              <CardDescription>
                Review and edit item details. Items with low confidence are highlighted.
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {lowConfidenceItemsCount > 0 && (
                <Badge variant="destructive">
                  {lowConfidenceItemsCount} need review
                </Badge>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowLowConfidenceOnly(!showLowConfidenceOnly)}
                className="flex items-center gap-2"
              >
                {showLowConfidenceOnly ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                {showLowConfidenceOnly ? 'Show All' : 'Show Issues Only'}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {filteredItems.map((item, index) => (
            <div key={item.id} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                  <Badge className={getConfidenceColor(item.confidence)}>
                    {Math.round(item.confidence * 100)}%
                  </Badge>
                  {item.confidence < 0.7 && (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                </div>
                {editingItemId === item.id ? (
                  <div className="flex items-center gap-2">
                    <Button size="sm" onClick={handleSaveItem}>
                      <Save className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleCancelEdit}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleItemEdit(item)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {editingItemId === item.id && editingItem ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div className="space-y-1">
                    <Label className="text-xs">Item Name</Label>
                    <Input
                      value={editingItem.name}
                      onChange={(e) => setEditingItem({ ...editingItem, name: e.target.value })}
                      className="text-sm"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs">Quantity</Label>
                    <Input
                      type="number"
                      value={editingItem.quantity}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        quantity: parseFloat(e.target.value) || 0
                      })}
                      className="text-sm"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs">Unit Price</Label>
                    <Input
                      type="number"
                      step="0.01"
                      value={editingItem.unit_price}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        unit_price: parseFloat(e.target.value) || 0
                      })}
                      className="text-sm"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs">Total Price</Label>
                    <Input
                      type="number"
                      step="0.01"
                      value={editingItem.total_price}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        total_price: parseFloat(e.target.value) || 0
                      })}
                      className="text-sm"
                    />
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div className="space-y-1">
                    <Label className="text-xs text-gray-500">Item Name</Label>
                    <p className="text-sm font-medium">{item.name}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-gray-500">Quantity</Label>
                    <p className="text-sm">{item.quantity}</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-gray-500">Unit Price</Label>
                    <p className="text-sm font-mono">{item.unit_price.toFixed(2)} ETB</p>
                  </div>
                  <div className="space-y-1">
                    <Label className="text-xs text-gray-500">Total</Label>
                    <p className="text-sm font-mono font-medium">{item.total_price.toFixed(2)} ETB</p>
                  </div>
                </div>
              )}

              {item.category && (
                <Badge variant="secondary">
                  {item.category}
                </Badge>
              )}
            </div>
          ))}

          {filteredItems.length === 0 && showLowConfidenceOnly && (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-8 w-8 mx-auto mb-2" />
              <p>All items extracted with high confidence!</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={onReject} className="flex items-center gap-2">
          <X className="h-4 w-4" />
          Reject & Manual Entry
        </Button>
        <Button onClick={onConfirm} className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4" />
          Confirm & Create Transaction
        </Button>
      </div>
    </div>
  )
}
