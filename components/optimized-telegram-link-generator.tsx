'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { QRCodeSVG } from 'qrcode.react'
import { CheckCircle, Copy, ExternalLink, MessageCircle, QrCode, Type, Unlink } from 'lucide-react'
import { useState } from 'react'
import { useToast } from '@/components/ui/use-toast'
import {
  useTelegramStatus,
  useGenerateTelegramLink,
  useUnlinkTelegram
} from '@/hooks/use-telegram-status'

interface OptimizedTelegramLinkGeneratorProps {
  memberId: string
  memberName: string
  memberPhone?: string | null
}

export function OptimizedTelegramLinkGenerator({
  memberId,
  memberName,
  memberPhone,
}: OptimizedTelegramLinkGeneratorProps) {
  const [generatedLink, setGeneratedLink] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [showQRCode, setShowQRCode] = useState(false)
  const { toast } = useToast()

  // Use optimized React Query hooks
  const {
    data: telegramStatus,
    isLoading: statusLoading,
    refetch: refetchStatus
  } = useTelegramStatus(memberId)

  const generateLinkMutation = useGenerateTelegramLink()
  const unlinkMutation = useUnlinkTelegram()

  const isLinked = telegramStatus?.isLinked || false
  const isGenerating = generateLinkMutation.isPending
  const isUnlinking = unlinkMutation.isPending

  const handleGenerateLink = async () => {
    try {
      const result = await generateLinkMutation.mutateAsync({ memberId })
      setGeneratedLink(result.link)
      toast.success("Share this link with the member to connect their Telegram account.")
    } catch (error) {
      // Show specific error message from the mutation
      const errorMessage = error instanceof Error ? error.message : "Failed to generate link"
      toast.error(errorMessage)
    }
  }

  const handleUnlink = async () => {
    try {
      await unlinkMutation.mutateAsync({ memberId })
      setGeneratedLink(null) // Clear any generated link
      setShowQRCode(false) // Hide QR code
      toast.success(`${memberName} has been successfully unlinked from Telegram.`)
    } catch (error) {
      // Show specific error message from the mutation
      const errorMessage = error instanceof Error ? error.message : "Failed to unlink"
      toast.error(errorMessage)
    }
  }

  const copyToClipboard = async () => {
    if (!generatedLink) return

    try {
      await navigator.clipboard.writeText(generatedLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success("Telegram link copied to clipboard!")
    } catch (error) {
      console.error('Failed to copy:', error)
      toast.error("Please copy the link manually.")
    }
  }

  const shareViaWhatsApp = () => {
    if (!generatedLink || !memberPhone) return

    const message = `Hi ${memberName}! 👋\n\nConnect your Telegram account to our loyalty program for instant notifications and AI assistance:\n\n${generatedLink}\n\nJust click the link and follow the instructions. Thanks!`
    const whatsappUrl = `https://wa.me/${memberPhone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const shareViaSMS = () => {
    if (!generatedLink || !memberPhone) return

    const message = `Hi ${memberName}! Connect your Telegram to our loyalty program: ${generatedLink}`
    const smsUrl = `sms:${memberPhone}?body=${encodeURIComponent(message)}`
    window.open(smsUrl)
  }

  if (statusLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Telegram Integration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          Telegram Integration
        </CardTitle>
        <CardDescription>
          Real-time Telegram connection management with optimistic updates
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Real-time Connection Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Connection Status:</span>
          {isLinked ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Connected
            </Badge>
          ) : (
            <Badge variant="secondary">Not Connected</Badge>
          )}
        </div>

        {/* Real-time Telegram Info */}
        {isLinked && telegramStatus && (
          <div className="space-y-2 p-3 bg-green-50 rounded-lg">
            {telegramStatus.telegramChatId && (
              <div className="text-sm">
                <span className="font-medium">Chat ID:</span> {telegramStatus.telegramChatId}
              </div>
            )}
            {telegramStatus.telegramUsername && (
              <div className="text-sm">
                <span className="font-medium">Username:</span> @{telegramStatus.telegramUsername}
              </div>
            )}
            {telegramStatus.linkedAt && (
              <div className="text-sm">
                <span className="font-medium">Connected:</span> {new Date(telegramStatus.linkedAt).toLocaleDateString()}
              </div>
            )}
          </div>
        )}

        {/* Action Buttons with Optimistic Updates */}
        {isLinked ? (
          <div className="space-y-2">
            <Button
              onClick={handleUnlink}
              disabled={isUnlinking}
              variant="destructive"
              className="w-full"
            >
              <Unlink className="mr-2 h-4 w-4" />
              {isUnlinking ? 'Unlinking...' : 'Unlink Telegram'}
            </Button>
            <p className="text-xs text-muted-foreground text-center">
              This will disconnect {memberName} from Telegram notifications
            </p>
          </div>
        ) : (
          <Button
            onClick={handleGenerateLink}
            disabled={isGenerating}
            className="w-full"
          >
            {isGenerating ? 'Generating...' : 'Generate Telegram Link'}
          </Button>
        )}

        {/* Generated Link with Enhanced Sharing */}
        {generatedLink && (
          <div className="space-y-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Generated Link:</label>
              <div className="flex gap-2">
                <Input
                  value={generatedLink}
                  readOnly
                  className="font-mono text-xs"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyToClipboard}
                >
                  {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Enhanced Share Options */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Share with Member:</label>
              <div className="flex gap-2">
                {memberPhone && (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={shareViaWhatsApp}
                      className="flex-1"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      WhatsApp
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={shareViaSMS}
                      className="flex-1"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      SMS
                    </Button>
                  </>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyToClipboard}
                  className="flex-1"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy Link
                </Button>
              </div>
            </div>

            {/* QR Code Toggle */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">QR Code:</label>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowQRCode(!showQRCode)}
                >
                  {showQRCode ? <Type className="h-4 w-4 mr-1" /> : <QrCode className="h-4 w-4 mr-1" />}
                  {showQRCode ? 'Hide QR' : 'Show QR'}
                </Button>
              </div>

              {showQRCode && (
                <div className="flex justify-center p-4 bg-white rounded-lg border">
                  <div className="text-center space-y-2">
                    <QRCodeSVG
                      value={generatedLink}
                      size={200}
                      level="M"
                      includeMargin={true}
                      className="mx-auto"
                    />
                    <p className="text-xs text-muted-foreground max-w-[200px]">
                      Scan with phone camera or Telegram to connect
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Instructions */}
            <div className="p-3 bg-blue-50 rounded-lg text-sm">
              <p className="font-medium mb-1">How to use:</p>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Share the link with {memberName}</li>
                <li>They click the link to open Telegram</li>
                <li>They tap &quot;Start&quot; to connect their account</li>
                <li>Connection updates in real-time</li>
              </ol>
              <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                <p className="font-medium text-yellow-800">⚠️ Important:</p>
                <p className="text-yellow-700">Each Telegram account can only be linked to one loyalty member. If a Telegram account is already connected elsewhere, it must be unlinked first.</p>
              </div>
            </div>
          </div>
        )}

        {/* Manual Refresh Button */}
        <div className="pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetchStatus()}
            className="w-full"
          >
            Refresh Status
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
