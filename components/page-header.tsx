import React from "react";

interface PageHeaderProps {
  heading: string;
  subheading?: string;
  children?: React.ReactNode;
  icon?: React.ReactNode;
}

export function PageHeader({ heading, subheading, children, icon }: PageHeaderProps) {
  return (
    <div className="flex flex-col gap-2">
      {icon && (
        <div className="flex items-center gap-2">
          {icon}
          <h1 className="text-3xl font-bold tracking-tight">{heading}</h1>
        </div>
      )}
      {!icon && (
        <h1 className="text-3xl font-bold tracking-tight">{heading}</h1>
      )}
      {subheading && (
        <p className="text-muted-foreground">{subheading}</p>
      )}
      {children}
    </div>
  );
}
