"use client"

import * as React from "react"
import { Check, ChevronsUpDown, User } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface Member {
  id: string
  name: string
  loyalty_id: string
  email?: string
  available_points?: number
}

interface MemberComboboxProps {
  members: Member[]
  value?: string
  onValueChange: (value: string | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function MemberCombobox({
  members,
  value,
  onValueChange,
  placeholder = "Select a member...",
  disabled = false,
  className,
}: MemberComboboxProps) {
  const [open, setOpen] = React.useState(false)

  const selectedMember = members.find((member) => member.id === value)

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between bg-card border",
            !selectedMember && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 truncate">
            <User className="h-4 w-4 flex-shrink-0" />
            {selectedMember ? (
              <span className="truncate">
                {selectedMember.name} ({selectedMember.loyalty_id})
              </span>
            ) : (
              placeholder
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput
            placeholder="Search members..."
            className="h-9"
          />
          <CommandList>
            <CommandEmpty>No member found.</CommandEmpty>
            <CommandGroup>
              {members.map((member) => (
                <CommandItem
                  key={member.id}
                  value={`${member.name} ${member.loyalty_id} ${member.email || ''}`}
                  onSelect={() => {
                    onValueChange(member.id === value ? undefined : member.id)
                    setOpen(false)
                  }}
                  className="flex items-center gap-2"
                >
                  <User className="h-4 w-4 opacity-50" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{member.name}</div>
                    <div className="text-xs text-muted-foreground">
                      ID: {member.loyalty_id}
                      {member.available_points !== undefined && ` • ${member.available_points} pts`}
                      {member.email && ` • ${member.email}`}
                    </div>
                  </div>
                  <Check
                    className={cn(
                      "ml-auto h-4 w-4",
                      value === member.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
