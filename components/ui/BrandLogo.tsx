import React from 'react';

/**
 * <PERSON><PERSON><PERSON> renders the full logo (icon + gradient Loyal text) with brand colors.
 * - Icon uses: from-purple-600 to-amber-500 (brand gradient only, no flag colors)
 * - Text uses: from-purple-600 to-amber-500 (brand gradient)
 */
const BrandLogo: React.FC<{ className?: string; textClassName?: string; size?: number }> = ({ className = '', textClassName = '', size = 40 }) => (
  <div className={`flex items-center gap-2 ${className}`} style={{ lineHeight: 1 }}>
    <div
      className="relative rounded-xl bg-gradient-to-r from-purple-600 to-amber-500 p-[2px]"
      style={{ height: size, width: size, minWidth: size, transform: 'none' }}
    >
      {/* Always contrasting inner background */}
      <div className="absolute inset-0 rounded-xl bg-background dark:bg-card m-[1px]" />
      <div className="relative h-full w-full rounded-[10px] bg-gradient-to-r from-purple-600 to-amber-500 flex items-center justify-center">
        <svg
          className="h-5 w-5 text-white"
          viewBox="0 0 24 24"
          fill="currentColor"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z"
            fill="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 11.5C13.1046 11.5 14 10.6046 14 9.5C14 8.39543 13.1046 7.5 12 7.5C10.8954 7.5 10 8.39543 10 9.5C10 10.6046 10.8954 11.5 12 11.5ZM12 11.5V16.5"
            stroke="white"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
    </div>
    <span
      className={`text-xl font-bold bg-gradient-to-r from-purple-600 to-amber-500 bg-clip-text text-transparent ${textClassName}`}
      style={{ opacity: 1, transform: 'none', position: 'relative', display: 'inline-block' }}
    >
      Loyal
      <span
        className="text-black dark:text-white absolute top-1 -right-4 text-xs font-bold"
        style={{ fontSize: '0.65em', lineHeight: 1, verticalAlign: 'super' }}
      >
        ET
      </span>
    </span>
  </div>
);

export default BrandLogo;
