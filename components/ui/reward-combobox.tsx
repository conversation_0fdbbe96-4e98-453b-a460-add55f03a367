"use client"

import * as React from "react"
import { Check, ChevronsUpDown, Trophy } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"

export interface Reward {
  id: string
  title: string
  description?: string
  points_required: number
  reward_value: number
  reward_value_type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SERVICE'
  is_active: boolean
  expiry_date?: string
}

interface RewardComboboxProps {
  rewards: Reward[]
  value?: string
  onValueChange: (value: string | undefined) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function RewardCombobox({
  rewards,
  value,
  onValueChange,
  placeholder = "Select a reward...",
  disabled = false,
  className,
}: RewardComboboxProps) {
  const [open, setOpen] = React.useState(false)

  const selectedReward = rewards.find((reward) => reward.id === value)

  const formatRewardValue = (reward: Reward) => {
    if (reward.reward_value_type === 'PERCENTAGE') {
      return `${reward.reward_value}% off`
    } else if (reward.reward_value_type === 'FIXED_AMOUNT') {
      return `${reward.reward_value} Birr off`
    } else {
      return 'Free service'
    }
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
          disabled={disabled}
        >
          {selectedReward ? (
            <div className="flex items-center gap-2 w-full">
              <Trophy className="h-4 w-4 text-yellow-500" />
              <div className="flex-1 text-left">
                <div className="font-medium">{selectedReward.title}</div>
                <div className="text-sm text-muted-foreground">
                  {selectedReward.points_required} points • {formatRewardValue(selectedReward)}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Trophy className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">{placeholder}</span>
            </div>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command>
          <CommandInput placeholder="Search rewards..." />
          <CommandList>
            <CommandEmpty>No rewards found.</CommandEmpty>
            <CommandGroup>
              {rewards
                .filter((reward) => reward.is_active)
                .map((reward) => (
                <CommandItem
                  key={reward.id}
                  value={`${reward.title} ${reward.description} ${reward.points_required}`}
                  onSelect={() => {
                    onValueChange(reward.id === value ? undefined : reward.id)
                    setOpen(false)
                  }}
                >
                  <div className="flex items-center gap-2 w-full">
                    <Trophy className="h-4 w-4 text-yellow-500" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{reward.title}</span>
                        <Badge variant="secondary" className="text-xs">
                          {reward.points_required} pts
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {formatRewardValue(reward)}
                        </Badge>
                      </div>
                      {reward.description && (
                        <div className="text-sm text-muted-foreground">
                          {reward.description}
                        </div>
                      )}
                      {reward.expiry_date && (
                        <div className="text-xs text-yellow-600">
                          Expires: {new Date(reward.expiry_date).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                    <Check
                      className={cn(
                        "h-4 w-4",
                        value === reward.id ? "opacity-100" : "opacity-0"
                      )}
                    />
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
