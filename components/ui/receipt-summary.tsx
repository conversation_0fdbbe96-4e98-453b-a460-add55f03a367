'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { CheckCircle, AlertCircle, Receipt, ShoppingCart, Building2, Calendar, CreditCard, Hash, MapPin } from 'lucide-react'

// Enhanced receipt data interface
interface ReceiptItem {
  description: string
  quantity: number
  unit_price: number
  total_price: number
}

interface EnhancedReceiptData {
  business_name: string
  financial_system_number: string
  total_amount: number
  subtotal?: number
  tax_amount?: number
  payment_method: string
  business_tin?: string
  receipt_date: string
  business_location?: string
  items: ReceiptItem[]
  pos_system?: string
  confidence: number
  processing_notes?: string
}

interface ReceiptSummaryProps {
  receiptData: EnhancedReceiptData
  confidence?: number
  className?: string
  showItems?: boolean
  compact?: boolean
}

export function ReceiptSummary({
  receiptData,
  confidence,
  className = '',
  showItems = true,
  compact = false
}: ReceiptSummaryProps) {
  const displayConfidence = confidence || receiptData.confidence || 0
  const confidencePercentage = Math.round(displayConfidence * 100)
  const isHighConfidence = displayConfidence >= 0.8
  const isMediumConfidence = displayConfidence >= 0.6

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
      minimumFractionDigits: 2
    }).format(amount)
  }

  if (compact) {
    return (
      <Card className={`border-green-200 bg-green-50 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Receipt Processed</span>
            </div>
            <Badge
              variant={isHighConfidence ? "default" : isMediumConfidence ? "secondary" : "destructive"}
              className={isHighConfidence ? "bg-green-100 text-green-800" : isMediumConfidence ? "bg-yellow-100 text-yellow-800" : ""}
            >
              {confidencePercentage}% Confidence
            </Badge>
          </div>

          <div className="grid grid-cols-2 gap-3 text-sm">
            <div>
              <span className="text-green-700 font-medium">{receiptData.business_name}</span>
            </div>
            <div className="text-right">
              <span className="font-mono text-green-900">{formatCurrency(receiptData.total_amount)}</span>
            </div>
            {receiptData.items.length > 0 && (
              <div className="col-span-2 text-xs text-green-600">
                {receiptData.items.length} item{receiptData.items.length !== 1 ? 's' : ''} detected
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`border-green-200 bg-green-50 ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Receipt className="h-5 w-5" />
            Receipt Summary
          </CardTitle>
          <Badge
            variant={isHighConfidence ? "default" : isMediumConfidence ? "secondary" : "destructive"}
            className={isHighConfidence ? "bg-green-100 text-green-800" : isMediumConfidence ? "bg-yellow-100 text-yellow-800" : ""}
          >
            {confidencePercentage}% Confidence
          </Badge>
        </div>
        <CardDescription className="text-green-700">
          AI-extracted receipt information {!isHighConfidence && '- Please verify accuracy'}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Business Information */}
        <div className="space-y-3">
          <div className="flex items-start gap-3">
            <Building2 className="h-4 w-4 text-green-600 mt-0.5" />
            <div className="flex-1">
              <div className="font-medium text-green-900">{receiptData.business_name}</div>
              {receiptData.business_location && (
                <div className="text-sm text-green-700 flex items-center gap-1 mt-1">
                  <MapPin className="h-3 w-3" />
                  {receiptData.business_location}
                </div>
              )}
              {receiptData.business_tin && (
                <div className="text-xs text-green-600 mt-1">TIN: {receiptData.business_tin}</div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <Hash className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-xs font-medium text-green-700 uppercase tracking-wide">FS Number</div>
                <div className="text-sm text-green-900 font-mono">{receiptData.financial_system_number}</div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-xs font-medium text-green-700 uppercase tracking-wide">Date</div>
                <div className="text-sm text-green-900">{receiptData.receipt_date}</div>
              </div>
            </div>
          </div>

          {receiptData.payment_method && (
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-xs font-medium text-green-700 uppercase tracking-wide">Payment Method</div>
                <div className="text-sm text-green-900">{receiptData.payment_method}</div>
              </div>
            </div>
          )}
        </div>

        {/* Items List */}
        {showItems && receiptData.items && receiptData.items.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4 text-green-600" />
                <h4 className="font-medium text-green-800">Items Purchased</h4>
              </div>

              <div className="space-y-2">
                {receiptData.items.map((item, index) => (
                  <div key={index} className="flex items-center justify-between py-2 px-3 bg-white/50 rounded-md border border-green-100">
                    <div className="flex-1">
                      <div className="font-medium text-green-900 text-sm">{item.description}</div>
                      <div className="text-xs text-green-700">
                        {item.quantity} × {formatCurrency(item.unit_price)}
                      </div>
                    </div>
                    <div className="font-mono font-medium text-green-900">
                      {formatCurrency(item.total_price)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Financial Summary */}
        <Separator />
        <div className="space-y-2 bg-white/30 p-3 rounded-md">
          {receiptData.subtotal && (
            <div className="flex justify-between text-sm">
              <span className="text-green-700">Subtotal:</span>
              <span className="font-mono text-green-900">{formatCurrency(receiptData.subtotal)}</span>
            </div>
          )}

          {receiptData.tax_amount && receiptData.tax_amount > 0 && (
            <div className="flex justify-between text-sm">
              <span className="text-green-700">Tax (15%):</span>
              <span className="font-mono text-green-900">{formatCurrency(receiptData.tax_amount)}</span>
            </div>
          )}

          <Separator />
          <div className="flex justify-between font-medium">
            <span className="text-green-800">Total:</span>
            <span className="font-mono text-green-900 text-lg">{formatCurrency(receiptData.total_amount)}</span>
          </div>
        </div>

        {/* System Information */}
        {(receiptData.pos_system || receiptData.processing_notes) && (
          <>
            <Separator />
            <div className="text-xs text-green-600 space-y-1">
              {receiptData.pos_system && (
                <div>POS System: {receiptData.pos_system}</div>
              )}
              {receiptData.processing_notes && (
                <div>Notes: {receiptData.processing_notes}</div>
              )}
            </div>
          </>
        )}

        {/* Low confidence warning */}
        {!isHighConfidence && (
          <div className="flex items-center gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
            <AlertCircle className="h-4 w-4 text-amber-600" />
            <span className="text-xs text-amber-700">
              {isMediumConfidence
                ? "Please verify the extracted information below for accuracy"
                : "Low confidence extraction - please review and correct the information manually"
              }
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
