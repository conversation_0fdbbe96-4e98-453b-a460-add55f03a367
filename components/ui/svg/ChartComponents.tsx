'use client'

import React from 'react'

export const SmoothLineChart = ({
  data = [250, 300, 200, 350, 275, 400, 325, 375],
  height = 200,
  showTooltips = true,
  className = ""
}: {
  data?: number[];
  height?: number;
  showTooltips?: boolean;
  className?: string;
}) => {
  // Compute dimensions and scales
  const maxValue = Math.max(...data);
  const points: [number, number][] = data.map((d, i) => {
    const x = (i / (data.length - 1)) * 100;
    const y = 100 - (d / maxValue) * 100;
    return [x, y];
  });

  // Generate SVG paths using cardinal spline for smoothing
  const generateSmoothPath = (points: [number, number][], tension = 0.5): string => {
    // Simple implementation of cardinal spline
    const getControlPoints = (p0: [number, number], p1: [number, number], p2: [number, number], t: number): [[number, number], [number, number]] => {
      const d01 = Math.sqrt(Math.pow(p1[0] - p0[0], 2) + Math.pow(p1[1] - p0[1], 2));
      const d12 = Math.sqrt(Math.pow(p2[0] - p1[0], 2) + Math.pow(p2[1] - p1[1], 2));

      const fa = t * d01 / (d01 + d12);
      const fb = t * d12 / (d01 + d12);

      const c1x = p1[0] - fa * (p2[0] - p0[0]);
      const c1y = p1[1] - fa * (p2[1] - p0[1]);
      const c2x = p1[0] + fb * (p2[0] - p0[0]);
      const c2y = p1[1] + fb * (p2[1] - p0[1]);

      return [[c1x, c1y], [c2x, c2y]];
    };

    if (points.length < 3) {
      return points.map(([x, y], i: number) => `${i === 0 ? 'M' : 'L'}${x},${y}`).join(' ');
    }

    let path = `M${points[0][0]},${points[0][1]}`;

    for (let i = 0; i < points.length - 2; i++) {
      const [c1, c2] = getControlPoints(points[i], points[i + 1], points[i + 2], tension);
      path += ` C${c1[0]},${c1[1]} ${c2[0]},${c2[1]} ${points[i + 2][0]},${points[i + 2][1]}`;
    }

    return path;
  };

  const linePath = generateSmoothPath(points);
  const areaPath = linePath + ` L100,100 L0,100 Z`;

  return (
    <div className={`relative h-${height} w-full ${className}`}>
      {/* Chart grid */}
      <div className="absolute inset-0 grid grid-cols-7 grid-rows-5">
        {Array(35).fill(null).map((_, i) => (
          <div key={i} className="border-t border-l border-gray-100 dark:border-gray-700"></div>
        ))}
      </div>

      {/* Chart visualization */}
      <div className="absolute inset-0">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <linearGradient id="chartGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#8B5CF6" />
              <stop offset="100%" stopColor="#F59E0B" />
            </linearGradient>
            <linearGradient id="chartAreaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.3" />
              <stop offset="100%" stopColor="#F59E0B" stopOpacity="0.05" />
            </linearGradient>
          </defs>

          {/* Line */}
          <path
            d={linePath}
            fill="none"
            stroke="url(#chartGradient)"
            strokeWidth="2"
            vectorEffect="non-scaling-stroke"
          />

          {/* Area fill */}
          <path
            d={areaPath}
            fill="url(#chartAreaGradient)"
            opacity="0.5"
          />
        </svg>

        {/* Axis labels */}
        <div className="absolute inset-y-0 left-0 flex flex-col justify-between text-xs text-gray-500 px-2">
          <div>{maxValue}</div>
          <div>{Math.round(maxValue * 0.75)}</div>
          <div>{Math.round(maxValue * 0.5)}</div>
          <div>{Math.round(maxValue * 0.25)}</div>
          <div>0</div>
        </div>

        {/* Tooltips */}
        {showTooltips && (
          <div className="absolute inset-0">
            {data.map((value, i) => {
              const x = (i / (data.length - 1)) * 100;
              const y = 100 - (value / maxValue) * 100;

              return (
                <div
                  key={i}
                  className="absolute w-4 h-4 rounded-full opacity-0 hover:opacity-100 cursor-pointer group"
                  style={{
                    left: `${x}%`,
                    top: `${y}%`,
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <div className="absolute w-3 h-3 bg-white dark:bg-gray-800 rounded-full border-2 border-purple-500 dark:border-purple-400 shadow-sm"></div>

                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-full bg-white dark:bg-gray-800 text-xs px-2 py-1 rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                    <span className="font-medium">{value}</span>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export const YAxisLabels = ({ values = [400, 300, 200, 100, 0], className = "" }: {
  values?: number[];
  className?: string;
}) => (
  <div className={`absolute left-0 inset-y-0 w-8 flex flex-col justify-between text-[10px] text-gray-500 dark:text-gray-400 pointer-events-none ${className}`}>
    {values.map((value, i) => (
      <div key={i} className="text-right pr-1">{value}</div>
    ))}
  </div>
);

export const XAxisLabels = ({ labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], className = "" }: {
  labels?: string[];
  className?: string;
}) => (
  <div className={`absolute bottom-0 left-0 right-0 flex justify-between text-xs text-gray-500 dark:text-gray-400 ${className}`}>
    {labels.map((label, i) => (
      <div key={i}>{label}</div>
    ))}
  </div>
);
