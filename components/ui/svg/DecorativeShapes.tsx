'use client'

import React from 'react'

export const GridPattern = () => (
  <div className="absolute inset-0 bg-grid-pattern opacity-[0.02] dark:opacity-[0.02]" 
    style={{ 
      backgroundImage: 'linear-gradient(to right, currentColor 1px, transparent 1px), linear-gradient(to bottom, currentColor 1px, transparent 1px)', 
      backgroundSize: '60px 60px' 
    }}>
  </div>
)

export const CrossShape = () => (
  <svg className="absolute top-20 left-10 w-24 h-24 text-purple-200 dark:text-purple-900/30 opacity-40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20 20 L80 20 L80 80 L20 80 Z" stroke="currentColor" strokeWidth="2" />
    <path d="M35 35 L65 65" stroke="currentColor" strokeWidth="2" />
    <path d="M35 65 L65 35" stroke="currentColor" strokeWidth="2" />
  </svg>
)

export const RoundedRectShape = () => (
  <svg className="absolute bottom-20 right-10 w-32 h-32 text-amber-200 dark:text-amber-900/30 opacity-40" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="20" y="20" width="60" height="60" rx="10" stroke="currentColor" strokeWidth="2" />
    <circle cx="50" cy="50" r="20" stroke="currentColor" strokeWidth="2" />
  </svg>
)

export const ChartDecorations = () => (
  <div className="absolute inset-0 overflow-hidden">
    <div className="absolute top-0 right-0 w-[300px] md:w-[500px] h-[300px] md:h-[500px] bg-purple-200/30 dark:bg-purple-900/20 rounded-full filter blur-3xl -translate-y-1/2 translate-x-1/3 animate-pulse"></div>
    <div className="absolute bottom-0 left-0 w-[300px] md:w-[500px] h-[300px] md:h-[500px] bg-amber-200/30 dark:bg-amber-900/20 rounded-full filter blur-3xl translate-y-1/2 -translate-x-1/3 animate-pulse" style={{ animationDelay: '2s' }}></div>
  </div>
)

export const DashboardDecorativePanel = () => (
  <svg className="absolute top-1/3 right-1/4 w-32 h-32 text-gray-300 dark:text-gray-700 opacity-30" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M25 25 L75 25 L75 75 L25 75 Z" stroke="currentColor" strokeWidth="2" />
    <path d="M40 40 L60 60" stroke="currentColor" strokeWidth="2" />
    <path d="M40 60 L60 40" stroke="currentColor" strokeWidth="2" />
  </svg>
)
