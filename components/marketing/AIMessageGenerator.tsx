'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Sparkles, Copy, RefreshCw, Lightbulb } from 'lucide-react'
import { useAIMarketing } from '@/hooks/use-ai-marketing'
import { toast } from 'sonner'

interface AIMessageGeneratorProps {
  onMessageGenerated: (message: string, subjectLines: string[]) => void
  businessName?: string
  businessType?: string
  initialMessage?: string
}

export function AIMessageGenerator({ 
  onMessageGenerated, 
  businessName = '', 
  businessType = '',
  initialMessage = ''
}: AIMessageGeneratorProps) {
  const { generateMessage, optimizeCampaign, isGenerating, isOptimizing } = useAIMarketing()
  const [formData, setFormData] = useState({
    campaignType: 'promotional',
    targetAudience: 'all_members',
    tone: 'friendly',
    keyMessage: '',
    callToAction: 'Visit us today!'
  })
  const [generatedContent, setGeneratedContent] = useState<{
    message: string
    subjectLines: string[]
  } | null>(null)
  const [optimization, setOptimization] = useState<{
    overallScore: number
    improvements: Array<{
      category: string
      suggestion: string
      impact: string
      reason: string
    }>
    optimizedMessage: string
    keyStrengths: string[]
    potentialIssues: string[]
  } | null>(null)

  const campaignTypes = [
    { value: 'promotional', label: 'Promotional Offer' },
    { value: 'birthday', label: 'Birthday Reward' },
    { value: 'loyalty_milestone', label: 'Loyalty Milestone' },
    { value: 'new_product', label: 'New Product Launch' },
    { value: 'seasonal', label: 'Seasonal Campaign' },
    { value: 'reactivation', label: 'Member Reactivation' },
    { value: 'referral', label: 'Referral Program' },
    { value: 'feedback', label: 'Feedback Request' }
  ]

  const tones = [
    { value: 'friendly', label: 'Friendly & Warm' },
    { value: 'professional', label: 'Professional' },
    { value: 'excited', label: 'Excited & Energetic' },
    { value: 'exclusive', label: 'Exclusive & Premium' },
    { value: 'casual', label: 'Casual & Relaxed' },
    { value: 'urgent', label: 'Urgent & Action-Oriented' }
  ]

  const audiences = [
    { value: 'all_members', label: 'All Members' },
    { value: 'vip_members', label: 'VIP Members' },
    { value: 'new_members', label: 'New Members' },
    { value: 'inactive_members', label: 'Inactive Members' },
    { value: 'high_spenders', label: 'High Spenders' },
    { value: 'frequent_visitors', label: 'Frequent Visitors' }
  ]

  const handleGenerate = async () => {
    if (!formData.keyMessage.trim()) {
      toast.error('Please enter a key message')
      return
    }

    const result = await generateMessage({
      ...formData,
      businessName: businessName || 'Your Business',
      businessType: businessType || 'retail'
    })

    if (result) {
      setGeneratedContent({
        message: result.message,
        subjectLines: result.subjectLines
      })
      onMessageGenerated(result.message, result.subjectLines)
    }
  }

  const handleOptimize = async () => {
    if (!generatedContent?.message && !initialMessage) {
      toast.error('No message to optimize')
      return
    }

    const messageToOptimize = generatedContent?.message || initialMessage
    const result = await optimizeCampaign({
      message: messageToOptimize,
      targetAudience: formData.targetAudience,
      businessType: businessType || 'retail',
      campaignGoal: formData.campaignType
    })

    if (result) {
      setOptimization(result)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast.success('Copied to clipboard!')
  }

  const useOptimizedMessage = () => {
    if (optimization?.optimizedMessage) {
      setGeneratedContent(prev => prev ? {
        ...prev,
        message: optimization.optimizedMessage
      } : {
        message: optimization.optimizedMessage,
        subjectLines: []
      })
      onMessageGenerated(optimization.optimizedMessage, generatedContent?.subjectLines || [])
      toast.success('Optimized message applied!')
    }
  }

  return (
    <div className="space-y-6">
      {/* AI Generator Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-600" />
            AI Marketing Message Generator
          </CardTitle>
          <CardDescription>
            Let AI create compelling marketing messages tailored to your audience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="campaignType">Campaign Type</Label>
              <Select
                value={formData.campaignType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, campaignType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {campaignTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetAudience">Target Audience</Label>
              <Select
                value={formData.targetAudience}
                onValueChange={(value) => setFormData(prev => ({ ...prev, targetAudience: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {audiences.map(audience => (
                    <SelectItem key={audience.value} value={audience.value}>
                      {audience.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tone">Tone</Label>
              <Select
                value={formData.tone}
                onValueChange={(value) => setFormData(prev => ({ ...prev, tone: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {tones.map(tone => (
                    <SelectItem key={tone.value} value={tone.value}>
                      {tone.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="callToAction">Call to Action</Label>
              <Input
                id="callToAction"
                value={formData.callToAction}
                onChange={(e) => setFormData(prev => ({ ...prev, callToAction: e.target.value }))}
                placeholder="e.g., Visit us today!"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="keyMessage">Key Message *</Label>
            <Textarea
              id="keyMessage"
              value={formData.keyMessage}
              onChange={(e) => setFormData(prev => ({ ...prev, keyMessage: e.target.value }))}
              placeholder="What's the main message you want to convey? (e.g., 20% off all items, New loyalty tier benefits, etc.)"
              rows={3}
            />
          </div>

          <Button 
            onClick={handleGenerate} 
            disabled={isGenerating || !formData.keyMessage.trim()}
            className="w-full"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            {isGenerating ? 'Generating...' : 'Generate AI Message'}
          </Button>
        </CardContent>
      </Card>

      {/* Generated Content */}
      {generatedContent && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Generated Marketing Message
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleOptimize}
                  disabled={isOptimizing}
                >
                  <Lightbulb className="w-4 h-4 mr-2" />
                  {isOptimizing ? 'Analyzing...' : 'Optimize'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(generatedContent.message)}
                >
                  <Copy className="w-4 h-4 mr-2" />
                  Copy
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-900 whitespace-pre-wrap">
                {generatedContent.message}
              </p>
            </div>

            {generatedContent.subjectLines.length > 0 && (
              <div>
                <Label className="text-sm font-medium">Suggested Subject Lines:</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {generatedContent.subjectLines.map((subject, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="cursor-pointer hover:bg-gray-200"
                      onClick={() => copyToClipboard(subject)}
                    >
                      {subject}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Optimization Results */}
      {optimization && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-yellow-600" />
              AI Campaign Analysis
              <Badge variant={optimization.overallScore >= 8 ? "default" : optimization.overallScore >= 6 ? "secondary" : "destructive"}>
                Score: {optimization.overallScore}/10
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {optimization.keyStrengths.length > 0 && (
              <div>
                <Label className="text-sm font-medium text-green-700">Key Strengths:</Label>
                <ul className="list-disc list-inside text-sm text-gray-600 mt-1">
                  {optimization.keyStrengths.map((strength: string, index: number) => (
                    <li key={index}>{strength}</li>
                  ))}
                </ul>
              </div>
            )}

            {optimization.improvements.length > 0 && (
              <div>
                <Label className="text-sm font-medium text-blue-700">Improvement Suggestions:</Label>
                <div className="space-y-2 mt-2">
                  {optimization.improvements.map((improvement, index: number) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-sm">{improvement.category}</span>
                        <Badge variant={improvement.impact === 'High' ? 'destructive' : improvement.impact === 'Medium' ? 'secondary' : 'outline'}>
                          {improvement.impact} Impact
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-1">{improvement.suggestion}</p>
                      <p className="text-xs text-gray-500">{improvement.reason}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {optimization.optimizedMessage && optimization.optimizedMessage !== generatedContent?.message && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-sm font-medium text-purple-700">Optimized Message:</Label>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={useOptimizedMessage}
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Use This Version
                  </Button>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <p className="text-sm text-gray-900 whitespace-pre-wrap">
                    {optimization.optimizedMessage}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
