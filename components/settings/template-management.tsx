'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Plus, Trash2, AlertCircle, CheckCircle } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import { useCompany } from '@/contexts/company-context'
import { toast } from 'sonner'
import { Skeleton } from '@/components/ui/skeleton'

interface ReceiptTemplate {
  id: string
  template_name: string
  template_image_url?: string
  ai_prompt_context?: string
  confidence_threshold: number
  is_active: boolean
  total_extractions: number
  successful_extractions: number
  avg_confidence_score: number
  created_at: string
  updated_at: string
}

interface TemplateForm {
  template_name: string
  ai_prompt_context: string
  confidence_threshold: number
}

export default function TemplateManagement() {
  const { company } = useCompany()

  const [templates, setTemplates] = useState<ReceiptTemplate[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [showUploadForm, setShowUploadForm] = useState(false)

  const [templateForm, setTemplateForm] = useState<TemplateForm>({
    template_name: '',
    ai_prompt_context: '',
    confidence_threshold: 0.8
  })

  const fetchTemplates = useCallback(async () => {
    if (!company?.id) return

    setIsLoading(true)
    const supabase = createClient()

    try {
      const { data, error } = await supabase
        .from('receipt_templates')
        .select('*')
        .eq('company_id', company.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching templates:', error)
        toast.error("Failed to fetch receipt templates")
      } else {
        setTemplates(data || [])
      }
    } catch (error) {
      console.error('Exception fetching templates:', error)
      toast.error("Failed to connect to database")
    } finally {
      setIsLoading(false)
    }
  }, [company?.id])

  useEffect(() => {
    fetchTemplates()
  }, [fetchTemplates])

  const handleInputChange = (field: keyof TemplateForm, value: string | number) => {
    setTemplateForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const resetForm = () => {
    setTemplateForm({
      template_name: '',
      ai_prompt_context: '',
      confidence_threshold: 0.8
    })
    setShowUploadForm(false)
  }

  const handleCreateTemplate = async () => {
    if (!company?.id) {
      toast.error("Company information not available")
      return
    }

    if (!templateForm.template_name.trim()) {
      toast.error("Template name is required")
      return
    }

    setIsUploading(true)
    const supabase = createClient()

    try {
      const { error } = await supabase
        .from('receipt_templates')
        .insert({
          company_id: company.id,
          template_name: templateForm.template_name,
          ai_prompt_context: templateForm.ai_prompt_context || 'Extract receipt data including items, prices, and totals.',
          confidence_threshold: templateForm.confidence_threshold,
          is_active: true,
          total_extractions: 0,
          successful_extractions: 0,
          avg_confidence_score: 0
        })

      if (error) {
        console.error('Error creating template:', error)
        toast.error("Failed to create template")
      } else {
        toast.success("Template created successfully")
        resetForm()
        fetchTemplates()
      }
    } catch (error) {
      console.error('Exception creating template:', error)
      toast.error("Failed to create template")
    } finally {
      setIsUploading(false)
    }
  }

  const handleToggleActive = async (templateId: string, currentStatus: boolean) => {
    const supabase = createClient()

    try {
      const { error } = await supabase
        .from('receipt_templates')
        .update({ is_active: !currentStatus })
        .eq('id', templateId)

      if (error) {
        console.error('Error updating template:', error)
        toast.error("Failed to update template status")
      } else {
        toast.success("Template status updated")
        fetchTemplates()
      }
    } catch (error) {
      console.error('Exception updating template:', error)
      toast.error("Failed to update template")
    }
  }

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('Are you sure you want to delete this template? This action cannot be undone.')) {
      return
    }

    const supabase = createClient()

    try {
      const { error } = await supabase
        .from('receipt_templates')
        .delete()
        .eq('id', templateId)

      if (error) {
        console.error('Error deleting template:', error)
        toast.error("Failed to delete template")
      } else {
        toast.success("Template deleted successfully")
        fetchTemplates()
      }
    } catch (error) {
      console.error('Exception deleting template:', error)
      toast.error("Failed to delete template")
    }
  }

  const calculateAccuracy = (template: ReceiptTemplate) => {
    if (template.total_extractions === 0) return 0
    return Math.round((template.successful_extractions / template.total_extractions) * 100)
  }

  if (!company) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Template Management</CardTitle>
          <CardDescription>Loading company information...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-20 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Upload New Template Form */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Receipt Templates</CardTitle>
              <CardDescription>
                Manage AI templates for improved receipt data extraction
              </CardDescription>
            </div>
            <Button
              onClick={() => setShowUploadForm(!showUploadForm)}
              variant={showUploadForm ? "outline" : "default"}
            >
              <Plus className="h-4 w-4 mr-2" />
              {showUploadForm ? 'Cancel' : 'Add Template'}
            </Button>
          </div>
        </CardHeader>

        {showUploadForm && (
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="templateName">Template Name *</Label>
                <Input
                  id="templateName"
                  placeholder="e.g., Standard POS Receipt v1.0"
                  value={templateForm.template_name}
                  onChange={(e) => handleInputChange('template_name', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="confidenceThreshold">Confidence Threshold</Label>
                <Input
                  id="confidenceThreshold"
                  type="number"
                  min="0"
                  max="1"
                  step="0.1"
                  placeholder="0.8"
                  value={templateForm.confidence_threshold}
                  onChange={(e) => handleInputChange('confidence_threshold', parseFloat(e.target.value) || 0.8)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="aiPrompt">AI Prompt Context</Label>
              <Textarea
                id="aiPrompt"
                placeholder="Describe what this template should extract from receipts..."
                value={templateForm.ai_prompt_context}
                onChange={(e) => handleInputChange('ai_prompt_context', e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleCreateTemplate}
                disabled={isUploading || !templateForm.template_name.trim()}
              >
                {isUploading ? 'Creating...' : 'Create Template'}
              </Button>
              <Button variant="outline" onClick={resetForm}>
                Reset
              </Button>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Templates List */}
      <Card>
        <CardHeader>
          <CardTitle>Active Templates ({templates.length})</CardTitle>
          <CardDescription>
            Manage your existing receipt templates
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-24 w-full" />
              ))}
            </div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Templates Found</h3>
              <p className="text-gray-600 mb-4">
                Create your first template to improve receipt processing accuracy.
              </p>
              <Button onClick={() => setShowUploadForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Template
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {templates.map((template) => {
                const accuracy = calculateAccuracy(template)
                return (
                  <div key={template.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-medium">{template.template_name}</h3>
                          <Badge variant={template.is_active ? "default" : "secondary"}>
                            {template.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>

                        <div className="text-sm text-gray-600 mb-2">
                          {template.ai_prompt_context || 'No AI prompt context provided'}
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Accuracy:</span>
                            <div className="flex items-center gap-1">
                              {accuracy >= 80 ? (
                                <CheckCircle className="h-3 w-3 text-green-500" />
                              ) : (
                                <AlertCircle className="h-3 w-3 text-yellow-500" />
                              )}
                              <span className="font-medium">{accuracy}%</span>
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-500">Extractions:</span>
                            <div className="font-medium">{template.total_extractions}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Successful:</span>
                            <div className="font-medium">{template.successful_extractions}</div>
                          </div>
                          <div>
                            <span className="text-gray-500">Confidence:</span>
                            <div className="font-medium">{Math.round(template.avg_confidence_score * 100)}%</div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleActive(template.id, template.is_active)}
                        >
                          {template.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTemplate(template.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Performance Overview */}
      {templates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
            <CardDescription>
              Template performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(templates.reduce((acc, t) => acc + calculateAccuracy(t), 0) / templates.length)}%
                </div>
                <div className="text-sm text-gray-600">Average Accuracy</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {templates.reduce((acc, t) => acc + t.total_extractions, 0)}
                </div>
                <div className="text-sm text-gray-600">Total Extractions</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">
                  {templates.filter(t => t.is_active).length}
                </div>
                <div className="text-sm text-gray-600">Active Templates</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.round(templates.reduce((acc, t) => acc + t.avg_confidence_score, 0) / templates.length * 100)}%
                </div>
                <div className="text-sm text-gray-600">Avg Confidence</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
