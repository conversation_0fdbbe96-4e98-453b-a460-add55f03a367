import { useEffect } from 'react'
import { useBirthdayEligibility } from '@/hooks/use-birthday-eligibility'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Cake, Gift, ChevronRight } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'

interface BirthdayRewardsBannerProps {
  memberId: string
}

export function BirthdayRewardsBanner({ memberId }: BirthdayRewardsBannerProps) {
  const router = useRouter()
  const { data, isLoading, error } = useBirthdayEligibility(memberId)
  
  // Show toast on error
  useEffect(() => {
    if (error) {
      toast.error('Failed to check birthday rewards eligibility')
    }
  }, [error])
  
  // If not eligible or no rewards, don't render anything
  if (isLoading || !data || !data.isEligible || data.birthdayRewards.length === 0) {
    return null
  }
  
  const { birthdayRewards } = data
  
  return (
    <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10 shadow-md">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <Cake className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-bold text-primary">Birthday Rewards Available!</CardTitle>
        </div>
        <CardDescription>
          Special rewards just for your birthday! Available for a limited time.
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="space-y-2">
          {birthdayRewards.map((reward) => (
            <div key={reward.id} className="flex items-center justify-between rounded-lg bg-background/80 p-2">
              <div className="flex items-center gap-2">
                <Gift className="h-4 w-4 text-primary" />
                <div>
                  <p className="font-medium">{reward.title}</p>
                  <p className="text-xs text-muted-foreground">{reward.points_required} points</p>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-8 w-8 p-0"
                onClick={() => router.push(`/rewards/${reward.id}`)}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="default" 
          size="sm" 
          className="w-full"
          onClick={() => router.push('/rewards')}
        >
          View All Birthday Rewards
        </Button>
      </CardFooter>
    </Card>
  )
}
