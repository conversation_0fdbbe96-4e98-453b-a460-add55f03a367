'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Copy, MessageCircle, ExternalLink, CheckCircle, Unlink, QrCode, Type } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import { QRCodeSVG } from 'qrcode.react'

interface TelegramLinkGeneratorProps {
  memberId: string
  memberName: string
  memberPhone?: string | null
  telegramChatId?: string | null
  telegramUsername?: string | null
}

export function TelegramLinkGenerator({
  memberId,
  memberName,
  memberPhone,
  telegramChatId,
  telegramUsername
}: TelegramLinkGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isUnlinking, setIsUnlinking] = useState(false)
  const [generatedLink, setGeneratedLink] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [currentlyLinked, setCurrentlyLinked] = useState(!!telegramChatId)
  const [showQRCode, setShowQRCode] = useState(false)
  const { toast } = useToast()

  const isLinked = currentlyLinked

  const generateTelegramLink = async () => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/telegram/generate-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberId: memberId,
          chatId: 'pending' // This will be filled when user clicks the link
        }),
      })

      const data = await response.json()

      if (response.ok && data.link) {
        setGeneratedLink(data.link)
        toast.success("Share this link with the member to connect their Telegram account.")
      } else {
        throw new Error(data.error || 'Failed to generate link')
      }
    } catch (error) {
      console.error('Error generating Telegram link:', error)
      toast.error("Failed to generate Telegram link. Please try again.")
    } finally {
      setIsGenerating(false)
    }
  }

  const unlinkTelegram = async () => {
    setIsUnlinking(true)
    try {
      const response = await fetch('/api/telegram/unlink', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          memberId: memberId
        }),
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setCurrentlyLinked(false)
        setGeneratedLink(null) // Clear any generated link
        setShowQRCode(false) // Hide QR code if it was shown
        toast.success(`${memberName} has been unlinked from Telegram`)

        // Refresh the page to ensure all components show the updated state
        window.location.reload()
      } else {
        throw new Error(data.error || 'Failed to unlink')
      }
    } catch (error) {
      console.error('Error unlinking Telegram:', error)
      toast.error("Failed to unlink Telegram. Please try again.")
    } finally {
      setIsUnlinking(false)
    }
  }

  const copyToClipboard = async () => {
    if (!generatedLink) return

    try {
      await navigator.clipboard.writeText(generatedLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success("Telegram link copied to clipboard!")
    } catch (error) {
      console.error('Failed to copy:', error)
      toast.error("Please copy the link manually.")
    }
  }

  const shareViaWhatsApp = () => {
    if (!generatedLink || !memberPhone) return

    const message = `Hi ${memberName}! 👋\n\nConnect your Telegram account to our loyalty program for instant notifications and AI assistance:\n\n${generatedLink}\n\nJust click the link and follow the instructions. Thanks!`
    const whatsappUrl = `https://wa.me/${memberPhone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const shareViaSMS = () => {
    if (!generatedLink || !memberPhone) return

    const message = `Hi ${memberName}! Connect your Telegram to our loyalty program: ${generatedLink}`
    const smsUrl = `sms:${memberPhone}?body=${encodeURIComponent(message)}`
    window.open(smsUrl)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5" />
          Telegram Integration
        </CardTitle>
        <CardDescription>
          Generate a secure link to connect this member&apos;s Telegram account
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Connection Status:</span>
          {isLinked ? (
            <Badge variant="default" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Connected
            </Badge>
          ) : (
            <Badge variant="secondary">Not Connected</Badge>
          )}
        </div>

        {/* Telegram Info (if connected) */}
        {isLinked && (
          <div className="space-y-2 p-3 bg-green-50 rounded-lg">
            <div className="text-sm">
              <span className="font-medium">Chat ID:</span> {telegramChatId}
            </div>
            {telegramUsername && (
              <div className="text-sm">
                <span className="font-medium">Username:</span> @{telegramUsername}
              </div>
            )}
          </div>
        )}

        {/* Generate Link or Unlink Button */}
        {isLinked ? (
          <div className="space-y-2">
            <Button
              onClick={unlinkTelegram}
              disabled={isUnlinking}
              variant="destructive"
              className="w-full"
            >
              <Unlink className="mr-2 h-4 w-4" />
              {isUnlinking ? 'Unlinking...' : 'Unlink Telegram'}
            </Button>
            <p className="text-xs text-muted-foreground text-center">
              This will disconnect {memberName} from Telegram notifications
            </p>
          </div>
        ) : (
          <Button
            onClick={generateTelegramLink}
            disabled={isGenerating}
            className="w-full"
          >
            {isGenerating ? 'Generating...' : 'Generate Telegram Link'}
          </Button>
        )}

        {/* Generated Link */}
        {generatedLink && (
          <div className="space-y-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Generated Link:</label>
              <div className="flex gap-2">
                <Input
                  value={generatedLink}
                  readOnly
                  className="font-mono text-xs"
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyToClipboard}
                >
                  {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {/* Share Options */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Share with Member:</label>
              <div className="flex gap-2">
                {memberPhone && (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={shareViaWhatsApp}
                      className="flex-1"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      WhatsApp
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={shareViaSMS}
                      className="flex-1"
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      SMS
                    </Button>
                  </>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyToClipboard}
                  className="flex-1"
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy Link
                </Button>
              </div>
            </div>

            {/* QR Code Toggle */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">QR Code:</label>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setShowQRCode(!showQRCode)}
                >
                  {showQRCode ? <Type className="h-4 w-4 mr-1" /> : <QrCode className="h-4 w-4 mr-1" />}
                  {showQRCode ? 'Hide QR' : 'Show QR'}
                </Button>
              </div>

              {showQRCode && (
                <div className="flex justify-center p-4 bg-white rounded-lg border">
                  <div className="text-center space-y-2">
                    <QRCodeSVG
                      value={generatedLink}
                      size={200}
                      level="M"
                      includeMargin={true}
                      className="mx-auto"
                    />
                    <p className="text-xs text-muted-foreground max-w-[200px]">
                      Scan with phone camera or Telegram to connect
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Instructions */}
            <div className="p-3 bg-blue-50 rounded-lg text-sm">
              <p className="font-medium mb-1">How to use:</p>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li>Share the link with {memberName}</li>
                <li>They click the link to open Telegram</li>
                <li>They tap &quot;Start&quot; to connect their account</li>
                <li>Connection is automatically established</li>
              </ol>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
