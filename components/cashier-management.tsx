'use client'

import { useState } from 'react'
import { useCompany } from '@/contexts/company-context'
import { useRole } from '@/hooks/use-role'
import { getSupabaseClient } from '@/lib/supabase'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

// UI Components
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Loader2, Plus, UserPlus, Trash2 } from 'lucide-react'

interface Administrator {
  id: string
  email: string
  name: string
  phone_number?: string
}

interface Cashier {
  id: string
  administrator_id: string
  company_id: string
  role: string
  created_at: string
  administrator: Administrator
}

interface SupabaseResponse {
  id: string
  administrator_id: string
  company_id: string
  role: string
  created_at: string
  administrator: Administrator | Administrator[]
}

export function CashierManagement() {
  const { company } = useCompany()
  const { isOwner, role } = useRole()
  
  console.log('CashierManagement - Debug Info:', { 
    companyId: company?.id,
    role,
    isOwner,
  })
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newCashier, setNewCashier] = useState({
    email: '',
    name: '',
    phone: ''
  })
  const queryClient = useQueryClient()

  // Query to fetch cashiers
  const { data: cashiers, isLoading } = useQuery({
    queryKey: ['cashiers', company?.id],
    queryFn: async () => {
      if (!company?.id) return []
      
      const supabase = getSupabaseClient()
      const { data, error } = await supabase
        .from('company_administrators')
        .select(`
          id,
          administrator_id,
          company_id,
          role,
          created_at,
          administrator:administrator_id (id, email, name, phone_number)
        `)
        .eq('company_id', company.id)
        .eq('role', 'CASHIER')
      
      if (error) {
        console.error('Error fetching cashiers:', error)
        toast.error('Failed to load cashiers')
        return []
      }
      
      // Transform the data to match the Cashier interface
      const cashiers: Cashier[] = (data as SupabaseResponse[] || []).map(item => {
        // Handle the case where administrator might be an array or object
        const adminData = item.administrator;
        const admin: Administrator = Array.isArray(adminData) 
          ? adminData[0] || { id: '', email: '', name: '' }
          : adminData || { id: '', email: '', name: '' };
          
        return {
          id: item.id,
          administrator_id: item.administrator_id,
          company_id: item.company_id,
          role: item.role,
          created_at: item.created_at,
          administrator: {
            id: admin.id,
            email: admin.email,
            name: admin.name,
            phone_number: admin.phone_number
          }
        };
      });
      
      return cashiers
    },
    enabled: !!company?.id && isOwner
  })

  // Mutation to add a new cashier
  const addCashierMutation = useMutation({
    mutationFn: async (cashierData: typeof newCashier) => {
      if (!company?.id) throw new Error('Company ID not found')
      
      const supabase = getSupabaseClient()
      
      // Call the add_cashier function we created in SQL
      const { data, error } = await supabase
        .rpc('add_cashier', {
          p_company_id: company.id,
          p_email: cashierData.email,
          p_name: cashierData.name,
          p_phone_number: cashierData.phone || null
        })
      
      if (error) throw error
      
      return data
    },
    onSuccess: () => {
      toast.success('Cashier added successfully')
      setIsAddDialogOpen(false)
      setNewCashier({ email: '', name: '', phone: '' })
      queryClient.invalidateQueries({ queryKey: ['cashiers', company?.id] })
    },
    onError: (error) => {
      console.error('Error adding cashier:', error)
      toast.error('Failed to add cashier')
    }
  })

  // Mutation to remove a cashier
  const removeCashierMutation = useMutation({
    mutationFn: async (cashierId: string) => {
      const supabase = getSupabaseClient()
      
      const { error } = await supabase
        .from('company_administrators')
        .delete()
        .eq('id', cashierId)
      
      if (error) throw error
    },
    onSuccess: () => {
      toast.success('Cashier removed successfully')
      queryClient.invalidateQueries({ queryKey: ['cashiers', company?.id] })
    },
    onError: (error) => {
      console.error('Error removing cashier:', error)
      toast.error('Failed to remove cashier')
    }
  })

  // Handle form submission
  const handleAddCashier = (e: React.FormEvent) => {
    e.preventDefault()
    addCashierMutation.mutate(newCashier)
  }

  // If not an owner, don't show this component
  if (!isOwner) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-muted-foreground" />
            <span>Cashier Management</span>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Cashier
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Cashier</DialogTitle>
                <DialogDescription>
                  Add a new cashier to manage day-to-day operations. They will have access to members and transactions.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddCashier}>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newCashier.email}
                      onChange={(e) => setNewCashier({ ...newCashier, email: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      placeholder="John Doe"
                      value={newCashier.name}
                      onChange={(e) => setNewCashier({ ...newCashier, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number (Optional)</Label>
                    <Input
                      id="phone"
                      placeholder="+1234567890"
                      value={newCashier.phone}
                      onChange={(e) => setNewCashier({ ...newCashier, phone: e.target.value })}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsAddDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    disabled={addCashierMutation.isPending}
                  >
                    {addCashierMutation.isPending && (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    Add Cashier
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </CardTitle>
        <CardDescription>
          Manage cashiers who can process transactions and manage members
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : cashiers && cashiers.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Added On</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {cashiers.map((cashier) => (
                <TableRow key={cashier.id}>
                  <TableCell className="font-medium">{cashier.administrator.name}</TableCell>
                  <TableCell>{cashier.administrator.email}</TableCell>
                  <TableCell>{cashier.administrator.phone_number || '-'}</TableCell>
                  <TableCell>{new Date(cashier.created_at).toLocaleDateString()}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeCashierMutation.mutate(cashier.id)}
                      disabled={removeCashierMutation.isPending}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No cashiers found. Add a cashier to manage day-to-day operations.
          </div>
        )}
      </CardContent>
    </Card>
  )
}
