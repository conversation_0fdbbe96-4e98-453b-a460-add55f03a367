'use client'

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Gift, Award } from "lucide-react"
import { motion } from "framer-motion"
import { useEffect, useState } from "react"

interface DashboardPreviewProps {
  showCompact?: boolean;
}

const DashboardPreview = ({ showCompact = false }: DashboardPreviewProps) => {
  // Use a more reliable hydration-safe approach
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    // This will only run once the component has mounted on the client
    setIsClient(true)
  }, [])

  // Animation variants - designed for smoother transitions
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: "beforeChildren",
        staggerChildren: 0.08,
        duration: 0.3,
        ease: "easeOut"
      }
    }
  }

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 10
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.1, 0.25, 1] // Use a custom cubic bezier for smoother motion
      }
    }
  }

  // Don't attempt to animate until client-side hydration is complete
  const shouldAnimate = isClient;

  return (
    <div className="w-full bg-white dark:bg-gray-900 rounded-md overflow-hidden shadow-lg border border-gray-200 dark:border-gray-700">
      {/* Dashboard Header */}
      <div className="bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <div className="flex items-center space-x-3">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div className="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <div className="flex items-center">
          <div className="bg-white dark:bg-gray-700 rounded-full px-3 py-1 text-xs text-gray-600 dark:text-gray-300 flex items-center shadow-sm">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
            Loyal Dashboard - Live
          </div>
        </div>
      </div>

      {/* Dashboard Content - Wait for client-side hydration before animating */}
      <div className="p-6">
        {shouldAnimate ? (
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="w-full"
          >
            {/* Stats Row */}
            <motion.div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8" variants={itemVariants}>
            {[
              { label: "Active Members", value: "1,248", change: "+12%", positive: true, icon: <Users className="h-5 w-5 text-purple-500 dark:text-purple-400" /> },
              { label: "Points Issued", value: "458,390", change: "+8%", positive: true, icon: <Gift className="h-5 w-5 text-amber-500 dark:text-amber-400" /> },
              { label: "Reward Redemptions", value: "284", change: "+5%", positive: true, icon: <Award className="h-5 w-5 text-purple-500 dark:text-purple-400" /> },
              { label: "Program ROI", value: "187%", change: "-2%", positive: false, icon: <TrendingUp className="h-5 w-5 text-amber-500 dark:text-amber-400" /> }
            ].map((stat, index) => (
              <div key={index} className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 p-3 sm:p-4 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-1 sm:mb-2">
                  <p className="text-xs font-medium text-gray-500 dark:text-gray-400">{stat.label}</p>
                  <div className="w-7 h-7 sm:w-8 sm:h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    {stat.icon}
                  </div>
                </div>
                <p className="text-xl sm:text-2xl font-bold mt-1 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">{stat.value}</p>
                <div className="flex items-center mt-1 sm:mt-2">
                  <span className={`text-xs px-1.5 sm:px-2 py-0.5 rounded-full ${stat.positive ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'}`}>
                    {stat.change}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-1 sm:ml-2">vs last month</span>
                </div>
              </div>
            ))}
            </motion.div>

            {/* Main Chart - Full Width */}
            <motion.div
              className="bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 p-5 shadow-sm mb-6 col-span-12"
              variants={itemVariants}
            >
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-base font-semibold">Member Activity</h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Daily engagement metrics</p>
                </div>
                <div className="flex space-x-2">
                  <span className="text-xs px-3 py-1.5 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full font-medium">Daily</span>
                  <span className="text-xs px-3 py-1.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">Weekly</span>
                  <span className="text-xs px-3 py-1.5 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">Monthly</span>
                </div>
              </div>

              {/* Member Activity Chart - Rebuilt from scratch to match Home component */}
              <div className="h-[150px] sm:h-[200px] relative">
                {/* Y-Axis Labels */}
                <div className="absolute left-0 top-0 bottom-0 w-8 flex flex-col justify-between text-[10px] text-gray-500 dark:text-gray-400 pointer-events-none">
                  {(() => {
                    const data = [250, 300, 200, 350, 275, 400, 325, 375];
                    const maxValue = Math.max(...data);
                    const displayMaxValue = Math.ceil(maxValue / 100) * 100;
                    const numYLabels = 5;
                    const yAxisValues = Array.from({ length: numYLabels }, (_, i) =>
                      Math.round(displayMaxValue * (1 - i / (numYLabels - 1)))
                    );

                    return yAxisValues.map((value, i) => (
                      <div key={i} className="text-right pr-1">{value}</div>
                    ));
                  })()}
                </div>

                {/* Chart Area */}
                <div className="absolute left-8 right-0 top-0 bottom-0">
                  {/* Background Grid */}
                  <div className="absolute inset-0 grid grid-cols-7 grid-rows-4 pointer-events-none">
                    {Array(28).fill(null).map((_, i) => (
                      <div key={i} className="border-t border-l border-gray-100 dark:border-gray-700 opacity-50"></div>
                    ))}
                  </div>

                  {/* SVG Chart */}
                  <div className="absolute inset-0">
                    {(() => {
                      const data = [250, 300, 200, 350, 275, 400, 325, 375];
                      const maxValue = Math.max(...data);
                      const displayMaxValue = Math.ceil(maxValue / 100) * 100;
                      const pointCount = data.length;

                      // Simple linear mapping functions
                      const getX = (i: number) => (i / (pointCount - 1)) * 100;
                      const getY = (v: number) => (1 - v / displayMaxValue) * 100;

                      const points = data.map((v, i) => [getX(i), getY(v)]);
                      const linePath = points.map(([x, y], idx) => `${idx === 0 ? 'M' : 'L'}${x},${y}`).join(' ');
                      const areaPath = `${linePath} L${getX(pointCount - 1)},100 L${getX(0)},100 Z`;

                      return (
                        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                          <defs>
                            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                              <stop offset="0%" stopColor="#8B5CF6" />
                              <stop offset="100%" stopColor="#F59E0B" />
                            </linearGradient>
                            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                              <stop offset="0%" stopColor="#8B5CF6" stopOpacity="0.2" />
                              <stop offset="100%" stopColor="#F59E0B" stopOpacity="0.05" />
                            </linearGradient>
                          </defs>
                          <path
                            d={areaPath}
                            fill="url(#areaGradient)"
                            opacity="0.6"
                          />
                          <path
                            d={linePath}
                            fill="none"
                            stroke="url(#gradient)"
                            strokeWidth="2"
                            vectorEffect="non-scaling-stroke"
                          />
                          {points.map(([x, y], i) => (
                            <g key={i}>
                              <circle
                                cx={x}
                                cy={y}
                                r="4"
                                fill="transparent"
                                className="cursor-pointer opacity-0 hover:opacity-100 transition-opacity"
                              />
                            </g>
                          ))}
                        </svg>
                      );
                    })()}
                  </div>
                </div>

                {/* X-Axis Labels */}
                <div className="absolute bottom-0 left-8 right-0 flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun', 'Mon'].map((day, i) => (
                    <div key={i}>{day}</div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Stop rendering here if compact mode is enabled */}
            {!showCompact && (
              <>
                {/* Side Widgets - Now in a grid side by side */}
                <div className="grid grid-cols-2 gap-6">
                  {/* Top Rewards */}
                  <motion.div
                    className="bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 p-5 shadow-sm"
                    variants={itemVariants}
                  >
                    <div className="flex justify-between items-center mb-4">
                      <div>
                        <h3 className="text-base font-semibold">Top Rewards</h3>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Redemption rate</p>
                      </div>
                      <div className="w-8 h-8 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                        <PieChart className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                      </div>
                    </div>

                    <div className="space-y-4 mt-4">
                      {[
                        { name: "Loyalty Discount", rate: "68%" },
                        { name: "Birthday Gift", rate: "53%" },
                        { name: "VIP Access", rate: "42%" },
                      ].map((item, i) => (
                        <div key={i} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">{item.name}</span>
                            <span className="text-sm font-bold">{item.rate}</span>
                          </div>
                          <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className="h-full rounded-full bg-gradient-to-r from-purple-500 to-amber-500"
                              style={{ width: item.rate }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </motion.div>

                  {/* Member Tiers */}
                  <motion.div
                    className="bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 p-5 shadow-sm"
                    variants={itemVariants}
                  >
                    <div className="flex justify-between items-center mb-4">
                      <div>
                        <h3 className="text-base font-semibold">Member Tiers</h3>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Distribution</p>
                      </div>
                      <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                        <BarChart className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                      </div>
                    </div>

                    <div className="space-y-4 mt-4">
                      {[
                        { tier: "Gold", count: "312", percentage: 17, color: "bg-gradient-to-r from-amber-400 to-amber-600" },
                        { tier: "Silver", count: "628", percentage: 33, color: "bg-gradient-to-r from-gray-300 to-gray-500" },
                        { tier: "Bronze", count: "944", percentage: 50, color: "bg-gradient-to-r from-amber-700 to-amber-900" },
                      ].map((tier, i) => (
                        <div key={i} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <div className={`w-3 h-3 rounded-full ${tier.color}`}></div>
                              <span className="ml-2 text-sm font-medium">{tier.tier}</span>
                            </div>
                            <span className="text-sm font-medium">{tier.count}</span>
                          </div>
                          <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className={`h-full rounded-full ${tier.color}`}
                              style={{ width: `${tier.percentage}%` }}
                            ></div>
                          </div>
                          <p className="text-xs text-right text-gray-500 dark:text-gray-400">{tier.percentage}%</p>
                        </div>
                      ))}
                    </div>
                  </motion.div>
                </div>
              </>
            )}
          </motion.div>
        ) : (
          // Render a static placeholder with the same dimensions until client-side hydration is complete
          <div className="opacity-0">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-8">
              {/* Static placeholders for stats */}
              {[0, 1, 2, 3].map((i) => (
                <div key={i} className="h-[90px] sm:h-[100px] bg-transparent"></div>
              ))}
            </div>
            <div className="h-[270px] sm:h-[320px] bg-transparent mb-6"></div>
            {!showCompact && (
              <div className="grid grid-cols-2 gap-6">
                <div className="h-[200px] bg-transparent"></div>
                <div className="h-[200px] bg-transparent"></div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default DashboardPreview
