"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider } from "next-themes"

type Attribute = "class" | "data-theme" | "data-mode"

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: string;
  enableSystem?: boolean;
  attribute?: Attribute | Attribute[];
  disableTransitionOnChange?: boolean;
  themes?: string[];
  forcedTheme?: string;
  storageKey?: string;
}

export function ThemeProvider({ 
  children, 
  ...props 
}: ThemeProviderProps) {
  const [mounted, setMounted] = React.useState(false)
  
  // Add the theme transition class to the body once the component is mounted
  React.useEffect(() => {
    const body = document.body
    setMounted(true)
    
    // Add the theme-transition class for smooth transitions
    if (!body.classList.contains('theme-transition')) {
      body.classList.add('theme-transition')
    }
    
    return () => {
      body.classList.remove('theme-transition')
    }
  }, [])
  
  // Prevents flash of unstyled content before client-side hydration
  if (!mounted) {
    return <>{children}</>
  }
  
  return (
    <NextThemesProvider
      attribute="class"
      enableSystem
      disableTransitionOnChange={false}
      defaultTheme="system"
      {...props}
    >
      {children}
    </NextThemesProvider>
  )
}
