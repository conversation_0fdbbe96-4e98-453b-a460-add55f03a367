'use client'

import { usePathname } from 'next/navigation'
import { Header } from './header'
import Footer from './Footer'

// Normalize pathname and strip trailing slash
function useNormalizedPath() {
  const pathname = usePathname()
  return pathname.endsWith('/') && pathname.length > 1 ? pathname.slice(0, -1) : pathname
}

// Conditionally render Header
export function ConditionalHeader() {
  const path = useNormalizedPath()
  if (path === '/login' || path === '/signup') return null
  return <Header />
}

// Conditionally render Footer
export function ConditionalFooter() {
  const path = useNormalizedPath()
  if (path === '/login' || path === '/signup') return null
  return <Footer />
}
