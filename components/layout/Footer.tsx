import Link from 'next/link';
import { Facebook, Twitter, Instagram } from 'lucide-react';
import Brand<PERSON>ogo from '@/components/ui/BrandLogo';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="w-full border-t border-border/40 bg-background/95 pt-10 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1">
            <Link href="/" className="inline-block mb-4">
              <BrandLogo size={32} />
            </Link>
            <p className="text-muted-foreground text-sm mb-6">
              Elevate your business with our premium loyalty program management solution.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-muted-foreground hover:text-primary transition-colors">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-primary transition-colors">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-primary transition-colors">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </Link>
            </div>
          </div>

          {/* <div className="col-span-1">
            <h3 className="text-foreground font-medium text-base mb-4">Product</h3>
            <ul className="space-y-3">
              <li><Link href="/features" className="text-muted-foreground hover:text-primary text-sm transition-colors">Features</Link></li>
              <li><Link href="/pricing" className="text-muted-foreground hover:text-primary text-sm transition-colors">Pricing</Link></li>
              <li><Link href="/integrations" className="text-muted-foreground hover:text-primary text-sm transition-colors">Integrations</Link></li>
              <li><Link href="/updates" className="text-muted-foreground hover:text-primary text-sm transition-colors">Updates</Link></li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-foreground font-medium text-base mb-4">Company</h3>
            <ul className="space-y-3">
              <li><Link href="/about" className="text-muted-foreground hover:text-primary text-sm transition-colors">About</Link></li>
              <li><Link href="/blog" className="text-muted-foreground hover:text-primary text-sm transition-colors">Blog</Link></li>
              <li><Link href="/careers" className="text-muted-foreground hover:text-primary text-sm transition-colors">Careers</Link></li>
              <li><Link href="/contact" className="text-muted-foreground hover:text-primary text-sm transition-colors">Contact</Link></li>
            </ul>
          </div>

          <div className="col-span-1">
            <h3 className="text-foreground font-medium text-base mb-4">Legal</h3>
            <ul className="space-y-3">
              <li><Link href="/privacy" className="text-muted-foreground hover:text-primary text-sm transition-colors">Privacy Policy</Link></li>
              <li><Link href="/terms" className="text-muted-foreground hover:text-primary text-sm transition-colors">Terms of Service</Link></li>
              <li><Link href="/cookies" className="text-muted-foreground hover:text-primary text-sm transition-colors">Cookie Policy</Link></li>
            </ul>
          </div> */}
        </div>

        <div className="h-px w-full bg-border/40 my-8"></div>

        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-muted-foreground text-sm">
            &copy; {currentYear} Loyal. All rights reserved.
          </p>
          <div className="mt-4 md:mt-0">
            <span className="text-muted-foreground text-xs">
              Made with <span className="text-amber-500 dark:text-amber-400">♥</span> for premium businesses
            </span>
          </div>
        </div>
      </div>
    </footer>
  );
}
