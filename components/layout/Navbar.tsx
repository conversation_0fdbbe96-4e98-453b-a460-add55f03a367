import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth, signOut } from '@/hooks/use-auth'
import BrandLogo from '@/components/ui/BrandLogo';

export default function Navbar() {
  const router = useRouter();
  const { user } = useAuth();

  const handleSignOut = async () => {
    await signOut();
    router.push('/login');
  };

  return (
    <nav className="navbar sticky top-0 z-50 w-full py-4 px-6">
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-1">
          <Link href={user ? '/dashboard' : '/'} className="flex items-center">
            <BrandLogo size={32} />
          </Link>
        </div>

        <div className="hidden md:flex items-center space-x-8">
          {user ? (
            <>
              <Link href="/dashboard" className="text-white hover:text-loyal-gold-400 transition-colors">
                Dashboard
              </Link>
              <Link href="/members" className="text-white hover:text-loyal-gold-400 transition-colors">
                Members
              </Link>
              <Link href="/rewards" className="text-white hover:text-loyal-gold-400 transition-colors">
                Rewards
              </Link>
              <Link href="/settings" className="text-white hover:text-loyal-gold-400 transition-colors">
                Settings
              </Link>
            </>
          ) : (
            <>
              <Link href="/features" className="text-white hover:text-loyal-gold-400 transition-colors">
                Features
              </Link>
              <Link href="/pricing" className="text-white hover:text-loyal-gold-400 transition-colors">
                Pricing
              </Link>
              <Link href="/about" className="text-white hover:text-loyal-gold-400 transition-colors">
                About
              </Link>
            </>
          )}
        </div>

        <div className="flex items-center space-x-4">
          {user ? (
            <div className="flex items-center space-x-4">
              <div className="hidden md:block">
                <span className="text-sm text-loyal-gold-300">{user.email}</span>
              </div>
              <button
                onClick={handleSignOut}
                className="btn-outline text-sm py-1.5 px-3"
              >
                Sign Out
              </button>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <Link href="/login" className="text-white hover:text-loyal-gold-400 transition-colors">
                Login
              </Link>
              <Link href="/signup" className="btn-primary text-sm py-1.5 px-3">
                Sign Up
              </Link>
            </div>
          )}
          <button className="md:hidden text-white">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </nav>
  );
}
