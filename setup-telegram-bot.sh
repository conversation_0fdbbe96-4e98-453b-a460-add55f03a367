#!/bin/bash

# Telegram Bot Setup Script
# This script sets up the webhook and tests the Telegram bot integration

echo "🤖 Setting up Telegram Bot Integration..."

# Set the webhook URL (adjust for your deployment)
WEBHOOK_URL="https://your-app.vercel.app/api/telegram/webhook"

# If running locally with ngrok or similar, use:
# WEBHOOK_URL="https://your-ngrok-url.ngrok.io/api/telegram/webhook"

echo "📡 Setting up webhook at: $WEBHOOK_URL"

# Set the webhook using our API endpoint
curl -X POST http://localhost:3000/api/telegram/setup \
  -H "Content-Type: application/json" \
  -d "{\"webhookUrl\": \"$WEBHOOK_URL\"}"

echo ""
echo "✅ Webhook setup complete!"
echo ""
echo "🔗 To test the bot:"
echo "1. Find your bot: @${TELEGRAM_BOT_USERNAME:-Loyal_ET_Bot}"
echo "2. Send /start to begin"
echo "3. Use /help to see available commands"
echo ""
echo "👨‍💼 For businesses to generate linking codes:"
echo "POST http://localhost:3000/api/telegram/generate-link"
echo '{"memberId": "member-uuid-here"}'
echo ""
echo "📊 Check your logs for webhook activity"
