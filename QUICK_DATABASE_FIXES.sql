-- =====================================================
-- QUICK COPY-PASTE SQL FOR DATABASE FIXES
-- =====================================================
-- Run these in order, section by section
-- TEST IN DEVELOPMENT ENVIRONMENT FIRST!

-- =====================================================
-- STEP 1: BACKUP YOUR DATA (CRITICAL!)
-- =====================================================
CREATE TABLE loyalty_members_backup AS SELECT * FROM loyalty_members;
CREATE TABLE points_transactions_backup AS SELECT * FROM points_transactions;
CREATE TABLE reward_redemptions_backup AS SELECT * FROM reward_redemptions;

-- =====================================================
-- STEP 2: ANALYZE CURRENT DATA ISSUES
-- =====================================================
-- Check for duplicate members
SELECT
    name, email, phone_number, company_id, COUNT(*) as duplicate_count,
    ARRAY_AGG(id) as member_ids, ARRAY_AGG(loyalty_id) as loyalty_ids
FROM loyalty_members
GROUP BY name, email, phone_number, company_id
HAVING COUNT(*) > 1;

-- Check points consistency
SELECT
    lm.loyalty_id, lm.name,
    lm.lifetime_points as stored_lifetime,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0) as calculated_lifetime,
    lm.redeemed_points as stored_redeemed,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0) as calculated_redeemed
FROM loyalty_members lm
LEFT JOIN points_transactions pt ON lm.id = pt.member_id
GROUP BY lm.id, lm.loyalty_id, lm.name, lm.lifetime_points, lm.redeemed_points
HAVING lm.lifetime_points != COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0)
    OR lm.redeemed_points != COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0);

-- =====================================================
-- STEP 3: CREATE NEW CLEAN VIEWS
-- =====================================================
-- New member points view (single source of truth)
CREATE OR REPLACE VIEW member_points_live AS
SELECT
    lm.id, lm.loyalty_id, lm.name, lm.email, lm.phone_number, lm.company_id,
    lm.loyalty_tier, lm.registration_date, lm.profile_image_url,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0) as lifetime_points,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0) as redeemed_points,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EXPIRE' THEN ABS(pt.points_change) ELSE 0 END), 0) as expired_points,
    COALESCE(SUM(pt.points_change), 0) as available_points,
    MAX(pt.created_at) as last_transaction_date,
    COUNT(pt.id) as total_transactions
FROM loyalty_members lm
LEFT JOIN points_transactions pt ON lm.id = pt.member_id
GROUP BY lm.id, lm.loyalty_id, lm.name, lm.email, lm.phone_number, lm.company_id, lm.loyalty_tier, lm.registration_date, lm.profile_image_url;

-- New dashboard metrics view (consolidated)
CREATE OR REPLACE VIEW dashboard_metrics_live AS
WITH transaction_stats AS (
    SELECT
        pt.company_id,
        COUNT(DISTINCT pt.member_id) as unique_members,
        SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END) as total_lifetime_points,
        SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END) as total_redeemed_points,
        SUM(CASE WHEN pt.transaction_type = 'EXPIRE' THEN ABS(pt.points_change) ELSE 0 END) as total_expired_points,
        SUM(pt.points_change) as total_available_points,
        COUNT(*) as total_transactions,
        COUNT(CASE WHEN pt.transaction_type = 'EARN' THEN 1 END) as earn_transactions,
        COUNT(CASE WHEN pt.transaction_type = 'REDEEM' THEN 1 END) as redeem_transactions,
        COUNT(DISTINCT CASE WHEN pt.created_at >= NOW() - INTERVAL '30 days' THEN pt.member_id END) as active_members_30d,
        MAX(pt.created_at) as last_transaction_date
    FROM points_transactions pt
    GROUP BY pt.company_id
)
SELECT
    c.id as company_id, c.name as company_name,
    COUNT(DISTINCT lm.id) as total_members,
    COALESCE(ts.active_members_30d, 0) as active_members_30d,
    COALESCE(ts.total_lifetime_points, 0) as total_lifetime_points,
    COALESCE(ts.total_redeemed_points, 0) as total_redeemed_points,
    COALESCE(ts.total_expired_points, 0) as total_expired_points,
    COALESCE(ts.total_available_points, 0) as total_available_points,
    COALESCE(ts.total_transactions, 0) as total_transactions,
    COALESCE(ts.earn_transactions, 0) as earn_transactions,
    COALESCE(ts.redeem_transactions, 0) as redeem_transactions,
    ts.last_transaction_date, NOW() as last_updated,
    CASE WHEN ts.total_lifetime_points > 0 THEN
        ROUND((ts.total_redeemed_points::NUMERIC / ts.total_lifetime_points::NUMERIC) * 100, 2)
    ELSE 0 END as redemption_rate_percentage
FROM companies c
LEFT JOIN loyalty_members lm ON lm.company_id = c.id
LEFT JOIN transaction_stats ts ON ts.company_id = c.id
GROUP BY c.id, c.name, ts.active_members_30d, ts.total_lifetime_points, ts.total_redeemed_points,
         ts.total_expired_points, ts.total_available_points, ts.total_transactions,
         ts.earn_transactions, ts.redeem_transactions, ts.last_transaction_date;

-- =====================================================
-- STEP 4: ADD PERFORMANCE INDEXES
-- =====================================================
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_points_transactions_member_created
ON points_transactions(member_id, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_points_transactions_company_type
ON points_transactions(company_id, transaction_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_loyalty_members_company_reg
ON loyalty_members(company_id, registration_date DESC);

-- =====================================================
-- STEP 5: ADD DATA VALIDATION CONSTRAINTS
-- =====================================================
-- Prevent duplicate loyalty IDs per company
ALTER TABLE loyalty_members
ADD CONSTRAINT unique_loyalty_id_per_company
UNIQUE (loyalty_id, company_id);

-- Valid transaction types only
ALTER TABLE points_transactions
ADD CONSTRAINT valid_transaction_type
CHECK (transaction_type IN ('EARN', 'REDEEM', 'EXPIRE', 'ADJUST'));

-- Non-zero points change
ALTER TABLE points_transactions
ADD CONSTRAINT non_zero_points_change
CHECK (points_change != 0);

-- =====================================================
-- STEP 6: VALIDATION QUERIES (Run after changes)
-- =====================================================
-- Verify consistency
SELECT
    'member_points_live' as source,
    COUNT(*) as member_count,
    SUM(lifetime_points) as total_lifetime,
    SUM(available_points) as total_available
FROM member_points_live
UNION ALL
SELECT
    'transactions_calc' as source,
    COUNT(DISTINCT member_id) as member_count,
    SUM(CASE WHEN transaction_type = 'EARN' THEN points_change ELSE 0 END) as total_lifetime,
    SUM(points_change) as total_available
FROM points_transactions;

-- Check for remaining duplicates
SELECT
    COUNT(*) as total_members,
    COUNT(DISTINCT loyalty_id) as unique_loyalty_ids,
    COUNT(DISTINCT email) as unique_emails
FROM loyalty_members;

-- =====================================================
-- SUCCESS CONFIRMATION
-- =====================================================
SELECT
    'Database fixes completed!' as status,
    COUNT(*) as total_members,
    SUM(available_points) as total_available_points,
    AVG(available_points) as avg_member_points
FROM member_points_live;
