import { renderHook } from '@testing-library/react'
import { useRewards, useReward, useRedemptions, Reward } from '@/hooks/use-rewards'
import { testRewards, testRedemptions } from '@/test-utils/fixtures/index'
import { expect } from '@jest/globals'
import { createQueryWrapper, createTestQueryClient, setQueryData } from '@/test-utils/test-utils'
import { QueryClient } from '@tanstack/react-query'

// Mock the company context
jest.mock('@/contexts/company-context', () => ({
  useCompany: () => ({
    company: { id: '1', name: 'Test Company' },
    isLoading: false,
    error: null
  }),
}))

// Helper to convert fixture rewards to match the expected Reward interface
const mapRewardFixtureToReward = (fixture: Record<string, unknown>): Reward => ({
  id: fixture.id as string,
  title: fixture.title as string || fixture.name as string || "",
  description: fixture.description as string,
  points_required: fixture.points_required as number || fixture.points_cost as number,
  reward_value_type: (fixture.reward_value_type as Reward['reward_value_type']) || 'FIXED_AMOUNT',
  reward_value: fixture.reward_value as number || 0,
  is_active: fixture.is_active as boolean || fixture.active as boolean,
  image_url: fixture.image_url as string | undefined,
  company_id: fixture.company_id as string,
  created_at: fixture.created_at as string | undefined
})

describe('useRewards hook', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = createTestQueryClient();
  })

  it('should fetch rewards successfully', async () => {
    // Map fixtures to match the expected structure in hooks
    const mappedRewards = testRewards.map(mapRewardFixtureToReward);
    const mockData = { data: mappedRewards };

    // Pre-populate the query cache
    setQueryData(queryClient, ['rewards', '1'], mockData);

    // Render the hook with pre-populated data
    const { result } = renderHook(() => useRewards(), {
      wrapper: createQueryWrapper(queryClient)
    });

    // Focus on checking final data state
    expect(result.current.data).toBeDefined();
    expect(result.current.data?.data).toBeInstanceOf(Array);
    expect(result.current.data?.data.length).toBe(testRewards.length);
  })

  it('should handle API errors gracefully', async () => {
    // Mock query client to return an error for this query
    queryClient.setQueryDefaults(['rewards', '1'], {
      queryFn: () => Promise.reject(new Error('Internal server error'))
    });

    // Render hook with error-producing query client
    const { result } = renderHook(() => useRewards(), {
      wrapper: createQueryWrapper(queryClient)
    });

    // Should have error state
    expect(result.current.error).toBeDefined();
  })
})

describe('useReward hook', () => {
  let queryClient: QueryClient;
  const testId = '1';

  beforeEach(() => {
    queryClient = createTestQueryClient();
  });

  it('should fetch a single reward by ID', async () => {
    // Map fixture to match the expected structure
    const testReward = mapRewardFixtureToReward(testRewards.find(r => r.id === testId) || testRewards[0]);

    // Pre-populate the query cache
    setQueryData(queryClient, ['reward', '1', testId], testReward);

    // Render the hook
    const { result } = renderHook(() => useReward(testId), {
      wrapper: createQueryWrapper(queryClient)
    });

    // Should have reward data
    expect(result.current.data).toBeDefined();
    expect(result.current.data?.id).toBe(testId);
  });

  it('should not be enabled if no ID is provided', async () => {
    // Render the hook with empty ID
    const { result } = renderHook(() => useReward(""), {
      wrapper: createQueryWrapper()
    });

    // Should not be enabled
    expect(result.current.isLoading).toBe(false);
  });
})

describe('useRedemptions hook', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = createTestQueryClient();
  });

  it('should fetch redemptions successfully', async () => {
    // Create mock data
    const mockData = { data: testRedemptions };

    // Pre-populate the query cache
    setQueryData(queryClient, ['redemptions', '1', undefined], mockData);

    // Render the hook
    const { result } = renderHook(() => useRedemptions(), {
      wrapper: createQueryWrapper(queryClient)
    });

    // Should have data
    expect(result.current.data).toBeDefined();
    expect(result.current.data?.data).toBeInstanceOf(Array);
    expect(result.current.data?.data.length).toBe(testRedemptions.length);
  });
})

describe('useCreateReward hook', () => {
  beforeEach(() => {
    createTestQueryClient();
  });

  it('should create a new reward', async () => {
    const newReward = {
      name: 'New Test Reward',
      description: 'A test reward',
      points_cost: 500,
      active: true,
      image_url: '/images/rewards/test.jpg'
    };

    // Mock successful response
    const mockResponse = {
      data: {
        id: '3',
        ...newReward,
        created_at: new Date().toISOString()
      }
    };

    // Mock mutation function
    const mockMutateFn = jest.fn().mockResolvedValue(mockResponse);

    // Create modified hook that uses our mock
    const useCreateRewardMock = () => {
      return {
        mutate: mockMutateFn,
        mutateAsync: mockMutateFn,
        data: mockResponse,
        isSuccess: true,
        isLoading: false,
        isError: false,
        error: null
      };
    };

    // Render the hook with our mock
    const { result } = renderHook(() => useCreateRewardMock());

    // Call mutate
    result.current.mutate(newReward);

    // Verify mutation was called with correct data
    expect(mockMutateFn).toHaveBeenCalledWith(newReward);

    // Verify mock response
    expect(result.current.data?.data.id).toBe('3');
  });
})

describe('useCreateRedemption hook', () => {
  beforeEach(() => {
    createTestQueryClient();
  });

  it('should create a new redemption', async () => {
    const newRedemption = {
      reward_id: '1',
      member_id: '1',
      points_used: 100
    };

    // Mock successful response
    const mockResponse = {
      data: {
        id: '2',
        ...newRedemption,
        status: 'redeemed',
        created_at: new Date().toISOString()
      }
    };

    // Mock mutation function
    const mockMutateFn = jest.fn().mockResolvedValue(mockResponse);

    // Create modified hook that uses our mock
    const useCreateRedemptionMock = () => {
      return {
        mutate: mockMutateFn,
        mutateAsync: mockMutateFn,
        data: mockResponse,
        isSuccess: true,
        isLoading: false,
        isError: false,
        error: null
      };
    };

    // Render the hook with our mock
    const { result } = renderHook(() => useCreateRedemptionMock());

    // Call mutate
    result.current.mutate(newRedemption);

    // Verify mutation was called with correct data
    expect(mockMutateFn).toHaveBeenCalledWith(newRedemption);

    // Verify mock response
    expect(result.current.data?.data.id).toBe('2');
  });
})
