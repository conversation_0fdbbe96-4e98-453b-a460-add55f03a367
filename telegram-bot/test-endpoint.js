// Test script for the test endpoint
// Run with: node test-endpoint.js

async function testEndpoint() {
  const baseUrl = 'https://8235-213-41-102-186.ngrok-free.app';
  const testApiUrl = `${baseUrl}/api/telegram/test`;
  
  // First check if the base URL is accessible
  try {
    console.log('Testing if ngrok tunnel is active...');
    const pingResponse = await fetch(baseUrl);
    console.log('Ngrok tunnel status:', pingResponse.status);
  } catch (error) {
    console.error('Error connecting to ngrok tunnel:', error.message);
    console.log('Please make sure your ngrok tunnel is running and the URL is correct.');
    return;
  }
  
  // Test GET endpoint
  console.log('\nTesting GET endpoint...');
  try {
    const getResponse = await fetch(testApiUrl);
    const getData = await getResponse.json();
    console.log('GET Response status:', getResponse.status);
    console.log('GET Response data:', JSON.stringify(getData, null, 2));
  } catch (error) {
    console.error('Error with GET request:', error);
  }
  
  // Test POST endpoint
  console.log('\nTesting POST endpoint...');
  const testData = {
    api_key: 'telegram-loyal-integration-key',
    test: true,
    message: 'This is a test'
  };
  
  try {
    const postResponse = await fetch(testApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    const postData = await postResponse.json();
    console.log('POST Response status:', postResponse.status);
    console.log('POST Response data:', JSON.stringify(postData, null, 2));
  } catch (error) {
    console.error('Error with POST request:', error);
  }
}

// Run the test
testEndpoint();
