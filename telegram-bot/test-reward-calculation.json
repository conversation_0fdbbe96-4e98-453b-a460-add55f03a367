{"nodes": [{"parameters": {}, "id": "14c332f9-3a56-4ab1-9af0-8fa2bd6b12ca", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [780, 380]}, {"parameters": {"jsCode": "// Test scenario: Fixed amount discount (Birthday Special Package - 100 Birr off)\nreturn {\n  api_key: 'telegram-loyal-integration-key',\n  company_id: 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6',\n  loyalty_id: 'F4634527',\n  reward_code: 'BD05',  // Birthday Special Package (100 Birr off)\n  receipt_total: 500,\n  receipt_number: 'TEST-' + Date.now().toString().slice(-6),\n  telegram_chat_id: '*********',\n  notes: 'Test fixed amount discount calculation'\n};"}, "id": "199b6e3b-d4dc-4ae7-a206-01a988836f40", "name": "Fixed Discount Test", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [980, 380]}, {"parameters": {"jsCode": "// Test scenario: Percentage discount (Half-Price Hair Coloring - 50% off)\nreturn {\n  api_key: 'telegram-loyal-integration-key',\n  company_id: 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6',\n  loyalty_id: 'F4634527',\n  reward_code: 'CL03',  // Half-Price Hair Coloring (50% off)\n  receipt_total: 400,\n  receipt_number: 'TEST-' + Date.now().toString().slice(-6),\n  telegram_chat_id: '*********',\n  notes: 'Test percentage discount calculation'\n};"}, "id": "ffcdd3a4-1234-5678-abcd-ef0*********", "name": "Percentage Discount Test", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [980, 500]}, {"parameters": {"method": "POST", "url": "https://loyal-et.vercel.app/api/telegram/redemptions", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {}, "options": {}, "bodyContentType": "json", "jsonParameters": true, "body": "={{ $json }}"}, "id": "915263e1-0151-485f-b12e-3f0ad9b0dc08", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1180, 380]}], "connections": {"Manual Trigger": {"main": [[{"node": "Fixed Discount Test", "type": "main", "index": 0}]]}, "Fixed Discount Test": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Percentage Discount Test": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "meta": {"instanceId": "bee413acb9414930982d62c120698aa010ed90bd17c4ca0b49cdc96d38963cb7"}}