{"name": "Telegram Member Registration", "nodes": [{"parameters": {"updateFields": {"mode": "define", "value": {"command": "register", "description": "Start the registration process"}}}, "id": "e03c6b41-7bb0-4476-930f-e068613f53d2", "name": "Set Command", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [320, 40]}, {"parameters": {"botToken": "={{ $env.TELEGRAM_BOT_TOKEN }}", "updates": ["message"], "options": {"parseMode": "HTML"}}, "id": "51a280f7-35b1-4541-bd2b-a233da983873", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [100, 220], "webhookId": "fb6dd7fe-b15d-46d4-920c-317727548a85"}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.message.text }}", "operation": "startsWith", "value2": "/register"}]}}, "id": "a16d8b88-399c-463a-984a-819861466d91", "name": "Is Register Command?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [320, 220]}, {"parameters": {"functionCode": "// Get the user information from Telegram\nconst telegramUser = $node[\"Telegram Trigger\"].json.message.from;\nconst chatId = $node[\"Telegram Trigger\"].json.message.chat.id;\nconst telegramId = telegramUser.id.toString();\n\n// Create registration link with Telegram ID parameter\n// Replace with your actual deployed domain\nconst baseUrl = \"https://loyal-et.vercel.app\";\nconst registrationLink = `${baseUrl}/telegram/register?telegram_id=${telegramId}`;\n\n// Return formatted message with button\nreturn {\n  chatId: chatId,\n  text: \"🎉 <b>Loyalty Program Registration</b> 🎉\\n\\nPlease complete your registration by clicking the button below to join our loyalty program and start earning rewards!\",\n  parse_mode: \"HTML\",\n  reply_markup: JSON.stringify({\n    inline_keyboard: [\n      [{ text: \"📝 Register Now\", url: registrationLink }]\n    ]\n  })\n};"}, "id": "b1e6d41d-6b8d-4a04-aa18-f5e46e74f09d", "name": "Create Registration Link", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [540, 220]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "additionalFields": {"parseMode": "HTML", "replyMarkup": "={{ $json.reply_markup }}"}}, "id": "c7c6e346-9cfb-4a7a-9427-9a75696735ed", "name": "Send Register Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [740, 220], "credentials": {"telegramApi": {"id": "1", "name": "Telegram Bot API account"}}}, {"parameters": {"path": "webhook/registration", "responseMode": "responseNode", "options": {"responseHeaders": {"values": {"entries": [{"name": "content-type", "value": "application/json"}]}}}}, "id": "95e99b94-a8e3-448e-b436-d78bc03975be", "name": "Registration Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1.1, "position": [100, 420], "webhookId": "db634b03-2399-4a5c-9f43-a45c04bcecc8"}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "name", "value": "={{ $json.body.Name }}"}, {"name": "phoneNumber", "value": "={{ $json.body[\"Phone Number \"] }}"}, {"name": "email", "value": "={{ $json.body.Email }}"}, {"name": "birthday", "value": "={{ $json.body.Birthday }}"}, {"name": "telegramId", "value": "={{ $json.body.telegram_id }}"}]}, "options": {}}, "id": "b2c79174-bd55-4fbe-97c2-a823386aec0d", "name": "Process Form Data", "type": "n8n-nodes-base.set", "typeVersion": 3.2, "position": [320, 420]}, {"parameters": {"method": "POST", "url": "={{ $env.LOYAL_API_BASE_URL }}/api/telegram/webhook", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-webhook-secret", "value": "={{ $env.WEBHOOK_SECRET }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "formData", "value": "={{ $json }}"}]}, "options": {}}, "id": "e08346ec-87ce-4c93-a7b2-fdbe8d8744c4", "name": "Send to API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [540, 420]}, {"parameters": {"functionCode": "// Get the result from the API\nconst apiResponse = $node[\"Send to API\"].json;\nconst telegramId = $node[\"Process Form Data\"].json.telegramId;\n\n// Prepare message based on registration status\nlet message = \"\";\nlet keyboard = [];\n\nswitch(apiResponse.status) {\n  case \"created\":\n    message = \"✅ <b>Registration Successful!</b>\\n\\nWelcome to our loyalty program! You can now start earning points and enjoying rewards. Use the buttons below to explore.\";\n    keyboard = [\n      [{ text: \"💰 View Points\", callback_data: \"/points\" }],\n      [{ text: \"🏆 Available Rewards\", callback_data: \"/rewards\" }]\n    ];\n    break;\n  \n  case \"linked\":\n    message = \"✅ <b>Account Linked!</b>\\n\\nYour existing account has been successfully linked to Telegram. You can now access your loyalty program features directly from this chat.\";\n    keyboard = [\n      [{ text: \"💰 View Points\", callback_data: \"/points\" }],\n      [{ text: \"🏆 Available Rewards\", callback_data: \"/rewards\" }]\n    ];\n    break;\n    \n  case \"existing\":\n    message = \"ℹ️ <b>Already Registered</b>\\n\\nYou are already registered in our loyalty program. You can use the buttons below to access your account features.\";\n    keyboard = [\n      [{ text: \"💰 View Points\", callback_data: \"/points\" }],\n      [{ text: \"🏆 Available Rewards\", callback_data: \"/rewards\" }]\n    ];\n    break;\n    \n  default:\n    message = \"❌ <b>Registration Error</b>\\n\\nSorry, there was an issue processing your registration. Please try again later or contact support.\";\n    keyboard = [\n      [{ text: \"🔄 Try Again\", callback_data: \"/register\" }]\n    ];\n}\n\nreturn {\n  chatId: telegramId,\n  text: message,\n  parse_mode: \"HTML\",\n  reply_markup: JSON.stringify({\n    inline_keyboard: keyboard\n  })\n};"}, "id": "e125562a-b86e-4b58-9c85-3bed0079bffb", "name": "Format Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 420]}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "additionalFields": {"parseMode": "HTML", "replyMarkup": "={{ $json.reply_markup }}"}}, "id": "27cccb1c-60cc-4ca8-b889-4003672ca403", "name": "Send Confirmation", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [940, 420], "credentials": {"telegramApi": {"id": "1", "name": "Telegram Bot API account"}}}, {"parameters": {"options": {}}, "id": "0e412be5-c8f3-4e19-bdc0-af6b2c79fb5e", "name": "Registration Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [940, 240]}, {"parameters": {"chatId": "={{ $node[\"Telegram Trigger\"].json.message.chat.id }}", "text": "I don't understand that command. Type /register to start the registration process.", "additionalFields": {}}, "id": "b77410fa-5f9d-4e0f-a611-e81c514b2762", "name": "Invalid Command", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [540, 80], "credentials": {"telegramApi": {"id": "1", "name": "Telegram Bot API account"}}}, {"parameters": {"functionCode": "// Return a success response to the webhook\nreturn {\n  status: 200,\n  data: {\n    success: true,\n    message: \"Form submission received\"\n  }\n};"}, "id": "3e0f37d1-89d3-4f94-8d12-900c8af46b62", "name": "Generate Success Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [740, 240]}], "connections": {"Telegram Trigger": {"main": [[{"node": "Is Register Command?", "type": "main", "index": 0}]]}, "Is Register Command?": {"main": [[{"node": "Create Registration Link", "type": "main", "index": 0}], [{"node": "Invalid Command", "type": "main", "index": 0}]]}, "Create Registration Link": {"main": [[{"node": "Send Register Message", "type": "main", "index": 0}]]}, "Registration Webhook": {"main": [[{"node": "Process Form Data", "type": "main", "index": 0}]]}, "Process Form Data": {"main": [[{"node": "Send to API", "type": "main", "index": 0}]]}, "Send to API": {"main": [[{"node": "Format Response", "type": "main", "index": 0}, {"node": "Generate Success Response", "type": "main", "index": 0}]]}, "Format Response": {"main": [[{"node": "Send Confirmation", "type": "main", "index": 0}]]}, "Generate Success Response": {"main": [[{"node": "Registration Webhook Response", "type": "main", "index": 0}]]}, "Set Command": {"main": [[{"node": "<PERSON>eg<PERSON>", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "all", "saveDataSuccessExecution": "all", "saveManualExecutions": false, "callerPolicy": "any", "errorWorkflow": "0"}, "staticData": null, "tags": [{"name": "Telegram"}, {"name": "Loyalty"}], "versionId": "7d0ba67f-1cfc-4ea9-8e57-75bb173807a1", "triggerCount": 2}