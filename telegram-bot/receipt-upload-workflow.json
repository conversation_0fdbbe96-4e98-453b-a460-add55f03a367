{"name": "receipt-upload-workflow", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [-440, 100], "webhookId": "telegram-receipt-upload", "id": "4cd721d0-3b0d-4e8d-9084-e4dc095f178e", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"jsCode": "// Set configuration values here\nconst config = {\n  API_BASE_URL: \"https://8235-213-41-102-186.ngrok-free.app\",\n  COMPANY_ID: \"e31cbdcd-a9f4-482d-ad65-d2be34dde3c6\",\n  TELEGRAM_ACCESS_TOKEN: '**********************************************',\n  SUPABASE_URL: \"https://vqltspteqqllvhyiupkf.supabase.co\",\n  SUPABASE_API_KEY: \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxbHRzcHRlcXFsbHZoeWl1cGtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjU2MDEwMSwiZXhwIjoyMDU4MTM2MTAxfQ.dLpfmGuitOpLQ3AB_Xlj-d-nwWx_pJOAbte5wT35h0Y\",\n  SUPABASE_STORAGE_BUCKET: \"receipts\"\n};\nreturn {\n  json: {\n    ...$input.item.json,\n    config\n  }\n};"}, "name": "Configuration", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-240, 100], "id": "243d402f-d568-4a71-9de2-22a01b4276e7"}, {"parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json.hasOwnProperty(\"message\") && $node[\"Telegram Trigger\"].json.message.hasOwnProperty(\"text\") && $node[\"Telegram Trigger\"].json.message.text.startsWith(\"/receipt\")}}", "value2": "={{true}}"}]}}, "name": "Is Receipt Command?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-40, 100], "id": "4a6cfce8-2c46-4872-a0f7-0c5fccdbe23b"}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "Please provide your loyalty ID and upload a photo of your receipt.\n\nFormat: YOUR_LOYALTY_ID\n(then attach a photo of the receipt)\n\nExample: F0051219", "additionalFields": {}}, "name": "Send Receipt Instructions", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [160, 0], "id": "ba961127-99c8-4690-a8a6-630f0ea68f39", "webhookId": "27d2576f-125b-4b77-9746-0c8d53d2b93a", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json.hasOwnProperty(\"message\") && $node[\"Telegram Trigger\"].json.message.hasOwnProperty(\"photo\")}}", "value2": "={{true}}"}]}}, "name": "Has Photo?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-40, 300], "id": "9cb4c6a9-2f54-4987-bd37-e1784bb797bf"}, {"parameters": {"url": "=https://api.telegram.org/bot{{$credential.telegramApi.accessToken}}/getFile", "method": "POST", "sendBody": true, "bodyParameters": {"parameters": [{"name": "file_id", "value": "={{ $node[\"Telegram Trigger\"].json.message.photo[$node[\"Telegram Trigger\"].json.message.photo.length-1].file_id }}"}]}, "options": {}}, "name": "Get File Info", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, 300], "id": "69953e74-da1a-4953-bad9-6c82ccc3079a"}, {"parameters": {"url": "=https://api.telegram.org/file/bot{{$json.config.TELEGRAM_ACCESS_TOKEN}}/{{ $json.result.file_path }}", "options": {"response": {"response": {"fullResponse": true, "responseFormat": "file"}}}}, "name": "Download Photo", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [360, 300], "id": "f37a949d-9d08-49dd-b441-02b4163e5b48"}, {"parameters": {"promptType": "define", "text": "Extract all information from this Fufi's Beauty Services receipt image.\n\nReturn ONLY a valid JSON object with the following structure - no explanation text, no markdown formatting:\n\n{\n  \"receipt_number\": \"FS No.XXXXXXXX\",\n  \"reference_number\": \"CSI-XXX-XX-XXXXXXX\", \n  \"transaction_date\": \"DD/MM/YYYY\",\n  \"transaction_time\": \"HH:MM\",\n  \"business_name\": \"FUFIS BEAUTY SERVICES P.L.C\",\n  \"business_tin\": \"0076655480\",\n  \"cashier_id\": \"EMPLOYEE XX\",\n  \"services\": [\n    {\n      \"description\": \"Service Name\",\n      \"quantity\": X,\n      \"unit_price\": XXX,\n      \"amount\": XXX\n    }\n  ],\n  \"subtotal\": XXX,\n  \"tax_percentage\": XX,\n  \"tax_amount\": XX.XX,\n  \"total_amount\": XXX.XX,\n  \"payment_method\": \"CASH/CARD\"\n}\n\nFollow this EXACT format, filling in the actual values from the receipt image.\n\n", "hasOutputParser": true, "attachments": {"binary": {"data": "={{ $node['Download Photo'].json.data }}"}}, "options": {"systemMessage": "You are a specialized receipt data extraction assistant for <PERSON><PERSON>'s Beauty Services. Your task is to accurately extract structured data from receipt images for a loyalty program database. Focus on precision and consistency when identifying fields like receipt numbers, dates, services, and amounts.\n\nAlways return data in valid JSON format without any explanatory text or markdown formatting. Never include code block delimiters or any content outside the JSON structure itself.\n\nFor <PERSON><PERSON>'s receipts specifically, pay attention to:\n1. The receipt number format (FS No.XXXXXXXX)\n2. Reference numbers (CSI-XXX-XX-XXXXXXX)\n3. Service descriptions and their quantities/prices\n4. Tax calculations (typically 15%)\n5. Total amounts\n\nIf any field is unclear or missing, leave it blank rather than guessing. Maintain the exact structure requested in the output format.\n\nDO NOT MAKE UP THE SERVICES OR ANYTHING IN THE RECEIPT DATA!!!\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [592, 300], "id": "cf59ef0b-9204-4ce6-a77e-e7ba4e88fab9", "name": "AI Agent", "onError": "continueErrorOutput"}, {"parameters": {"functionCode": "// Input is the AI Agent's output\nconst agentOutput = $input.item.json.output; \nlet extractedData = {};\n\n// Attempt to parse the JSON directly from the agent's output string\ntry {\n  extractedData = JSON.parse(agentOutput);\n} catch (e) {\n  console.log('Failed to parse direct JSON, trying regex extraction from:', agentOutput);\n  // Fallback: Use regex if direct parsing fails \n  const jsonMatch = agentOutput.match(/\\{\\s*[\\S\\s]*?\\}/);\n  if (jsonMatch && jsonMatch[0]) {\n    try {\n      extractedData = JSON.parse(jsonMatch[0]);\n    } catch (e2) {\n      console.log('Failed to parse JSON even after regex extraction:', e2);\n      extractedData = { error: 'AI parsing failed', raw_output: agentOutput };\n    }\n  } else {\n    extractedData = { error: 'No JSON found in AI output', raw_output: agentOutput };\n  }\n}\n\n// --- Loyalty ID Extraction Logic (from original Parse Extraction node) ---\nlet loyaltyId = null;\nconst message = $node[\"Telegram Trigger\"].json.message;\nif (message && message.reply_to_message && message.reply_to_message.text) {\n  const matchId = message.reply_to_message.text.match(/[A-Za-z]\\d{7}/);\n  if (matchId) loyaltyId = matchId[0];\n} else if (message && message.caption) {\n  const matchId = message.caption.match(/[A-Za-z]\\d{7}/);\n  if (matchId) loyaltyId = matchId[0];\n}\n\nconst messageHistory = $node[\"Telegram Trigger\"].json.message_history || [];\nif (!loyaltyId && messageHistory.length > 0) {\n  for (let i = messageHistory.length - 1; i >= 0; i--) {\n    if (messageHistory[i].from.id === message.from.id) {\n      const matchId = messageHistory[i].text?.match(/[A-Za-z]\\d{7}/);\n      if (matchId) {\n        loyaltyId = matchId[0];\n        break;\n      }\n    }\n  }\n}\n\n// --- Data Formatting Logic ---\nlet purchaseDate = new Date().toISOString(); // Default to now\nif (extractedData.transaction_date) {\n  try {\n    const dateParts = extractedData.transaction_date.split('/'); // Expects DD/MM/YYYY\n    const timePart = extractedData.transaction_time || '00:00'; // Default time if missing\n    const isoDateStr = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${timePart}:00`;\n    const parsedDate = new Date(isoDateStr);\n    if (!isNaN(parsedDate)) {\n      purchaseDate = parsedDate.toISOString();\n    } else {\n       console.log(\"Failed to parse date from AI: \", extractedData.transaction_date, extractedData.transaction_time);\n    }\n  } catch (error) {\n    console.log(\"Error converting date:\", error);\n  }\n}\n\n// Calculate points (1 point per Birr spent)\nconst pointsAwarded = Math.floor(extractedData.total_amount || 0);\n\n// Format service description for services array if present\nlet serviceDescription = \"Service from receipt\";\nif (extractedData.services && Array.isArray(extractedData.services) && extractedData.services.length > 0) {\n  serviceDescription = extractedData.services.map(service => \n    `${service.description} (${service.quantity} x ${service.unit_price})`\n  ).join(', ');\n} else if (extractedData.service_description) { \n  serviceDescription = extractedData.service_description;\n}\n\n// Generate a receipt number if missing\nlet receiptNumber = extractedData.receipt_number || null;\nif (receiptNumber && receiptNumber.includes('FS No.')) {\n  receiptNumber = receiptNumber.replace('FS No.', '').trim();\n}\n\n// Format file name for Supabase storage\nconst timestamp = Date.now();\nconst fileName = `${loyaltyId || 'unknown'}/${timestamp}.jpg`;\n\n// Compile the final data aligned with DB schema\nconst telegram_chat_id = message.chat.id.toString();\nconst company_id = $json.config.COMPANY_ID;\n\nreturn {\n  json: {\n    // Receipt DB fields matching schema\n    receipt_number: receiptNumber,\n    reference_number: extractedData.reference_number || null,\n    loyalty_id: loyaltyId,\n    uploader_telegram_id: telegram_chat_id,\n    purchase_date: purchaseDate,\n    total_amount: extractedData.total_amount || 0,\n    subtotal: extractedData.subtotal || extractedData.total_amount || 0,\n    tax_amount: extractedData.tax_amount || 0,\n    tax_percentage: extractedData.tax_percentage || 0,\n    service_description: serviceDescription,\n    cashier_id: extractedData.cashier_id || null,\n    points_awarded: pointsAwarded,\n    payment_method: extractedData.payment_method || 'CASH',\n    business_name: extractedData.business_name || null,\n    business_tin: extractedData.business_tin || null,\n    business_location: extractedData.business_location || null,\n    company_id: company_id,\n    \n    // Metadata for workflow\n    telegram_chat_id: telegram_chat_id,\n    raw_extraction: agentOutput,\n    parsed_extraction: extractedData,\n    file_name: fileName, // For Supabase upload\n    timestamp: timestamp // For reference\n  }\n};"}, "name": "Parse Agent Output & Format Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [792, 300], "id": "0945176a-d892-47b6-8f0c-468db6ab44a0"}, {"parameters": {"method": "POST", "url": "={{ $json.config.SUPABASE_URL }}/storage/v1/object/{{ $json.config.SUPABASE_STORAGE_BUCKET }}/{{ $json.file_name }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "={{ $json.config.SUPABASE_API_KEY }}"}, {"name": "Authorization", "value": "=Bearer {{ $json.config.SUPABASE_API_KEY }}"}, {"name": "Content-Type", "value": "image/jpeg"}]}, "sendBody": true, "contentType": "binaryData", "bodyBinary": "={{ $node[\"Download Photo\"].binary.data.data }}", "options": {"response": {"response": {"fullResponse": true}}}}, "name": "Upload to Supabase Storage", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [900, 380], "id": "supabase-upload-node-id", "retryOnFail": true}, {"parameters": {"functionCode": "// Process Supabase upload response and add image URL to receipt data\nconst receiptData = $node[\"Parse Agent Output & Format Data\"].json;\nconst supabaseResponse = $node[\"Upload to Supabase Storage\"].json;\n\n// Check if upload was successful\nconst uploadSuccess = supabaseResponse.statusCode >= 200 && supabaseResponse.statusCode < 300;\n\n// Generate the public URL for the uploaded file\nconst imageUrl = uploadSuccess \n  ? `${receiptData.config.SUPABASE_URL}/storage/v1/object/public/${receiptData.config.SUPABASE_STORAGE_BUCKET}/${receiptData.file_name}` \n  : null;\n\n// Update the receipt data with the image URL\nreturn {\n  json: {\n    ...receiptData,\n    receipt_image_url: imageUrl,\n    upload_success: uploadSuccess,\n    upload_response: supabaseResponse\n  }\n};"}, "name": "Process Upload Response", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1000, 380], "id": "process-upload-node-id"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"https://json-schema.org/draft/2020-12/schema\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"receipt_number\": { \"type\": \"string\" },\n    \"reference_number\": { \"type\": \"string\" },\n    \"transaction_date\": { \"type\": \"string\" },\n    \"transaction_time\": { \"type\": \"string\" },\n    \"business_name\": { \"type\": \"string\" },\n    \"business_tin\": { \"type\": \"string\" },\n    \"cashier_id\": { \"type\": \"string\" },\n    \"services\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"description\": { \"type\": \"string\" },\n          \"quantity\": { \"type\": \"number\" },\n          \"unit_price\": { \"type\": \"number\" },\n          \"amount\": { \"type\": \"number\" }\n        },\n        \"required\": [\"description\", \"quantity\", \"unit_price\", \"amount\"]\n      }\n    },\n    \"subtotal\": { \"type\": \"number\" },\n    \"tax_percentage\": { \"type\": \"number\" },\n    \"tax_amount\": { \"type\": \"number\" },\n    \"total_amount\": { \"type\": \"number\" },\n    \"payment_method\": { \"type\": \"string\" }\n  },\n  \"required\": [\n    \"receipt_number\",\n    \"reference_number\",\n    \"transaction_date\",\n    \"transaction_time\",\n    \"business_name\",\n    \"subtotal\",\n    \"total_amount\",\n    \"services\"\n  ]\n}\n\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [800, 500], "id": "99e31696-a8ae-46b6-8749-89e164a8ecc1", "name": "Structured Output Parser"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [680, 500], "id": "206cc47b-175c-4f4c-b135-c4cec65fb140", "name": "Calculator"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4-vision-preview", "mode": "list", "cachedResultName": "gpt-4-vision-preview"}, "options": {"maxRetries": 5}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [560, 500], "id": "a4d99e64-5052-49b9-bd1b-62f37bb8caa7", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "OpenAI API account", "name": "OpenAI API account"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ !!$json.loyalty_id }}", "value2": "={{ true }}"}]}}, "name": "Has Loyalty ID?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1150, 380], "id": "23c47327-ca98-4f42-891b-783b9f0b220f"}, {"parameters": {"method": "POST", "url": "={{ $json.config.API_BASE_URL }}/api/telegram/receipts", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}, {"name": "Integration-Key", "value": "={{ $env['telegram-loyal-integration-key'] }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"receipt_number\": \"{{ $json.receipt_number }}\",\n  \"reference_number\": \"{{ $json.reference_number }}\",\n  \"loyalty_id\": \"{{ $json.loyalty_id }}\",\n  \"uploader_telegram_id\": \"{{ $json.uploader_telegram_id }}\",\n  \"purchase_date\": \"{{ $json.purchase_date }}\",\n  \"total_amount\": {{ $json.total_amount }},\n  \"subtotal\": {{ $json.subtotal }},\n  \"tax_amount\": {{ $json.tax_amount }},\n  \"tax_percentage\": {{ $json.tax_percentage }},\n  \"service_description\": \"{{ $json.service_description }}\",\n  \"cashier_id\": \"{{ $json.cashier_id }}\",\n  \"points_awarded\": {{ $json.points_awarded }},\n  \"payment_method\": \"{{ $json.payment_method }}\",\n  \"receipt_image_url\": \"{{ $json.receipt_image_url }}\",\n  \"business_name\": \"{{ $json.business_name }}\",\n  \"business_tin\": \"{{ $json.business_tin }}\",\n  \"business_location\": \"{{ $json.business_location }}\",\n  \"company_id\": \"{{ $json.company_id }}\"\n}", "options": {}}, "name": "Submit Receipt Data", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1300, 380], "id": "submit-receipt-node-id"}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.success }}", "value2": "={{ true }}"}]}}, "name": "Success?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1460, 380], "id": "f0051219-7b8a-4b1c-8a5f-e4b9a3c5d6e7"}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=🎉 Receipt processed successfully!\n\nMember: {{ $json.member.name }}\nPoints earned: {{ $json.pointsEarned }}\nNew balance: {{ $json.member.available_points }} points\n\nReceipt details (AI-extracted):\n📄 Receipt #: {{ $node[\"Parse Extraction\"].json.receipt_number || 'Not detected' }}\n{{ $node[\"Parse Extraction\"].json.reference_number ? '📋 Reference #: ' + $node[\"Parse Extraction\"].json.reference_number : '' }}\n💼 Business: {{ $node[\"Parse Extraction\"].json.business_name || 'Not detected' }}\n{{ $node[\"Parse Extraction\"].json.business_tin ? '🏢 TIN: ' + $node[\"Parse Extraction\"].json.business_tin : '' }}\n{{ $node[\"Parse Extraction\"].json.business_location ? '📍 Location: ' + $node[\"Parse Extraction\"].json.business_location : '' }}\n📆 Date: {{ new Date($node[\"Parse Extraction\"].json.purchase_date).toLocaleDateString() }}\n💰 Total: {{ $node[\"Parse Extraction\"].json.total_amount }} Birr\n{{ $node[\"Parse Extraction\"].json.subtotal && $node[\"Parse Extraction\"].json.subtotal !== $node[\"Parse Extraction\"].json.total_amount ? '💵 Subtotal: ' + $node[\"Parse Extraction\"].json.subtotal + ' Birr' : '' }}\n{{ $node[\"Parse Extraction\"].json.tax_amount ? '💸 Tax: ' + $node[\"Parse Extraction\"].json.tax_amount + ' Birr (' + $node[\"Parse Extraction\"].json.tax_percentage + '%)' : '' }}\n🧾 Service: {{ $node[\"Parse Extraction\"].json.service_description || 'Not specified' }}\n{{ $node[\"Parse Extraction\"].json.payment_method ? '💳 Payment: ' + $node[\"Parse Extraction\"].json.payment_method : '' }}\n\nThank you for your purchase!", "additionalFields": {}}, "name": "Send Success Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1660, 200], "id": "9a44f13e-da8a-448d-ab6f-ed4e81d0be2b", "webhookId": "a0ab5545-d0d5-4700-a9e7-8b547ba1fa69", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=❌ Error processing receipt: {{ $json.error }}\n\nPlease check your loyalty ID and try again.", "additionalFields": {}}, "name": "Send Error Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1660, 500], "id": "23c47327-ca98-4f42-891b-783b9f0b220f", "webhookId": "7625a58c-846c-4bbd-b5e0-341c270d8e24", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "Is Receipt Command?", "type": "main", "index": 0}, {"node": "Has Photo?", "type": "main", "index": 0}]]}, "Is Receipt Command?": {"main": [[{"node": "Send Receipt Instructions", "type": "main", "index": 0}]]}, "Has Photo?": {"main": [[{"node": "Get File Info", "type": "main", "index": 0}]]}, "Get File Info": {"main": [[{"node": "Download Photo", "type": "main", "index": 0}]]}, "Download Photo": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Parse Agent Output & Format Data", "type": "main", "index": 0}]]}, "Parse Agent Output & Format Data": {"main": [[{"node": "Upload to Supabase Storage", "type": "main", "index": 0}]]}, "Upload to Supabase Storage": {"main": [[{"node": "Process Upload Response", "type": "main", "index": 0}]]}, "Process Upload Response": {"main": [[{"node": "Has Loyalty ID?", "type": "main", "index": 0}]]}, "Has Loyalty ID?": {"main": [[{"node": "Submit Receipt Data", "type": "main", "index": 0}]], "else": [[{"node": "Request Loyalty ID", "type": "main", "index": 0}]]}, "Submit Receipt Data": {"main": [[{"node": "Send Success Message", "type": "main", "index": 0}]], "error": [[{"node": "Send Error Message", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0603686f-5ec3-499d-aa4c-b285eff351cc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "bee413acb9414930982d62c120698aa010ed90bd17c4ca0b49cdc96d38963cb7"}, "id": "y4bwnHstz491ptZm", "tags": []}