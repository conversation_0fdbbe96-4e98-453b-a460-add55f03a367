const fetch = require('node-fetch');

async function testMemberSignup() {
  const apiUrl = 'https://8235-213-41-102-186.ngrok-free.app/api/telegram/members';
  const requestBody = {
    api_key: 'telegram-loyal-integration-key',
    company_id: 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6',
    name: 'Test User',
    phone_number: '*********0',
    email: '<EMAIL>',
    telegram_chat_id: '*********',
    initial_points: 0
  };

  console.log('Sending request to:', apiUrl);
  console.log('Request body:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
}

testMemberSignup();
