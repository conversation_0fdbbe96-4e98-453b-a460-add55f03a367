{"name": "Telegram Member Registration", "nodes": [{"parameters": {"botToken": "={{ $env.TELEGRAM_BOT_TOKEN }}", "updates": ["message"], "options": {"parseMode": "HTML"}}, "id": "1a9eaece-e45a-4573-9c34-8bf5e234e603", "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1.1, "position": [240, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.message.text }}", "operation": "startsWith", "value2": "/register"}]}}, "id": "1648d1e9-7a3e-48fa-a552-74c3e994ad27", "name": "Is Register Command", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"id": "b6693f5a-0158-47e6-8e9a-0ed4aa8e25c2", "type": "n8n-nodes-base.function", "parameters": {"functionCode": "// Get the user information from Telegram\nconst telegramUser = $node['Telegram Trigger'].json.message.from;\nconst chatId = $node['Telegram Trigger'].json.message.chat.id;\nconst telegramId = telegramUser.id.toString();\n\n// Create registration link\nconst baseUrl = 'https://loyal-et.vercel.app';\nconst registrationLink = `${baseUrl}/telegram/register?telegram_id=${telegramId}`;\n\nreturn {\n  json: {\n    chatId,\n    text: '🎉 <b>Loyalty Program Registration</b> 🎉\n\nComplete your registration:',\n    parse_mode: 'HTML',\n    reply_markup: JSON.stringify({ inline_keyboard: [[{ text: '📝 Register Now', url: registrationLink }]] })\n  }\n};"}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "additionalFields": {"parseMode": "HTML", "disable_web_page_preview": true, "disable_notification": false, "reply_to_message_id": "", "reply_markup": "={{ $json.reply_markup }}"}}, "id": "ec0f71ab-56d6-453c-9bb1-7640c0866a88", "name": "Send Register Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [900, 240], "credentials": {"telegramApi": {"id": "1", "name": "Telegram Bot API"}}}, {"parameters": {"chatId": "={{ $node[\"Telegram Trigger\"].json.message.chat.id }}", "text": "I don't understand that command. Type /register to start the registration process.", "additionalFields": {}}, "id": "eb8bfb4f-b2a2-4a41-ba8f-ca3278cc0101", "name": "Invalid Command", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [680, 380], "credentials": {"telegramApi": {"id": "1", "name": "Telegram Bot API"}}}, {"parameters": {"httpMethod": "POST", "path": "registration", "responseMode": "responseNode", "options": {}}, "id": "c5c639d6-7fbf-4cb1-8a77-b233368e07b4", "name": "Registration Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1.1, "position": [240, 560]}, {"id": "60b9f962-0e4f-4726-97f2-9a168f142c07", "type": "n8n-nodes-base.set", "parameters": {"keepOnlySet": true, "values": {"string": [{"name": "name", "value": "={{ $json.body.Name }}"}, {"name": "phoneNumber", "value": "={{ $json.body['Phone Number'] }}"}, {"name": "email", "value": "={{ $json.body.Email }}"}, {"name": "birthday", "value": "={{ $json.body.Birthday }}"}, {"name": "telegramId", "value": "={{ $json.body.telegram_id }}"}]}}}, {"parameters": {"url": "={{ $env.LOYAL_API_BASE_URL }}/api/telegram/webhook", "authentication": "none", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-webhook-secret", "value": "={{ $env.WEBHOOK_SECRET }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ {\"formData\": $json} }}", "options": {"redirect": {"redirect": "follow"}}}, "id": "8079ca20-bc2e-411e-91c9-c2e6ea205a1b", "name": "Send to API", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 560]}, {"id": "aa547dc7-b835-48ce-8fb5-830813347292", "type": "n8n-nodes-base.function", "parameters": {"functionCode": "// Format API response and prepare Telegram message\nconst apiResponse = $node['Send to API'].json;\nconst telegramId = $node['Process Form Data'].json.telegramId;\nlet message = ''; let keyboard = [];\nswitch (apiResponse.status) {\n  case 'created': message = '✅ <b>Registration Successful!</b>'; keyboard = [[{ text: '💰 View Points', callback_data: '/points' }]]; break;\n  /* other cases */\n}\nreturn { json: { chatId: telegramId, text: message, parse_mode: 'HTML', reply_markup: JSON.stringify({ inline_keyboard: keyboard }) } };"}}, {"parameters": {"chatId": "={{ $json.chatId }}", "text": "={{ $json.text }}", "additionalFields": {"parseMode": "HTML", "disable_web_page_preview": true, "disable_notification": false, "reply_markup": "={{ $json.reply_markup }}"}}, "id": "a832e4c9-d763-4bd1-9672-5b9a666d7a61", "name": "Send Confirmation", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [1120, 560], "credentials": {"telegramApi": {"id": "1", "name": "Telegram Bot API"}}}, {"parameters": {"jsCode": "// Return a success response to the webhook\nreturn {\n  json: {\n    status: 200,\n    data: {\n      success: true,\n      message: \"Form submission received\"\n    }\n  }\n};"}, "id": "7cf7b087-5789-4194-8318-466346a528f3", "name": "Generate Response", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {}}, "id": "61c2ee3e-6ddc-4035-9053-2c8fd75c225a", "name": "Webhook Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 400]}], "connections": {"Telegram Trigger": {"main": [[{"node": "Is Register Command", "type": "main", "index": 0}]]}, "Is Register Command": {"main": [[{"node": "Create Registration Link", "type": "main", "index": 0}], [{"node": "Invalid Command", "type": "main", "index": 0}]]}, "Create Registration Link": {"main": [[{"node": "Send Register Message", "type": "main", "index": 0}]]}, "Registration Webhook": {"main": [[{"node": "Process Form Data", "type": "main", "index": 0}]]}, "Process Form Data": {"main": [[{"node": "Send to API", "type": "main", "index": 0}]]}, "Send to API": {"main": [[{"node": "Format Response", "type": "main", "index": 0}, {"node": "Generate Response", "type": "main", "index": 0}]]}, "Format Response": {"main": [[{"node": "Send Confirmation", "type": "main", "index": 0}]]}, "Generate Response": {"main": [[{"node": "Webhook Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "saveDataErrorExecution": "none", "saveDataSuccessExecution": "none", "saveManualExecutions": false, "callerPolicy": "any", "errorWorkflow": ""}, "staticData": null, "tags": [{"name": "Telegram"}, {"name": "Loyalty"}], "triggerCount": 2}