{"name": "reward-redemption-workflow", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [-440, 100], "webhookId": "telegram-reward-redemption", "id": "4cd721d0-3b0d-4e8d-9084-e4dc095f178e", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"jsCode": "// Set configuration values here\nconst config = {\n  // For local development with ngrok tunnel\n  API_BASE_URL: \"https://8235-213-41-102-186.ngrok-free.app\",\n  \n  // For production (uncomment when deploying)\n  // API_BASE_URL: \"https://your-loyal-app.com\",\n  \n  // Your company ID from the database\n  COMPANY_ID: \"e31cbdcd-a9f4-482d-ad65-d2be34dde3c6\"\n};\n\n// In n8n 1.88.0, we need to use $input instead of item\nreturn {\n  json: {\n    ...$input.item.json,\n    config\n  }\n};"}, "name": "Configuration", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-240, 100], "id": "243d402f-d568-4a71-9de2-22a01b4276e7"}, {"parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"text\"]}}", "operation": "startsWith", "value2": "/redeem"}]}}, "name": "Redeem Command?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-40, 100], "id": "4a6cfce8-2c46-4872-a0f7-0c5fccdbe23b"}, {"parameters": {"functionCode": "// Extract loyalty ID from the /rewards command\nconst text = $node[\"Telegram Trigger\"].json.message.text;\nconst parts = text.split(' ');\n\n// If it's just /rewards without loyalty ID, return empty\nif (parts.length === 1) {\n  return {\n    json: {\n      loyaltyId: null,\n      hasLoyaltyId: false\n    }\n  };\n}\n\n// Extract the loyalty ID (second part)\nconst loyaltyId = parts[1]?.trim();\n\nreturn {\n  json: {\n    loyaltyId: loyaltyId,\n    hasLoyaltyId: !!loyaltyId\n  }\n};"}, "name": "Extract Loyalty ID", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [40, 0], "id": "extract-loyalty-id-node"}, {"parameters": {"method": "POST", "url": "https://8235-213-41-102-186.ngrok-free.app/api/telegram/members/lookup", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "api_key", "value": "telegram-loyal-integration-key"}, {"name": "company_id", "value": "={{$input.item.json.config.COMPANY_ID}}"}, {"name": "loyalty_id", "value": "={{$node[\"Extract Loyalty ID\"].json[\"loyaltyId\"]}}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [160, 0], "id": "member-lookup-node", "name": "Lookup Member"}, {"parameters": {"method": "GET", "url": "https://8235-213-41-102-186.ngrok-free.app/api/telegram/rewards", "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "telegram-loyal-integration-key"}, {"name": "company_id", "value": "={{$input.item.json.config.COMPANY_ID}}"}, {"name": "active", "value": "true"}, {"name": "member_id", "value": "={{$node[\"Lookup Member\"].json[\"data\"][\"id\"]}}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [280, 0], "id": "ba961127-99c8-4690-a8a6-630f0ea68f39", "name": "Fetch Available Rewards"}, {"parameters": {"functionCode": "// First, we need to get the member's loyalty ID from the message\nconst text = $node[\"Telegram Trigger\"].json.message.text;\nconst isRewardsCommand = text === '/rewards';\n\n// If it's just /rewards command, we need to ask for loyalty ID\nif (isRewardsCommand) {\n  return {\n    json: {\n      rewardsList: \"To see your available rewards, please use:\\n/rewards [YOUR_LOYALTY_ID]\\n\\nExample: /rewards F9895494\\n\\nYour loyalty ID can be found on your membership card or receipt.\"\n    }\n  };\n}\n\n// If it includes loyalty ID, format the rewards list\nconst rewards = $input.item.json.data || [];\n\nlet rewardsList = \"Available Rewards:\\n\\n\";\n\nif (rewards.length === 0) {\n  rewardsList = \"No rewards are currently available for redemption. You may have already redeemed all available rewards or don't have enough points.\";\n} else {\n  rewards.forEach((reward, index) => {\n    rewardsList += `${index + 1}. ${reward.title} (${reward.points_required} points)\\n   Code: ${reward.code}\\n   ${reward.description}\\n\\n`;\n  });\n  \n  rewardsList += \"\\nTo redeem a reward, reply with:\\n/redeem [YOUR_LOYALTY_ID], [REWARD_CODE]\\n\\nExample: /redeem F9895494, HA12\";\n}\n\nreturn {\n  json: {\n    rewardsList\n  }\n};"}, "name": "Format Rewards List", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [400, 0], "id": "69953e74-da1a-4953-bad9-6c82ccc3079a"}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "={{$node[\"Format Rewards List\"].json[\"rewardsList\"]}}", "additionalFields": {}}, "name": "Send Rewards List", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [600, 0], "id": "9a44f13e-da8a-448d-ab6f-ed4e81d0be2b", "webhookId": "a0ab5545-d0d5-4700-a9e7-8b547ba1fa69", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"text\"]}}", "operation": "regex", "value2": "^\\/redeem\\s+[A-Za-z0-9]+,\\s*[A-Z0-9]{4}$"}]}}, "name": "Is Redemption Request?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [160, 200], "id": "9cb4c6a9-2f54-4987-bd37-e1784bb797bf"}, {"parameters": {"functionCode": "// Extract loyalty ID and reward code from message\nconst text = $input.item.json.message.text;\nconst parts = text.replace('/redeem', '').trim().split(',').map(part => part.trim());\n\nconst loyalty_id = parts[0];\nconst reward_code = parts[1];\nconst telegram_chat_id = $input.item.json.message.chat.id.toString();\n\n// Get company ID from configuration\nconst company_id = $input.item.json.config.COMPANY_ID;\n\nreturn {\n  json: {\n    loyalty_id,\n    reward_code,\n    company_id,\n    telegram_chat_id\n  }\n};"}, "name": "Parse Redemption Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [360, 200], "id": "0945176a-d892-47b6-8f0c-468db6ab44a0"}, {"parameters": {"method": "GET", "url": "https://8235-213-41-102-186.ngrok-free.app/api/telegram/members/lookup", "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "telegram-loyal-integration-key"}, {"name": "company_id", "value": "={{ $json.company_id }}"}, {"name": "loyalty_id", "value": "={{ $json.loyalty_id }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [560, 200], "id": "f37a949d-9d08-49dd-b441-02b4163e5b48", "name": "Lookup Member"}, {"parameters": {"method": "GET", "url": "https://8235-213-41-102-186.ngrok-free.app/api/telegram/rewards/lookup", "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "telegram-loyal-integration-key"}, {"name": "company_id", "value": "={{ $json.company_id }}"}, {"name": "reward_code", "value": "={{ $json.reward_code }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [760, 200], "id": "c2b5c903-d877-4800-aefb-bfb9a19dd959", "name": "Lookup <PERSON><PERSON>"}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.success }}", "value2": "={{ true }}"}]}}, "name": "Member Found?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [560, 400], "id": "23c47327-ca98-4f42-891b-783b9f0b220f"}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.success }}", "value2": "={{ true }}"}]}}, "name": "Reward Found?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [760, 400], "id": "b32c80ca-02a9-4379-a527-7f5d9116d8e2"}, {"parameters": {"functionCode": "// Check if member has enough points\nconst member = $input.item.json.data;\nconst reward = $node[\"Lookup Reward\"].json.data;\n\nconst availablePoints = member.available_points || 0;\nconst requiredPoints = reward.points_required || 0;\n\nconst hasEnoughPoints = availablePoints >= requiredPoints;\n\nreturn {\n  json: {\n    member,\n    reward,\n    hasEnoughPoints,\n    availablePoints,\n    requiredPoints\n  }\n};"}, "name": "Check Points", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [960, 400], "id": "e799d57a-0d4a-4d54-b2e2-6182708df634"}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.hasEnoughPoints }}", "value2": "={{ true }}"}]}}, "name": "Has Enough Points?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1160, 400], "id": "d0805d2d-1288-401a-b636-a509271594ae"}, {"parameters": {"method": "POST", "url": "https://8235-213-41-102-186.ngrok-free.app/api/telegram/redemptions", "sendBody": true, "bodyParameters": {"parameters": [{"name": "api_key", "value": "telegram-loyal-integration-key"}, {"name": "company_id", "value": "={{ $json.member.company_id }}"}, {"name": "member_id", "value": "={{ $json.member.id }}"}, {"name": "reward_id", "value": "={{ $json.reward.id }}"}, {"name": "notes", "value": "Redeemed via Telegram"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1360, 300], "id": "3db8e9f3-f472-4a6b-94a2-a9886a6cf0b1", "name": "Process Redemption"}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=🎉 Reward redeemed successfully!\n\nReward: {{ $json.reward.title }}\nPoints used: {{ $json.redemption.points_used }}\nNew balance: {{ $json.member.available_points }} points\n\nShow this message to the cashier to claim your reward.", "additionalFields": {}}, "name": "Send Success Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1560, 300], "id": "98861aa9-18e7-43a2-b419-1c28b1f97a0b", "webhookId": "a0ab5545-d0d5-4700-a9e7-8b547ba1fa69", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=❌ You don't have enough points for this reward.\n\nReward: {{ $json.reward.title }}\nPoints required: {{ $json.requiredPoints }}\nYour points: {{ $json.availablePoints }}", "additionalFields": {}}, "name": "Send Not Enough Points", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1360, 500], "id": "e31cbdcd-a9f4-482d-ad65-d2be34dde3c6", "webhookId": "7625a58c-846c-4bbd-b5e0-341c270d8e24", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=❌ <PERSON>ward not found.\n\nPlease check the reward code and try again.", "additionalFields": {}}, "name": "Send Reward Not Found", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [760, 600], "id": "f0051219-7b8a-4b1c-8a5f-e4b9a3c5d6e7", "webhookId": "7625a58c-846c-4bbd-b5e0-341c270d8e24", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=❌ Member not found.\n\nPlease check your loyalty ID and try again.", "additionalFields": {}}, "name": "Send Member Not Found", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [560, 600], "id": "a509271594ae-1288-401a-b636-d0805d2d", "webhookId": "7625a58c-846c-4bbd-b5e0-341c270d8e24", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "I couldn't understand your message. To redeem a reward, please use this format:\n\n/redeem YOUR_LOYALTY_ID, REWARD_CODE\n\nExample: /redeem ABC123, HA12", "additionalFields": {}}, "name": "Send Format Error", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [360, 400], "id": "a9886a6cf0b1-f472-4a6b-94a2-3db8e9f3", "webhookId": "cf27fcf0-2dcb-48cd-9bcb-72fbfa434e86", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "Redeem Command?", "type": "main", "index": 0}]]}, "Redeem Command?": {"main": [[{"node": "Fetch Available Rewards", "type": "main", "index": 0}], [{"node": "Is Redemption Request?", "type": "main", "index": 0}]]}, "Fetch Available Rewards": {"main": [[{"node": "Format Rewards List", "type": "main", "index": 0}]]}, "Format Rewards List": {"main": [[{"node": "Send Rewards List", "type": "main", "index": 0}]]}, "Is Redemption Request?": {"main": [[{"node": "Parse Redemption Data", "type": "main", "index": 0}], [{"node": "Send Format Error", "type": "main", "index": 0}]]}, "Parse Redemption Data": {"main": [[{"node": "Lookup Member", "type": "main", "index": 0}]]}, "Lookup Member": {"main": [[{"node": "Member Found?", "type": "main", "index": 0}]]}, "Member Found?": {"main": [[{"node": "Lookup <PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Send Member Not Found", "type": "main", "index": 0}]]}, "Lookup Reward": {"main": [[{"node": "Reward Found?", "type": "main", "index": 0}]]}, "Reward Found?": {"main": [[{"node": "Check Points", "type": "main", "index": 0}], [{"node": "Send Reward Not Found", "type": "main", "index": 0}]]}, "Check Points": {"main": [[{"node": "Has Enough Points?", "type": "main", "index": 0}]]}, "Has Enough Points?": {"main": [[{"node": "Process Redemption", "type": "main", "index": 0}], [{"node": "Send Not Enough Points", "type": "main", "index": 0}]]}, "Process Redemption": {"main": [[{"node": "Send Success Message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0603686f-5ec3-499d-aa4c-b285eff351cc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "bee413acb9414930982d62c120698aa010ed90bd17c4ca0b49cdc96d38963cb7"}, "id": "y4bwnHstz491ptZm", "tags": []}