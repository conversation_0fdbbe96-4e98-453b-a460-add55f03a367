// Modern JavaScript version of the test script
// Run with: node test-api-modern.js

// Test member signup API
async function testMemberSignup() {
  // Check if ngrok URL is working
  const baseUrl = 'https://8235-213-41-102-186.ngrok-free.app';
  const apiUrl = `${baseUrl}/api/telegram/members`;
  
  // First check if the base URL is accessible
  try {
    console.log('Testing if ngrok tunnel is active...');
    const pingResponse = await fetch(baseUrl);
    console.log('Ngrok tunnel status:', pingResponse.status);
  } catch (error) {
    console.error('Error connecting to ngrok tunnel:', error.message);
    console.log('Please make sure your ngrok tunnel is running and the URL is correct.');
    return;
  }
  
  const requestBody = {
    api_key: 'telegram-loyal-integration-key',
    company_id: 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6',
    name: 'Test User',
    phone_number: '*********0',
    email: '<EMAIL>',
    telegram_chat_id: '*********',
    initial_points: 0
  };

  console.log('Sending request to:', apiUrl);
  console.log('Request body:', JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    const data = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
testMemberSignup();
