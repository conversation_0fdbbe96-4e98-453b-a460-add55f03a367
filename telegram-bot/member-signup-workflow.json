{"name": "member-signup-workflow", "nodes": [{"parameters": {"updates": ["message"], "additionalFields": {}}, "name": "<PERSON>eg<PERSON>", "type": "n8n-nodes-base.telegramTrigger", "typeVersion": 1, "position": [-440, 100], "webhookId": "telegram-member-signup", "id": "4cd721d0-3b0d-4e8d-9084-e4dc095f178e", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"jsCode": "// Set configuration values here\nconst config = {\n  // For local development with ngrok tunnel\n  API_BASE_URL: \"https://8235-213-41-102-186.ngrok-free.app\",\n  \n  // For production (uncomment when deploying)\n  // API_BASE_URL: \"https://your-loyal-app.com\",\n  \n  // Your company ID from the database\n  COMPANY_ID: \"e31cbdcd-a9f4-482d-ad65-d2be34dde3c6\"\n};\n\n// In n8n 1.88.0, we need to use $input instead of item\nreturn {\n  json: {\n    ...$input.item.json,\n    config\n  }\n};"}, "name": "Configuration", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-240, 100], "id": "243d402f-d568-4a71-9de2-22a01b4276e7"}, {"parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"text\"]}}", "operation": "startsWith", "value2": "/signup"}]}}, "name": "Signup Command?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-40, 100], "id": "4a6cfce8-2c46-4872-a0f7-0c5fccdbe23b"}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "Welcome to our loyalty program! Please provide the following information to sign up:\n\n1. Your full name\n2. Your phone number\n3. Your email (optional)\n\nExample: <PERSON>, +251912345678, <EMAIL>", "additionalFields": {}}, "name": "Send Signup Instructions", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [160, 0], "id": "ba961127-99c8-4690-a8a6-630f0ea68f39", "webhookId": "27d2576f-125b-4b77-9746-0c8d53d2b93a", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"text\"]}}", "operation": "regex", "value2": "^[^,]+,[^,]+,?.*$"}]}}, "name": "Is Signup Data?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [160, 200], "id": "9cb4c6a9-2f54-4987-bd37-e1784bb797bf"}, {"parameters": {"functionCode": "// Extract name, phone, and optional email from message\nconst text = $input.item.json.message.text;\nconst parts = text.split(',').map(part => part.trim());\n\nconst name = parts[0];\nconst phone = parts[1];\nconst email = parts.length > 2 ? parts[2] : '';\nconst telegram_chat_id = $input.item.json.message.chat.id.toString();\n\n// Get company ID from configuration\nconst company_id = $input.item.json.config.COMPANY_ID;\n\nreturn {\n  json: {\n    company_id,\n    name,\n    phone_number: phone,\n    email,\n    telegram_chat_id,\n    initial_points: 0\n  }\n};"}, "name": "Parse Member Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [360, 200], "id": "69953e74-da1a-4953-bad9-6c82ccc3079a"}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $json.isNew }}", "value2": "={{ true }}"}]}}, "name": "New Member?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [980, 200], "id": "c2b5c903-d877-4800-aefb-bfb9a19dd959"}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=Welcome to our loyalty program, {{ $json.data.name }}! \n\nYour loyalty ID is: {{ $json.data.loyalty_id }}\n\nYou can use this ID when making purchases to earn points. Keep it safe!", "additionalFields": {}}, "name": "Send Welcome Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1180, 100], "id": "9a44f13e-da8a-448d-ab6f-ed4e81d0be2b", "webhookId": "a0ab5545-d0d5-4700-a9e7-8b547ba1fa69", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "=Welcome back, {{$node[\"Create/Update Member\"].json.data.name}}! \n\nYour loyalty ID is: {{$node[\"Create/Update Member\"].json.data.loyalty_id}}\n\nYour current points balance: {{$node[\"Create/Update Member\"].json.data.available_points || 0}} points", "additionalFields": {}}, "name": "Send Update Message", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [1180, 300], "id": "23c47327-ca98-4f42-891b-783b9f0b220f", "webhookId": "7625a58c-846c-4bbd-b5e0-341c270d8e24", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{$node[\"Telegram Trigger\"].json[\"message\"][\"chat\"][\"id\"]}}", "text": "I couldn't understand your message. To sign up, please send your information in this format:\n\nYour Name, Your Phone Number, Your Email (optional)\n\nExample: <PERSON>, +251912345678, <EMAIL>", "additionalFields": {}}, "name": "Send Format Error", "type": "n8n-nodes-base.telegram", "typeVersion": 1, "position": [360, 400], "id": "0945176a-d892-47b6-8f0c-468db6ab44a0", "webhookId": "cf27fcf0-2dcb-48cd-9bcb-72fbfa434e86", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"method": "POST", "url": "https://8235-213-41-102-186.ngrok-free.app/api/telegram/members", "sendBody": true, "bodyParameters": {"parameters": [{"name": "api_key", "value": "telegram-loyal-integration-key"}, {"name": "company_id", "value": "e31cbdcd-a9f4-482d-ad65-d2be34dde3c6"}, {"name": "name", "value": "={{ $json.name }}"}, {"name": "phone_number", "value": "={{ $json.phone_number }}"}, {"name": "email", "value": "={{ $json.email }}"}, {"name": "telegram_chat_id", "value": "={{ $json.telegram_chat_id }}"}, {"name": "initial_points", "value": "={{ $json.initial_points }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [620, 200], "id": "f37a949d-9d08-49dd-b441-02b4163e5b48", "name": "HTTP Request"}], "pinData": {}, "connections": {"Telegram Trigger": {"main": [[{"node": "Configuration", "type": "main", "index": 0}]]}, "Configuration": {"main": [[{"node": "Signup Command?", "type": "main", "index": 0}]]}, "Signup Command?": {"main": [[{"node": "Send Signup Instructions", "type": "main", "index": 0}], [{"node": "Is Signup Data?", "type": "main", "index": 0}]]}, "Is Signup Data?": {"main": [[{"node": "Parse Member Data", "type": "main", "index": 0}], [{"node": "Send Format Error", "type": "main", "index": 0}]]}, "Parse Member Data": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "New Member?": {"main": [[{"node": "Send Welcome Message", "type": "main", "index": 0}], [{"node": "Send Update Message", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "New Member?", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "0603686f-5ec3-499d-aa4c-b285eff351cc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "bee413acb9414930982d62c120698aa010ed90bd17c4ca0b49cdc96d38963cb7"}, "id": "y4bwnHstz491ptZm", "tags": []}