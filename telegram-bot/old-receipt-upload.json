{"nodes": [{"parameters": {"method": "POST", "url": "=https://vqltspteqqllvhyiupkf.supabase.co/storage/v1/object/fufis/receipts/{{ $json['Loyalty Id'] }}/{{ $('On form submission').item.json.submittedAt }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxbHRzcHRlcXFsbHZoeWl1cGtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjU2MDEwMSwiZXhwIjoyMDU4MTM2MTAxfQ.dLpfmGuitOpLQ3AB_Xlj-d-nwWx_pJOAbte5wT35h0Y"}, {"name": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxbHRzcHRlcXFsbHZoeWl1cGtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjU2MDEwMSwiZXhwIjoyMDU4MTM2MTAxfQ.dLpfmGuitOpLQ3AB_Xlj-d-nwWx_pJOAbte5wT35h0Y"}]}, "sendBody": true, "contentType": "binaryData", "inputDataFieldName": "upload_you_receipt", "options": {"redirect": {"redirect": {"maxRedirects": 10}}, "response": {"response": {"fullResponse": true}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [340, 180], "id": "dd2e2462-6bf8-4238-9c57-656a834349f9", "name": "HTTP Request"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0aa9e8b6-c260-4d41-b324-43a32f82ac6f", "leftValue": "={{ $json.statusCode }}", "rightValue": 200, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [670, 180], "id": "18182b16-ce55-4734-84c7-6e0a9de5bc17", "name": "If"}, {"parameters": {"assignments": {"assignments": [{"id": "a4acad9f-f785-41dc-84f0-c56be822bc95", "name": "=name", "value": "={{ $json['Loyalty Id'] }}", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [120, 180], "id": "8f0758f3-1ef0-4efc-afb3-ace3db61b463", "name": "<PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "0ecdc039-8651-4c94-88b6-9f96a7c940aa", "name": "link", "value": "=https://vqltspteqqllvhyiupkf.supabase.co/storage/v1/object/fufis/receipts/{{ $('Edit Fields').item.json['Loyalty Id'] }}/{{ $('On form submission').item.json.submittedAt }}", "type": "string"}, {"id": "62241bcd-9b3c-4746-955d-f76b854de018", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1000, 180], "id": "ce3a6485-d861-4f4b-adfe-1730b0e04bb3", "name": "Edit Fields1"}, {"parameters": {"promptType": "define", "text": "Extract all information from this Fufi's Beauty Services receipt image.\n\nReturn ONLY a valid JSON object with the following structure - no explanation text, no markdown formatting:\n\n{\n  \"receipt_number\": \"FS No.XXXXXXXX\",\n  \"reference_number\": \"CSI-XXX-XX-XXXXXXX\", \n  \"transaction_date\": \"DD/MM/YYYY\",\n  \"transaction_time\": \"HH:MM\",\n  \"business_name\": \"FUFIS BEAUTY SERVICES P.L.C\",\n  \"business_tin\": \"0076655480\",\n  \"cashier_id\": \"EMPLOYEE XX\",\n  \"services\": [\n    {\n      \"description\": \"Service Name\",\n      \"quantity\": X,\n      \"unit_price\": XXX,\n      \"amount\": XXX\n    }\n  ],\n  \"subtotal\": XXX,\n  \"tax_percentage\": XX,\n  \"tax_amount\": XX.XX,\n  \"total_amount\": XXX.XX,\n  \"payment_method\": \"CASH/CARD\"\n}\n\nFollow this EXACT format, filling in the actual values from the receipt image.\n\n", "hasOutputParser": true, "options": {"systemMessage": "You are a specialized receipt data extraction assistant for <PERSON><PERSON>'s Beauty Services. Your task is to accurately extract structured data from receipt images for a loyalty program database. Focus on precision and consistency when identifying fields like receipt numbers, dates, services, and amounts.\n\nAlways return data in valid JSON format without any explanatory text or markdown formatting. Never include code block delimiters or any content outside the JSON structure itself.\n\nFor <PERSON><PERSON>'s receipts specifically, pay attention to:\n1. The receipt number format (FS No.XXXXXXXX)\n2. Reference numbers (CSI-XXX-XX-XXXXXXX)\n3. Service descriptions and their quantities/prices\n4. Tax calculations (typically 15%)\n5. Total amounts\n\nIf any field is unclear or missing, leave it blank rather than guessing. Maintain the exact structure requested in the output format.\n\nDO NOT MAKE UP THE SERVICES OR ANYTHING IN THE RECEIPT DATA!!!\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.8, "position": [592, 480], "id": "cf59ef0b-9204-4ce6-a77e-e7ba4e88fab9", "name": "AI Agent", "onError": "continueErrorOutput"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"$schema\": \"https://json-schema.org/draft/2020-12/schema\",\n  \"type\": \"object\",\n  \"properties\": {\n    \"receipt_number\": { \"type\": \"string\" },\n    \"reference_number\": { \"type\": \"string\" },\n    \"transaction_date\": { \"type\": \"string\" },\n    \"transaction_time\": { \"type\": \"string\" },\n    \"business_name\": { \"type\": \"string\" },\n    \"business_tin\": { \"type\": \"string\" },\n    \"cashier_id\": { \"type\": \"string\" },\n    \"services\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"description\": { \"type\": \"string\" },\n          \"quantity\": { \"type\": \"number\" },\n          \"unit_price\": { \"type\": \"number\" },\n          \"amount\": { \"type\": \"number\" }\n        },\n        \"required\": [\"description\", \"quantity\", \"unit_price\", \"amount\"]\n      }\n    },\n    \"subtotal\": { \"type\": \"number\" },\n    \"tax_percentage\": { \"type\": \"number\" },\n    \"tax_amount\": { \"type\": \"number\" },\n    \"total_amount\": { \"type\": \"number\" },\n    \"payment_method\": { \"type\": \"string\" }\n  },\n  \"required\": [\n    \"receipt_number\",\n    \"reference_number\",\n    \"transaction_date\",\n    \"transaction_time\",\n    \"business_name\",\n    \"subtotal\",\n    \"total_amount\",\n    \"services\"\n  ]\n}\n\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [800, 700], "id": "99e31696-a8ae-46b6-8749-89e164a8ecc1", "name": "Structured Output Parser"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.toolCalculator", "typeVersion": 1, "position": [680, 700], "id": "206cc47b-175c-4f4c-b135-c4cec65fb140", "name": "Calculator"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-nano", "mode": "list", "cachedResultName": "gpt-4.1-nano"}, "options": {"maxRetries": 5}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [560, 700], "id": "a4d99e64-5052-49b9-bd1b-62f37bb8caa7", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "pFbTVEFwuXH3wP6L", "name": "OpenAi account"}}}, {"parameters": {"operation": "completion", "completionTitle": "Success! 🎉", "completionMessage": "Tap \"Close\" to return to Telegram \n", "limitWaitTime": true, "resumeAmount": 0.1, "resumeUnit": "minutes", "options": {"customCss": ":root {\n\t--font-family: 'Open Sans', sans-serif;\n\t--font-weight-normal: 400;\n\t--font-weight-bold: 600;\n\t--font-size-body: 12px;\n\t--font-size-label: 14px;\n\t--font-size-test-notice: 12px;\n\t--font-size-input: 14px;\n\t--font-size-header: 20px;\n\t--font-size-paragraph: 14px;\n\t--font-size-link: 12px;\n\t--font-size-error: 12px;\n\t--font-size-html-h1: 28px;\n\t--font-size-html-h2: 20px;\n\t--font-size-html-h3: 16px;\n\t--font-size-html-h4: 14px;\n\t--font-size-html-h5: 12px;\n\t--font-size-html-h6: 10px;\n\t--font-size-subheader: 14px;\n\n\t/* Colors */\n\t--color-background: #fbfcfe;\n\t--color-test-notice-text: #e6a23d;\n\t--color-test-notice-bg: #fefaf6;\n\t--color-test-notice-border: #f6dcb7;\n\t--color-card-bg: #ffffff;\n\t--color-card-border: #dbdfe7;\n\t--color-card-shadow: rgba(99, 77, 255, 0.06);\n\t--color-link: #7e8186;\n\t--color-header: #525356;\n\t--color-label: #555555;\n\t--color-input-border: #dbdfe7;\n\t--color-input-text: #71747A;\n\t--color-focus-border: rgb(90, 76, 194);\n\t--color-submit-btn-bg: #ff6d5a;\n\t--color-submit-btn-text: #ffffff;\n\t--color-error: #ea1f30;\n\t--color-required: #ff6d5a;\n\t--color-clear-button-bg: #7e8186;\n\t--color-html-text: #555;\n\t--color-html-link: #ff6d5a;\n\t--color-header-subtext: #7e8186;\n\n\t/* Border Radii */\n\t--border-radius-card: 8px;\n\t--border-radius-input: 6px;\n\t--border-radius-clear-btn: 50%;\n\t--card-border-radius: 8px;\n\n\t/* Spacing */\n\t--padding-container-top: 24px;\n\t--padding-card: 24px;\n\t--padding-test-notice-vertical: 12px;\n\t--padding-test-notice-horizontal: 24px;\n\t--margin-bottom-card: 16px;\n\t--padding-form-input: 12px;\n\t--card-padding: 24px;\n\t--card-margin-bottom: 16px;\n\n\t/* Dimensions */\n\t--container-width: 448px;\n\t--submit-btn-height: 48px;\n\t--checkbox-size: 18px;\n\n\t/* Others */\n\t--box-shadow-card: 0px 4px 16px 0px var(--color-card-shadow);\n\t--opacity-placeholder: 0.5;\n}"}}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [2540, 230], "id": "9de52567-2758-46fb-87a0-c96729378032", "name": "Form", "webhookId": "4638ea13-b53b-405b-844d-438a56c95125", "alwaysOutputData": true, "onError": "continueErrorOutput"}, {"parameters": {"jsCode": "function processReceiptData(items) {\n  try {\n    // Find the link object\n    const linkObj = items.find(item => item.json.link);\n    // Find the receipt data object\n    const dataObj = items.find(item => item.json.ai_agent_output);\n\n    if (!linkObj || !dataObj) {\n      throw new Error(\"Link or receipt data missing in input\");\n    }\n\n    const receiptData = dataObj.json.ai_agent_output;\n    const customerRewardId = dataObj.json.customer_reward_id;\n    const uploaderTelegramId = dataObj.json.receipt_uploader_tid;\n    const receiptImageUrl = linkObj.json.link;\n\n    if (!customerRewardId) {\n      throw new Error(\"Customer reward ID is missing\");\n    }\n\n    if (!receiptData) {\n      throw new Error(\"Receipt data is missing\");\n    }\n\n    // Format date for database (YYYY-MM-DDThh:mm:ss)\n    const dateParts = receiptData.transaction_date.split('/');\n    const formattedDate = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}T${receiptData.transaction_time}:00`;\n\n    // Calculate points (1 point per unit spent)\n    const pointsAwarded = Math.floor(receiptData.total_amount);\n\n    // Format service description\n    const serviceDescription = receiptData.services.map(service => \n      `${service.description} (${service.quantity} x ${service.unit_price})`\n    ).join(', ');\n\n    // Format receipt data for database insertion\n    const receipt = {\n      receipt_number: receiptData.receipt_number,\n      reference_number: receiptData.reference_number,\n      loyalty_id: customerRewardId, // Link to customer by loyalty ID\n      uploader_telegram_id: uploaderTelegramId, // Track who uploaded\n      purchase_date: formattedDate,\n      total_amount: receiptData.total_amount,\n      subtotal: receiptData.subtotal,\n      tax_amount: receiptData.tax_amount,\n      tax_percentage: receiptData.tax_percentage,\n      cashier_id: receiptData.cashier_id,\n      service_description: serviceDescription,\n      points_awarded: pointsAwarded,\n      payment_method: receiptData.payment_method || \"CASH\",\n      receipt_image_url: receiptImageUrl // <-- Add the image URL here\n    };\n\n    // Format points transaction for database insertion\n    const pointsTransaction = {\n      loyalty_id: customerRewardId,\n      receipt_number: receiptData.receipt_number,\n      points_change: pointsAwarded,\n      description: `Earned ${pointsAwarded} points for ${serviceDescription}`\n    };\n\n    return {\n      json: {\n        receipt: receipt,\n        pointsTransaction: pointsTransaction,\n        customerRewardId: customerRewardId,\n        uploaderTelegramId: uploaderTelegramId,\n        success: true\n      }\n    };\n  } catch (error) {\n    console.error(\"Receipt processing error:\", error);\n    return {\n      json: {\n        success: false,\n        error: error.message\n      }\n    };\n  }\n}\n\nreturn processReceiptData(items);\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1440, 330], "id": "f40fc143-6021-4f12-8e93-db34ca0ec6f7", "name": "Code"}, {"parameters": {"tableId": "receipts", "fieldsUi": {"fieldValues": [{"fieldId": "receipt_number", "fieldValue": "={{ $json.receipt.receipt_number }}"}, {"fieldId": "reference_number", "fieldValue": "={{ $json.receipt.reference_number }}"}, {"fieldId": "loyalty_id", "fieldValue": "={{ $json.receipt.loyalty_id }}"}, {"fieldId": "uploader_telegram_id", "fieldValue": "={{ $json.receipt.uploader_telegram_id }}"}, {"fieldId": "purchase_date", "fieldValue": "={{ $json.receipt.purchase_date }}"}, {"fieldId": "total_amount", "fieldValue": "={{ $json.receipt.total_amount }}"}, {"fieldId": "tax_amount", "fieldValue": "={{ $json.receipt.tax_amount }}"}, {"fieldId": "service_description", "fieldValue": "={{ $json.receipt.service_description }}"}, {"fieldId": "points_awarded", "fieldValue": "={{ $json.receipt.points_awarded }}"}, {"fieldId": "payment_method", "fieldValue": "={{ $json.receipt.payment_method }}"}, {"fieldId": "subtotal", "fieldValue": "={{ $json.receipt.subtotal }}"}, {"fieldId": "receipt_image_url", "fieldValue": "={{ $json.receipt.receipt_image_url }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1660, 330], "id": "8fafb242-fc16-4a14-ab42-ed6f8a326e01", "name": "Supabase", "credentials": {"supabaseApi": {"id": "Ujw3uNUXGgYnUe9D", "name": "<PERSON><PERSON>'s"}}, "onError": "continueErrorOutput"}, {"parameters": {"operation": "completion", "completionTitle": "Error 😭", "completionMessage": "Try again please 🙏 ", "options": {}}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [1880, 430], "id": "fa09b23e-13f2-446a-8489-1586900e5645", "name": "Form1", "webhookId": "c5747bbf-ab54-4e29-9bec-c58ea24875d3"}, {"parameters": {"operation": "get", "tableId": "fufi_loyalty_members", "filters": {"conditions": [{"keyName": "loyalty_id", "keyValue": "={{ $json.loyalty_id }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1880, 230], "id": "af25654a-e5b6-49f8-9885-9c76962108a8", "name": "Supabase1", "credentials": {"supabaseApi": {"id": "Ujw3uNUXGgYnUe9D", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"chatId": "={{ $json.telegram_chat_id }}", "text": "=hi  {{ $('Supabase1').item.json.name }}, You got more points. \n\nYour total points: {{ $json.points }} \nYour loyalty Id:{{ $json.loyalty_id }}", "additionalFields": {"appendAttribution": false}}, "type": "n8n-nodes-base.telegram", "typeVersion": 1.2, "position": [2320, 230], "id": "bfc8dbe8-cdf2-4191-9af2-2f1e56e391d4", "name": "Telegram", "webhookId": "37c5cc90-e941-4280-b247-c8b78e63f716", "credentials": {"telegramApi": {"id": "tZ0keZ7jwrO1e0BD", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"operation": "update", "tableId": "fufi_loyalty_members", "filters": {"conditions": [{"keyName": "loyalty_id", "condition": "eq", "keyValue": "={{ $json.loyalty_id }}"}]}, "fieldsUi": {"fieldValues": [{"fieldId": "points", "fieldValue": "={{ Number($json.points || 0) + Number($('Supabase').item.json.points_awarded || 0) }}\n"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2100, 230], "id": "d9a75419-7d9c-4b98-b80d-c124590c5034", "name": "Supabase2", "credentials": {"supabaseApi": {"id": "Ujw3uNUXGgYnUe9D", "name": "<PERSON><PERSON>'s"}}}, {"parameters": {"assignments": {"assignments": [{"id": "13981ed9-eccd-4f59-bae5-c46e0660f6a4", "name": "customer_reward_id", "value": "={{ $('On form submission').item.json['Loyalty Id'] }}", "type": "string"}, {"id": "12b672e1-6212-4de1-a338-226f3a8ed67e", "name": "receipt_uploader_tid", "value": "={{ $('On form submission').item.json['Telegram ID'] }}", "type": "string"}, {"id": "3e7e4c65-1b56-4b94-9e4e-de43c4081eac", "name": "ai_agent_output", "value": "={{ $json.output }}", "type": "object"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1000, 480], "id": "c0fa04bb-772e-49de-ae89-0fe77c515c7e", "name": "Edit Fields2"}, {"parameters": {"formTitle": "Upload receipt", "formDescription": "input details below", "formFields": {"values": [{"fieldLabel": "Loyalty Id"}, {"fieldLabel": "upload you receipt", "fieldType": "file"}, {"fieldLabel": "Telegram ID"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-100, 330], "id": "b448e227-c10d-4228-b6d8-8d0aea59466e", "name": "On form submission", "webhookId": "e11c910b-1cb3-4ef2-97ea-602b13ff1a62"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [2760, 230], "id": "b297da00-eff6-4b36-8362-f72f2971c4ba", "name": "No Operation, do nothing"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1220, 330], "id": "5cb345b1-fbac-4031-a189-afc44e155524", "name": "<PERSON><PERSON>"}], "connections": {"HTTP Request": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent", "type": "ai_outputParser", "index": 0}]]}, "Calculator": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Form": {"main": [[{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "Supabase1", "type": "main", "index": 0}], [{"node": "Form1", "type": "main", "index": 0}]]}, "Supabase1": {"main": [[{"node": "Supabase2", "type": "main", "index": 0}]]}, "Telegram": {"main": [[{"node": "Form", "type": "main", "index": 0}]]}, "Supabase2": {"main": [[{"node": "Telegram", "type": "main", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "On form submission": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}, {"node": "<PERSON>", "type": "main", "index": 0}]]}, "Merge": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "bee413acb9414930982d62c120698aa010ed90bd17c4ca0b49cdc96d38963cb7"}}