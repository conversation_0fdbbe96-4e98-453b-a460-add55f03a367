# Telegram Bot Integration for Loyal

This directory contains n8n workflow definitions for integrating the Loyal loyalty management system with Telegram bots. These workflows enable members to sign up, submit receipts, and redeem rewards directly through Telegram.

## Prerequisites

1. A Telegram bot created through BotFather
2. n8n instance set up and running
3. Loyal API endpoints deployed and accessible

## Setup Instructions

### 1. Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Start a chat and use the `/newbot` command
3. Follow the instructions to create your bot
4. Save the API token provided by BotFather

### 2. Configure n8n

1. Import the workflow JSON files into your n8n instance
2. Since you're using the free version of n8n without environment variables, you have these alternatives:
   
   **Option A: Use Credentials (Recommended)**
   - Install the community node [n8n-nodes-globals](https://github.com/umanamente/n8n-nodes-globals)
   - Add the "Global Constants" node to your workflow
   - Create a credential with your configuration values:
     - `API_BASE_URL`: The base URL of your Loyal API
     - `TELEGRAM_BOT_TOKEN`: The token provided by <PERSON><PERSON><PERSON><PERSON>
   - Reference these values in your workflow using the node's output

   **Option B: Hardcode Values in Function Nodes**
   - Edit each workflow's function nodes to replace references to `$env.API_BASE_URL` with your actual API URL
   - For example, change:
     ```javascript
     url: "={{$env.API_BASE_URL}}/api/members"
     ```
     to:
     ```javascript
     url: "https://your-loyal-app.com/api/members"
     ```

   **Option C: Use a Configuration Node**
   - Add a "Set" node at the beginning of each workflow
   - Define your configuration values as JSON:
     ```json
     {
       "API_BASE_URL": "https://your-loyal-app.com",
       "TELEGRAM_BOT_TOKEN": "your_token_here"
     }
     ```
   - Reference these values using `$node["Configuration"].json.API_BASE_URL`

### 3. Configure Company ID

In each workflow, replace the `{{COMPANY_ID}}` placeholder with your actual company UUID from the Loyal database.

### 4. Set Up Webhook

Configure your Telegram bot to use webhooks by setting the webhook URL to your n8n instance:

```
https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook?url=<YOUR_N8N_WEBHOOK_URL>
```

## Workflows

### 1. Member Signup (`member-signup-workflow.json`)

This workflow allows users to sign up for the loyalty program via Telegram:

- **Trigger**: User sends `/signup` command
- **Flow**:
  1. Bot sends instructions for signup format
  2. User provides name, phone, and optional email
  3. System creates or updates member record
  4. Bot confirms signup and provides loyalty ID

### 2. Receipt Upload (`receipt-upload-workflow.json`)

This workflow enables members to submit receipts and earn points:

- **Trigger**: User sends `/receipt` command
- **Flow**:
  1. Bot sends instructions for receipt submission format
  2. User provides loyalty ID, amount, and optional receipt number
  3. System processes receipt and awards points
  4. Bot confirms points earned and new balance

### 3. Reward Redemption (`reward-redemption-workflow.json`)

This workflow allows members to view and redeem rewards:

- **Trigger**: User sends `/redeem` command
- **Flow**:
  1. Without parameters, bot displays available rewards with codes
  2. With parameters (loyalty ID and reward code), system processes redemption
  3. Bot confirms successful redemption or explains any errors

## Testing the Workflows

1. Start a chat with your Telegram bot
2. Test the `/signup` command and follow the instructions
3. Test the `/receipt` command with a valid loyalty ID
4. Test the `/redeem` command to view and redeem rewards

## Testing with Local Development Environment

When testing with a local development environment, you'll need to make your local server accessible to Telegram's servers. Here's how to set it up:

### Using a Tunneling Service

1. **Install and set up a tunneling service**:
   ```bash
   # Using ngrok (install first if you haven't)
   ngrok http 3000
   ```

2. **Update the Configuration node** in each workflow:
   - In each workflow, find the "Configuration" Code node
   - Uncomment the local development line and replace with your tunnel URL:
   ```javascript
   // For local development with ngrok tunnel
   API_BASE_URL: "https://abc123.ngrok.io",  // Replace with your actual ngrok URL
   
   // Comment out the production URL
   // API_BASE_URL: "https://your-loyal-app.com",
   ```

3. **Start your Next.js development server**:
   ```bash
   npm run dev
   ```

4. **Test the workflows** by sending messages to your Telegram bot

### Important Notes for Local Testing

- The tunnel URL will change each time you restart ngrok (unless you have a paid account)
- Make sure your local server is running before testing
- You may need to update your Telegram bot webhook URL in n8n for each new tunnel URL
- For production, remember to switch back to your deployed URL

## Troubleshooting

- Check n8n execution logs for any errors
- Verify that the Loyal API endpoints are accessible
- Ensure the company ID is correctly set in all workflows
- Confirm that the Telegram webhook is properly configured

### Common Issues

1. **"Referenced node is unexecuted" Error**
   - This happens when trying to access a node's data before it has executed
   - Solution: Make sure your workflow connections are in the correct order
   - Our current implementation avoids this by passing configuration through the workflow data

2. **Webhook Not Receiving Messages**
   - Check that your tunnel is running and accessible
   - Verify that the Telegram webhook is properly set in n8n
   - Try restarting the workflow

3. **API Endpoints Not Found**
   - Ensure your API_BASE_URL is correct and includes the full URL with protocol (https://)
   - Check that your Next.js server is running
   - Verify the API routes are correctly implemented in your Next.js app

## Security Considerations

- The workflows assume that the Telegram chat ID is a reliable identifier for users
- For additional security, consider implementing verification steps for high-value redemptions
- Regularly audit redemption logs for suspicious activity
