# Supabase credentials
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Optional: Supabase service role key (only for server-side operations that need elevated permissions)
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application settings
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-from-botfather
TELEGRAM_BOT_USERNAME=your-bot-username-without-@

# Cypress test user credentials
TEST_USER_EMAIL=your-test-user-email
TEST_USER_PASSWORD=your-test-user-password
