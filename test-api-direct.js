// Test the API endpoint directly with proper authentication
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function testAPIWithAuth() {
  try {
    console.log('Testing API endpoint with authentication...');
    
    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    );
    
    // Sign in as the test user
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'your-password-here' // You'll need to provide the actual password
    });
    
    if (authError) {
      console.error('Auth error:', authError);
      return;
    }
    
    console.log('Signed in successfully:', authData.user.id);
    
    // Get the session token
    const { data: sessionData } = await supabase.auth.getSession();
    const accessToken = sessionData.session?.access_token;
    
    if (!accessToken) {
      console.error('No access token found');
      return;
    }
    
    // Make API call with proper authentication
    const response = await fetch('http://localhost:3000/api/admin-status', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    console.log('API Response Status:', response.status);
    console.log('API Response:', JSON.stringify(result, null, 2));
    
    // Sign out
    await supabase.auth.signOut();
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// For now, let's just test the service role client directly
async function testServiceRoleClient() {
  try {
    console.log('Testing service role client directly...');
    
    const { createClient } = require('@supabase/supabase-js');
    
    // Create service role client
    const serviceSupabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    const testUserId = '0557e2e2-75dd-4b23-a6fb-ad5ac0211b00';
    
    console.log('Checking companies table...');
    const { data: companyData, error: companyError } = await serviceSupabase
      .from('companies')
      .select('id, name, administrator_id')
      .eq('administrator_id', testUserId)
      .maybeSingle();
    
    console.log('Companies result:', { companyData, companyError });
    
    console.log('Checking company_administrators table...');
    const { data: adminData, error: adminError } = await serviceSupabase
      .from('company_administrators')
      .select(`
        company_id, 
        administrator_id, 
        role, 
        created_at,
        companies!inner(id, name)
      `)
      .eq('administrator_id', testUserId)
      .eq('role', 'OWNER')
      .maybeSingle();
    
    console.log('Company_administrators result:', { adminData, adminError });
    
    if (companyData || adminData) {
      console.log('✅ User should have admin access');
    } else {
      console.log('❌ User should NOT have admin access');
    }
    
  } catch (error) {
    console.error('Service role test failed:', error);
  }
}

testServiceRoleClient();
