describe('Members Management', () => {
  beforeEach(() => {
    // Login using environment variables
    cy.login();
    
    // Mock all Supabase API calls to prevent fetch errors
    cy.mockSupabaseApis();
    
    // Mock API responses
    cy.intercept('GET', '/api/members*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: '1',
            name: 'Test User',
            loyalty_id: 'LOY123',
            phone_number: '+251912345678',
            email: '<EMAIL>',
            loyalty_tier: 'SILVER',
            available_points: 500,
            total_points: 1000,
            registration_date: '2025-01-01T00:00:00.000Z',
            telegram_chat_id: '123456789'
          },
          {
            id: '2',
            name: 'Another User',
            loyalty_id: 'LOY456',
            phone_number: '+251987654321',
            email: '<EMAIL>',
            loyalty_tier: 'GOLD',
            available_points: 1500,
            total_points: 2500,
            registration_date: '2025-01-02T00:00:00.000Z',
            telegram_chat_id: '987654321'
          }
        ]
      }
    }).as('getMembers')

    // Visit the members page
    cy.visit('/members')
  })

  it('should display member list', () => {
    // Check that the page loaded properly
    cy.contains('h1', 'Members').should('be.visible')
    
    // Check that members are displayed in the table
    cy.contains('Test User').should('be.visible')
    cy.contains('Another User').should('be.visible')
    cy.contains('LOY123').should('be.visible')
    cy.contains('LOY456').should('be.visible')
  })

  it('should search for members', () => {
    // Filter the member list by typing in the search input
    cy.get('input[placeholder*="Search"]').type('Test')

    // Verify filtered results locally
    cy.contains('Test User').should('be.visible')
    cy.contains('Another User').should('not.exist')
  })

  it('should navigate to member details', () => {
    // Stub the member details response
    cy.intercept('GET', '/api/members/*', {
      statusCode: 200,
      body: {
        id: '1',
        name: 'Test User',
        loyalty_id: 'LOY123',
        phone_number: '+251912345678',
        email: '<EMAIL>',
        loyalty_tier: 'SILVER',
        available_points: 500,
        total_points: 1000,
        registration_date: '2025-01-01T00:00:00.000Z',
        telegram_chat_id: '123456789'
      }
    }).as('getMember')

    // Stub transactions for this member
    cy.intercept('GET', '/api/transactions*member_id=1*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: '1',
            type: 'EARNING',
            points: 500,
            created_at: '2025-01-05T00:00:00.000Z',
            receipt_id: '1'
          },
          {
            id: '2',
            type: 'REDEMPTION',
            points: -100,
            created_at: '2025-01-10T00:00:00.000Z',
            redemption_id: '1'
          }
        ]
      }
    }).as('getMemberTransactions')

    // Click on the member row or view button
    cy.contains('tr', 'Test User').within(() => {
      cy.get('button, a').filter(':contains("View"), :contains("Details"), [aria-label*="view"], [aria-label*="detail"], [title*="view"], [title*="detail"]')
        .first()
        .click();
    })
    
    // Wait for the member details to load
    cy.wait('@getMember')
    cy.wait('@getMemberTransactions')
    
    // Verify member details page
    cy.contains('Member Profile: Test User').should('be.visible')
    cy.contains('Loyalty ID: LOY123').should('be.visible')
    cy.contains('Available Points: 500').should('be.visible')
    
    // Verify transaction history
    cy.contains('Transaction History').should('be.visible')
    cy.contains('Earned 500 points').should('be.visible')
    cy.contains('Redeemed 100 points').should('be.visible')
  })

  it('should create a new member', () => {
    // Stub the create member API
    cy.intercept('POST', '/api/members', {
      statusCode: 201,
      body: {
        id: '3',
        name: 'New Member',
        loyalty_id: 'LOY789',
        phone_number: '+251923456789',
        email: '<EMAIL>',
        loyalty_tier: 'SILVER',
        available_points: 0,
        total_points: 0,
        registration_date: new Date().toISOString(),
        telegram_chat_id: null
      }
    }).as('createMember')

    // Click the "Add Member" button
    cy.get('button, a').filter(':contains("Add"), :contains("Create"), :contains("New")')
      .filter(':contains("Member")')
      .first()
      .click();
    
    // Fill out the form
    cy.get('form').within(() => {
      cy.get('input[name="name"]').type('New Member')
      cy.get('input[name="phone_number"]').type('+251923456789')
      cy.get('input[name="email"]').type('<EMAIL>')
      // Assuming there's a select for tier
      cy.get('select[name="loyalty_tier"]').select('SILVER')
      
      // Submit the form
      cy.contains('button', 'Create').click()
    })
    
    // Wait for the API call
    cy.wait('@createMember')
    
    // Ensure success message is shown
    cy.contains('Member created successfully').should('be.visible')
    
    // Refresh members list
    cy.intercept('GET', '/api/members*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: '1',
            name: 'Test User',
            loyalty_id: 'LOY123',
            phone_number: '+251912345678',
            email: '<EMAIL>',
            loyalty_tier: 'SILVER',
            available_points: 500,
            total_points: 1000,
            registration_date: '2025-01-01T00:00:00.000Z',
            telegram_chat_id: '123456789'
          },
          {
            id: '2',
            name: 'Another User',
            loyalty_id: 'LOY456',
            phone_number: '+251987654321',
            email: '<EMAIL>',
            loyalty_tier: 'GOLD',
            available_points: 1500,
            total_points: 2500,
            registration_date: '2025-01-02T00:00:00.000Z',
            telegram_chat_id: '987654321'
          },
          {
            id: '3',
            name: 'New Member',
            loyalty_id: 'LOY789',
            phone_number: '+251923456789',
            email: '<EMAIL>',
            loyalty_tier: 'SILVER',
            available_points: 0,
            total_points: 0,
            registration_date: new Date().toISOString(),
            telegram_chat_id: null
          }
        ]
      }
    }).as('getUpdatedMembers')
    
    // Check that the new member appears in the list
    cy.contains('New Member').should('be.visible')
    cy.contains('LOY789').should('be.visible')
  })
})
