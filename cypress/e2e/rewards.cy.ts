describe('Rewards Management', () => {
  beforeEach(() => {
    // <PERSON>gin using environment variables
    cy.login();
    
    // Check if login was successful before proceeding
    cy.url().then(url => {
      if (url.includes('login')) {
        cy.log('<PERSON><PERSON> failed, attempting to debug');
        cy.get('body').then($body => {
          if ($body.text().includes('Invalid')) {
            cy.log('Login form shows invalid credentials message');
          }
        });
        // Force navigation to rewards page
        cy.visit('/rewards');
      } else {
        cy.log('Login successful');
      }
    });
    
    // Mock all Supabase API calls to prevent fetch errors
    cy.mockSupabaseApis();
    
    // Stub the authentication
    cy.intercept('POST', '/api/auth/signin', {
      statusCode: 200,
      body: { user: { id: 'admin-id', name: 'Admin User' } }
    }).as('signin')

    cy.intercept('GET', '/api/rewards*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: '1',
            name: 'Discount Reward',
            description: 'Get 10% off your next purchase',
            code: 'AB12',
            points_cost: 100,
            active: true,
            company_id: '1',
            created_at: '2025-01-01T00:00:00.000Z'
          },
          {
            id: '2',
            name: 'Free Service',
            description: 'Get a free service',
            code: 'CD34',
            points_cost: 500,
            active: true,
            company_id: '1',
            created_at: '2025-01-01T00:00:00.000Z'
          }
        ]
      }
    }).as('getRewards')

    // Visit the rewards page
    cy.visit('/rewards')
  })

  it('should display rewards list', () => {
    // Check that the page loaded properly
    cy.get('h1, h2, .page-title, .header-title')
      .filter(':contains("Reward")')
      .should('be.visible', { timeout: 10000 });
      
    // Check for reward elements that might contain our mock data
    cy.get('table tbody tr, .card, .reward-item, .list-item')
      .should('have.length.at.least', 1)
      .first()
      .within(() => {
        // Check for price/points info
        cy.get('*').contains(/points|pts|\d+p/i).should('exist');
      });
  })

  it('should create a new reward with 4-char code', () => {
    // Stub the create reward API
    cy.intercept('POST', '/api/rewards', {
      statusCode: 201,
      body: {
        id: '3',
        name: 'Premium Service',
        description: 'Get a premium service',
        code: 'EF56',
        points_cost: 1000,
        active: true,
        company_id: '1',
        created_at: new Date().toISOString()
      }
    }).as('createReward')

    // Find and click the add reward button with flexible selectors
    cy.get('button, a')
      .filter(':contains("Add"), :contains("Create"), :contains("New")')
      .filter(':contains("Reward")')
      .first()
      .click();
    
    // Fill out the form
    cy.get('form').within(() => {
      cy.get('input[name="name"]').type('Premium Service')
      cy.get('textarea[name="description"]').type('Get a premium service')
      cy.get('input[name="points_cost"]').type('1000')
      // Test the 4-char code generation and validation
      cy.get('input[name="code"]').type('TOOLONG') // Invalid code
      // Should show validation error
      cy.contains('Code must be 4 characters (2 letters followed by 2 numbers)').should('be.visible')
      
      // Clear and enter a valid code
      cy.get('input[name="code"]').clear().type('EF56')
      // Validation error should disappear
      cy.contains('Code must be 4 characters').should('not.exist')
      
      // Submit the form
      cy.contains('button', 'Create').click()
    })
    
    // Wait for the API call
    cy.wait('@createReward')
    
    // Ensure success message is shown
    cy.contains('Reward created successfully').should('be.visible')
    
    // Refresh rewards list
    cy.intercept('GET', '/api/rewards*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: '1',
            name: 'Discount Reward',
            description: 'Get 10% off your next purchase',
            code: 'AB12',
            points_cost: 100,
            active: true,
            company_id: '1',
            created_at: '2025-01-01T00:00:00.000Z'
          },
          {
            id: '2',
            name: 'Free Service',
            description: 'Get a free service',
            code: 'CD34',
            points_cost: 500,
            active: true,
            company_id: '1',
            created_at: '2025-01-01T00:00:00.000Z'
          },
          {
            id: '3',
            name: 'Premium Service',
            description: 'Get a premium service',
            code: 'EF56',
            points_cost: 1000,
            active: true,
            company_id: '1',
            created_at: new Date().toISOString()
          }
        ]
      }
    }).as('getUpdatedRewards')
    
    // Check that the new reward appears in the list
    cy.contains('Premium Service').should('be.visible')
    cy.contains('EF56').should('be.visible')
  })

  it('should handle redemption flow with points validation', () => {
    // Stub the members API to get a member for redemption
    cy.intercept('GET', '/api/members*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: '1',
            name: 'Test User',
            loyalty_id: 'LOY123',
            phone_number: '+251912345678',
            email: '<EMAIL>',
            loyalty_tier: 'SILVER',
            available_points: 500,
            total_points: 1000,
            registration_date: '2025-01-01T00:00:00.000Z',
            telegram_chat_id: '*********'
          }
        ]
      }
    }).as('getMembers')

    // Stub the redemption API
    cy.intercept('POST', '/api/redemptions', {
      statusCode: 201,
      body: {
        redemption: {
          id: '3',
          member_id: '1',
          reward_id: '1',
          points_used: 100,
          company_id: '1',
          created_at: new Date().toISOString()
        },
        new_points_balance: 400
      }
    }).as('createRedemption')

    // Click the "Redeem" button for the first reward
    cy.contains('tr', 'Discount Reward').find('button[aria-label="Redeem"]').click()
    
    // Select a member in the redemption modal
    cy.get('select[name="member_id"]').select('Test User')
    
    // Submit the redemption
    cy.contains('button', 'Confirm Redemption').click()
    
    // Wait for the API call
    cy.wait('@createRedemption')
    
    // Ensure success message is shown
    cy.contains('Reward redeemed successfully').should('be.visible')
    cy.contains('New points balance: 400').should('be.visible')
  })

  it('should prevent redemption if member has insufficient points', () => {
    // Stub the members API with a member who has insufficient points
    cy.intercept('GET', '/api/members*', {
      statusCode: 200,
      body: {
        data: [
          {
            id: '3',
            name: 'New Member',
            loyalty_id: 'LOY789',
            phone_number: '+251923456789',
            email: '<EMAIL>',
            loyalty_tier: 'SILVER',
            available_points: 50, // Not enough for the 100-point reward
            total_points: 50,
            registration_date: '2025-01-03T00:00:00.000Z',
            telegram_chat_id: null
          }
        ]
      }
    }).as('getMembers')

    // Stub the redemption API to return an error
    cy.intercept('POST', '/api/redemptions', {
      statusCode: 400,
      body: {
        message: 'Insufficient points'
      }
    }).as('createRedemption')

    // Click the "Redeem" button for the first reward
    cy.contains('tr', 'Discount Reward').find('button[aria-label="Redeem"]').click()
    
    // Select a member in the redemption modal
    cy.get('select[name="member_id"]').select('New Member')
    
    // Submit the redemption
    cy.contains('button', 'Confirm Redemption').click()
    
    // Wait for the API call
    cy.wait('@createRedemption')
    
    // Ensure error message is shown
    cy.contains('Error: Insufficient points').should('be.visible')
  })
})
