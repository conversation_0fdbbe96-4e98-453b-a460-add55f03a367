// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'
import './auth-commands'

// Set environment variable to indicate we're running in Cypress
// This will be detected by the middleware to bypass auth checks
Cypress.env('CYPRESS', 'true');

// Also set it as a browser environment variable
// to ensure it's available to the application
Cypress.on('window:before:load', (win: Window & { process?: { env?: Record<string, string> } }) => {
  // Ensure process object exists
  win.process = win.process || {};
  win.process.env = win.process.env || {};
  win.process.env.CYPRESS = 'true';
  win.process.env.NEXT_PUBLIC_CYPRESS = 'true';
});

// Suppress uncaught exceptions
Cypress.on('uncaught:exception', (err) => {
  // Returning false prevents Cypress from failing the test
  if (err.message.includes('Failed to fetch') ||
      err.message.includes('Invalid time value') ||
      err.message.includes('AuthInvalidTokenResponseError')) {
    console.log('Ignoring known error:', err.message);
    return false;
  }
  // Let other errors fail the test
  return true;
});