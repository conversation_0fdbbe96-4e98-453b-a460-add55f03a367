/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Completely rewritten login command for better reliability
Cypress.Commands.add('login', () => {
  cy.log('Setting up login');
  
  // Mock API calls that might fail
  cy.intercept('**/rest/v1/tiers**', { statusCode: 200, body: [] }).as('tiers');
  cy.intercept('**/rest/v1/company**', { statusCode: 200, body: [] }).as('company');
  cy.intercept('**/rest/v1/**', { statusCode: 200, body: [] }).as('supabaseGeneric');
  
  // Visit login page
  cy.visit('/login', { failOnStatusCode: false });
  
  // Fill out the login form
  cy.get('input[type="email"], input[name="email"], input[placeholder*="mail"]')
    .first()
    .should('be.visible')
    .clear()
    .type(Cypress.env('TEST_USER_EMAIL') || '<EMAIL>');
  
  cy.get('input[type="password"], input[name="password"], input[placeholder*="password"]')
    .first()
    .should('be.visible')
    .clear()
    .type(Cypress.env('TEST_USER_PASSWORD') || 'password123');
  
  // Submit the form
  cy.get('form').submit();
  
  // Wait for redirect
  cy.wait(2000);
  
  // Visit dashboard directly (since middleware redirect is disabled)
  cy.visit('/dashboard', { failOnStatusCode: false });
  
  // Verify we're on the dashboard page
  cy.url().should('include', '/dashboard');
});

// Add a new command to mock some Supabase API calls
Cypress.Commands.add('mockSupabaseApis', () => {
  // Mock tiers endpoint
  cy.intercept('**/rest/v1/tiers**', {
    statusCode: 200,
    body: [
      { id: 1, name: 'Bronze', points_required: 0 },
      { id: 2, name: 'Silver', points_required: 100 },
      { id: 3, name: 'Gold', points_required: 500 }
    ]
  }).as('tiers');
  
  // Mock company endpoint
  cy.intercept('**/rest/v1/company**', {
    statusCode: 200,
    body: [
      { id: 1, name: 'Test Company', logo_url: null }
    ]
  }).as('company');
  
  // Mock any other Supabase REST API calls
  cy.intercept('**/rest/v1/**', (req) => {
    // Only intercept if not already handled
    if (!req.alias) {
      req.reply({
        statusCode: 200,
        body: []
      });
    }
  }).as('supabaseGeneric');
});

declare global {
  namespace Cypress {
    interface Chainable {
      login(): Chainable<void>
      mockSupabaseApis(): Chainable<void>
    }
  }
}

export {};