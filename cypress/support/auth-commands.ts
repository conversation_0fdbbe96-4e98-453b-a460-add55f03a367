/// <reference types="cypress" />

/**
 * Enhanced Cypress commands for authentication testing
 * This file provides commands that work both with and without auth enabled
 */

// Enhanced login command that works with real auth
Cypress.Commands.add('loginReal', (email?: string, password?: string) => {
  const testEmail = email || Cypress.env('TEST_USER_EMAIL') || '<EMAIL>';
  const testPassword = password || Cypress.env('TEST_USER_PASSWORD') || 'password123';

  cy.log(`Attempting real login with email: ${testEmail}`);

  // Clear any existing session
  cy.clearCookies();
  cy.clearLocalStorage();

  // Visit login page
  cy.visit('/login', { failOnStatusCode: false });

  // Wait for page to load
  cy.get('body').should('be.visible');

  // Fill login form
  cy.get('input[type="email"], input[name="email"]')
    .should('be.visible')
    .clear()
    .type(testEmail);

  cy.get('input[type="password"], input[name="password"]')
    .should('be.visible')
    .clear()
    .type(testPassword);

  // Submit form and wait for redirect
  cy.get('form').submit();

  // Wait for authentication to complete
  cy.wait(2000);

  // Should either be on dashboard or redirected there
  cy.url().should('satisfy', (url) => {
    return url.includes('/dashboard') || url.includes('/login');
  });

  // If still on login, there might be an error
  cy.get('body').then($body => {
    if ($body.find('[role="alert"], .error, .alert-error').length > 0) {
      cy.log('Login error detected');
      cy.get('[role="alert"], .error, .alert-error').then($error => {
        cy.log(`Login error: ${$error.text()}`);
      });
    }
  });
});

// Command to check auth status
Cypress.Commands.add('checkAuthStatus', () => {
  cy.window().then((win) => {
    return cy.wrap(win.localStorage.getItem('supabase.auth.token'));
  });
});

// Enhanced mock command that handles both scenarios
Cypress.Commands.add('mockSupabaseApisAdvanced', () => {
  // Mock tiers endpoint with more realistic data
  cy.intercept('**/rest/v1/tiers**', {
    statusCode: 200,
    body: [
      {
        id: 1,
        name: 'Bronze',
        points_required: 0,
        created_at: '2023-01-01T00:00:00Z',
        benefits: ['Basic rewards'],
        color: '#CD7F32'
      },
      {
        id: 2,
        name: 'Silver',
        points_required: 100,
        created_at: '2023-01-01T00:00:00Z',
        benefits: ['Better rewards', 'Priority support'],
        color: '#C0C0C0'
      },
      {
        id: 3,
        name: 'Gold',
        points_required: 500,
        created_at: '2023-01-01T00:00:00Z',
        benefits: ['Premium rewards', 'VIP treatment', 'Exclusive offers'],
        color: '#FFD700'
      }
    ]
  }).as('tiers');

  // Mock company endpoint
  cy.intercept('**/rest/v1/company**', {
    statusCode: 200,
    body: [
      {
        id: 1,
        name: 'Test Company',
        logo_url: null,
        created_at: '2023-01-01T00:00:00Z',
        settings: {}
      }
    ]
  }).as('company');

  // Mock members endpoint
  cy.intercept('**/rest/v1/members**', {
    statusCode: 200,
    body: [
      {
        id: 1,
        name: 'Test Member',
        email: '<EMAIL>',
        phone: '+1234567890',
        points: 150,
        tier_id: 2,
        created_at: '2023-01-01T00:00:00Z'
      }
    ]
  }).as('members');

  // Mock rewards endpoint
  cy.intercept('**/rest/v1/rewards**', {
    statusCode: 200,
    body: [
      {
        id: 1,
        name: 'Free Coffee',
        points_cost: 50,
        description: 'Enjoy a free coffee on us',
        is_active: true,
        created_at: '2023-01-01T00:00:00Z'
      }
    ]
  }).as('rewards');

  // Mock auth endpoints
  cy.intercept('**/auth/v1/token**', {
    statusCode: 200,
    body: {
      access_token: 'mock-token',
      token_type: 'bearer',
      expires_in: 3600,
      refresh_token: 'mock-refresh-token'
    }
  }).as('authToken');

  // Generic fallback for other Supabase calls
  cy.intercept('**/rest/v1/**', (req) => {
    if (!req.alias) {
      req.reply({
        statusCode: 200,
        body: []
      });
    }
  }).as('supabaseGeneric');
});

// Command to test if auth is enabled
Cypress.Commands.add('isAuthEnabled', () => {
  return cy.request({
    url: '/dashboard',
    followRedirect: false,
    failOnStatusCode: false
  }).then((response) => {
    // If we get a redirect, auth is likely enabled
    return response.status === 302 || response.status === 307;
  });
});

// Conditional login that adapts to auth status
Cypress.Commands.add('smartLogin', () => {
  cy.isAuthEnabled().then((authEnabled) => {
    if (authEnabled) {
      cy.log('Auth is enabled, using real login');
      cy.loginReal();
    } else {
      cy.log('Auth is disabled, using mock login');
      cy.mockSupabaseApisAdvanced();
      cy.visit('/dashboard', { failOnStatusCode: false });
    }
  });
});

// Command to create test user (for setup)
Cypress.Commands.add('createTestUser', (email: string, password: string) => {
  cy.request({
    method: 'POST',
    url: `${Cypress.env('SUPABASE_URL')}/auth/v1/signup`,
    headers: {
      'apikey': Cypress.env('SUPABASE_ANON_KEY'),
      'Content-Type': 'application/json'
    },
    body: {
      email,
      password,
      email_confirm: true
    },
    failOnStatusCode: false
  }).then((response) => {
    if (response.status === 200 || response.status === 201) {
      cy.log(`Test user created: ${email}`);
    } else {
      cy.log(`User creation failed or user already exists: ${email}`);
    }
  });
});

// Command to wait for auth initialization
Cypress.Commands.add('waitForAuth', (timeout = 5000) => {
  cy.window({ timeout }).should((win) => {
    // Wait for Supabase client to be initialized
    expect(win).to.have.property('localStorage');
  });
});

declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Cypress {
    interface Chainable {
      login(): Chainable<void>
      loginReal(email?: string, password?: string): Chainable<void>
      mockSupabaseApis(): Chainable<void>
      mockSupabaseApisAdvanced(): Chainable<void>
      checkAuthStatus(): Chainable<string | null>
      isAuthEnabled(): Chainable<boolean>
      smartLogin(): Chainable<void>
      createTestUser(email: string, password: string): Chainable<void>
      waitForAuth(timeout?: number): Chainable<void>
    }
  }
}

export {};
