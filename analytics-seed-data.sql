-- Comprehensive seed data for the loyal app database
-- This will populate the database with realistic test data to demonstrate analytics functionality

-- Insert test companies (if not already exists)
INSERT INTO companies (id, company_name, company_description, industry_type, created_at)
VALUES
  ('7c4b5389-b630-4d2b-b9b1-9f6460117371', 'FUFIS Beauty Services P.L.C', 'Premium beauty and wellness services', 'Beauty & Wellness', NOW() - INTERVAL '6 months')
ON CONFLICT (id) DO NOTHING;

-- Insert additional business items with realistic data
INSERT INTO business_items (id, company_id, item_name, item_code, standard_price, item_category, item_subcategory, description, is_active, total_sales_count, total_revenue, avg_selling_price, last_sold_date, ai_recognition_patterns, common_variations, created_at, updated_at)
VALUES
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Full Manicure', 'MAN001', 450.00, 'Nail Care', 'Manicure', 'Complete nail care with polish', true, 35, 15750.00, 450.00, NOW() - INTERVAL '2 days', ARRAY['manicure', 'nail care', 'polish'], ARRAY['full mani', 'manicure service', 'nail polish'], NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Hair Cut & Style', 'HAI001', 800.00, 'Hair Care', 'Styling', 'Professional haircut with styling', true, 42, 33600.00, 800.00, NOW() - INTERVAL '1 day', ARRAY['haircut', 'styling', 'hair'], ARRAY['hair cut', 'style', 'haircut styling'], NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Facial Treatment', 'FAC001', 1200.00, 'Skin Care', 'Facial', 'Deep cleansing facial treatment', true, 28, 33600.00, 1200.00, NOW() - INTERVAL '3 days', ARRAY['facial', 'skin care', 'treatment'], ARRAY['face treatment', 'facial care', 'skin facial'], NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Pedicure Deluxe', 'PED001', 650.00, 'Nail Care', 'Pedicure', 'Luxury pedicure with massage', true, 31, 20150.00, 650.00, NOW() - INTERVAL '1 day', ARRAY['pedicure', 'foot care', 'deluxe'], ARRAY['foot treatment', 'luxury pedicure', 'deluxe pedi'], NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Hair Coloring', 'COL001', 1500.00, 'Hair Care', 'Coloring', 'Professional hair coloring service', true, 18, 27000.00, 1500.00, NOW() - INTERVAL '5 days', ARRAY['hair color', 'coloring', 'dye'], ARRAY['hair dye', 'color treatment', 'hair painting'], NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Eyebrow Threading', 'EYE001', 300.00, 'Beauty Service', 'Eyebrows', 'Precision eyebrow shaping', true, 55, 16500.00, 300.00, NOW() - INTERVAL '1 day', ARRAY['eyebrow', 'threading', 'shaping'], ARRAY['brow threading', 'eyebrow shape', 'brow shaping'], NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Deep Conditioning', 'TRT001', 600.00, 'Hair Care', 'Treatment', 'Intensive hair conditioning treatment', true, 22, 13200.00, 600.00, NOW() - INTERVAL '4 days', ARRAY['conditioning', 'hair treatment', 'deep'], ARRAY['hair mask', 'conditioning treatment', 'deep condition'], NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Massage Therapy', 'MAS001', 1000.00, 'Wellness', 'Massage', 'Relaxing full body massage', true, 25, 25000.00, 1000.00, NOW() - INTERVAL '2 days', ARRAY['massage', 'therapy', 'relaxation'], ARRAY['body massage', 'massage therapy', 'relaxing massage'], NOW() - INTERVAL '3 months', NOW());

-- Insert test loyalty members with realistic data
INSERT INTO loyalty_members (id, company_id, loyalty_id, full_name, email, phone_number, date_of_birth, registration_date, total_points, current_tier, lifetime_points_earned, total_visits, total_spent, last_visit_date, referral_source, preferences, created_at, updated_at)
VALUES
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'LOYAL001', 'Amara Johnson', '<EMAIL>', '+************', '1990-05-15', NOW() - INTERVAL '5 months', 2450, 'gold', 3200, 15, 8750.00, NOW() - INTERVAL '3 days', 'social_media', '{"preferred_services": ["Hair Care", "Nail Care"], "communication_preference": "email"}', NOW() - INTERVAL '5 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'LOYAL002', 'Bethlehem Tadesse', '<EMAIL>', '+************', '1985-11-22', NOW() - INTERVAL '4 months', 1890, 'silver', 2890, 12, 6340.00, NOW() - INTERVAL '1 day', 'referral', '{"preferred_services": ["Skin Care", "Wellness"], "communication_preference": "sms"}', NOW() - INTERVAL '4 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'LOYAL003', 'Hanan Mohammed', '<EMAIL>', '+************', '1992-08-10', NOW() - INTERVAL '6 months', 3150, 'platinum', 4500, 20, 12450.00, NOW() - INTERVAL '2 days', 'walk_in', '{"preferred_services": ["Hair Care", "Beauty Service"], "communication_preference": "email"}', NOW() - INTERVAL '6 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'LOYAL004', 'Meron Assefa', '<EMAIL>', '+************', '1988-03-18', NOW() - INTERVAL '3 months', 1560, 'silver', 2100, 9, 4890.00, NOW() - INTERVAL '5 days', 'google', '{"preferred_services": ["Nail Care", "Beauty Service"], "communication_preference": "sms"}', NOW() - INTERVAL '3 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'LOYAL005', 'Sara Ahmed', '<EMAIL>', '+************', '1995-12-05', NOW() - INTERVAL '2 months', 980, 'bronze', 1250, 6, 3210.00, NOW() - INTERVAL '1 week', 'social_media', '{"preferred_services": ["Hair Care", "Skin Care"], "communication_preference": "email"}', NOW() - INTERVAL '2 months', NOW());

-- Insert receipt templates with more realistic data
INSERT INTO receipt_templates (id, company_id, template_name, template_image_url, template_metadata, ai_prompt_context, validation_rules, confidence_threshold, is_active, total_extractions, successful_extractions, avg_confidence_score, created_at, updated_at)
VALUES
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'FUFIS Standard Receipt v2.1', 'https://example.com/template1.jpg', '{"version": "2.1", "type": "service_receipt", "layout": "standard"}', 'Extract service items, prices, and customer information from FUFIS Beauty Services receipts. Look for service names, individual prices, total amount, customer ID, and date.', '{"required_fields": ["total_amount", "service_items"], "optional_fields": ["customer_id", "date"]}', 0.85, true, 45, 41, 0.91, NOW() - INTERVAL '2 months', NOW()),
  (gen_random_uuid(), '7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Mobile Payment Receipt', 'https://example.com/template2.jpg', '{"version": "1.0", "type": "mobile_receipt", "layout": "compact"}', 'Process mobile payment receipts for beauty services. Focus on transaction details, service codes, and payment confirmation.', '{"required_fields": ["total_amount", "transaction_id"], "optional_fields": ["service_description"]}', 0.80, true, 12, 10, 0.83, NOW() - INTERVAL '1 month', NOW());

-- Insert receipts with realistic service data
DO $$
DECLARE
    member_ids uuid[] := ARRAY(SELECT id FROM loyalty_members WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371' LIMIT 5);
    template_ids uuid[] := ARRAY(SELECT id FROM receipt_templates WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371' LIMIT 2);
    service_descriptions text[] := ARRAY['Hair Cut & Style', 'Full Manicure', 'Facial Treatment', 'Pedicure Deluxe', 'Hair Coloring', 'Eyebrow Threading', 'Deep Conditioning', 'Massage Therapy'];
    amounts numeric[] := ARRAY[800.00, 450.00, 1200.00, 650.00, 1500.00, 300.00, 600.00, 1000.00];
    i int;
    receipt_id uuid;
    member_id uuid;
    template_id uuid;
    service_desc text;
    amount numeric;
    confidence_score numeric;
BEGIN
    FOR i IN 1..25 LOOP
        receipt_id := gen_random_uuid();
        member_id := member_ids[1 + (i % array_length(member_ids, 1))];
        template_id := CASE WHEN i % 3 = 0 THEN template_ids[1 + (i % array_length(template_ids, 1))] ELSE NULL END;
        service_desc := service_descriptions[1 + (i % array_length(service_descriptions, 1))];
        amount := amounts[1 + (i % array_length(amounts, 1))] + (random() * 200 - 100); -- Add some variation
        confidence_score := CASE WHEN template_id IS NOT NULL THEN 0.85 + (random() * 0.15) ELSE 0.65 + (random() * 0.25) END;

        INSERT INTO receipts (
            id, company_id, member_id, loyalty_id, receipt_url, receipt_text_content,
            total_amount, currency, service_description, receipt_date, extraction_confidence,
            template_id, processing_status, ai_extracted_data, manual_verification_needed,
            points_awarded, created_at, updated_at
        ) VALUES (
            receipt_id,
            '7c4b5389-b630-4d2b-b9b1-9f6460117371',
            member_id,
            (SELECT loyalty_id FROM loyalty_members WHERE id = member_id),
            'https://example.com/receipts/' || receipt_id || '.jpg',
            'Receipt for ' || service_desc || ' - Amount: ' || amount || ' ETB',
            amount,
            'ETB',
            service_desc,
            NOW() - (format('%s days', floor(random() * 90))::text)::interval,
            confidence_score,
            template_id,
            'completed',
            format('{"service": "%s", "amount": %s, "confidence": %s}', service_desc, amount, confidence_score)::jsonb,
            CASE WHEN confidence_score < 0.8 THEN true ELSE false END,
            floor(amount / 10), -- 1 point per 10 ETB
            NOW() - (format('%s days', floor(random() * 90))::text)::interval,
            NOW()
        );

        -- Insert receipt items for some receipts
        IF i % 2 = 0 THEN
            INSERT INTO receipt_items (
                id, receipt_id, item_name, quantity, unit_price, total_price,
                item_category, extracted_confidence, business_item_match_id, created_at
            ) VALUES (
                gen_random_uuid(),
                receipt_id,
                service_desc,
                1,
                amount,
                amount,
                CASE
                    WHEN service_desc LIKE '%Hair%' THEN 'Hair Care'
                    WHEN service_desc LIKE '%Manicure%' OR service_desc LIKE '%Pedicure%' THEN 'Nail Care'
                    WHEN service_desc LIKE '%Facial%' THEN 'Skin Care'
                    WHEN service_desc LIKE '%Massage%' THEN 'Wellness'
                    ELSE 'Beauty Service'
                END,
                confidence_score,
                (SELECT id FROM business_items WHERE item_name = service_desc AND company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371' LIMIT 1),
                NOW() - (format('%s days', floor(random() * 90))::text)::interval
            );
        END IF;
    END LOOP;
END $$;

-- Insert points transactions for the receipts
INSERT INTO points_transactions (id, member_id, company_id, transaction_type, points_amount, description, receipt_id, expiry_date, created_at)
SELECT
    gen_random_uuid(),
    member_id,
    company_id,
    'earned',
    points_awarded,
    'Points earned from purchase: ' || service_description,
    id,
    created_at + INTERVAL '1 year',
    created_at
FROM receipts
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'
AND points_awarded > 0;

-- Update template extraction statistics
UPDATE receipt_templates SET
    total_extractions = (
        SELECT COUNT(*) FROM receipts
        WHERE template_id = receipt_templates.id
    ),
    successful_extractions = (
        SELECT COUNT(*) FROM receipts
        WHERE template_id = receipt_templates.id
        AND extraction_confidence > confidence_threshold
    ),
    avg_confidence_score = (
        SELECT COALESCE(AVG(extraction_confidence), 0) FROM receipts
        WHERE template_id = receipt_templates.id
    )
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';

-- Update business items sales statistics
UPDATE business_items SET
    total_sales_count = COALESCE((
        SELECT SUM(quantity) FROM receipt_items
        WHERE business_item_match_id = business_items.id
    ), 0),
    total_revenue = COALESCE((
        SELECT SUM(total_price) FROM receipt_items
        WHERE business_item_match_id = business_items.id
    ), 0),
    avg_selling_price = COALESCE((
        SELECT AVG(unit_price) FROM receipt_items
        WHERE business_item_match_id = business_items.id
    ), standard_price),
    last_sold_date = (
        SELECT MAX(created_at) FROM receipt_items
        WHERE business_item_match_id = business_items.id
    )
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';

-- Update loyalty member statistics
UPDATE loyalty_members SET
    total_visits = (
        SELECT COUNT(*) FROM receipts
        WHERE member_id = loyalty_members.id
    ),
    total_spent = (
        SELECT COALESCE(SUM(total_amount), 0) FROM receipts
        WHERE member_id = loyalty_members.id
    ),
    last_visit_date = (
        SELECT MAX(receipt_date) FROM receipts
        WHERE member_id = loyalty_members.id
    ),
    total_points = (
        SELECT COALESCE(SUM(CASE WHEN transaction_type = 'earned' THEN points_amount ELSE -points_amount END), 0)
        FROM points_transactions
        WHERE member_id = loyalty_members.id
    )
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';

-- Insert some reward redemptions for testing
INSERT INTO reward_redemptions (id, member_id, company_id, points_used, redemption_type, description, status, redeemed_at, created_at)
SELECT
    gen_random_uuid(),
    id,
    company_id,
    500,
    'service_discount',
    '10% discount on next service',
    'completed',
    NOW() - (format('%s days', floor(random() * 30))::text)::interval,
    NOW() - (format('%s days', floor(random() * 30))::text)::interval
FROM loyalty_members
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'
AND total_points >= 500
LIMIT 3;

-- Create some audit log entries
INSERT INTO audit_log (id, company_id, user_id, action, resource_type, resource_id, old_values, new_values, ip_address, user_agent, created_at)
SELECT
    gen_random_uuid(),
    '7c4b5389-b630-4d2b-b9b1-9f6460117371',
    (SELECT id FROM users WHERE email LIKE '%admin%' LIMIT 1),
    'CREATE',
    'receipt',
    id,
    '{}',
    format('{"service": "%s", "amount": %s}', service_description, total_amount)::jsonb,
    '*************',
    'Mozilla/5.0 (Analytics Seed)',
    created_at
FROM receipts
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'
LIMIT 10;

-- Final verification queries to check data integrity
SELECT
    'Business Items' as table_name,
    COUNT(*) as record_count,
    SUM(total_revenue) as total_revenue,
    SUM(total_sales_count) as total_sales
FROM business_items
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'

UNION ALL

SELECT
    'Receipts',
    COUNT(*),
    SUM(total_amount),
    NULL
FROM receipts
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'

UNION ALL

SELECT
    'Loyalty Members',
    COUNT(*),
    SUM(total_spent),
    SUM(total_visits)
FROM loyalty_members
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371'

UNION ALL

SELECT
    'Receipt Templates',
    COUNT(*),
    AVG(avg_confidence_score),
    SUM(total_extractions)
FROM receipt_templates
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';
