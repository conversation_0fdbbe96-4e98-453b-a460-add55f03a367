import { defineConfig } from "cypress";
import dotenv from 'dotenv'

dotenv.config({ path: '.env.local' })

export default defineConfig({
  env: {
    TEST_USER_EMAIL: process.env.TEST_USER_EMAIL,
    TEST_USER_PASSWORD: process.env.TEST_USER_PASSWORD,
  },
  e2e: {
    baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    setupNodeEvents(/* on, config */) {
      // implement node event listeners here
    },
  },
});
