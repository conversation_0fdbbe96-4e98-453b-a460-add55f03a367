-- Permanent solution: Create a SECURITY DEFINER function that bypasses RLS
-- This function runs with the privileges of the function owner (postgres/service_role)
-- and bypasses all RLS policies

CREATE OR REPLACE FUNCTION public.check_user_admin_status_secure(user_id UUID)
RETURNS TABLE (
  is_admin BOOLEAN,
  admin_type TEXT,
  company_id UUID,
  company_name TEXT,
  role_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- This function runs with SECURITY DEFINER, so it bypasses RLS policies
  -- Check if user is company owner (administrator_id in companies table)
  RETURN QUERY
  SELECT 
    true as is_admin,
    'owner'::TEXT as admin_type,
    c.id as company_id,
    c.name as company_name,
    'OWNER'::TEXT as role_name
  FROM companies c
  WHERE c.administrator_id = user_id
  LIMIT 1;
  
  -- If no results from companies table, check company_administrators table
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      true as is_admin,
      'administrator'::TEXT as admin_type,
      ca.company_id,
      c.name as company_name,
      ca.role::TEXT as role_name
    FROM company_administrators ca
    JOIN companies c ON c.id = ca.company_id
    WHERE ca.administrator_id = user_id 
      AND ca.role = 'OWNER'
    LIMIT 1;
  END IF;
  
  -- If still no results, user is not an admin
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      false as is_admin,
      null::TEXT as admin_type,
      null::UUID as company_id,
      null::TEXT as company_name,
      null::TEXT as role_name
    LIMIT 1;
  END IF;
  
  RETURN;
END;
$$;

-- Grant execution permissions to all relevant roles
GRANT EXECUTE ON FUNCTION public.check_user_admin_status_secure(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.check_user_admin_status_secure(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION public.check_user_admin_status_secure(UUID) TO anon;

-- Test the function with your user ID
SELECT * FROM public.check_user_admin_status_secure('0557e2e2-75dd-4b23-a6fb-ad5ac0211b00'::UUID);
