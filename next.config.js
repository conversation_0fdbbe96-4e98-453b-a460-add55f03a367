/* eslint-disable */
/** @type {import('next').NextConfig} */

// Next.js requires CommonJS module format for the config file
const withBundleAnalyzer = process.env.ANALYZE === 'true'
  ? require('@next/bundle-analyzer')({
      enabled: true,
    })
  : (config) => config;

const nextConfig = {
  // Enable React strict mode for better development experience
  reactStrictMode: true,

  // Image optimization configuration
  images: {
    domains: ['images.unsplash.com', 'placehold.co', 'picsum.photos', 'vqltspteqqllvhyiupkf.supabase.co'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 3600, // Match Supabase's cache-control max-age (1 hour)
  },

  // Temporarily disable ESLint during builds to allow CI/CD to succeed
  eslint: {
    // This allows production builds to successfully complete even with ESLint errors
    ignoreDuringBuilds: true,
  },

  // Configure compiler options for better performance
  compiler: {
    // Remove console.log in production
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // Configure turbopack for faster development builds
  turbopack: {
    rules: {
      '*.svg': ['@svgr/webpack'],
    }
  },

  // External packages for server components
  serverExternalPackages: [],

  // Webpack configuration for improved performance
  webpack: (config, { isServer }) => {
    // Add optimization for SVG files
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    // Optimize packages that should be transpiled
    if (!isServer) {
      config.resolve.alias = {
        ...config.resolve.alias,
        // Add performance-sensitive libraries here
      };
    }

    return config;
  },
};

// Export the config with bundle analyzer if enabled
module.exports = withBundleAnalyzer(nextConfig);
