-- Test script to validate birthday month-day and telegram notification implementations

-- Test 1: Check if birthday_month_day column exists and is populated correctly
SELECT
    'Birthday Month-Day Column Test' as test_name,
    COUNT(*) as total_members,
    COUNT(birthday_month_day) as members_with_month_day,
    COUNT(birthday) as members_with_full_birthday
FROM loyalty_members;

-- Test 2: Sample of birthday conversions
SELECT
    name,
    birthday,
    birthday_month_day,
    TO_CHAR(birthday, 'MM-DD') as expected_format
FROM loyalty_members
WHERE birthday IS NOT NULL
LIMIT 5;

-- Test 3: Test birthday eligibility function
SELECT
    'Birthday Eligibility Test' as test_name,
    COUNT(*) as eligible_members
FROM loyalty_members m
WHERE is_member_birthday_eligible(m.id) = TRUE;

-- Test 4: Check telegram_notifications table structure
SELECT
    'Telegram Notifications Table Test' as test_name,
    COUNT(*) as total_notifications
FROM telegram_notifications;

-- Test 5: Check if telegram notification trigger exists
SELECT
    'Telegram Trigger Test' as test_name,
    trigger_name,
    event_manipulation,
    action_statement
FROM information_schema.triggers
WHERE trigger_name = 'tr_telegram_notification_for_points';

-- Test 6: Check notification function exists
SELECT
    'Notification Function Test' as test_name,
    routine_name,
    routine_type
FROM information_schema.routines
WHERE routine_name = 'send_telegram_notification_for_transaction';

-- Test 7: Members with telegram chat IDs (eligible for notifications)
SELECT
    'Members with Telegram' as test_name,
    COUNT(*) as members_with_telegram,
    COUNT(CASE WHEN (notification_preferences->>'points_earned')::boolean = true THEN 1 END) as members_with_notifications_enabled
FROM loyalty_members
WHERE telegram_chat_id IS NOT NULL;
