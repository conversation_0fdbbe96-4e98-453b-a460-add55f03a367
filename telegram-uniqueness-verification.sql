-- Telegram Chat ID Uniqueness Enforcement Script
-- This script ensures that one Telegram account can only be connected to one member

-- 1. Check current constraint status
SELECT
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
    ON tc.constraint_name = kcu.constraint_name
WHERE tc.table_name = 'loyalty_members'
AND kcu.column_name = 'telegram_chat_id';

-- 2. Check for any duplicate telegram_chat_id values
SELECT
    telegram_chat_id,
    COUNT(*) as count,
    STRING_AGG(loyalty_id, ', ') as member_ids,
    STRING_AGG(name, ', ') as member_names
FROM loyalty_members
WHERE telegram_chat_id IS NOT NULL
GROUP BY telegram_chat_id
HAVING COUNT(*) > 1;

-- 3. Create unique constraint if it doesn't exist (should already exist)
-- Note: This will fail if constraint already exists, which is expected
DO $$
BEGIN
    BEGIN
        ALTER TABLE loyalty_members
        ADD CONSTRAINT unique_telegram_chat_id
        UNIQUE (telegram_chat_id);

        RAISE NOTICE 'Added unique constraint on telegram_chat_id';
    EXCEPTION
        WHEN duplicate_table THEN
            RAISE NOTICE 'Unique constraint on telegram_chat_id already exists';
    END;
END $$;

-- 4. Verify the constraint is in place
SELECT
    conname as constraint_name,
    contype as constraint_type
FROM pg_constraint
WHERE conrelid = 'loyalty_members'::regclass
AND conname = 'unique_telegram_chat_id';

-- 5. Current Telegram connections summary
SELECT
    COUNT(*) as total_telegram_connections,
    COUNT(DISTINCT telegram_chat_id) as unique_chat_ids,
    COUNT(DISTINCT telegram_username) as unique_usernames
FROM loyalty_members
WHERE telegram_chat_id IS NOT NULL;

-- 6. List all current Telegram connections
SELECT
    loyalty_id,
    name,
    telegram_chat_id,
    telegram_username,
    linked_at,
    CASE
        WHEN linked_at IS NOT NULL THEN 'Linked'
        WHEN linking_token IS NOT NULL THEN 'Pending'
        ELSE 'Not Connected'
    END as connection_status
FROM loyalty_members
WHERE telegram_chat_id IS NOT NULL
   OR linking_token IS NOT NULL
ORDER BY linked_at DESC NULLS LAST;
