-- COMPREHENSIVE FIX FOR MEMBER F1513243 AND SYSTEM-WIDE ISSUES
-- This script addresses:
-- 1. Failed Telegram unlinking for F1513243
-- 2. Point balance discrepancy (redeemed_points not updated)
-- 3. Root cause: Missing trigger/function to update redeemed_points

-- ==============================================
-- PART 1: IMMEDIATE FIXES FOR F1513243
-- ==============================================

-- Fix 1: Manually unlink Telegram account for F1513243
UPDATE loyalty_members
SET
  telegram_chat_id = NULL,
  telegram_username = NULL,
  linking_token = NULL,
  linked_at = NULL
WHERE loyalty_id = 'F1513243';

-- Fix 2: Correct redeemed_points for F1513243
UPDATE loyalty_members
SET redeemed_points = (
  SELECT COALESCE(SUM(ABS(points_change)), 0)
  FROM points_transactions
  WHERE member_id = loyalty_members.id
  AND transaction_type = 'REDEEM'
)
WHERE loyalty_id = 'F1513243';

-- ==============================================
-- PART 2: SYSTEM-WIDE POINT BALANCE FIXES
-- ==============================================

-- Fix redeemed_points for ALL members
UPDATE loyalty_members
SET redeemed_points = (
  SELECT COALESCE(SUM(ABS(points_change)), 0)
  FROM points_transactions
  WHERE member_id = loyalty_members.id
  AND transaction_type = 'REDEEM'
);

-- ==============================================
-- PART 3: CREATE TRIGGER TO PREVENT FUTURE ISSUES
-- ==============================================

-- Create function to automatically update member point balances when transactions change
CREATE OR REPLACE FUNCTION update_member_point_balances()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the member's aggregated point values
  UPDATE loyalty_members
  SET
    lifetime_points = (
      SELECT COALESCE(SUM(points_change), 0)
      FROM points_transactions
      WHERE member_id = loyalty_members.id
      AND transaction_type = 'EARN'
    ),
    redeemed_points = (
      SELECT COALESCE(SUM(ABS(points_change)), 0)
      FROM points_transactions
      WHERE member_id = loyalty_members.id
      AND transaction_type = 'REDEEM'
    )
  WHERE id = COALESCE(NEW.member_id, OLD.member_id);

  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update member balances when transactions are inserted/updated/deleted
DROP TRIGGER IF EXISTS trigger_update_member_point_balances ON points_transactions;
CREATE TRIGGER trigger_update_member_point_balances
  AFTER INSERT OR UPDATE OR DELETE ON points_transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_member_point_balances();

-- ==============================================
-- PART 4: VERIFICATION QUERIES
-- ==============================================

-- Verify F1513243 fixes
SELECT
  loyalty_id,
  name,
  telegram_chat_id,
  telegram_username,
  linking_token,
  linked_at,
  lifetime_points,
  redeemed_points,
  expired_points,
  (lifetime_points - COALESCE(redeemed_points, 0) - COALESCE(expired_points, 0)) as available_points
FROM loyalty_members
WHERE loyalty_id = 'F1513243';

-- Verify all members with point balances
SELECT
  lm.loyalty_id,
  lm.name,
  lm.lifetime_points as stored_lifetime_points,
  lm.redeemed_points as stored_redeemed_points,
  SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END) as calculated_lifetime_points,
  SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END) as calculated_redeemed_points,
  (lm.lifetime_points - lm.redeemed_points - COALESCE(lm.expired_points, 0)) as corrected_available_points
FROM loyalty_members lm
LEFT JOIN points_transactions pt ON lm.id = pt.member_id
GROUP BY lm.id, lm.loyalty_id, lm.name, lm.lifetime_points, lm.redeemed_points, lm.expired_points
ORDER BY lm.loyalty_id;

-- ==============================================
-- PART 5: ADDITIONAL SAFETY CHECKS
-- ==============================================

-- Check for any other members with Telegram unlinking issues
SELECT
  loyalty_id,
  name,
  telegram_chat_id,
  telegram_username,
  linking_token,
  linked_at
FROM loyalty_members
WHERE telegram_chat_id IS NOT NULL
ORDER BY loyalty_id;
