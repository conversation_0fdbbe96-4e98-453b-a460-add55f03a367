-- Analytics Data Relationship Fixes
-- This script fixes the broken relationships between receipt_items and business_items
-- and creates comprehensive analytics views for better performance

-- ==============================================================================
-- PHASE 1: FIX RECEIPT_ITEMS → BUSINESS_ITEMS RELATIONSHIPS
-- ==============================================================================

-- First, let's see which receipt_items are missing business_item linkage
SELECT
    ri.id as receipt_item_id,
    ri.extracted_description,
    ri.unit_price,
    ri.total_price,
    r.company_id,
    r.service_description,
    ri.business_item_id
FROM receipt_items ri
JOIN receipts r ON ri.receipt_id = r.id
WHERE ri.business_item_id IS NULL;

-- Create missing business_items for unlinked receipt_items
INSERT INTO business_items (
    id,
    company_id,
    item_name,
    item_category,
    standard_price,
    total_sales_count,
    total_revenue,
    avg_selling_price,
    last_sold_date,
    is_active,
    created_at,
    updated_at
)
SELECT
    gen_random_uuid() as id,
    r.company_id,
    COALESCE(ri.extracted_description, 'Unspecified Service') as item_name,
    'General' as item_category,
    ri.unit_price as standard_price,
    COUNT(*)::INTEGER as total_sales_count,
    SUM(ri.total_price) as total_revenue,
    AVG(ri.unit_price) as avg_selling_price,
    MAX(r.created_at) as last_sold_date,
    true as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM receipt_items ri
JOIN receipts r ON ri.receipt_id = r.id
WHERE ri.business_item_id IS NULL
    AND ri.extracted_description IS NOT NULL
    AND NOT EXISTS (
        SELECT 1 FROM business_items bi
        WHERE bi.company_id = r.company_id
        AND LOWER(bi.item_name) = LOWER(ri.extracted_description)
    )
GROUP BY r.company_id, ri.extracted_description, ri.unit_price
ON CONFLICT DO NOTHING;

-- Link receipt_items to their corresponding business_items
WITH item_matches AS (
    SELECT DISTINCT
        ri.id as receipt_item_id,
        bi.id as business_item_id
    FROM receipt_items ri
    JOIN receipts r ON ri.receipt_id = r.id
    JOIN business_items bi ON (
        bi.company_id = r.company_id
        AND LOWER(TRIM(bi.item_name)) = LOWER(TRIM(ri.extracted_description))
    )
    WHERE ri.business_item_id IS NULL
        AND ri.extracted_description IS NOT NULL
        AND ri.extracted_description != ''
)
UPDATE receipt_items
SET business_item_id = im.business_item_id,
    updated_at = NOW()
FROM item_matches im
WHERE receipt_items.id = im.receipt_item_id;

-- ==============================================================================
-- PHASE 2: CREATE COMPREHENSIVE ANALYTICS VIEWS
-- ==============================================================================

-- Drop existing views if they exist to recreate them
DROP VIEW IF EXISTS analytics_customer_insights CASCADE;
DROP VIEW IF EXISTS analytics_business_performance CASCADE;
DROP VIEW IF EXISTS analytics_template_metrics CASCADE;
DROP VIEW IF EXISTS analytics_summary_dashboard CASCADE;

-- 1. Customer Insights View
CREATE OR REPLACE VIEW analytics_customer_insights AS
WITH customer_base AS (
    SELECT
        lm.id as member_id,
        lm.company_id,
        lm.loyalty_id,
        lm.name,
        lm.lifetime_points,
        lm.loyalty_tier,
        lm.registration_date,
        COUNT(DISTINCT r.id) as total_visits,
        COALESCE(SUM(r.total_amount), 0) as total_spent,
        COALESCE(AVG(r.total_amount), 0) as avg_order_value,
        MAX(r.created_at) as last_visit_date,
        COUNT(DISTINCT DATE(r.created_at)) as unique_visit_days
    FROM loyalty_members lm
    LEFT JOIN receipts r ON lm.id = r.member_id
    WHERE lm.company_id IS NOT NULL
    GROUP BY lm.id, lm.company_id, lm.loyalty_id, lm.name, lm.lifetime_points, lm.loyalty_tier, lm.registration_date
),
member_preferences AS (
    SELECT
        lm.id as member_id,
        bi.item_name,
        bi.item_category,
        COUNT(*) as purchase_count,
        SUM(ri.total_price) as total_spent_on_item,
        ROW_NUMBER() OVER (PARTITION BY lm.id ORDER BY COUNT(*) DESC, SUM(ri.total_price) DESC) as preference_rank
    FROM loyalty_members lm
    JOIN receipts r ON lm.id = r.member_id
    JOIN receipt_items ri ON r.id = ri.receipt_id
    JOIN business_items bi ON ri.business_item_id = bi.id
    GROUP BY lm.id, bi.item_name, bi.item_category
)
SELECT
    cb.*,
    mp.item_name as favorite_item,
    mp.item_category as favorite_category,
    mp.purchase_count as favorite_item_purchases,
    mp.total_spent_on_item as favorite_item_spent
FROM customer_base cb
LEFT JOIN member_preferences mp ON cb.member_id = mp.member_id AND mp.preference_rank = 1;

-- 2. Business Performance View
CREATE OR REPLACE VIEW analytics_business_performance AS
WITH item_stats AS (
    SELECT
        bi.id,
        bi.company_id,
        bi.item_name,
        bi.item_category,
        bi.standard_price,
        COUNT(ri.id) as total_sales,
        SUM(ri.quantity) as total_quantity_sold,
        SUM(ri.total_price) as total_revenue,
        AVG(ri.unit_price) as avg_selling_price,
        COUNT(DISTINCT r.member_id) as unique_customers,
        MAX(r.created_at) as last_sold_date,
        -- Calculate popularity score: 60% sales weight + 40% customer diversity
        (COUNT(ri.id)::NUMERIC * 0.6 + COUNT(DISTINCT r.member_id)::NUMERIC * 0.4) as popularity_score
    FROM business_items bi
    LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
    LEFT JOIN receipts r ON ri.receipt_id = r.id
    WHERE bi.is_active = true
    GROUP BY bi.id, bi.company_id, bi.item_name, bi.item_category, bi.standard_price
),
category_stats AS (
    SELECT
        company_id,
        item_category,
        COUNT(*) as items_in_category,
        SUM(total_revenue) as category_revenue,
        SUM(total_sales) as category_sales,
        AVG(avg_selling_price) as category_avg_price
    FROM item_stats
    GROUP BY company_id, item_category
)
SELECT
    ist.*,
    cs.category_revenue,
    cs.category_sales,
    cs.items_in_category,
    CASE
        WHEN ist.total_revenue > 0 THEN
            ROUND((ist.total_revenue / cs.category_revenue * 100)::NUMERIC, 2)
        ELSE 0
    END as revenue_share_in_category,
    CASE
        WHEN ist.avg_selling_price > ist.standard_price * 1.1 THEN 'Premium Pricing'
        WHEN ist.avg_selling_price < ist.standard_price * 0.9 THEN 'Discounted'
        ELSE 'Standard Pricing'
    END as pricing_strategy
FROM item_stats ist
LEFT JOIN category_stats cs ON ist.company_id = cs.company_id AND ist.item_category = cs.item_category;

-- 3. Template Analytics View
CREATE OR REPLACE VIEW analytics_template_metrics AS
SELECT
    rt.id,
    rt.company_id,
    rt.template_name,
    rt.confidence_threshold,
    rt.total_extractions,
    rt.successful_extractions,
    rt.avg_confidence_score,
    CASE
        WHEN rt.total_extractions > 0 THEN
            ROUND((rt.successful_extractions::NUMERIC / rt.total_extractions * 100)::NUMERIC, 2)
        ELSE 0
    END as success_rate_percentage,
    COUNT(r.id) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_extractions,
    AVG(r.extraction_confidence) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_avg_confidence,
    CASE
        WHEN rt.total_extractions > 0 AND rt.avg_confidence_score IS NOT NULL THEN
            ((rt.successful_extractions::NUMERIC / rt.total_extractions * 0.7) + (rt.avg_confidence_score * 0.3))
        ELSE 0
    END as effectiveness_score,
    rt.created_at,
    rt.updated_at
FROM receipt_templates rt
LEFT JOIN receipts r ON rt.id = r.template_id
WHERE rt.is_active = true
GROUP BY rt.id, rt.company_id, rt.template_name, rt.confidence_threshold,
         rt.total_extractions, rt.successful_extractions, rt.avg_confidence_score,
         rt.created_at, rt.updated_at;

-- 4. Dashboard Summary View
CREATE OR REPLACE VIEW analytics_summary_dashboard AS
SELECT
    c.id as company_id,
    c.name as company_name,
    c.business_type,
    -- Customer Metrics
    COUNT(DISTINCT lm.id) as total_customers,
    COUNT(DISTINCT lm.id) FILTER (WHERE lm.registration_date >= NOW() - INTERVAL '30 days') as new_customers_30d,
    COUNT(DISTINCT lm.id) FILTER (WHERE lm.lifetime_points > 0) as active_customers,
    -- Transaction Metrics
    COUNT(DISTINCT r.id) as total_receipts,
    COUNT(DISTINCT r.id) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as receipts_30d,
    COALESCE(SUM(r.total_amount), 0) as total_revenue,
    COALESCE(SUM(r.total_amount) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days'), 0) as revenue_30d,
    CASE
        WHEN COUNT(DISTINCT r.id) > 0 THEN
            ROUND((SUM(r.total_amount) / COUNT(DISTINCT r.id))::NUMERIC, 2)
        ELSE 0
    END as avg_transaction_value,
    -- Business Items
    COUNT(DISTINCT bi.id) as total_business_items,
    COUNT(DISTINCT bi.id) FILTER (WHERE bi.is_active = true) as active_business_items,
    -- Template Metrics
    COUNT(DISTINCT rt.id) as total_templates,
    COUNT(DISTINCT r.id) FILTER (WHERE r.template_id IS NOT NULL) as template_enhanced_receipts,
    CASE
        WHEN COUNT(DISTINCT r.id) > 0 THEN
            ROUND((COUNT(DISTINCT r.id) FILTER (WHERE r.template_id IS NOT NULL)::NUMERIC / COUNT(DISTINCT r.id) * 100)::NUMERIC, 2)
        ELSE 0
    END as template_usage_percentage,
    AVG(r.extraction_confidence) as avg_extraction_confidence,
    -- Calculated at timestamp
    NOW() as calculated_at
FROM companies c
LEFT JOIN loyalty_members lm ON c.id = lm.company_id
LEFT JOIN receipts r ON c.id = r.company_id
LEFT JOIN business_items bi ON c.id = bi.company_id
LEFT JOIN receipt_templates rt ON c.id = rt.company_id AND rt.is_active = true
GROUP BY c.id, c.name, c.business_type;

-- ==============================================================================
-- PHASE 3: CREATE ANALYTICS API FUNCTIONS
-- ==============================================================================

-- Function to get customer insights for a specific company
CREATE OR REPLACE FUNCTION get_customer_insights(p_company_id UUID)
RETURNS TABLE(
    member_id UUID,
    loyalty_id TEXT,
    customer_name TEXT,
    total_visits BIGINT,
    total_spent NUMERIC,
    avg_order_value NUMERIC,
    favorite_item TEXT,
    favorite_category TEXT,
    loyalty_tier VARCHAR,
    lifetime_points INTEGER,
    last_visit_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        aci.member_id,
        aci.loyalty_id,
        aci.name as customer_name,
        aci.total_visits,
        aci.total_spent,
        aci.avg_order_value,
        aci.favorite_item,
        aci.favorite_category,
        aci.loyalty_tier,
        aci.lifetime_points,
        aci.last_visit_date
    FROM analytics_customer_insights aci
    WHERE aci.company_id = p_company_id
        AND aci.total_visits > 0  -- Only customers with transactions
    ORDER BY aci.total_spent DESC, aci.total_visits DESC
    LIMIT 50;
END;
$$ LANGUAGE plpgsql;

-- Function to get business performance metrics
CREATE OR REPLACE FUNCTION get_business_performance(p_company_id UUID)
RETURNS TABLE(
    item_id UUID,
    item_name TEXT,
    item_category TEXT,
    total_sales BIGINT,
    total_revenue NUMERIC,
    avg_selling_price NUMERIC,
    unique_customers BIGINT,
    popularity_score NUMERIC,
    pricing_strategy TEXT,
    revenue_share_in_category NUMERIC,
    last_sold_date TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        abp.id as item_id,
        abp.item_name,
        abp.item_category,
        abp.total_sales,
        abp.total_revenue,
        abp.avg_selling_price,
        abp.unique_customers,
        abp.popularity_score,
        abp.pricing_strategy,
        abp.revenue_share_in_category,
        abp.last_sold_date
    FROM analytics_business_performance abp
    WHERE abp.company_id = p_company_id
        AND abp.total_sales > 0  -- Only items with sales
    ORDER BY abp.popularity_score DESC, abp.total_revenue DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get template metrics
CREATE OR REPLACE FUNCTION get_template_analytics(p_company_id UUID)
RETURNS TABLE(
    template_id UUID,
    template_name TEXT,
    total_extractions INTEGER,
    success_rate_percentage NUMERIC,
    avg_confidence_score NUMERIC,
    recent_extractions BIGINT,
    recent_avg_confidence NUMERIC,
    effectiveness_score NUMERIC,
    last_updated TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        atm.id as template_id,
        atm.template_name,
        atm.total_extractions,
        atm.success_rate_percentage,
        atm.avg_confidence_score,
        atm.recent_extractions,
        atm.recent_avg_confidence,
        atm.effectiveness_score,
        atm.updated_at as last_updated
    FROM analytics_template_metrics atm
    WHERE atm.company_id = p_company_id
    ORDER BY atm.effectiveness_score DESC;
END;
$$ LANGUAGE plpgsql;

-- ==============================================================================
-- VERIFICATION QUERIES
-- ==============================================================================

-- Verify the fixes worked
SELECT 'Analytics Data Health Check' as check_type, COUNT(*) as count
FROM (
    -- Check 1: Verify receipt_items are now linked
    SELECT 'receipt_items_linked' as metric, COUNT(*) as value
    FROM receipt_items ri
    WHERE ri.business_item_id IS NOT NULL

    UNION ALL

    -- Check 2: Verify business items have sales data
    SELECT 'business_items_with_sales' as metric, COUNT(*) as value
    FROM business_items bi
    WHERE bi.total_sales_count > 0

    UNION ALL

    -- Check 3: Verify customer insights are populated
    SELECT 'customers_with_insights' as metric, COUNT(*) as value
    FROM analytics_customer_insights
    WHERE total_visits > 0

    UNION ALL

    -- Check 4: Verify business performance metrics
    SELECT 'items_with_performance' as metric, COUNT(*) as value
    FROM analytics_business_performance
    WHERE total_sales > 0
) verification_results;

-- Final verification query to show analytics summary
SELECT * FROM analytics_summary_dashboard;
