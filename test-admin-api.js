// Test script to verify admin API endpoint
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

async function testAdminAPI() {
  try {
    console.log('Testing admin API endpoint...');

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Test user ID from logs
    const testUserId = '0557e2e2-75dd-4b23-a6fb-ad5ac0211b00';

    console.log('Checking user admin status directly in database...');

    // Check if user is company owner
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('id, name, administrator_id')
      .eq('administrator_id', testUserId)
      .maybeSingle();

    if (companyError) {
      console.error('Error checking companies table:', companyError);
    } else if (companyData) {
      console.log('✅ User is company owner:', companyData);
    } else {
      console.log('❌ User is not a company owner');
    }

    // Check if user is admin in company_administrators table
    const { data: adminData, error: adminError } = await supabase
      .from('company_administrators')
      .select(`
        company_id,
        administrator_id,
        role,
        created_at,
        companies!inner(id, name)
      `)
      .eq('administrator_id', testUserId)
      .maybeSingle();

    if (adminError) {
      console.error('Error checking company_administrators table:', adminError);
    } else if (adminData) {
      console.log('✅ User is admin via company_administrators:', adminData);
    } else {
      console.log('❌ User is not in company_administrators table');
    }

    // Test the API endpoint response format
    const isOwner = !!companyData;
    const isAdminWithOwnerRole = adminData?.role === 'OWNER';

    if (isOwner || isAdminWithOwnerRole) {
      console.log('✅ User should have admin access');

      const mockResponse = {
        isAdmin: true,
        adminData: {
          company_id: companyData?.id || adminData?.company_id,
          administrator_id: testUserId,
          role: 'OWNER',
          created_at: new Date().toISOString()
        },
        companyId: companyData?.id || adminData?.company_id,
        companyName: companyData?.name || adminData?.companies?.name
      };

      console.log('Expected API response:', JSON.stringify(mockResponse, null, 2));
    } else {
      console.log('❌ User should NOT have admin access');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

testAdminAPI();
