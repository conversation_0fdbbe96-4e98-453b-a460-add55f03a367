-- ===============================================================================
-- URGENT: ANALYTICS FUNCTION FIX - No Views Required
-- This creates a simplified version of get_business_performance that works without views
-- ===============================================================================

-- Replace the get_business_performance function with a version that doesn't use views
CREATE OR REPLACE FUNCTION get_business_performance(input_company_id UUID)
RETURNS TABLE (
    item_id UUID,
    item_name TEXT,
    item_category TEXT,
    standard_price NUMERIC,
    total_sales BIGINT,
    total_quantity_sold NUMERIC,
    total_revenue NUMERIC,
    avg_selling_price NUMERIC,
    unique_customers BIGINT,
    last_sold_date TIMESTAMPTZ,
    popularity_score NUMERIC,
    revenue_share_in_category NUMERIC,
    pricing_strategy TEXT
)
LANGUAGE SQL
SECURITY DEFINER
AS $$
    WITH item_stats AS (
        SELECT
            bi.id as item_id,
            bi.item_name,
            bi.item_category,
            bi.standard_price,
            COUNT(ri.id) as total_sales,
            COALESCE(SUM(ri.quantity), 0) as total_quantity_sold,
            COALESCE(SUM(ri.total_price), 0) as total_revenue,
            COALESCE(AVG(ri.unit_price), bi.standard_price) as avg_selling_price,
            COUNT(DISTINCT r.member_id) as unique_customers,
            MAX(r.created_at) as last_sold_date,
            -- Calculate popularity score: 60% sales weight + 40% customer diversity
            (COUNT(ri.id)::NUMERIC * 0.6 + COUNT(DISTINCT r.member_id)::NUMERIC * 0.4) as popularity_score
        FROM business_items bi
        LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
        LEFT JOIN receipts r ON ri.receipt_id = r.id
        WHERE bi.company_id = input_company_id AND bi.is_active = true
        GROUP BY bi.id, bi.item_name, bi.item_category, bi.standard_price
    ),
    category_stats AS (
        SELECT
            item_category,
            SUM(total_revenue) as category_revenue
        FROM item_stats
        GROUP BY item_category
    )
    SELECT
        ist.item_id,
        ist.item_name,
        ist.item_category,
        ist.standard_price,
        ist.total_sales,
        ist.total_quantity_sold,
        ist.total_revenue,
        ist.avg_selling_price,
        ist.unique_customers,
        ist.last_sold_date,
        ist.popularity_score,
        CASE
            WHEN ist.total_revenue > 0 AND cs.category_revenue > 0 THEN
                ROUND((ist.total_revenue / cs.category_revenue * 100)::NUMERIC, 2)
            ELSE 0
        END as revenue_share_in_category,
        CASE
            WHEN ist.avg_selling_price > ist.standard_price * 1.1 THEN 'Premium Pricing'
            WHEN ist.avg_selling_price < ist.standard_price * 0.9 THEN 'Discounted'
            ELSE 'Standard Pricing'
        END as pricing_strategy
    FROM item_stats ist
    LEFT JOIN category_stats cs ON ist.item_category = cs.item_category
    ORDER BY ist.popularity_score DESC, ist.total_revenue DESC;
$$;

-- Also create a template analytics function that doesn't rely on views
CREATE OR REPLACE FUNCTION get_template_analytics(input_company_id UUID)
RETURNS TABLE (
    template_id UUID,
    template_name TEXT,
    total_extractions BIGINT,
    successful_extractions BIGINT,
    success_rate NUMERIC,
    avg_confidence NUMERIC,
    recent_extractions BIGINT,
    effectiveness_score NUMERIC
)
LANGUAGE SQL
SECURITY DEFINER
AS $$
    SELECT
        rt.id as template_id,
        rt.template_name,
        COUNT(r.id) as total_extractions,
        COUNT(r.id) FILTER (WHERE r.extraction_confidence >= COALESCE(rt.confidence_threshold, 0.8)) as successful_extractions,
        CASE
            WHEN COUNT(r.id) > 0 THEN
                ROUND((COUNT(r.id) FILTER (WHERE r.extraction_confidence >= COALESCE(rt.confidence_threshold, 0.8))::NUMERIC / COUNT(r.id) * 100)::NUMERIC, 2)
            ELSE 0
        END as success_rate,
        COALESCE(AVG(r.extraction_confidence), 0) as avg_confidence,
        COUNT(r.id) FILTER (WHERE r.created_at >= NOW() - INTERVAL '30 days') as recent_extractions,
        CASE
            WHEN COUNT(r.id) > 0 AND AVG(r.extraction_confidence) IS NOT NULL THEN
                ((COUNT(r.id) FILTER (WHERE r.extraction_confidence >= COALESCE(rt.confidence_threshold, 0.8))::NUMERIC / COUNT(r.id) * 0.7) + (AVG(r.extraction_confidence) * 0.3))
            ELSE 0
        END as effectiveness_score
    FROM receipt_templates rt
    LEFT JOIN receipts r ON rt.id = r.template_id
    WHERE rt.company_id = input_company_id AND rt.is_active = true
    GROUP BY rt.id, rt.template_name, rt.confidence_threshold
    ORDER BY effectiveness_score DESC;
$$;

-- ===============================================================================
-- TEST THE FUNCTIONS
-- ===============================================================================

-- Test customer insights (should work)
SELECT 'Customer Insights Function Test' as test,
       COUNT(*) as customer_count
FROM get_customer_insights('d10aed7e-3116-403c-a572-c16ab870d761');

-- Test business performance (fixed version)
SELECT 'Business Performance Function Test' as test,
       COUNT(*) as item_count
FROM get_business_performance('d10aed7e-3116-403c-a572-c16ab870d761');

-- Test template analytics (new function)
SELECT 'Template Analytics Function Test' as test,
       COUNT(*) as template_count
FROM get_template_analytics('d10aed7e-3116-403c-a572-c16ab870d761');
