-- Create a function to get company configuration that bypasses RLS
-- This function will run with elevated privileges

CREATE OR REPLACE FUNCTION get_company_config(company_id UUID)
RETURNS SETOF companies
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT * FROM companies WHERE id = company_id;
$$;

-- Grant execution permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_company_config(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_company_config(UUID) TO service_role;
