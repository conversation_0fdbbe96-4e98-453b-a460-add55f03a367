#!/bin/bash

echo "🔄 Migrating to SSR-compatible Supabase clients..."

# Backup original files
echo "📦 Creating backups..."
cp lib/supabase.ts lib/supabase.ts.backup.$(date +%Y%m%d_%H%M%S)
cp hooks/use-consolidated-auth.ts hooks/use-consolidated-auth.ts.backup.$(date +%Y%m%d_%H%M%S)
cp middleware.ts middleware.ts.backup.$(date +%Y%m%d_%H%M%S)

# Replace the main files with fixed versions
echo "🔧 Applying fixes..."
mv lib/supabase-fixed.ts lib/supabase.ts
mv hooks/use-consolidated-auth-fixed.ts hooks/use-consolidated-auth.ts
mv middleware-fixed.ts middleware.ts

echo "✅ Core files updated!"

# Update auth hook imports to use the fixed version
echo "🔄 Updating auth hook imports..."

# List of files that need auth hook updates
files=(
  "components/layout/Navbar.tsx"
  "components/layout/header.tsx"
  "app/company/create/page.tsx"
  "app/company/edit/page.tsx"
  "app/dashboard/page.tsx"
  "app/dashboard/components/business-stats.tsx"
  "app/dashboard/components/welcome-state.tsx"
  "app/items/page.tsx"
  "app/members/page.tsx"
  "app/members/add/page.tsx"
  "app/members/[id]/page.tsx"
  "app/members/[id]/add-points/page.tsx"
  "app/members/[id]/deduct-points/page.tsx"
  "app/members/[id]/edit/page.tsx"
  "app/members/telegram-links/page.tsx"
  "app/transactions/page.tsx"
  "app/transactions/add/page.tsx"
  "app/transactions/unified/page.tsx"
)

count=0
for file in "${files[@]}"; do
  if [[ -f "$file" ]]; then
    # Update Supabase client imports to use the SSR-compatible approach
    sed -i.bak "s|from '@/lib/supabase'|from '@/lib/supabase/client'|g" "$file"
    sed -i.bak "s|getSupabaseClient()|createClient()|g" "$file"

    # Clean up backup files
    rm -f "$file.bak"

    echo "✅ Updated $file"
    ((count++))
  else
    echo "⚠️  File not found: $file"
  fi
done

echo ""
echo "🎉 Migration completed! Updated $count files."
echo ""
echo "📋 Summary of changes:"
echo "   • Replaced manual cookie handling with proper @supabase/ssr approach"
echo "   • Fixed client creation to use separate browser/server/middleware clients"
echo "   • Updated auth hook to work with SSR-compatible clients"
echo "   • Fixed middleware to use proper SSR patterns"
echo ""
echo "🚀 Next steps:"
echo "   1. Restart your development server"
echo "   2. Test login/logout functionality"
echo "   3. Check console for any remaining client instance warnings"
echo "   4. Verify session persistence across page refreshes"
