# AI-Powered Receipt OCR Implementation Plan
## Using Vercel AI SDK + Gemini 2.5 Flash for Transaction Form Enhancement

---

## 🎯 **Project Overview**

We will enhance the transaction addition form (`/transactions/add`) to automatically extract receipt information using Google's Gemini 2.5 Flash vision model via the Vercel AI SDK. Users will upload a receipt image, and the AI will automatically populate the transaction form fields.

### **Key Goals:**
1. **Auto-populate form fields** from receipt image analysis
2. **Store extracted raw data** for audit trail and future processing
3. **Maintain existing manual flow** as fallback option
4. **Seamless UX** with smart field population

---

## 📋 **Current State Analysis**

### **Existing Database Schema:**
- ✅ `receipts` table has `receipt_image_url` field
- ✅ `points_transactions` table links to receipts via `receipt_id`
- ✅ File upload functionality already implemented
- ✅ Supabase storage bucket "fufis" configured

### **Current Form Fields:**
- Member selection
- Transaction type (EARN/REDEEM)
- Points amount
- Description
- Receipt ID (optional)
- Receipt image upload (newly added)
- Transaction date

### **Receipt Example Analysis:**
From the provided receipt image, we can extract:
- **FS No**: `00006000` (Financial System Number)
- **Total Amount**: `517.50`
- **Business Name**: `FUFIS BEAUTY SERVICES P.L.C`
- **Service**: `Beard Shaving`
- **Date**: `08/04/2025`
- **Payment Method**: `CASH`
- **Business TIN**: `007688354`

---

## 🏗️ **Technical Architecture**

### **Tech Stack:**
- **Frontend**: Next.js 14 with App Router
- **AI SDK**: Vercel AI SDK (`ai` package)
- **Vision Model**: Google Gemini 2.5 Flash via `@ai-sdk/google`
- **File Upload**: Existing Supabase Storage
- **Database**: PostgreSQL (Supabase)
- **Form Management**: React Hook Form + Zod validation

### **Data Flow:**
```
User uploads receipt image
    ↓
Upload to Supabase Storage
    ↓
Send image to Gemini 2.5 Flash via AI SDK
    ↓
Extract structured data (JSON)
    ↓
Auto-populate form fields
    ↓
Save transaction + receipt record
    ↓
Store raw extracted data
```

---

## 🔧 **Implementation Plan**

### **Phase 1: Setup AI SDK Infrastructure**

#### **1.1 Install Dependencies**
```bash
npm install ai @ai-sdk/google zod
```

#### **1.2 Environment Setup**
Add to `.env.local`:
```bash
GOOGLE_GENERATIVE_AI_API_KEY=your_gemini_api_key
```

#### **1.3 Create AI Configuration**
```typescript
// lib/ai-config.ts
import { google } from '@ai-sdk/google';

export const geminiFlash = google('gemini-2.5-flash');
```

### **Phase 2: Database Schema Enhancements**

#### **2.1 Add OCR Data Storage**
Add new columns to `points_transactions` table:
```sql
ALTER TABLE points_transactions
ADD COLUMN receipt_ocr_data JSONB,
ADD COLUMN receipt_ocr_confidence DECIMAL(3,2),
ADD COLUMN receipt_processing_status TEXT DEFAULT 'pending';
```

#### **2.2 Update Receipts Table**
Ensure receipts table can store extracted business data:
```sql
-- Already exists, but verify these fields:
-- business_name, business_tin, business_location
-- total_amount, subtotal, tax_amount, tax_percentage
-- receipt_number (maps to FS No)
```

### **Phase 3: Create OCR Service**

#### **3.1 Receipt OCR Utility**
```typescript
// lib/receipt-ocr.ts
import { generateObject } from 'ai';
import { geminiFlash } from '@/lib/ai-config';
import { z } from 'zod';

const ReceiptSchema = z.object({
  business_name: z.string().describe("Name of the business/company"),
  financial_system_number: z.string().describe("FS No or receipt number"),
  total_amount: z.number().describe("Total amount paid"),
  subtotal: z.number().optional().describe("Subtotal before tax"),
  tax_amount: z.number().optional().describe("Tax amount"),
  service_description: z.string().describe("Service or item description"),
  payment_method: z.string().describe("Payment method (CASH, CARD, etc.)"),
  business_tin: z.string().optional().describe("Business TIN number"),
  receipt_date: z.string().describe("Date on receipt (DD/MM/YYYY format)"),
  business_location: z.string().optional().describe("Business address or location"),
  confidence: z.number().min(0).max(1).describe("Confidence level of extraction")
});

export async function extractReceiptData(imageBuffer: Buffer) {
  const { object } = await generateObject({
    model: geminiFlash,
    schema: ReceiptSchema,
    messages: [
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: `Extract the following information from this receipt image.
                   Be very careful with numbers and dates.
                   If any field is unclear or missing, mark confidence as lower.
                   Extract all financial amounts as numbers (no currency symbols).`
          },
          {
            type: 'image',
            image: imageBuffer,
          },
        ],
      },
    ],
  });

  return object;
}
```

#### **3.2 API Endpoint for OCR Processing**
```typescript
// app/api/receipts/ocr/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { extractReceiptData } from '@/lib/receipt-ocr';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    const extractedData = await extractReceiptData(buffer);

    return NextResponse.json({
      success: true,
      data: extractedData
    });
  } catch (error) {
    console.error('OCR processing error:', error);
    return NextResponse.json(
      { error: 'Failed to process receipt' },
      { status: 500 }
    );
  }
}
```

### **Phase 4: Enhanced Transaction Form**

#### **4.1 Update Form Schema**
```typescript
// Update existing schema in app/transactions/add/page.tsx
const transactionSchema = z.object({
  member_id: z.string().uuid({ message: 'Please select a member' }),
  transaction_type: z.enum(['EARN', 'REDEEM']),
  points_change: z.coerce.number().positive(),
  description: z.string().min(3),
  receipt_id: z.string().optional().or(z.literal('')),
  transaction_date: z.string(),
  receipt_image: z.instanceof(File).optional(),
  // New OCR-populated fields
  total_amount: z.coerce.number().optional(),
  business_name: z.string().optional(),
  financial_system_number: z.string().optional(),
});
```

#### **4.2 Add OCR Processing State**
```typescript
// Add to transaction form component
const [isProcessingOCR, setIsProcessingOCR] = useState(false);
const [ocrData, setOcrData] = useState(null);
const [showOcrResults, setShowOcrResults] = useState(false);
```

#### **4.3 OCR Processing Function**
```typescript
const processReceiptOCR = async (file: File) => {
  setIsProcessingOCR(true);
  try {
    const formData = new FormData();
    formData.append('image', file);

    const response = await fetch('/api/receipts/ocr', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) throw new Error('OCR processing failed');

    const result = await response.json();
    setOcrData(result.data);

    // Auto-populate form fields
    populateFormFromOCR(result.data);
    setShowOcrResults(true);

    toast.success('Receipt processed successfully!');
  } catch (error) {
    toast.error('Failed to process receipt');
  } finally {
    setIsProcessingOCR(false);
  }
};

const populateFormFromOCR = (data: any) => {
  // Auto-populate form fields from OCR data
  form.setValue('description', data.service_description);
  form.setValue('receipt_id', data.financial_system_number);
  form.setValue('total_amount', data.total_amount);
  form.setValue('business_name', data.business_name);
  form.setValue('financial_system_number', data.financial_system_number);

  // Convert date format and set transaction_date
  if (data.receipt_date) {
    const convertedDate = convertDateFormat(data.receipt_date);
    form.setValue('transaction_date', convertedDate);
  }
};
```

### **Phase 5: Enhanced UI Components**

#### **5.1 OCR Results Display**
```typescript
// Add to transaction form JSX
{showOcrResults && ocrData && (
  <Card className="mt-4 border-green-200 bg-green-50">
    <CardHeader>
      <CardTitle className="text-green-800">
        ✅ Receipt Processed Successfully
      </CardTitle>
      <CardDescription>
        Confidence: {(ocrData.confidence * 100).toFixed(1)}%
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <strong>Business:</strong> {ocrData.business_name}
        </div>
        <div>
          <strong>FS No:</strong> {ocrData.financial_system_number}
        </div>
        <div>
          <strong>Amount:</strong> ${ocrData.total_amount}
        </div>
        <div>
          <strong>Service:</strong> {ocrData.service_description}
        </div>
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setShowOcrResults(false)}
        className="mt-2"
      >
        Hide Details
      </Button>
    </CardContent>
  </Card>
)}
```

#### **5.2 Smart Upload Component**
```typescript
// Enhanced file upload with OCR trigger
<div className="space-y-2">
  <Label htmlFor="receipt_image">Receipt Image (Optional)</Label>
  <div className="space-y-2">
    <Input
      id="receipt_image"
      type="file"
      accept="image/*"
      disabled={createTransactionMutation.isPending || isProcessingOCR}
      className="bg-card border"
      onChange={async (e) => {
        const file = e.target.files?.[0];
        if (file) {
          setReceiptFile(file);
          // Automatically trigger OCR processing
          await processReceiptOCR(file);
        }
      }}
    />

    {isProcessingOCR && (
      <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-md">
        <LoadingSpinner size="sm" />
        <span className="text-sm text-blue-700">
          Processing receipt with AI...
        </span>
      </div>
    )}
  </div>
</div>
```

### **Phase 6: Data Storage Enhancement**

#### **6.1 Update Transaction Creation**
```typescript
// Enhanced form submission in app/transactions/add/page.tsx
const onSubmit = async (data: TransactionFormValues) => {
  try {
    let receiptId = data.receipt_id || undefined;

    // If there's a receipt image with OCR data
    if (receiptFile && ocrData) {
      const { url: imageUrl } = await uploadImage(receiptFile);

      // Create enhanced receipt record with OCR data
      const receiptResponse = await fetch('/api/receipts/enhanced', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          company_id: company.id,
          member_id: data.member_id,
          receipt_image_url: imageUrl,
          // OCR extracted data
          receipt_number: ocrData.financial_system_number,
          business_name: ocrData.business_name,
          business_tin: ocrData.business_tin,
          total_amount: ocrData.total_amount,
          subtotal: ocrData.subtotal,
          tax_amount: ocrData.tax_amount,
          service_description: ocrData.service_description,
          payment_method: ocrData.payment_method,
          business_location: ocrData.business_location,
          // Metadata
          ocr_confidence: ocrData.confidence,
          ocr_raw_data: ocrData,
        }),
      });

      const receiptData = await receiptResponse.json();
      receiptId = receiptData.data.id;
    }

    // Create transaction with OCR metadata
    await createTransactionMutation.mutateAsync({
      member_id: data.member_id,
      transaction_type: data.transaction_type,
      points_change: data.points_change,
      description: data.description,
      transaction_date: data.transaction_date,
      receipt_id: receiptId,
      receipt_ocr_data: ocrData,
      receipt_ocr_confidence: ocrData?.confidence,
      receipt_processing_status: 'completed'
    });

    toast.success('Transaction added successfully!');
    router.push('/transactions');
  } catch (error) {
    toast.error('Failed to add transaction');
  }
};
```

---

## 🧪 **Testing Strategy**

### **Test Cases:**
1. **Clear, high-quality receipts** → Should extract with >90% confidence
2. **Blurry or low-quality images** → Should extract with lower confidence + manual review
3. **Different receipt formats** → Test various business receipt styles
4. **Non-receipt images** → Should gracefully fail with helpful error
5. **Multiple items on receipt** → Should extract primary service/total
6. **Different currencies** → Should handle various currency formats

### **Fallback Scenarios:**
- OCR fails → Manual form entry (existing flow)
- Low confidence → Show extracted data but allow manual editing
- Network issues → Queue for later processing

---

## 📊 **Success Metrics**

### **Accuracy Targets:**
- **95%+ accuracy** for total amounts
- **90%+ accuracy** for business names
- **85%+ accuracy** for dates and receipt numbers
- **< 3 seconds** processing time per receipt

### **User Experience:**
- **Reduce form filling time** by 70%
- **Improve data accuracy** through AI assistance
- **Maintain manual override** capability

---

## 🚀 **Deployment Checklist**

### **Pre-deployment:**
- [ ] Add Gemini API key to environment
- [ ] Test with various receipt formats
- [ ] Implement error handling and fallbacks
- [ ] Add database migrations for new columns
- [ ] Update API documentation

### **Post-deployment:**
- [ ] Monitor OCR accuracy rates
- [ ] Collect user feedback on auto-populated fields
- [ ] Fine-tune extraction prompts based on real data
- [ ] Implement batch processing for failed OCRs

---

## 💡 **Future Enhancements**

### **Phase 2 Features:**
1. **Receipt Templates** → Learn from business-specific receipt formats
2. **Bulk Processing** → Handle multiple receipts at once
3. **Smart Validation** → Cross-check extracted data against business rules
4. **Advanced Analytics** → Spending patterns from receipt data
5. **Multi-language Support** → Handle receipts in different languages

### **Integration Opportunities:**
- **Email Receipt Processing** → Extract from email attachments
- **Telegram Bot Enhancement** → Direct receipt upload via bot
- **Mobile App** → Camera capture with immediate processing
- **Accounting Integration** → Export to accounting software

---

## 🔒 **Security & Privacy**

### **Data Protection:**
- **Image Processing** → Images processed via secure Gemini API
- **Data Retention** → Raw images stored securely in Supabase
- **Access Control** → Company-level data isolation maintained
- **Audit Trail** → Track all OCR operations and manual overrides

### **Compliance:**
- **GDPR** → User consent for image processing
- **Data Minimization** → Store only necessary extracted data
- **Right to Deletion** → Allow users to remove receipt images

---

This comprehensive plan provides a roadmap for implementing cutting-edge AI-powered receipt OCR functionality while maintaining the robustness and user experience of the existing transaction system. The phased approach allows for incremental development and testing, ensuring a smooth rollout of this innovative feature.
