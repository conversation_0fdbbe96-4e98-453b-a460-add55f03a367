-- Create the api_metrics table if it doesn't exist
CREATE TABLE IF NOT EXISTS api_metrics (
  id SERIAL PRIMARY KEY,
  endpoint TEXT NOT NULL,
  method TEXT NOT NULL,
  count INTEGER NOT NULL,
  total_duration NUMERIC NOT NULL,
  avg_duration NUMERIC NOT NULL,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Function to check if api_metrics table exists
CREATE OR REPLACE FUNCTION check_api_metrics_table_exists()
RETURNS boolean
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  table_exists boolean;
BEGIN
  SELECT EXISTS (
    SELECT FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'api_metrics'
  ) INTO table_exists;

  RETURN table_exists;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Function to execute SQL directly (for table creation)
CREATE OR REPLACE FUNCTION execute_sql(sql text)
RETURNS void
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  EXECUTE sql;
END;
$$;

-- Function to get API metrics summary
CREATE OR REPLACE FUNCTION get_api_metrics_summary(
  time_period text DEFAULT '24h'
)
RETURNS TABLE (
  endpoint text,
  method text,
  total_calls bigint,
  avg_duration numeric,
  last_called timestamp with time zone
)
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  start_time timestamp with time zone;
BEGIN
  -- Determine the start time based on the time_period parameter
  CASE time_period
    WHEN '1h' THEN start_time := NOW() - INTERVAL '1 hour';
    WHEN '6h' THEN start_time := NOW() - INTERVAL '6 hours';
    WHEN '24h' THEN start_time := NOW() - INTERVAL '24 hours';
    WHEN '7d' THEN start_time := NOW() - INTERVAL '7 days';
    WHEN '30d' THEN start_time := NOW() - INTERVAL '30 days';
    ELSE start_time := NOW() - INTERVAL '24 hours'; -- Default to last 24 hours
  END CASE;

  RETURN QUERY
  SELECT
    am.endpoint,
    am.method,
    SUM(am.count)::bigint as total_calls,
    SUM(am.total_duration) / SUM(am.count) as avg_duration,
    MAX(am.timestamp) as last_called
  FROM
    api_metrics am
  WHERE
    am.timestamp >= start_time
  GROUP BY
    am.endpoint, am.method
  ORDER BY
    total_calls DESC;
END;
$$;