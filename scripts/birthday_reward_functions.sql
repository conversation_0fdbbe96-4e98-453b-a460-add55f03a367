-- Function to get all members who are currently eligible for birthday rewards
CREATE OR REPLACE FUNCTION get_birthday_eligible_members(p_company_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  email TEXT,
  phone_number TEXT,
  birthday DATE,
  telegram_chat_id TEXT,
  available_points INTEGER
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    m.id,
    m.name,
    m.email,
    m.phone_number,
    m.birthday,
    m.telegram_chat_id,
    mp.available_points
  FROM 
    loyalty_members m
  JOIN
    member_points_live mp ON m.id = mp.id
  WHERE 
    m.company_id = p_company_id
    AND m.telegram_chat_id IS NOT NULL
    AND is_member_birthday_eligible(m.id) = TRUE;
END;
$$;
