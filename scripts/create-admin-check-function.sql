-- Create a function to check if a user has admin access
-- This function bypasses RLS policies by using SECURITY DEFINER
CREATE OR REPLACE FUNCTION check_user_admin_status(user_id UUID)
RETURNS TABLE (
  admin_type TEXT,
  company_id UUID,
  company_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Check if user is company owner (administrator_id in companies table)
  RETURN QUERY
  SELECT 
    'owner'::TEXT as admin_type,
    c.id as company_id,
    c.name as company_name
  FROM companies c
  WHERE c.administrator_id = user_id
  LIMIT 1;
  
  -- If no results from companies table, check company_administrators table
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      ca.role::TEXT as admin_type,
      ca.company_id,
      c.name as company_name
    FROM company_administrators ca
    JOIN companies c ON c.id = ca.company_id
    WHERE ca.administrator_id = user_id 
      AND ca.role = 'OWNER'
    LIMIT 1;
  END IF;
  
  RETURN;
E<PERSON>;
$$;

-- Grant execution permissions
GRANT EXECUTE ON FUNCTION check_user_admin_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_user_admin_status(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION check_user_admin_status(UUID) TO anon;
