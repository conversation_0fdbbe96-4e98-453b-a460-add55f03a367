-- Migration script to add timestamp columns to tables
-- This fixes the "column X.created_at does not exist" errors

-- Start a transaction so we can roll back if needed
BEGIN;

-- Add created_at column to points_transactions if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'points_transactions' AND column_name = 'created_at'
  ) THEN
    ALTER TABLE points_transactions ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Added created_at column to points_transactions table';
  ELSE
    RAISE NOTICE 'created_at column already exists in points_transactions table';
  END IF;
END $$;

-- Add updated_at column to points_transactions if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'points_transactions' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE points_transactions ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Added updated_at column to points_transactions table';
  ELSE
    RAISE NOTICE 'updated_at column already exists in points_transactions table';
  END IF;
END $$;

-- Add created_at column to reward_redemptions if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'reward_redemptions' AND column_name = 'created_at'
  ) THEN
    ALTER TABLE reward_redemptions ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Added created_at column to reward_redemptions table';
  ELSE
    RAISE NOTICE 'created_at column already exists in reward_redemptions table';
  END IF;
END $$;

-- Add updated_at column to reward_redemptions if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'reward_redemptions' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE reward_redemptions ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Added updated_at column to reward_redemptions table';
  ELSE
    RAISE NOTICE 'updated_at column already exists in reward_redemptions table';
  END IF;
END $$;

-- Fix permissions issues
-- Get the current database user
DO $$
DECLARE
  current_user_name TEXT;
  anon_role TEXT := 'anon';
  service_role TEXT := 'service_role';
BEGIN
  SELECT current_user INTO current_user_name;
  RAISE NOTICE 'Current user: %', current_user_name;
  
  -- Grant permissions to points_transactions table
  EXECUTE 'GRANT SELECT, INSERT, UPDATE, DELETE ON points_transactions TO ' || current_user_name;
  RAISE NOTICE 'Granted permissions on points_transactions to %', current_user_name;
  
  -- Grant permissions to reward_redemptions table
  EXECUTE 'GRANT SELECT, INSERT, UPDATE, DELETE ON reward_redemptions TO ' || current_user_name;
  RAISE NOTICE 'Granted permissions on reward_redemptions to %', current_user_name;
  
  -- Grant permissions to Supabase anon role if it exists
  BEGIN
    EXECUTE 'GRANT SELECT ON points_transactions TO ' || anon_role;
    EXECUTE 'GRANT SELECT ON reward_redemptions TO ' || anon_role;
    RAISE NOTICE 'Granted SELECT permissions to anon role';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'Could not grant permissions to anon role: %', SQLERRM;
  END;
  
  -- Grant permissions to Supabase service_role if it exists
  BEGIN
    EXECUTE 'GRANT ALL ON points_transactions TO ' || service_role;
    EXECUTE 'GRANT ALL ON reward_redemptions TO ' || service_role;
    RAISE NOTICE 'Granted ALL permissions to service_role';
  EXCEPTION
    WHEN OTHERS THEN
      RAISE NOTICE 'Could not grant permissions to service_role: %', SQLERRM;
  END;
END $$;

-- Commit the changes
COMMIT;
