/**
 * API Health Check Script
 * 
 * This script tests all API endpoints in the application for potential issues
 * that could cause infinite loops or other problems.
 * 
 * Usage:
 * 1. Run with `npx ts-node scripts/api-health-check.ts`
 * 2. Check the output for any issues
 */

// Use isomorphic-fetch instead of node-fetch for better compatibility
import 'isomorphic-fetch';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Supabase setup
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Please check your .env.local file.');
  process.exit(1);
}

// We'll use Supabase client in future versions of this script for direct DB health checks
// const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Configuration
const API_BASE_URL = 'http://localhost:3000/api';
const API_TIMEOUT_MS = 5000; // 5 seconds timeout for API calls
const MAX_RETRIES = 2;

// Test data
const TEST_COMPANY_ID = process.env.TEST_COMPANY_ID || 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6'; // Use a valid company ID from your database
// We'll use member ID in future versions of this script for member-specific endpoint testing
// const TEST_MEMBER_ID = process.env.TEST_MEMBER_ID || 'aa040cbe-5113-4dd3-8bd6-e23efd9acc52';

// Define response types
interface ApiResponse {
  data?: Record<string, unknown>;
  error?: string;
  [key: string]: unknown;
}

// Helper function to test an API endpoint
async function testEndpoint(
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  body?: Record<string, unknown>,
  queryParams?: Record<string, string>
): Promise<{ success: boolean; message: string; responseTime: number }> {
  const startTime = Date.now();
  let url = `${API_BASE_URL}/${endpoint}`;
  
  // Add query parameters if provided
  if (queryParams) {
    const params = new URLSearchParams();
    Object.entries(queryParams).forEach(([key, value]) => {
      params.append(key, value);
    });
    url += `?${params.toString()}`;
  }
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT_MS);
    
    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: body ? JSON.stringify(body) : undefined,
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;
    
    if (!response.ok) {
      const errorText = await response.text();
      return {
        success: false,
        message: `Failed with status ${response.status}: ${errorText}`,
        responseTime,
      };
    }
    
    const responseData: ApiResponse = await response.json();
    
    // Check for specific error patterns that might indicate infinite loop potential
    if (responseData.error && responseData.error.includes('unrecognized configuration parameter')) {
      return {
        success: false,
        message: `Database configuration error: ${responseData.error}`,
        responseTime,
      };
    }
    
    return {
      success: true,
      message: 'Success',
      responseTime,
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return {
      success: false,
      message: `Error: ${errorMessage}`,
      responseTime,
    };
  }
}

// Define the endpoints to test with proper typing
interface EndpointTest {
  name: string;
  endpoint: string;
  params: Record<string, string>;
}

const endpointsToTest: EndpointTest[] = [
  { name: 'transactions', endpoint: 'transactions', params: { companyId: TEST_COMPANY_ID, limit: '5' } },
  { name: 'members', endpoint: 'members', params: { companyId: TEST_COMPANY_ID } },
  { name: 'members-count', endpoint: 'members/count', params: { companyId: TEST_COMPANY_ID } },
  { name: 'active-members', endpoint: 'active-members', params: { companyId: TEST_COMPANY_ID, days: '30' } },
  { name: 'points-data', endpoint: 'points-data', params: { companyId: TEST_COMPANY_ID, timeRange: '30d' } },
  { name: 'rewards', endpoint: 'rewards', params: { companyId: TEST_COMPANY_ID } },
  { name: 'redemptions', endpoint: 'redemptions', params: { companyId: TEST_COMPANY_ID, limit: '5' } },
  { name: 'receipts', endpoint: 'receipts', params: { companyId: TEST_COMPANY_ID, limit: '5' } },
  { name: 'tiers', endpoint: 'tiers', params: { companyId: TEST_COMPANY_ID } },
];

// Run the tests
async function runTests() {
  console.log('🔍 Starting API Health Check');
  console.log('==========================\n');
  
  const results = [];
  
  for (const { name, endpoint, params } of endpointsToTest) {
    console.log(`Testing endpoint: ${name}`);
    
    let result;
    let retries = 0;
    
    do {
      if (retries > 0) {
        console.log(`  Retry attempt ${retries}/${MAX_RETRIES}`);
      }
      
      result = await testEndpoint(endpoint, 'GET', undefined, params);
      retries++;
      
      if (!result.success && retries <= MAX_RETRIES) {
        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } while (!result.success && retries <= MAX_RETRIES);
    
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`  ${status} (${result.responseTime}ms): ${result.message}`);
    
    results.push({
      endpoint: name,
      success: result.success,
      responseTime: result.responseTime,
      message: result.message,
    });
    
    // Add a small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // Generate summary
  console.log('\n📊 Test Summary');
  console.log('==============');
  
  const passCount = results.filter(r => r.success).length;
  const failCount = results.length - passCount;
  
  console.log(`Total Endpoints: ${results.length}`);
  console.log(`Passed: ${passCount}`);
  console.log(`Failed: ${failCount}`);
  
  if (failCount > 0) {
    console.log('\n❌ Failed Endpoints:');
    results
      .filter(r => !r.success)
      .forEach(r => {
        console.log(`  - ${r.endpoint}: ${r.message}`);
      });
  }
  
  // Save results to a file
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const resultsFile = path.join(process.cwd(), `api-health-check-${timestamp}.json`);
  
  fs.writeFileSync(
    resultsFile,
    JSON.stringify({ timestamp: new Date().toISOString(), results }, null, 2)
  );
  
  console.log(`\nResults saved to: ${resultsFile}`);
}

// Run the tests
runTests().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
