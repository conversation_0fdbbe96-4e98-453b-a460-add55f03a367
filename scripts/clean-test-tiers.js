// Script to clean test tiers from the database
// eslint-disable-next-line @typescript-eslint/no-var-requires
const dotenv = require('dotenv');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Supabase client with service role key to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Fufi's Beauty Services company ID
const FUFI_COMPANY_ID = 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6';

async function cleanTestTiers() {
  try {
    console.log('Cleaning test tiers...');
    
    // Delete tiers with names containing 'test' (case insensitive)
    const { error } = await supabase
      .from('tier_definitions')
      .delete()
      .ilike('tier_name', '%test%')
      .eq('company_id', FUFI_COMPANY_ID);
    
    if (error) {
      console.error('Error cleaning test tiers:', error);
      return;
    }
    
    console.log('Test tiers cleaned successfully');
  } catch (error) {
    console.error('Error in cleanTestTiers:', error);
  }
}

// Run the clean function
cleanTestTiers();
