-- Add OCR data storage columns to points_transactions table
-- This script adds support for storing AI-extracted receipt data

BEGIN;

-- Add OCR data columns to points_transactions table
ALTER TABLE points_transactions
ADD COLUMN IF NOT EXISTS receipt_ocr_data JSONB,
ADD COLUMN IF NOT EXISTS receipt_ocr_confidence DECIMAL(3,2) CHECK (receipt_ocr_confidence >= 0 AND receipt_ocr_confidence <= 1),
ADD COLUMN IF NOT EXISTS receipt_processing_status TEXT DEFAULT 'pending' CHECK (receipt_processing_status IN ('pending', 'processing', 'completed', 'failed')),
ADD COLUMN IF NOT EXISTS receipt_image_url TEXT,
ADD COLUMN IF NOT EXISTS receipt_number TEXT,
ADD COLUMN IF NOT EXISTS business_name TEXT,
ADD COLUMN IF NOT EXISTS total_amount DECIMAL(10,2);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_points_transactions_processing_status ON points_transactions(receipt_processing_status);
CREATE INDEX IF NOT EXISTS idx_points_transactions_ocr_confidence ON points_transactions(receipt_ocr_confidence);
CREATE INDEX IF NOT EXISTS idx_points_transactions_receipt_number ON points_transactions(receipt_number);
CREATE INDEX IF NOT EXISTS idx_points_transactions_business_name ON points_transactions(business_name);

-- Add index on JSONB data for fast querying
CREATE INDEX IF NOT EXISTS idx_points_transactions_ocr_data_gin ON points_transactions USING GIN (receipt_ocr_data);

-- Update existing records to have default processing status
UPDATE points_transactions
SET receipt_processing_status = 'completed'
WHERE receipt_processing_status IS NULL AND receipt_id IS NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN points_transactions.receipt_ocr_data IS 'Raw OCR data extracted from receipt images using AI';
COMMENT ON COLUMN points_transactions.receipt_ocr_confidence IS 'Confidence score (0-1) of OCR extraction accuracy';
COMMENT ON COLUMN points_transactions.receipt_processing_status IS 'Status of receipt processing: pending, processing, completed, failed';
COMMENT ON COLUMN points_transactions.receipt_image_url IS 'URL of uploaded receipt image in Supabase storage';
COMMENT ON COLUMN points_transactions.receipt_number IS 'Receipt number or FS number extracted from receipt';
COMMENT ON COLUMN points_transactions.business_name IS 'Business name extracted from receipt';
COMMENT ON COLUMN points_transactions.total_amount IS 'Total amount from receipt';

COMMIT;
