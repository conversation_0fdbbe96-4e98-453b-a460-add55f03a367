-- ============================================================================
-- RECEIPT DATA EXTRACTION ENHANCEMENT - DATABASE SCHEMA
-- Phase 1: Template-Based OCR with Business Items Master Data
--
-- Date: August 12, 2025
-- Purpose: Add business-specific templates and item-level analytics
-- Target: Supabase PostgreSQL Database
--
-- INSTRUCTIONS:
-- 1. Open Supabase SQL Editor
-- 2. Copy and paste this entire file
-- 3. Execute all commands (they are safe and backwards-compatible)
-- 4. Verify completion with the final SELECT statements
-- ============================================================================

BEGIN;

-- =====================================================
-- STEP 0: Enable Required Extensions
-- =====================================================

-- Enable trigram extension for fuzzy text matching (must be done first)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- =====================================================
-- STEP 1: Receipt Templates Table
-- =====================================================

CREATE TABLE IF NOT EXISTS receipt_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  template_name VARCHAR(255) NOT NULL,
  template_image_url TEXT NOT NULL,
  template_metadata JSONB NOT NULL DEFAULT '{}',
  ai_prompt_context TEXT NOT NULL,
  validation_rules JSONB DEFAULT '{}',
  confidence_threshold DECIMAL(3,2) DEFAULT 0.85,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Performance tracking fields
  total_extractions INTEGER DEFAULT 0,
  successful_extractions INTEGER DEFAULT 0,
  avg_confidence_score DECIMAL(3,2) DEFAULT 0.0
);

-- =====================================================
-- STEP 2: Business Items Master Data Table
-- =====================================================

CREATE TABLE IF NOT EXISTS business_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) NOT NULL,
  item_name TEXT NOT NULL, -- e.g., "Beard Shaving", "Shellac Polish"
  item_code TEXT, -- Optional: internal business code
  standard_price DECIMAL(10,2), -- Standard/most common price
  item_category TEXT NOT NULL, -- Beauty Service, Food, Beverage, Product
  item_subcategory TEXT, -- Nail Service, Grooming, Coffee, etc.
  description TEXT,
  is_active BOOLEAN DEFAULT true,

  -- Analytics fields
  total_sales_count INTEGER DEFAULT 0,
  total_revenue DECIMAL(12,2) DEFAULT 0,
  avg_selling_price DECIMAL(10,2),
  last_sold_date TIMESTAMP WITH TIME ZONE,

  -- AI learning fields
  ai_recognition_patterns TEXT[], -- Common ways AI recognizes this item
  common_variations TEXT[], -- "refill gel", "gel refill", etc.

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  UNIQUE(company_id, item_name) -- Prevent duplicates per business
);

-- =====================================================
-- STEP 3: Receipt Items Linking Table
-- =====================================================

CREATE TABLE IF NOT EXISTS receipt_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  receipt_id UUID REFERENCES receipts(id) ON DELETE CASCADE,
  business_item_id UUID REFERENCES business_items(id), -- Link to master item

  -- Transaction-specific data (can vary from standard price)
  quantity DECIMAL(10,2) DEFAULT 1,
  unit_price DECIMAL(10,2) NOT NULL, -- Actual price paid
  total_price DECIMAL(10,2) NOT NULL, -- quantity × unit_price

  -- AI extraction metadata
  extracted_description TEXT, -- What AI initially extracted
  confidence_score DECIMAL(3,2), -- How confident AI was in matching
  manual_override BOOLEAN DEFAULT false, -- If human corrected the match

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STEP 4: Item Matching Suggestions Table
-- =====================================================

CREATE TABLE IF NOT EXISTS item_matching_suggestions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  receipt_item_id UUID REFERENCES receipt_items(id),
  suggested_business_item_id UUID REFERENCES business_items(id),
  confidence_score DECIMAL(3,2),
  matching_reason TEXT, -- "exact_match", "fuzzy_match", "price_match", etc.
  was_accepted BOOLEAN, -- Did user accept this suggestion?
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STEP 5: Extend Existing Tables
-- =====================================================

-- Add template support to existing tables
ALTER TABLE points_transactions
ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES receipt_templates(id),
ADD COLUMN IF NOT EXISTS item_count INTEGER DEFAULT 0;

-- Add template support to receipts table
ALTER TABLE receipts
ADD COLUMN IF NOT EXISTS template_id UUID REFERENCES receipt_templates(id),
ADD COLUMN IF NOT EXISTS extraction_confidence DECIMAL(3,2),
ADD COLUMN IF NOT EXISTS validation_score DECIMAL(3,2),
ADD COLUMN IF NOT EXISTS pos_system TEXT;

-- =====================================================
-- STEP 6: Create Performance Indexes
-- =====================================================

-- Receipt templates indexes
CREATE INDEX IF NOT EXISTS idx_receipt_templates_company_id ON receipt_templates(company_id);
CREATE INDEX IF NOT EXISTS idx_receipt_templates_active ON receipt_templates(is_active, company_id);

-- Business items indexes
CREATE INDEX IF NOT EXISTS idx_business_items_company_id ON business_items(company_id);
CREATE INDEX IF NOT EXISTS idx_business_items_category ON business_items(item_category, company_id);
CREATE INDEX IF NOT EXISTS idx_business_items_active ON business_items(is_active, company_id);
CREATE INDEX IF NOT EXISTS idx_business_items_name_trgm ON business_items USING gin(item_name gin_trgm_ops);

-- Receipt items indexes
CREATE INDEX IF NOT EXISTS idx_receipt_items_receipt_id ON receipt_items(receipt_id);
CREATE INDEX IF NOT EXISTS idx_receipt_items_business_item_id ON receipt_items(business_item_id);

-- Item matching suggestions indexes
CREATE INDEX IF NOT EXISTS idx_item_matching_suggestions_receipt_item ON item_matching_suggestions(receipt_item_id);

-- =====================================================
-- STEP 7: Seed Data for Addis Beauty Salon
-- =====================================================

-- Insert business items for Addis Beauty Salon based on real receipt data
INSERT INTO business_items (
  company_id,
  item_name,
  standard_price,
  item_category,
  item_subcategory,
  description,
  ai_recognition_patterns,
  common_variations
) VALUES
-- From Receipt 1 & 2 analysis (using actual company ID from database)
('7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Refill gel with shellac', 1200.00, 'Beauty Service', 'Nail Service', 'Nail refill with shellac application', ARRAY['refill gel', 'shellac refill', 'gel with shellac'], ARRAY['refill gel with shellac', 'gel refill shellac', 'shellac gel refill']),

('7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Shellac Polish', 550.00, 'Beauty Service', 'Nail Service', 'Shellac nail polish application', ARRAY['shellac polish', 'nail polish', 'shellac'], ARRAY['shellac polish', 'polish shellac', 'nail shellac']),

('7c4b5389-b630-4d2b-b9b1-9f6460117371', 'Beard Shaving', 450.00, 'Beauty Service', 'Grooming Service', 'Professional beard shaving service', ARRAY['beard shaving', 'shaving', 'beard trim'], ARRAY['beard shaving', 'shaving beard', 'beard cut'])

ON CONFLICT (company_id, item_name) DO NOTHING;

-- =====================================================
-- STEP 8: Create Utility Functions
-- =====================================================

-- Function to match receipt item descriptions to business items
CREATE OR REPLACE FUNCTION match_business_item(
  p_company_id UUID,
  p_description TEXT,
  p_price DECIMAL DEFAULT NULL
) RETURNS TABLE(
  business_item_id UUID,
  item_name TEXT,
  confidence_score DECIMAL,
  matching_reason TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    bi.id as business_item_id,
    bi.item_name,
    CASE
      -- Exact match gets highest score
      WHEN LOWER(bi.item_name) = LOWER(p_description) THEN 1.0
      -- Check variations array
      WHEN LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations))) THEN 0.95
      -- Fuzzy text similarity using trigrams
      WHEN similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.6 THEN similarity(LOWER(bi.item_name), LOWER(p_description))
      -- Price match (if provided and within 10% of standard price)
      WHEN p_price IS NOT NULL AND bi.standard_price IS NOT NULL
           AND ABS(p_price - bi.standard_price) / bi.standard_price <= 0.1 THEN 0.7
      ELSE 0.0
    END::DECIMAL(3,2) as confidence_score,
    CASE
      WHEN LOWER(bi.item_name) = LOWER(p_description) THEN 'exact_match'
      WHEN LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations))) THEN 'variation_match'
      WHEN similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.6 THEN 'fuzzy_match'
      WHEN p_price IS NOT NULL AND bi.standard_price IS NOT NULL
           AND ABS(p_price - bi.standard_price) / bi.standard_price <= 0.1 THEN 'price_match'
      ELSE 'no_match'
    END as matching_reason
  FROM business_items bi
  WHERE bi.company_id = p_company_id
    AND bi.is_active = true
    AND (
      LOWER(bi.item_name) = LOWER(p_description)
      OR LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations)))
      OR similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.3
      OR (p_price IS NOT NULL AND bi.standard_price IS NOT NULL
          AND ABS(p_price - bi.standard_price) / bi.standard_price <= 0.1)
    )
  ORDER BY confidence_score DESC
  LIMIT 5;
END;
$$ LANGUAGE plpgsql;

-- Function to update business item statistics
CREATE OR REPLACE FUNCTION update_business_item_stats(
  p_business_item_id UUID,
  p_sale_price DECIMAL,
  p_quantity DECIMAL DEFAULT 1
) RETURNS VOID AS $$
BEGIN
  UPDATE business_items
  SET
    total_sales_count = total_sales_count + p_quantity::INTEGER,
    total_revenue = total_revenue + (p_sale_price * p_quantity),
    avg_selling_price = (total_revenue + (p_sale_price * p_quantity)) / (total_sales_count + p_quantity::INTEGER),
    last_sold_date = NOW(),
    updated_at = NOW()
  WHERE id = p_business_item_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 9: RLS Policies
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE receipt_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE receipt_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE item_matching_suggestions ENABLE ROW LEVEL SECURITY;

-- Receipt templates policies
CREATE POLICY receipt_templates_company_access ON receipt_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = receipt_templates.company_id
    )
  );

-- Business items policies
CREATE POLICY business_items_company_access ON business_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = business_items.company_id
    )
  );

-- Receipt items policies
CREATE POLICY receipt_items_company_access ON receipt_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM receipts r
      JOIN company_administrators ca ON r.company_id = ca.company_id
      WHERE r.id = receipt_items.receipt_id
      AND ca.administrator_id = auth.uid()
    )
  );

-- Item matching suggestions policies
CREATE POLICY item_matching_suggestions_access ON item_matching_suggestions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM receipt_items ri
      JOIN receipts r ON ri.receipt_id = r.id
      JOIN company_administrators ca ON r.company_id = ca.company_id
      WHERE ri.id = item_matching_suggestions.receipt_item_id
      AND ca.administrator_id = auth.uid()
    )
  );

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check that all tables were created
SELECT 'Tables created successfully' as status,
       COUNT(*) as table_count
FROM information_schema.tables
WHERE table_name IN ('receipt_templates', 'business_items', 'receipt_items', 'item_matching_suggestions')
  AND table_schema = 'public';

-- Check Addis Beauty Salon seed data
SELECT 'Addis Beauty Salon business items created' as status,
       COUNT(*) as item_count,
       STRING_AGG(item_name, ', ') as items
FROM business_items
WHERE company_id = '7c4b5389-b630-4d2b-b9b1-9f6460117371';

-- Test the matching function
SELECT 'Item matching function test' as status;
SELECT * FROM match_business_item(
  '7c4b5389-b630-4d2b-b9b1-9f6460117371',
  'beard shaving',
  450.00
);

-- Show table relationships
SELECT
  tc.table_name,
  kcu.column_name,
  ccu.table_name AS references_table,
  ccu.column_name AS references_column
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_name IN ('receipt_templates', 'business_items', 'receipt_items', 'item_matching_suggestions')
ORDER BY tc.table_name;

SELECT 'Migration completed successfully!' as final_status;
