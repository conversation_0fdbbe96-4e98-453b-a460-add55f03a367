// Seed script for <PERSON><PERSON>'s Beauty Services
// eslint-disable-next-line @typescript-eslint/no-var-requires
const dotenv = require('dotenv');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { createClient } = require('@supabase/supabase-js');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { v4: uuidv4 } = require('uuid');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Supabase client with service role key to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Fufi's Beauty Services company ID
const FUFI_COMPANY_ID = 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6';

// Ethiopian first and last names combined
const ETHIOPIAN_NAMES = [
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 
  '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON> <PERSON>e', '<PERSON>e <PERSON>', '<PERSON>mma Mamo', '<PERSON>u Negash', 
  'N<PERSON>ie <PERSON>esse', '<PERSON>s<PERSON>ye', '<PERSON> <PERSON>olde', '<PERSON>',
  '<PERSON>', '<PERSON>esse <PERSON>', '<PERSON><PERSON> <PERSON>', '<PERSON>',
  '<PERSON>emu', '<PERSON> <PERSON>e', '<PERSON>u <PERSON>', '<PERSON>ta Eshetu',
  'Eden Feleke', 'Frehiwot Girma', 'Genet Hailu', 'Hanna Ibrahim',
  'Iman Kebede', 'Jerusalem Lemma', 'Kidist Mamo', 'Liya Negash',
  'Makeda Tadesse', 'Nardos Tesfaye', 'Rahel Wolde', 'Seble Yilma',
  'Tigist Zewde', 'Yeshi Mekonnen', 'Zema Mengistu'
];

// Beauty salon services with realistic prices and point multipliers
const BEAUTY_SERVICES = [
  { name: 'Haircut', priceRange: [30, 60], pointsMultiplier: 5 },
  { name: 'Hair Coloring', priceRange: [80, 150], pointsMultiplier: 8 },
  { name: 'Manicure', priceRange: [25, 45], pointsMultiplier: 4 },
  { name: 'Pedicure', priceRange: [35, 55], pointsMultiplier: 5 },
  { name: 'Facial Treatment', priceRange: [60, 120], pointsMultiplier: 7 },
  { name: 'Hair Styling', priceRange: [40, 80], pointsMultiplier: 6 },
  { name: 'Full Makeup', priceRange: [70, 150], pointsMultiplier: 8 },
  { name: 'Eyebrow Threading', priceRange: [15, 25], pointsMultiplier: 3 },
  { name: 'Hair Extensions', priceRange: [150, 300], pointsMultiplier: 10 },
  { name: 'Massage Therapy', priceRange: [80, 150], pointsMultiplier: 8 }
];

// Beauty salon rewards
const REWARDS = [
  {
    id: '26b53e7c-4144-4e3c-b810-8ebf37e70081',
    reward_code: 'HAIRCUT',
    title: 'Free Haircut',
    description: 'Redeem for a free haircut service',
    reward_type: 'GENERAL',
    reward_value_type: 'FREE_SERVICE',
    reward_value: 50,
    points_required: 500,
    is_active: true,
    code: 'HC01'
  },
  {
    id: '180b4bfe-b285-4cc1-8b8c-88863dbee6f7',
    reward_code: 'MANICURE',
    title: 'Free Manicure',
    description: 'Redeem for a free manicure service',
    reward_type: 'GENERAL',
    reward_value_type: 'FREE_SERVICE',
    reward_value: 30,
    points_required: 300,
    is_active: true,
    code: 'MN02'
  },
  {
    id: '3a5c9d8e-7f6b-4a2c-9e1d-8b7a6c5d4e3f',
    reward_code: 'COLORING',
    title: 'Half-Price Hair Coloring',
    description: 'Get 50% off any hair coloring service',
    reward_type: 'GENERAL',
    reward_value_type: 'PERCENTAGE',
    reward_value: 50,
    points_required: 400,
    is_active: true,
    code: 'CL03'
  },
  {
    id: '4b6c0e9d-8a7b-5c4d-3e2f-1a9b8c7d6e5f',
    reward_code: 'FACIAL',
    title: 'Free Facial Treatment',
    description: 'Redeem for a complimentary facial treatment',
    reward_type: 'GENERAL',
    reward_value_type: 'FREE_SERVICE',
    reward_value: 60,
    points_required: 600,
    is_active: true,
    code: 'FC04'
  },
  {
    id: '5c7d1e0f-9b8a-6d5e-4f3a-2a0b9c8d7e6a',
    reward_code: 'BIRTHDAY',
    title: 'Birthday Special Package',
    description: 'Special beauty package for your birthday month',
    reward_type: 'BIRTHDAY',
    reward_value_type: 'FIXED_AMOUNT',
    reward_value: 100,
    points_required: 800,
    is_active: true,
    code: 'BD05'
  }
];

// Tier definitions
const TIERS = [
  { 
    id: uuidv4(),
    tier_name: 'Silver', 
    minimum_points: 0,
    benefits_description: 'Basic tier with standard benefits'
  },
  { 
    id: uuidv4(),
    tier_name: 'Gold', 
    minimum_points: 1000,
    benefits_description: 'Mid-tier with enhanced benefits and priority booking'
  },
  { 
    id: uuidv4(),
    tier_name: 'Platinum', 
    minimum_points: 2500,
    benefits_description: 'Premium tier with exclusive benefits, priority booking, and special discounts'
  }
];

// Helper function to generate random transaction dates
function generateTransactionDates(startDate, count) {
  const dates = [];
  const start = new Date(startDate);
  const now = new Date();
  
  // Generate random dates between start date and now
  for (let i = 0; i < count; i++) {
    const randomDate = new Date(start.getTime() + Math.random() * (now.getTime() - start.getTime()));
    dates.push(randomDate);
  }
  
  // Sort dates in ascending order
  return dates.sort((a, b) => a - b);
}

// Helper function to determine tier based on points
function getTierForPoints(points) {
  for (let i = TIERS.length - 1; i >= 0; i--) {
    if (points >= TIERS[i].minimum_points) {
      return TIERS[i].tier_name;
    }
  }
  return TIERS[0].tier_name; // Default to lowest tier
}

// Clean existing data
async function cleanExistingData() {
  console.log('Cleaning existing data...');
  
  try {
    // Delete existing transactions
    await supabase.from('points_transactions').delete().eq('company_id', FUFI_COMPANY_ID);
    
    // Delete existing redemptions
    await supabase.from('reward_redemptions').delete().eq('company_id', FUFI_COMPANY_ID);
    
    // Delete existing members
    await supabase.from('loyalty_members').delete().eq('company_id', FUFI_COMPANY_ID);
    
    // Delete existing rewards
    await supabase.from('rewards').delete().eq('company_id', FUFI_COMPANY_ID);
    
    // Delete test tiers
    await supabase.from('tier_definitions').delete().like('tier_name', 'test_%').eq('company_id', FUFI_COMPANY_ID);
    
    console.log('Data cleaned successfully');
  } catch (error) {
    console.error('Error cleaning data:', error);
  }
}

// Create tier definitions
async function createTiers() {
  console.log('Creating tier definitions for Fufi\'s Beauty Services...');
  
  for (const tier of TIERS) {
    try {
      const { error } = await supabase
        .from('tier_definitions')
        .insert({
          ...tier,
          company_id: FUFI_COMPANY_ID,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error(`Error creating tier ${tier.tier_name}:`, error);
      } else {
        console.log(`Created tier: ${tier.tier_name}`);
      }
    } catch (error) {
      console.error(`Error in createTiers for ${tier.tier_name}:`, error);
    }
  }
}

// Create rewards
async function createRewards() {
  console.log('Creating rewards for Fufi\'s Beauty Services...');
  
  for (const reward of REWARDS) {
    try {
      const now = new Date().toISOString();
      const oneYearFromNow = new Date();
      oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1);
      
      const { error } = await supabase
        .from('rewards')
        .insert({
          ...reward,
          company_id: FUFI_COMPANY_ID,
          additional_details: {
            valid_during_month: true,
            days_after_birthday: 7,
            days_before_birthday: 7
          },
          start_date: now,
          expiration_date: oneYearFromNow.toISOString(),
          created_at: now,
          updated_at: now
        });
      
      if (error) {
        console.error(`Error creating reward ${reward.title}:`, error);
      } else {
        console.log(`Created reward: ${reward.title}`);
      }
    } catch (error) {
      console.error(`Error in createRewards for ${reward.title}:`, error);
    }
  }
}

// Create a member
async function createMember(name) {
  try {
    // Split the name into parts
    const nameParts = name.split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.length > 1 ? nameParts[1] : '';
    
    // Generate email and phone
    const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${Math.floor(Math.random() * 1000)}@example.com`;
    const phone = `+251${Math.floor(Math.random() * 1000000000)}`;
    
    // Generate a random birthday between 18 and 60 years ago
    const now = new Date();
    const minAge = 18;
    const maxAge = 60;
    const yearsAgo = minAge + Math.floor(Math.random() * (maxAge - minAge));
    const birthday = new Date(now.getFullYear() - yearsAgo, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
    
    // Registration date between 1 year ago and 1 month ago
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
    
    const registrationDate = new Date(oneYearAgo.getTime() + Math.random() * (oneMonthAgo.getTime() - oneYearAgo.getTime()));
    
    // Generate a valid loyalty_id in the format F#######
    const loyaltyId = `F${String(Math.floor(Math.random() * 9000000) + 1000000)}`;
    
    const memberData = {
      id: uuidv4(),
      name: name,
      email: email,
      phone_number: phone,
      birthday: birthday.toISOString().split('T')[0],
      registration_date: registrationDate.toISOString(),
      loyalty_id: loyaltyId,
      loyalty_tier: 'Silver', // Default tier
      lifetime_points: 0,
      redeemed_points: 0,
      expired_points: 0,
      company_id: FUFI_COMPANY_ID
    };
    
    const { data, error } = await supabase
      .from('loyalty_members')
      .insert(memberData)
      .select();
    
    if (error) {
      console.error('Error creating member:', error);
      return null;
    }
    
    return data[0];
  } catch (error) {
    console.error('Error in createMember:', error);
    return null;
  }
}

// Create a transaction
async function createTransaction(tx) {
  try {
    // Ensure created_at is set
    const now = new Date().toISOString();
    const transaction = {
      ...tx,
      created_at: tx.created_at || now
    };
    
    const { data, error } = await supabase
      .from('points_transactions')
      .insert(transaction)
      .select();
    
    if (error) {
      console.error('Error creating transaction:', error);
      return null;
    }
    
    return data ? data[0] : null;
  } catch (error) {
    console.error('Error in createTransaction:', error);
    return null;
  }
}

// Create a redemption
async function createRedemption(redemption) {
  try {
    // Ensure created_at is set
    const now = new Date().toISOString();
    const redemptionData = {
      ...redemption,
      created_at: redemption.created_at || now
    };
    
    const { data, error } = await supabase
      .from('reward_redemptions')
      .insert(redemptionData)
      .select();
    
    if (error) {
      console.error('Error creating redemption:', error);
      return null;
    }
    
    return data ? data[0] : null;
  } catch (error) {
    console.error('Error in createRedemption:', error);
    return null;
  }
}

// Update member points
async function updateMemberPoints(memberId, lifetimePoints, redeemedPoints = 0, expiredPoints = 0) {
  try {
    const tier = getTierForPoints(lifetimePoints);
    
    // Update member with all points
    const { data, error } = await supabase
      .from('loyalty_members')
      .update({ 
        lifetime_points: lifetimePoints,
        redeemed_points: redeemedPoints,
        expired_points: expiredPoints,
        loyalty_tier: tier
      })
      .eq('id', memberId)
      .select();
    
    if (error) {
      console.error('Error updating member points:', error);
      return null;
    }
    
    const availablePoints = lifetimePoints - redeemedPoints - expiredPoints;
    console.log(`Member ${memberId}: Available points = ${availablePoints}`);
    
    return data ? data[0] : null;
  } catch (error) {
    console.error('Error in updateMemberPoints:', error);
    return null;
  }
}

// Generate transactions for a member
async function generateTransactionsForMember(member) {
  // Determine how many transactions based on registration date
  const daysSinceRegistration = Math.floor((new Date() - new Date(member.registration_date)) / (1000 * 60 * 60 * 24));
  const transactionCount = Math.min(20, Math.max(3, Math.floor(daysSinceRegistration / 10)));
  
  // Generate transaction dates
  const transactionDates = generateTransactionDates(member.registration_date, transactionCount);
  
  let lifetimePoints = 0;
  let redeemedPoints = 0;
  let expiredPoints = 0;
  
  // Create earn transactions
  for (let i = 0; i < transactionDates.length; i++) {
    // Select a random beauty service
    const service = BEAUTY_SERVICES[Math.floor(Math.random() * BEAUTY_SERVICES.length)];
    const price = Math.floor(service.priceRange[0] + Math.random() * (service.priceRange[1] - service.priceRange[0]));
    
    // Calculate points
    const pointsEarned = Math.floor(price * service.pointsMultiplier);
    lifetimePoints += pointsEarned;
    
    // Set expiration date (1 year from transaction)
    const expirationDate = new Date(transactionDates[i]);
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);
    
    // Create the earn transaction
    const earnTransaction = {
      id: uuidv4(),
      member_id: member.id,
      loyalty_id: member.loyalty_id,
      points_change: pointsEarned,
      transaction_date: transactionDates[i].toISOString(),
      description: `Points earned from ${service.name} service ($${price})`,
      transaction_type: 'EARN',
      expiration_date: expirationDate.toISOString().split('T')[0],
      company_id: FUFI_COMPANY_ID,
      created_at: transactionDates[i].toISOString()
    };
    
    await createTransaction(earnTransaction);
    
    // Occasionally create redemption transactions
    if (i > 0 && i % 3 === 0 && lifetimePoints - redeemedPoints - expiredPoints >= 300) {
      // Pick a random reward
      const reward = REWARDS[Math.floor(Math.random() * REWARDS.length)];
      const pointsToRedeem = reward.points_required;
      
      if (lifetimePoints - redeemedPoints - expiredPoints >= pointsToRedeem) {
        // Create redemption transaction 1-3 days after earning
        const redemptionDate = new Date(transactionDates[i]);
        redemptionDate.setDate(redemptionDate.getDate() + 1 + Math.floor(Math.random() * 3));
        
        // Create redemption record
        const redemptionData = {
          id: uuidv4(),
          reward_id: reward.id,
          member_id: member.id,
          redemption_date: redemptionDate.toISOString(),
          points_used: pointsToRedeem,
          applied_value: pointsToRedeem,
          status: 'REDEEMED',
          notes: `Redeemed for ${reward.title}`,
          company_id: FUFI_COMPANY_ID,
          created_at: redemptionDate.toISOString()
        };
        
        await createRedemption(redemptionData);
        
        // Create corresponding points transaction
        const redeemTransaction = {
          id: uuidv4(),
          member_id: member.id,
          loyalty_id: member.loyalty_id,
          points_change: -pointsToRedeem, // Negative for redemptions
          transaction_date: redemptionDate.toISOString(),
          description: `Redeemed points for ${reward.title}`,
          transaction_type: 'REDEEM',
          expiration_date: '2099-12-31', // Far future date for REDEEM transactions
          company_id: FUFI_COMPANY_ID,
          created_at: redemptionDate.toISOString()
        };
        
        await createTransaction(redeemTransaction);
        
        redeemedPoints += pointsToRedeem;
      }
    }
    
    // Occasionally create expired points for older transactions
    const transactionAge = Math.floor((new Date() - transactionDates[i]) / (1000 * 60 * 60 * 24));
    if (transactionAge > 300 && Math.random() > 0.7) {
      const pointsToExpire = Math.floor(pointsEarned * 0.3); // Expire about 30% of earned points
      expiredPoints += pointsToExpire;
      
      const expirationDate = new Date(transactionDates[i]);
      expirationDate.setFullYear(expirationDate.getFullYear() + 1);
      
      // Create expiration transaction
      const expireTransaction = {
        id: uuidv4(),
        member_id: member.id,
        loyalty_id: member.loyalty_id,
        points_change: -pointsToExpire, // Negative for expirations
        transaction_date: expirationDate.toISOString(),
        description: `Points expired from ${service.name} service`,
        transaction_type: 'EXPIRE',
        expiration_date: '2099-12-31', // Far future date for EXPIRE transactions
        company_id: FUFI_COMPANY_ID,
        created_at: expirationDate.toISOString()
      };
      
      await createTransaction(expireTransaction);
    }
  }
  
  // Update member with final points
  await updateMemberPoints(member.id, lifetimePoints, redeemedPoints, expiredPoints);
  
  return { lifetimePoints, redeemedPoints, expiredPoints };
}

// Main function to seed the database
async function seedDatabase() {
  try {
    // Clean existing data
    await cleanExistingData();
    
    // Create tier definitions
    await createTiers();
    
    // Create rewards
    await createRewards();
    
    // Create 20 Ethiopian loyalty members
    console.log('Creating 20 loyalty members for Fufi\'s Beauty Services...');
    
    // Shuffle the names array to get random names
    const shuffledNames = [...ETHIOPIAN_NAMES].sort(() => 0.5 - Math.random());
    const selectedNames = shuffledNames.slice(0, 20);
    
    const members = [];
    
    for (let i = 0; i < selectedNames.length; i++) {
      const name = selectedNames[i];
      const member = await createMember(name);
      
      if (member) {
        console.log(`Created member ${i + 1}/20: ${name}`);
        members.push(member);
        
        // Generate transactions for this member
        const { lifetimePoints, redeemedPoints, expiredPoints } = await generateTransactionsForMember(member);
        console.log(`  - Generated transactions: ${lifetimePoints} lifetime points, ${redeemedPoints} redeemed points, ${expiredPoints} expired points`);
      }
    }
    
    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
}

// Run the seed function
seedDatabase();
