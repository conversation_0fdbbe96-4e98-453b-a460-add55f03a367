// <PERSON>ript to send notifications for points about to expire
// eslint-disable-next-line @typescript-eslint/no-var-requires
const dotenv = require('dotenv');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { createClient } = require('@supabase/supabase-js');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const { v4: uuidv4 } = require('uuid');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Supabase client with service role key to bypass RLS
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Company ID for Fufi's Beauty Services
const FUFI_COMPANY_ID = 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6';

// Days before expiration to send notification
const NOTIFICATION_DAYS = 7;

/**
 * Send notifications for points that are about to expire
 */
async function sendExpiryNotifications() {
  try {
    console.log('Checking for points about to expire...');
    
    // Calculate the date range for points about to expire
    const today = new Date();
    const expiryDate = new Date();
    expiryDate.setDate(today.getDate() + NOTIFICATION_DAYS);
    
    const todayStr = today.toISOString().split('T')[0];
    const expiryDateStr = expiryDate.toISOString().split('T')[0];
    
    console.log(`Looking for points expiring between ${todayStr} and ${expiryDateStr}`);
    
    // Find transactions with points expiring soon
    const { data: transactions, error: transactionsError } = await supabase
      .from('points_transactions')
      .select('member_id, points_change, expiration_date')
      .eq('transaction_type', 'EARN')
      .gte('expiration_date', todayStr)
      .lte('expiration_date', expiryDateStr)
      .eq('company_id', FUFI_COMPANY_ID);
    
    if (transactionsError) {
      console.error('Error fetching expiring transactions:', transactionsError);
      return;
    }
    
    if (!transactions || transactions.length === 0) {
      console.log('No points found that are about to expire');
      return;
    }
    
    console.log(`Found ${transactions.length} transactions with points about to expire`);
    
    // Process transactions manually to group by member
    const memberPoints = {};
    
    transactions.forEach(tx => {
      if (!memberPoints[tx.member_id]) {
        memberPoints[tx.member_id] = {
          points: 0,
          expiration_date: tx.expiration_date
        };
      }
      
      memberPoints[tx.member_id].points += tx.points_change;
    });
    
    // Convert to array format
    const expiringPoints = Object.entries(memberPoints)
      .filter(([, data]) => data.points > 0) // Only include positive points
      .map(([member_id, data]) => ({
        member_id,
        sum: data.points,
        expiration_date: data.expiration_date
      }));
    
    if (expiringPoints.length === 0) {
      console.log('No members found with positive points about to expire');
      return;
    }
    
    console.log(`Found ${expiringPoints.length} members with points about to expire`);
    
    // Get member details
    const memberIds = expiringPoints.map(item => item.member_id);
    
    const { data: members, error: membersError } = await supabase
      .from('loyalty_members')
      .select('id, name, email, phone_number, telegram_chat_id')
      .in('id', memberIds);
    
    if (membersError) {
      console.error('Error fetching member details:', membersError);
      return;
    }
    
    // Create a map of member details for easy lookup
    const memberMap = {};
    members.forEach(member => {
      memberMap[member.id] = member;
    });
    
    // Process each member with expiring points
    for (const item of expiringPoints) {
      const member = memberMap[item.member_id];
      if (!member) continue;
      
      const pointsExpiring = Math.round(item.sum);
      const expiryDate = new Date(item.expiration_date).toLocaleDateString();
      
      console.log(`Member ${member.name} has ${pointsExpiring} points expiring on ${expiryDate}`);
      
      // Check if notifications table exists
      try {
        // Create notification in the database
        const { error: notificationError } = await supabase
          .from('notifications')
          .insert({
            id: uuidv4(),
            member_id: member.id,
            company_id: FUFI_COMPANY_ID,
            notification_type: 'POINTS_EXPIRY',
            title: 'Points Expiring Soon',
            message: `You have ${pointsExpiring} points expiring on ${expiryDate}. Visit us soon to use them!`,
            status: 'PENDING',
            created_at: new Date().toISOString()
          });
        
        if (notificationError) {
          // If the notifications table doesn't exist, log the error but continue
          console.error('Error creating notification:', notificationError);
          console.log('Would send notification to:', {
            name: member.name,
            email: member.email,
            phone: member.phone_number,
            telegram: member.telegram_chat_id,
            message: `You have ${pointsExpiring} points expiring on ${expiryDate}. Visit us soon to use them!`
          });
        } else {
          console.log(`Created notification for ${member.name}`);
        }
      } catch (error) {
        // If notifications table doesn't exist, simulate sending notification
        console.log('Notifications table may not exist. Would send notification to:', {
          name: member.name,
          email: member.email,
          phone: member.phone_number,
          telegram: member.telegram_chat_id,
          message: `You have ${pointsExpiring} points expiring on ${expiryDate}. Visit us soon to use them!`
        });
      }
    }
    
    // Log the notification job execution
    try {
      const { error: logEntryError } = await supabase
        .from('job_execution_log')
        .insert({
          job_name: 'points_expiry_notifications',
          status: 'SUCCESS',
          affected_rows: expiringPoints.length,
          execution_time_ms: 0,
          company_id: FUFI_COMPANY_ID
        });
      
      if (logEntryError) {
        console.error('Error creating job execution log entry:', logEntryError);
      } else {
        console.log(`Logged notification job for ${expiringPoints.length} members`);
      }
    } catch (logError) {
      console.error('Error logging job execution:', logError);
    }
    
  } catch (error) {
    console.error('Error in sendExpiryNotifications:', error);
  }
}

// Run the notification sender
sendExpiryNotifications();
