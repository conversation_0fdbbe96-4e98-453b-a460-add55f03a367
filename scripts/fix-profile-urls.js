require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

// Use your Supabase URL and service role key
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixProfileUrls() {
  try {
    // First get the list of actual files in the profile-images folder
    const { data: files, error: filesError } = await supabase.storage
      .from('fufis')
      .list('profile-images')

    if (filesError) {
      console.error('Error getting profile-images files:', filesError)
      return
    }

    console.log(`\n===== Found ${files?.length || 0} profile image files in storage =====`)
    files.forEach(file => console.log(`- ${file.name}`))
    console.log('=====\n')

    // Now get all members with profile images to debug
    const { data: allMembersWithImages, error: debugFetchError } = await supabase
      .from('loyalty_members')
      .select('id, name, profile_image_url')
      .not('profile_image_url', 'is', null)

    if (debugFetchError) {
      console.error('Error fetching all members with images:', debugFetchError)
    } else {
      console.log(`\n===== DEBUG INFO: All members with profile images (${allMembersWithImages?.length || 0}) =====`)
      for (const member of allMembersWithImages || []) {
        console.log(`${member.name}: ${member.profile_image_url}`)

        // Try to assign the most recent file from storage to this member
        if (files && files.length > 0) {
          const newestFile = files[0]; // Get the most recent file (they're sorted by date)
          const fileUrl = `https://vqltspteqqllvhyiupkf.supabase.co/storage/v1/object/public/fufis/profile-images/${newestFile.name}`;

          console.log(`Updating ${member.name} with newest file: ${fileUrl}`);

          const { error: updateError } = await supabase
            .from('loyalty_members')
            .update({ profile_image_url: fileUrl })
            .eq('id', member.id);

          if (updateError) {
            console.error(`Failed to update ${member.name}:`, updateError);
          } else {
            console.log(`✅ Successfully updated ${member.name} with working image URL`);
          }
        }
      }      console.log('=====\n')
    }

    // Now verify the updated images are accessible with a fetch request
    console.log('\n===== VERIFYING IMAGE ACCESS =====')
    const { data: updatedMembers } = await supabase
      .from('loyalty_members')
      .select('id, name, profile_image_url')
      .not('profile_image_url', 'is', null)

    // Make a simple HTTP request to verify the image is accessible
    if (updatedMembers) {
      for (const member of updatedMembers) {
        try {
          const response = await fetch(member.profile_image_url);
          if (response.ok) {
            console.log(`✅ Image accessible for ${member.name}: Status ${response.status}`);
          } else {
            console.log(`❌ Image NOT accessible for ${member.name}: Status ${response.status}`);
          }
        } catch (error) {
          console.log(`❌ Error checking image for ${member.name}:`, error.message);
        }
      }
    }
    console.log('=====\n')

    // Original logic for fixing URLs (now obsolete but keeping for reference)
    const { data: members, error: fetchError } = await supabase
      .from('loyalty_members')
      .select('id, name, profile_image_url')
      .like('profile_image_url', '%/receipts/%')

    if (fetchError) {
      console.error('Error fetching members:', fetchError)
      return
    }

    console.log(`Found ${members?.length || 0} members with wrong URLs`)

    // Update each member
    for (const member of members || []) {
      if (member.profile_image_url) {
        const correctedUrl = member.profile_image_url.replace('/receipts/', '/profile-images/')

        const { error: updateError } = await supabase
          .from('loyalty_members')
          .update({ profile_image_url: correctedUrl })
          .eq('id', member.id)

        if (updateError) {
          console.error(`Error updating ${member.name}:`, updateError)
        } else {
          console.log(`✓ Updated ${member.name}`)
          console.log(`  Old: ${member.profile_image_url}`)
          console.log(`  New: ${correctedUrl}`)
        }
      }
    }
  } catch (error) {
    console.error('Script error:', error)
  }
}

fixProfileUrls()
