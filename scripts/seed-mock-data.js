#!/usr/bin/env node

/**
 * Seed script for Loyal app to create realistic beauty salon mock data
 * 
 * This script will generate:
 * - 20 realistic loyalty members for Fufi's Beauty Services
 * - Points transactions for each member (earning from services, redeeming for rewards)
 * - Reward redemptions for appropriate members
 * 
 * Usage: node scripts/seed-mock-data.js
 */

const { createClient } = require('@supabase/supabase-js');
const { v4: uuidv4 } = require('uuid');
const { faker } = require('@faker-js/faker');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase client with service role key (for bypassing RLS)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Fufi's Beauty Services company ID (from database inspection)
const FUFI_COMPANY_ID = 'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6';

// Tier IDs (from database inspection)
const TIERS = {
  SILVER: '3c60ee48-48ac-46fa-a22d-a0224ee5650f',  // 0 points
  GOLD: 'd7521d73-86a0-4903-927b-62b7ecd9595c',    // 1000 points
  PLATINUM: 'cd212414-9259-4955-a5ae-8c80087552eb' // 5000 points
};

// Beauty service types for transaction descriptions
const BEAUTY_SERVICES = [
  { name: 'Haircut & Style', priceRange: [50, 100], pointsMultiplier: 1 },
  { name: 'Hair Coloring', priceRange: [80, 200], pointsMultiplier: 1.2 },
  { name: 'Highlights', priceRange: [120, 250], pointsMultiplier: 1.3 },
  { name: 'Natural Hair Braiding', priceRange: [150, 300], pointsMultiplier: 1.5 },
  { name: 'Ethiopian Traditional Hairstyle', priceRange: [100, 200], pointsMultiplier: 1.4 },
  { name: 'Dreadlocks Installation', priceRange: [200, 350], pointsMultiplier: 1.5 },
  { name: 'Manicure', priceRange: [30, 60], pointsMultiplier: 1 },
  { name: 'Pedicure', priceRange: [40, 70], pointsMultiplier: 1 },
  { name: 'Facial Treatment', priceRange: [60, 150], pointsMultiplier: 1.2 },
  { name: 'Full Makeup', priceRange: [80, 150], pointsMultiplier: 1.2 },
  { name: 'Bridal Makeup & Hair', priceRange: [250, 500], pointsMultiplier: 2 },
  { name: 'Eyebrow Shaping', priceRange: [20, 40], pointsMultiplier: 0.8 },
  { name: 'Eyelash Extensions', priceRange: [100, 200], pointsMultiplier: 1.3 },
  { name: 'Waxing Service', priceRange: [30, 80], pointsMultiplier: 1 },
  { name: 'Massage Therapy', priceRange: [70, 150], pointsMultiplier: 1.2 },
  { name: 'Hair Treatment', priceRange: [50, 120], pointsMultiplier: 1.1 },
  { name: 'Hair Extensions', priceRange: [150, 300], pointsMultiplier: 1.5 }
];

// Ethiopian names for more realistic data
const ETHIOPIAN_FIRST_NAMES = [
  'Abeba', 'Alemu', 'Almaz', 'Amanuel', 'Ayana', 'Bekele', 'Bethlehem', 'Biruk',
  'Dagmawi', 'Dawit', 'Eden', 'Eleni', 'Eshetu', 'Eyerusalem', 'Fasika', 'Fekadu',
  'Feven', 'Frehiwot', 'Getachew', 'Hanna', 'Haile', 'Helen', 'Henok', 'Kidist',
  'Konjit', 'Leul', 'Makeda', 'Melkamu', 'Meseret', 'Meron', 'Nardos', 'Rahel',
  'Samuel', 'Sara', 'Selam', 'Solomon', 'Tadesse', 'Tewodros', 'Tigist', 'Yared',
  'Yohannes', 'Yonas', 'Zelalem', 'Zewdu', 'Zufan'
];

const ETHIOPIAN_LAST_NAMES = [
  'Abebe', 'Alemu', 'Assefa', 'Bekele', 'Demissie', 'Desta', 'Endalkachew', 'Feleke',
  'Gebre', 'Gebrehiwot', 'Hailu', 'Kebede', 'Mamo', 'Mekonnen', 'Mengistu', 'Mulatu',
  'Negash', 'Tadesse', 'Tekle', 'Tesfaye', 'Wolde', 'Yilma', 'Zewde'
];

// Beauty salon rewards
const REWARDS = [
  {
    id: '26b53e7c-4144-4e3c-b810-8ebf37e70081',
    title: 'Free Haircut',
    points: 500,
    description: 'Redeem for a free haircut service'
  },
  {
    id: '180b4bfe-b285-4cc1-8b8c-88863dbee6f7',
    title: 'Free Manicure',
    points: 300,
    description: 'Redeem for a free manicure service'
  },
  {
    id: '3a5c9d8e-7f6b-4a2c-9e1d-8b7a6c5d4e3f',
    title: 'Half-Price Hair Coloring',
    points: 400,
    description: 'Get 50% off any hair coloring service'
  },
  {
    id: '4b6c0e9d-8a7b-5c4d-3e2f-1a9b8c7d6e5f',
    title: 'Free Facial Treatment',
    points: 600,
    description: 'Redeem for a complimentary facial treatment'
  },
  {
    id: '5c7d1e0f-9b8a-6d5e-4f3a-2a0b9c8d7e6a',
    title: 'Birthday Special Package',
    points: 800,
    description: 'Special beauty package for your birthday month'
  }
];

// Generate a unique loyalty ID for Fufi (F prefix + 7 digits)
function generateLoyaltyId() {
  return 'F' + Math.floor(1000000 + Math.random() * 9000000).toString();
}

// Determine tier based on points
function getTierForPoints(points) {
  if (points >= 5000) return TIERS.PLATINUM;
  if (points >= 1000) return TIERS.GOLD;
  return TIERS.SILVER;
}

// Generate random date between start and end dates
function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Generate transaction date that makes sense (older members have older transactions)
function generateTransactionDates(registrationDate, count) {
  const now = new Date();
  const dates = [];
  
  // Ensure we spread transactions between registration date and now
  const timeSpan = now.getTime() - new Date(registrationDate).getTime();
  const timeIncrement = timeSpan / (count + 1);
  
  for (let i = 1; i <= count; i++) {
    const transactionTime = new Date(registrationDate).getTime() + (timeIncrement * i);
    dates.push(new Date(transactionTime));
  }
  
  // Add some randomness so transactions aren't perfectly spaced
  return dates.map(date => {
    const randomOffset = Math.random() * timeIncrement * 0.6 - (timeIncrement * 0.3);
    return new Date(date.getTime() + randomOffset);
  }).sort((a, b) => a - b);
}

// Clean existing data
async function cleanExistingData() {
  console.log('Cleaning existing data...');
  
  // Clean points transactions
  const { error: txError } = await supabase
    .from('points_transactions')
    .delete()
    .eq('company_id', FUFI_COMPANY_ID);
  
  if (txError) console.error('Error cleaning transactions:', txError);
  
  // Clean reward redemptions
  const { error: redError } = await supabase
    .from('reward_redemptions')
    .delete()
    .eq('company_id', FUFI_COMPANY_ID);
  
  if (redError) console.error('Error cleaning redemptions:', redError);
  
  // Clean members
  const { error: memberError } = await supabase
    .from('loyalty_members')
    .delete()
    .eq('company_id', FUFI_COMPANY_ID);
  
  if (memberError) console.error('Error cleaning members:', memberError);
  
  console.log('Data cleaned successfully');
}

// Create a loyalty member
async function createMember(memberData) {
  const { data, error } = await supabase
    .from('loyalty_members')
    .insert([memberData])
    .select();
  
  if (error) {
    console.error('Error creating member:', error);
    return null;
  }
  
  return data[0];
}

// Create a points transaction
async function createTransaction(transactionData) {
  try {
    // Create a clean data object without modifying the original
    const cleanData = { ...transactionData };
    
    // Ensure expiration_date is not null for EARN transactions
    if (cleanData.transaction_type === 'EARN' && !cleanData.expiration_date) {
      // Set default expiration to 1 year from transaction date
      const txDate = new Date(cleanData.transaction_date);
      cleanData.expiration_date = new Date(txDate.getFullYear() + 1, txDate.getMonth(), txDate.getDate()).toISOString().split('T')[0];
    }
    
    // For REDEEM and EXPIRE transactions, set a dummy expiration date if needed
    if ((cleanData.transaction_type === 'REDEEM' || cleanData.transaction_type === 'EXPIRE') && !cleanData.expiration_date) {
      cleanData.expiration_date = '2099-12-31'; // Far future date
    }
    
    // Use raw SQL to bypass the trigger issue
    const { data, error } = await supabase.rpc('insert_transaction', {
      tx_id: cleanData.id,
      tx_member_id: cleanData.member_id,
      tx_loyalty_id: cleanData.loyalty_id,
      tx_points_change: cleanData.points_change,
      tx_transaction_date: cleanData.transaction_date,
      tx_description: cleanData.description,
      tx_transaction_type: cleanData.transaction_type,
      tx_expiration_date: cleanData.expiration_date,
      tx_company_id: cleanData.company_id,
      tx_created_at: cleanData.created_at
    });
    
    if (error) {
      // Fall back to direct insert if RPC fails
      console.log('Falling back to direct insert for transaction');
      const { data: insertData, error: insertError } = await supabase
        .from('points_transactions')
        .insert({
          ...cleanData,
          updated_at: cleanData.created_at // Explicitly set updated_at
        })
        .select()
        .single();
      
      if (insertError) {
        console.error('Error creating transaction:', insertError);
        return null;
      }
      
      return insertData;
    }
    
    return data;
  } catch (error) {
    console.error('Error in createTransaction:', error);
    return null;
  }
}

// Create a reward redemption
async function createRedemption(redemptionData) {
  try {
    // Create a clean data object without modifying the original
    const cleanData = { ...redemptionData };
    
    // Fix the reward ID for the Birthday Special Package if it's using the old invalid format
    if (cleanData.reward_id === '5c7d1e0f-9b8a-6d5e-4f3g-2a0b9c8d7e6a' || 
        cleanData.reward_id === '5c7d1e0f-9b8a-6d5e-4f3g-2a0b9c8d7e6f') {
      // Use a valid UUID format
      cleanData.reward_id = '5c7d1e0f-9b8a-6d5e-4f3a-2a0b9c8d7e6a';
    }
    
    // Ensure company_id is set correctly
    if (!cleanData.company_id) {
      cleanData.company_id = FUFI_COMPANY_ID;
    }
    
    // First check if the reward exists
    const { error: rewardError } = await supabase
      .from('rewards')
      .select('*')
      .eq('id', cleanData.reward_id)
      .single();
    
    if (rewardError) {
      console.error('Error fetching reward:', rewardError);
      
      // Create the reward if it doesn't exist
      const matchingReward = REWARDS.find(r => r.id === cleanData.reward_id);
      if (matchingReward) {
        const { error: newRewardError } = await supabase
          .from('rewards')
          .insert({
            id: matchingReward.id,
            title: matchingReward.title,
            description: matchingReward.description,
            points: matchingReward.points,
            company_id: FUFI_COMPANY_ID,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();
        
        if (newRewardError) {
          console.error('Error creating reward:', newRewardError);
          return null;
        }
      } else {
        console.error('Reward not found in REWARDS array');
        return null;
      }
    }
    
    // Use direct insert with updated_at explicitly set
    const { data, error } = await supabase
      .from('reward_redemptions')
      .insert({
        ...cleanData,
        updated_at: cleanData.created_at // Explicitly set updated_at
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating redemption:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in createRedemption:', error);
    return null;
  }
}

// Update member points after transactions
async function updateMemberPoints(memberId, lifetimePoints, redeemedPoints, expiredPoints) {
  try {
    // Calculate available points (for logging purposes)
    const availablePoints = lifetimePoints - redeemedPoints - expiredPoints;
    console.log(`Member ${memberId}: Available points = ${availablePoints}`);
    
    // Determine tier based on lifetime points
    const tier = getTierForPoints(lifetimePoints);
    
    // First check if the member exists and get current data
    const { data: memberData, error: memberError } = await supabase
      .from('loyalty_members')
      .select('*')
      .eq('id', memberId)
      .single();
    
    if (memberError) {
      console.error('Error fetching member:', memberError);
      return null;
    }
    
    // Update only fields that exist in the table
    const updateData = {
      lifetime_points: lifetimePoints,
      loyalty_tier: tier,
      updated_at: new Date().toISOString() // Explicitly set updated_at
    };
    
    // Only add these fields if they exist in the table schema
    if ('redeemed_points' in memberData) {
      updateData.redeemed_points = redeemedPoints;
    }
    
    if ('expired_points' in memberData) {
      updateData.expired_points = expiredPoints;
    }
    
    // If available_points exists in the schema, update it
    if ('available_points' in memberData) {
      updateData.available_points = availablePoints;
    }
    
    const { data, error } = await supabase
      .from('loyalty_members')
      .update(updateData)
      .eq('id', memberId)
      .select();
    
    if (error) {
      console.error('Error updating member points:', error);
      return null;
    }
    
    return data[0];
  } catch (error) {
    console.error('Error in updateMemberPoints:', error);
    return null;
  }
}

// Generate transactions for a member
async function generateTransactionsForMember(member) {
  // Determine how many transactions based on registration date
  // Older members have more transactions
  const daysSinceRegistration = Math.floor((new Date() - new Date(member.registration_date)) / (1000 * 60 * 60 * 24));
  const transactionCount = Math.min(30, Math.max(5, Math.floor(daysSinceRegistration / 7)));
  
  // Generate transaction dates
  const transactionDates = generateTransactionDates(member.registration_date, transactionCount);
  
  let lifetimePoints = 0;
  let redeemedPoints = 0;
  let expiredPoints = 0;
  
  // Create earn transactions
  for (let i = 0; i < transactionDates.length; i++) {
    // Select a random beauty service
    const service = BEAUTY_SERVICES[Math.floor(Math.random() * BEAUTY_SERVICES.length)];
    const price = Math.floor(service.priceRange[0] + Math.random() * (service.priceRange[1] - service.priceRange[0]));
    
    // Calculate points (using service-specific multiplier)
    const pointsEarned = Math.floor(price * service.pointsMultiplier);
    lifetimePoints += pointsEarned;
    
    // Set expiration date (1 year from transaction)
    const expirationDate = new Date(transactionDates[i]);
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);
    
    // Create the earn transaction
    const earnTransaction = {
      id: uuidv4(),
      member_id: member.id,
      loyalty_id: member.loyalty_id,
      points_change: pointsEarned,
      transaction_date: transactionDates[i].toISOString(),
      description: `Points earned from ${service.name} service ($${price})`,
      transaction_type: 'EARN',
      expiration_date: expirationDate.toISOString().split('T')[0],
      company_id: FUFI_COMPANY_ID,
      created_at: transactionDates[i].toISOString()
    };
    
    await createTransaction(earnTransaction);
    
    // Occasionally create redemption transactions (about 1 for every 3-5 earn transactions)
    if (i > 0 && i % (3 + Math.floor(Math.random() * 3)) === 0 && lifetimePoints - redeemedPoints - expiredPoints >= 100) {
      // Pick a random reward
      const reward = REWARDS[Math.floor(Math.random() * REWARDS.length)];
      const pointsToRedeem = reward.points;
      
      if (lifetimePoints - redeemedPoints - expiredPoints >= pointsToRedeem) {
        // Create redemption transaction 1-3 days after earning
        const redemptionDate = new Date(transactionDates[i]);
        redemptionDate.setDate(redemptionDate.getDate() + Math.floor(1 + Math.random() * 3));
        
        // Create redemption record
        const redemptionData = {
          id: uuidv4(),
          reward_id: reward.id,
          member_id: member.id,
          redemption_date: redemptionDate.toISOString(),
          points_used: pointsToRedeem,
          applied_value: pointsToRedeem,
          status: 'REDEEMED',
          notes: `Redeemed for ${reward.title}`,
          company_id: FUFI_COMPANY_ID,
          created_at: redemptionDate.toISOString()
        };
        
        await createRedemption(redemptionData);
        
        // Create corresponding points transaction
        const redeemTransaction = {
          id: uuidv4(),
          member_id: member.id,
          loyalty_id: member.loyalty_id,
          points_change: -pointsToRedeem, // Make sure it's negative for REDEEM
          transaction_date: redemptionDate.toISOString(),
          description: `Redeemed points for ${reward.title}`,
          transaction_type: 'REDEEM',
          expiration_date: '2099-12-31', // Far future date for REDEEM transactions
          company_id: FUFI_COMPANY_ID,
          created_at: redemptionDate.toISOString()
        };
        
        await createTransaction(redeemTransaction);
        
        redeemedPoints += pointsToRedeem;
      }
    }
    
    // Occasionally create expired points (for older transactions)
    const transactionAge = Math.floor((new Date() - transactionDates[i]) / (1000 * 60 * 60 * 24));
    if (transactionAge > 300 && Math.random() > 0.7) {
      const pointsToExpire = Math.floor(pointsEarned * 0.3); // Expire about 30% of earned points
      expiredPoints += pointsToExpire;
      
      const expirationDate = new Date(transactionDates[i]);
      expirationDate.setDate(expirationDate.getDate() + 365); // Points expire after 1 year
      
      // Create expiration transaction
      const expireTransaction = {
        id: uuidv4(),
        member_id: member.id,
        loyalty_id: member.loyalty_id,
        points_change: -pointsToExpire, // Make sure it's negative for EXPIRE
        transaction_date: expirationDate.toISOString(),
        description: `Points expired from ${service.name} service`,
        transaction_type: 'EXPIRE',
        expiration_date: '2099-12-31', // Far future date for EXPIRE transactions
        company_id: FUFI_COMPANY_ID,
        created_at: expirationDate.toISOString()
      };
      
      await createTransaction(expireTransaction);
    }
  }
  
  // Update member with final points
  await updateMemberPoints(member.id, lifetimePoints, redeemedPoints, expiredPoints);
  
  return { lifetimePoints, redeemedPoints, expiredPoints };
}

// Main function to seed the database
async function seedDatabase() {
  try {
    // Clean existing data
    await cleanExistingData();
    
    console.log('Creating 20 loyalty members for Fufi\'s Beauty Services...');
    
    // Create 20 members with varied registration dates
    const now = new Date();
    const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
    
    for (let i = 1; i <= 20; i++) {
      // Create more older members than newer ones
      let registrationDate;
      if (i <= 5) {
        // First 5 members registered 9-12 months ago
        registrationDate = randomDate(
          new Date(now.getFullYear(), now.getMonth() - 12, now.getDate()),
          new Date(now.getFullYear(), now.getMonth() - 9, now.getDate())
        );
      } else if (i <= 12) {
        // Next 7 members registered 3-9 months ago
        registrationDate = randomDate(
          new Date(now.getFullYear(), now.getMonth() - 9, now.getDate()),
          new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
        );
      } else {
        // Last 8 members registered in the last 3 months
        registrationDate = randomDate(
          new Date(now.getFullYear(), now.getMonth() - 3, now.getDate()),
          now
        );
      }
      
      // Generate Ethiopian names for more realistic data
      const firstName = ETHIOPIAN_FIRST_NAMES[Math.floor(Math.random() * ETHIOPIAN_FIRST_NAMES.length)];
      const lastName = ETHIOPIAN_LAST_NAMES[Math.floor(Math.random() * ETHIOPIAN_LAST_NAMES.length)];
      const name = `${firstName} ${lastName}`;
      
      // Create member
      const memberData = {
        id: uuidv4(),
        name: name,
        phone_number: faker.phone.number('+251#########'),
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${Math.floor(Math.random() * 100)}@gmail.com`,
        birthday: faker.date.birthdate({ min: 18, max: 70, mode: 'age' }).toISOString(),
        registration_date: registrationDate.toISOString(),
        telegram_chat_id: Math.random() > 0.7 ? Math.floor(********* + Math.random() * *********).toString() : null,
        loyalty_id: generateLoyaltyId(),
        loyalty_tier: TIERS.SILVER, // Default tier, will be updated after transactions
        lifetime_points: 0,
        redeemed_points: 0,
        expired_points: 0,
        company_id: FUFI_COMPANY_ID
      };
      
      const member = await createMember(memberData);
      
      if (member) {
        console.log(`Created member ${i}/20: ${member.name}`);
        
        // Generate transactions for this member
        const { lifetimePoints, redeemedPoints, expiredPoints } = await generateTransactionsForMember(member);
        console.log(`  - Generated transactions: ${lifetimePoints} lifetime points, ${redeemedPoints} redeemed points, ${expiredPoints} expired points`);
      }
    }
    
    console.log('Database seeding completed successfully!');
    
  } catch (error) {
    console.error('Error seeding database:', error);
  }
}

// Run the seeding function
seedDatabase().catch(console.error);
