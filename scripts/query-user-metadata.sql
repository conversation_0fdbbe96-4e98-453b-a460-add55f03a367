-- Query to view user metadata
-- Run this in Supabase SQL Editor or any PostgreSQL client

-- All users with their metadata
SELECT
  id,
  email,
  created_at,
  raw_user_meta_data->>'company_name' as company_name,
  raw_user_meta_data->>'business_type' as business_type,
  raw_user_meta_data
FROM auth.users
ORDER BY created_at DESC;

-- Specific user metadata
SELECT
  id,
  email,
  created_at,
  raw_user_meta_data,
  raw_app_meta_data
FROM auth.users
WHERE email = '<EMAIL>';

-- Combined view: User metadata + Company data
SELECT
  u.id as user_id,
  u.email,
  u.created_at as user_created,
  u.raw_user_meta_data->>'company_name' as metadata_company_name,
  u.raw_user_meta_data->>'business_type' as metadata_business_type,
  c.id as company_id,
  c.name as actual_company_name,
  c.business_type as actual_business_type,
  c.created_at as company_created
FROM auth.users u
LEFT JOIN companies c ON c.administrator_id = u.id
WHERE u.email = '<EMAIL>';
