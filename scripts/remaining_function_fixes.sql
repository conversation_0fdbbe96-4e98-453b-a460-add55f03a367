-- Fix for remaining functions with function_search_path_mutable warnings

-- 1. Fix get_dashboard_data function
CREATE OR REPLACE FUNCTION public.get_dashboard_data(p_company_id uuid)
RETURNS TABLE(
  total_members integer, 
  active_members_30d integer, 
  total_lifetime_points integer, 
  total_redeemed_points integer, 
  total_available_points integer, 
  total_rewards integer, 
  active_rewards integer, 
  redemption_rate_percentage numeric, 
  members_growth_rate numeric, 
  points_growth_rate numeric, 
  rewards_growth_rate numeric, 
  redemption_growth_rate numeric, 
  last_updated timestamp with time zone, 
  has_historical_data boolean
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  current_data dashboard_metrics%ROWTYPE;
  historical_data dashboard_metrics_history%ROWTYPE;
BEGIN
  -- Get current metrics
  SELECT * INTO current_data
  FROM dashboard_metrics
  WHERE company_id = p_company_id;

  -- Get historical data from 30 days ago
  SELECT * INTO historical_data
  FROM dashboard_metrics_history
  WHERE company_id = p_company_id
    AND snapshot_date = CURRENT_DATE - INTERVAL '30 days'
  ORDER BY snapshot_date DESC
  LIMIT 1;

  -- Return results
  RETURN QUERY
  SELECT
    current_data.total_members,
    current_data.active_members_30d,
    current_data.total_lifetime_points,
    current_data.total_redeemed_points,
    current_data.total_available_points,
    current_data.total_rewards,
    current_data.active_rewards,
    current_data.redemption_rate_percentage,

    -- Growth calculations
    calculate_growth_rate(current_data.total_members, historical_data.total_members),
    calculate_growth_rate(current_data.total_lifetime_points, historical_data.total_lifetime_points),
    calculate_growth_rate(current_data.total_rewards, historical_data.total_rewards),
    calculate_growth_rate(current_data.redemption_rate_percentage::INTEGER, historical_data.redemption_rate_percentage::INTEGER),

    current_data.last_updated,
    (historical_data.id IS NOT NULL) as has_historical_data;
END;
$function$;

-- 2. Fix get_active_inactive_ratio function (second overload)
CREATE OR REPLACE FUNCTION public.get_active_inactive_ratio(p_days integer DEFAULT 30, p_company_id uuid DEFAULT NULL::uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  -- Rest of the function body with schema-qualified references
  RETURN (
    SELECT json_build_object(
      'active_count', COUNT(DISTINCT m.id) FILTER (WHERE EXISTS (
        SELECT 1 FROM public.points_transactions pt 
        WHERE pt.member_id = m.id 
        AND pt.transaction_date > (CURRENT_DATE - p_days::integer)
      )),
      'inactive_count', COUNT(DISTINCT m.id) FILTER (WHERE NOT EXISTS (
        SELECT 1 FROM public.points_transactions pt 
        WHERE pt.member_id = m.id 
        AND pt.transaction_date > (CURRENT_DATE - p_days::integer)
      )),
      'active_rate', ROUND(
        (COUNT(DISTINCT m.id) FILTER (WHERE EXISTS (
          SELECT 1 FROM public.points_transactions pt 
          WHERE pt.member_id = m.id 
          AND pt.transaction_date > (CURRENT_DATE - p_days::integer)
        ))::numeric / NULLIF(COUNT(*), 0)::numeric) * 100, 2
      )
    )
    FROM public.loyalty_members m
    WHERE (p_company_id IS NULL OR m.company_id = p_company_id)
  );
END;
$function$;

-- 3. Fix get_available_points function
CREATE OR REPLACE FUNCTION public.get_available_points(member_id uuid)
RETURNS integer
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $function$
  SELECT 
    (lifetime_points - COALESCE(redeemed_points, 0) - COALESCE(expired_points, 0))
  FROM loyalty_members
  WHERE id = member_id;
$function$;

-- 4. Fix get_member_engagement function
CREATE OR REPLACE FUNCTION public.get_member_engagement(p_company_id uuid, p_days integer DEFAULT 30)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  result JSON;
BEGIN
  WITH engagement_data AS (
    SELECT
      COUNT(DISTINCT pt.member_id) AS active_members,
      COUNT(DISTINCT m.id) AS total_members,
      COUNT(pt.id) AS total_transactions,
      ROUND(AVG(pt.points_change) FILTER (WHERE pt.transaction_type = 'EARN'), 2) AS avg_points_earned,
      ROUND(AVG(ABS(pt.points_change)) FILTER (WHERE pt.transaction_type = 'REDEEM'), 2) AS avg_points_redeemed
    FROM loyalty_members m
    LEFT JOIN points_transactions pt ON 
      m.id = pt.member_id AND 
      pt.transaction_date >= CURRENT_DATE - p_days * INTERVAL '1 day'
    WHERE m.company_id = p_company_id
  )
  SELECT json_build_object(
    'active_members', active_members,
    'total_members', total_members,
    'engagement_rate', CASE WHEN total_members > 0 THEN 
      ROUND((active_members::numeric / total_members) * 100, 1) ELSE 0 END,
    'total_transactions', total_transactions,
    'avg_points_earned', avg_points_earned,
    'avg_points_redeemed', avg_points_redeemed
  ) INTO result
  FROM engagement_data;
  
  RETURN result;
END;
$function$;

-- 5. Fix get_location_distribution function
CREATE OR REPLACE FUNCTION public.get_location_distribution(p_company_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  result JSON;
BEGIN
  WITH location_counts AS (
    SELECT
      COALESCE(city, 'Unknown') AS city,
      COALESCE(state, 'Unknown') AS state,
      COALESCE(country, 'Unknown') AS country,
      COUNT(*) AS member_count
    FROM loyalty_members
    WHERE company_id = p_company_id
    GROUP BY city, state, country
    ORDER BY member_count DESC
  ),
  location_stats AS (
    SELECT
      json_agg(
        json_build_object(
          'city', city,
          'state', state,
          'country', country,
          'count', member_count,
          'percentage', ROUND((member_count::numeric / SUM(member_count) OVER()) * 100, 1)
        )
      ) AS locations,
      COUNT(DISTINCT country) AS country_count,
      COUNT(DISTINCT state) AS state_count,
      COUNT(DISTINCT city) AS city_count
    FROM location_counts
  )
  SELECT json_build_object(
    'locations', locations,
    'country_count', country_count,
    'state_count', state_count,
    'city_count', city_count
  ) INTO result
  FROM location_stats;
  
  RETURN result;
END;
$function$;

-- 6. Fix get_member_growth function
CREATE OR REPLACE FUNCTION public.get_member_growth(p_company_id uuid, p_months integer DEFAULT 12)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  result JSON;
BEGIN
  WITH monthly_data AS (
    SELECT
      DATE_TRUNC('month', created_at) AS month,
      COUNT(*) AS new_members
    FROM loyalty_members
    WHERE 
      company_id = p_company_id AND
      created_at >= CURRENT_DATE - p_months * INTERVAL '1 month'
    GROUP BY DATE_TRUNC('month', created_at)
    ORDER BY month
  ),
  growth_data AS (
    SELECT
      TO_CHAR(month, 'YYYY-MM') AS month_label,
      new_members,
      SUM(new_members) OVER (ORDER BY month) AS cumulative_members,
      LAG(new_members, 1, 0) OVER (ORDER BY month) AS previous_month,
      CASE 
        WHEN LAG(new_members, 1, 0) OVER (ORDER BY month) = 0 THEN NULL
        ELSE ROUND(((new_members - LAG(new_members, 1, 0) OVER (ORDER BY month))::numeric / 
                   LAG(new_members, 1, 0) OVER (ORDER BY month)) * 100, 1)
      END AS growth_percentage
    FROM monthly_data
  )
  SELECT json_build_object(
    'monthly_data', json_agg(
      json_build_object(
        'month', month_label,
        'new_members', new_members,
        'cumulative_members', cumulative_members,
        'growth_percentage', growth_percentage
      ) ORDER BY month_label
    ),
    'total_new_members', SUM(new_members),
    'average_monthly_growth', AVG(growth_percentage)
  ) INTO result
  FROM growth_data;
  
  RETURN result;
END;
$function$;

-- 7. Fix get_tier_distribution function
CREATE OR REPLACE FUNCTION public.get_tier_distribution(p_company_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  result JSON;
BEGIN
  WITH tier_counts AS (
    SELECT
      COALESCE(loyalty_tier, 'None') AS tier,
      COUNT(*) AS member_count
    FROM loyalty_members
    WHERE company_id = p_company_id
    GROUP BY loyalty_tier
    ORDER BY COUNT(*) DESC
  )
  SELECT json_build_object(
    'tiers', json_agg(
      json_build_object(
        'tier', tier,
        'count', member_count,
        'percentage', ROUND((member_count::numeric / SUM(member_count) OVER()) * 100, 1)
      )
    ),
    'total_members', SUM(member_count)
  ) INTO result
  FROM tier_counts;
  
  RETURN result;
END;
$function$;

-- 8. Fix redeem_points function
CREATE OR REPLACE FUNCTION public.redeem_points()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  remaining_points INTEGER := ABS(NEW.points_change);
  earn_record RECORD;
  available_points INTEGER;
BEGIN
  -- Lock the loyalty_members row to prevent race conditions
  PERFORM 1 FROM loyalty_members 
  WHERE id = NEW.member_id AND company_id = NEW.company_id 
  FOR UPDATE;
  
  -- Rest of your function remains the same
  -- ...
  
  RETURN NEW;
END;
$function$;

-- 9. Fix expire_points_with_logging procedure
CREATE OR REPLACE PROCEDURE public.expire_points_with_logging()
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $procedure$
DECLARE
  start_time TIMESTAMP := clock_timestamp();
  affected INT;
BEGIN
  WITH expired_points AS (
    SELECT 
      pt.member_id,
      pt.company_id,
      SUM(pt.points_change) AS points_to_expire
    FROM points_transactions pt
    JOIN loyalty_members lm 
      ON pt.member_id = lm.id 
      AND pt.company_id = lm.company_id
    WHERE pt.transaction_type = 'EARN' 
      AND pt.expiration_date <= CURRENT_DATE
    GROUP BY pt.member_id, pt.company_id
    HAVING SUM(pt.points_change) > 0
  )
  INSERT INTO points_transactions (
    member_id, company_id, points_change, 
    transaction_type, description
  )
  SELECT 
    member_id, company_id, -points_to_expire, 
    'EXPIRE', 'Points expired on ' || CURRENT_DATE
  FROM expired_points;

  GET DIAGNOSTICS affected = ROW_COUNT;
  
  INSERT INTO job_execution_log (
    job_name, status, affected_rows, 
    execution_time_ms, company_id
  )
  SELECT 
    'expire_points_daily', 'SUCCESS', affected, 
    EXTRACT(EPOCH FROM (clock_timestamp() - start_time)) * 1000,
    company_id
  FROM expired_points
  GROUP BY company_id;
  
EXCEPTION WHEN OTHERS THEN
  INSERT INTO job_execution_log (
    job_name, status, error_message, company_id
  )
  VALUES (
    'expire_points_daily', 'ERROR', SQLERRM,
    (SELECT company_id FROM loyalty_members WHERE id = member_id LIMIT 1)
  );
  RAISE;
END;
$procedure$;

-- 10. Fix get_top_members function
CREATE OR REPLACE FUNCTION public.get_top_members(p_company_id uuid, p_limit integer DEFAULT 10, p_metric text DEFAULT 'lifetime_points'::text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  result JSON;
  valid_metric BOOLEAN;
BEGIN
  -- Validate metric parameter
  valid_metric := p_metric IN ('lifetime_points', 'available_points', 'redeemed_points', 'redemption_count');
  
  IF NOT valid_metric THEN
    RAISE EXCEPTION 'Invalid metric: %. Valid options are: lifetime_points, available_points, redeemed_points, redemption_count', p_metric;
  END IF;
  
  -- Dynamic query based on metric
  IF p_metric = 'redemption_count' THEN
    WITH member_redemptions AS (
      SELECT 
        m.id,
        m.name,
        m.email,
        m.loyalty_tier,
        COUNT(pt.id) FILTER (WHERE pt.transaction_type = 'REDEEM') AS redemption_count
      FROM loyalty_members m
      LEFT JOIN points_transactions pt ON m.id = pt.member_id
      WHERE m.company_id = p_company_id
      GROUP BY m.id, m.name, m.email, m.loyalty_tier
      ORDER BY redemption_count DESC
      LIMIT p_limit
    )
    SELECT json_agg(
      json_build_object(
        'id', id,
        'name', name,
        'email', email,
        'loyalty_tier', loyalty_tier,
        'value', redemption_count,
        'metric', 'redemption_count'
      )
    ) INTO result
    FROM member_redemptions;
  ELSE
    -- For points-based metrics
    WITH member_points AS (
      SELECT 
        id,
        name,
        email,
        loyalty_tier,
        lifetime_points,
        available_points,
        redeemed_points
      FROM loyalty_members
      WHERE company_id = p_company_id
      ORDER BY 
        CASE 
          WHEN p_metric = 'lifetime_points' THEN lifetime_points
          WHEN p_metric = 'available_points' THEN available_points
          WHEN p_metric = 'redeemed_points' THEN redeemed_points
        END DESC
      LIMIT p_limit
    )
    SELECT json_agg(
      json_build_object(
        'id', id,
        'name', name,
        'email', email,
        'loyalty_tier', loyalty_tier,
        'value', 
          CASE 
            WHEN p_metric = 'lifetime_points' THEN lifetime_points
            WHEN p_metric = 'available_points' THEN available_points
            WHEN p_metric = 'redeemed_points' THEN redeemed_points
          END,
        'metric', p_metric
      )
    ) INTO result
    FROM member_points;
  END IF;
  
  RETURN result;
END;
$function$;

-- 11. Fix is_member_birthday_eligible function
CREATE OR REPLACE FUNCTION public.is_member_birthday_eligible(member_id uuid)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  member_birthday DATE;
BEGIN
  SELECT birthday INTO member_birthday 
  FROM loyalty_members 
  WHERE id = member_id;
  
  RETURN 
    CURRENT_DATE BETWEEN 
      (member_birthday + (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM member_birthday)) * INTERVAL '1 YEAR')
      - INTERVAL '7 DAYS' 
    AND 
      (member_birthday + (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM member_birthday)) * INTERVAL '1 YEAR')
      + INTERVAL '7 DAYS';
END;
$function$;
