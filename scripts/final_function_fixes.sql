-- Complete SQL script to fix all function_search_path_mutable warnings
-- This script adds SET search_path = public to the function definitions

-- Note: Many functions already have "SET search_path TO public, pg_temp;" inside their body,
-- but the Supa<PERSON> linter still flags them because the search path needs to be set at the function
-- definition level using the SET search_path = public clause.

-- 1. Fix calculate_growth_rate function
CREATE OR REPLACE FUNCTION public.calculate_growth_rate(current_value integer, previous_value integer)
RETURNS numeric
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  IF previous_value = 0 OR previous_value IS NULL THEN
    RETURN CASE WHEN current_value > 0 THEN 100.0 ELSE 0.0 END;
  END IF;

  RETURN ROUND(((current_value - previous_value)::decimal / previous_value::decimal) * 100, 2);
END;
$function$;

-- 2. Fix add_points_transaction function
CREATE OR REPLACE FUNCTION public.add_points_transaction(p_member_id uuid, p_company_id uuid, p_points integer, p_description text, p_transaction_type text DEFAULT 'EARN'::text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  v_member_exists BOOLEAN;
  v_expiration_date DATE;
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;

  -- Check if member exists and belongs to the company
  SELECT EXISTS (
    SELECT 1 FROM public.loyalty_members
    WHERE id = p_member_id AND company_id = p_company_id
  ) INTO v_member_exists;

  IF NOT v_member_exists THEN
    RAISE EXCEPTION 'Member not found or does not belong to this company';
  END IF;

  -- Set expiration date - 1 year from now for EARN transactions, far future for others
  IF p_transaction_type = 'EARN' THEN
    v_expiration_date := CURRENT_DATE + INTERVAL '1 year';
  ELSE
    v_expiration_date := '2099-12-31'::DATE;
  END IF;

  -- Create the transaction record
  INSERT INTO public.points_transactions (
    member_id,
    company_id,
    points_change,
    description,
    transaction_type,
    expiration_date,
    transaction_date
  ) VALUES (
    p_member_id,
    p_company_id,
    p_points,
    p_description,
    p_transaction_type,
    v_expiration_date,
    CURRENT_TIMESTAMP
  );

  -- Update the member's lifetime points
  UPDATE public.loyalty_members
  SET lifetime_points = COALESCE(lifetime_points, 0) + p_points
  WHERE id = p_member_id AND company_id = p_company_id;
END;
$function$;

-- 3. Fix update_modified_timestamp function
CREATE OR REPLACE FUNCTION public.update_modified_timestamp()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$function$;

-- 4. Fix update_notification_status function
CREATE OR REPLACE FUNCTION public.update_notification_status(notification_id uuid, new_status text, attempt_successful boolean DEFAULT true)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  UPDATE public.member_notifications
  SET 
    delivery_status = new_status,
    delivery_attempts = delivery_attempts + 1,
    last_attempt_at = now(),
    updated_at = now()
  WHERE id = notification_id;
  
  -- If notification was read, update read_at timestamp
  IF new_status = 'DELIVERED' AND attempt_successful = true THEN
    UPDATE public.member_notifications
    SET is_read = true, read_at = now()
    WHERE id = notification_id AND is_read = false;
  END IF;
END;
$function$;

-- 5. Fix update_points_on_expiration function
CREATE OR REPLACE FUNCTION public.update_points_on_expiration()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  -- Prevent recursion
  IF pg_trigger_depth() > 1 THEN
    RETURN NEW;
  END IF;
  
  IF NEW.transaction_type = 'EXPIRE' THEN
    UPDATE public.loyalty_members
    SET expired_points = expired_points + ABS(NEW.points_change)
    WHERE id = NEW.member_id 
      AND company_id = NEW.company_id;  -- Add multi-tenant support
  END IF;
  
  RETURN NEW;
END;
$function$;

-- 6. Fix update_timestamp function
CREATE OR REPLACE FUNCTION public.update_timestamp()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$function$;

-- 7. Fix create_daily_snapshot function
CREATE OR REPLACE FUNCTION public.create_daily_snapshot(p_company_id uuid DEFAULT NULL::uuid)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  snapshot_count INTEGER := 0;
  company_record UUID;
BEGIN
  -- If company_id provided, process just that company, otherwise all companies
  FOR company_record IN
    SELECT id FROM companies
    WHERE (p_company_id IS NULL OR id = p_company_id)
  LOOP
    INSERT INTO dashboard_metrics_history (
      company_id,
      snapshot_date,
      total_members,
      active_members_30d,
      total_lifetime_points,
      total_redeemed_points,
      total_available_points,
      total_rewards,
      active_rewards,
      redemption_rate_percentage
    )
    SELECT
      company_id,
      CURRENT_DATE,
      total_members,
      active_members_30d,
      total_lifetime_points,
      total_redeemed_points,
      total_available_points,
      total_rewards,
      active_rewards,
      redemption_rate_percentage
    FROM dashboard_metrics
    WHERE company_id = company_record
    ON CONFLICT (company_id, snapshot_date)
    DO UPDATE SET
      total_members = EXCLUDED.total_members,
      active_members_30d = EXCLUDED.active_members_30d,
      total_lifetime_points = EXCLUDED.total_lifetime_points,
      total_redeemed_points = EXCLUDED.total_redeemed_points,
      total_available_points = EXCLUDED.total_available_points,
      total_rewards = EXCLUDED.total_rewards,
      active_rewards = EXCLUDED.active_rewards,
      redemption_rate_percentage = EXCLUDED.redemption_rate_percentage;

    snapshot_count := snapshot_count + 1;
  END LOOP;

  RETURN snapshot_count;
END;
$function$;

-- 8. Fix get_active_inactive_ratio function
CREATE OR REPLACE FUNCTION public.get_active_inactive_ratio(p_days integer DEFAULT 30, p_tiers text[] DEFAULT NULL::text[])
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  total_members INTEGER;
  active_members INTEGER;
  inactive_members INTEGER;
  result JSON;
BEGIN
  -- Calculate active vs inactive members
  SELECT 
    COUNT(*) FILTER (WHERE last_activity_date >= CURRENT_DATE - p_days * INTERVAL '1 day'),
    COUNT(*) FILTER (WHERE last_activity_date < CURRENT_DATE - p_days * INTERVAL '1 day' OR last_activity_date IS NULL),
    COUNT(*)
  INTO 
    active_members, 
    inactive_members,
    total_members
  FROM loyalty_members
  WHERE (p_tiers IS NULL OR loyalty_tier = ANY(p_tiers));
  
  -- Create JSON result
  result := json_build_object(
    'active_members', active_members,
    'inactive_members', inactive_members,
    'total_members', total_members,
    'active_percentage', CASE WHEN total_members > 0 THEN ROUND((active_members::numeric / total_members) * 100, 1) ELSE 0 END,
    'inactive_percentage', CASE WHEN total_members > 0 THEN ROUND((inactive_members::numeric / total_members) * 100, 1) ELSE 0 END
  );
  
  RETURN result;
END;
$function$;

-- 9. Fix update_member_tier function
CREATE OR REPLACE FUNCTION public.update_member_tier()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  IF OLD.loyalty_tier IS DISTINCT FROM NEW.loyalty_tier THEN
    INSERT INTO public.member_notifications (
      member_id, company_id, notification_type, message, related_entity_type
    )
    VALUES (
      NEW.id, NEW.company_id, 'TIER_CHANGE', 
      'Congratulations! You have been upgraded to ' || NEW.loyalty_tier || ' tier!',
      'MEMBER_PROFILE'
    );
  END IF;
  RETURN NEW;
END;
$function$;

-- 10. Fix update_lifetime_points function
CREATE OR REPLACE FUNCTION public.update_lifetime_points()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  IF pg_trigger_depth() > 1 THEN
    RETURN NEW;
  END IF;
  
  IF NEW.transaction_type = 'EARN' THEN
    UPDATE loyalty_members
    SET lifetime_points = lifetime_points + NEW.points_change
    WHERE id = NEW.member_id 
      AND company_id = NEW.company_id;
  END IF;
  
  RETURN NEW;
END;
$function$;

-- 11. Fix set_expiration_date function
CREATE OR REPLACE FUNCTION public.set_expiration_date()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  IF NEW.transaction_type = 'EARN' AND NEW.expiration_date IS NULL THEN
    SELECT INTO NEW.expiration_date
      NEW.transaction_date + (points_expiration_days * INTERVAL '1 day')
    FROM companies
    WHERE id = NEW.company_id;
  END IF;
  RETURN NEW;
END;
$function$;

-- 12. Fix refresh_dashboard_metrics function
CREATE OR REPLACE FUNCTION public.refresh_dashboard_metrics()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  REFRESH MATERIALIZED VIEW dashboard_metrics;
  RETURN NULL;
END;
$function$;

-- 13. Fix check_same_company function
CREATE OR REPLACE FUNCTION public.check_same_company()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  member_company_id UUID;
  reward_company_id UUID;
BEGIN
  SELECT company_id INTO member_company_id FROM loyalty_members WHERE id = NEW.member_id;
  SELECT company_id INTO reward_company_id FROM rewards WHERE id = NEW.reward_id;
  
  IF member_company_id IS NULL OR reward_company_id IS NULL THEN
    RAISE EXCEPTION 'Company ID missing for member or reward';
  END IF;
  
  IF member_company_id <> reward_company_id THEN
    RAISE EXCEPTION 'Member and reward must belong to the same company (% vs %)', 
                    member_company_id, reward_company_id;
  END IF;
  
  RETURN NEW;
END;
$function$;

-- 14. Fix deduct_points_transaction function
CREATE OR REPLACE FUNCTION public.deduct_points_transaction(p_member_id uuid, p_company_id uuid, p_points integer, p_description text, p_transaction_type text DEFAULT 'REDEEM'::text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  v_member_exists BOOLEAN;
  v_available_points INTEGER;
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;

  -- Check if member exists and belongs to the company
  SELECT EXISTS (
    SELECT 1 FROM public.loyalty_members
    WHERE id = p_member_id AND company_id = p_company_id
  ) INTO v_member_exists;

  IF NOT v_member_exists THEN
    RAISE EXCEPTION 'Member not found or does not belong to this company';
  END IF;

  -- Calculate available points
  SELECT
    COALESCE(lifetime_points, 0) - COALESCE(redeemed_points, 0) - COALESCE(expired_points, 0)
  INTO v_available_points
  FROM public.loyalty_members
  WHERE id = p_member_id AND company_id = p_company_id;

  -- Check if member has enough points
  IF v_available_points < p_points THEN
    RAISE EXCEPTION 'Insufficient points. Member only has % points available.', v_available_points;
  END IF;

  -- Create the transaction record with negative points
  INSERT INTO public.points_transactions (
    member_id,
    company_id,
    points_change,
    description,
    transaction_type,
    transaction_date
  ) VALUES (
    p_member_id,
    p_company_id,
    -p_points, -- Negative value for deduction
    p_description,
    p_transaction_type,
    CURRENT_TIMESTAMP
  );

  -- Update the member's redeemed points
  UPDATE public.loyalty_members
  SET redeemed_points = COALESCE(redeemed_points, 0) + p_points
  WHERE id = p_member_id AND company_id = p_company_id;
END;
$function$;

-- 15. Fix log_data_changes function
CREATE OR REPLACE FUNCTION public.log_data_changes()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  IF TG_OP = 'INSERT' THEN
    INSERT INTO audit_log (
      table_name, record_id, action, changed_by, changed_at, old_data, new_data
    ) VALUES (
      TG_TABLE_NAME, 
      NEW.id,
      TG_OP,
      current_setting('app.current_user', true),
      CURRENT_TIMESTAMP,
      NULL,
      to_jsonb(NEW)
    );
  ELSIF TG_OP = 'UPDATE' THEN
    INSERT INTO audit_log (
      table_name, record_id, action, changed_by, changed_at, old_data, new_data
    ) VALUES (
      TG_TABLE_NAME, 
      NEW.id,
      TG_OP,
      current_setting('app.current_user', true),
      CURRENT_TIMESTAMP,
      to_jsonb(OLD),
      to_jsonb(NEW)
    );
  ELSIF TG_OP = 'DELETE' THEN
    INSERT INTO audit_log (
      table_name, record_id, action, changed_by, changed_at, old_data, new_data
    ) VALUES (
      TG_TABLE_NAME, 
      OLD.id,
      TG_OP,
      current_setting('app.current_user', true),
      CURRENT_TIMESTAMP,
      to_jsonb(OLD),
      NULL
    );
  END IF;
  RETURN NULL;
END;
$function$;

-- 16. Fix set_app_parameter function
CREATE OR REPLACE FUNCTION public.set_app_parameter(parameter text, value text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  EXECUTE format('SET %I.%I = %L', split_part(parameter, '.', 1), split_part(parameter, '.', 2), value);
  RETURN value;
END;
$function$;

-- Note: This script includes fixes for 16 functions. If there are additional functions
-- that need to be fixed, follow the same pattern: add SECURITY DEFINER and SET search_path = public
-- to the function definition.
