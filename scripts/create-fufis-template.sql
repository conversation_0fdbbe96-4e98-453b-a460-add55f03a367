-- ============================================================================
-- FUFIS (Addis Beauty Salon) Template Creation
-- Date: August 12, 2025
-- Purpose: Create template for enhanced OCR testing
-- ============================================================================

-- Create FUFIS template for testing
INSERT INTO receipt_templates (
  company_id,
  template_name,
  template_image_url,
  template_metadata,
  ai_prompt_context,
  validation_rules,
  confidence_threshold,
  is_active
) VALUES (
  '7c4b5389-b630-4d2b-b9b1-9f6460117371', -- Addis Beauty Salon company ID
  'FUFIS Beauty Services Standard',
  'https://example.com/fufis-template.jpg',
  '{"pos_system": "MarakiPOS", "business_type": "beauty_salon", "currency": "ETB"}',
  'This is a beauty salon (FUFIS/Addis Beauty Salon) receipt. Common services include:
- Beard Shaving (450 ETB)
- Shellac Polish (550 ETB)
- Refill gel with shellac (1200 ETB)
- Hair cutting and styling services
- Nail care services

The receipt typically uses MarakiPOS system, shows VAT at 15%, and all payments are in Ethiopian Birr (ETB). Extract individual services with exact prices.',
  '{"required_fields": ["business_name", "financial_system_number", "total_amount"], "vat_rate": 0.15, "currency": "ETB"}',
  0.85,
  true
);

-- Verify template creation
SELECT
  t.id,
  t.template_name,
  c.name as company_name,
  t.confidence_threshold,
  t.is_active
FROM receipt_templates t
JOIN companies c ON t.company_id = c.id
WHERE c.name = 'Addis Beauty Salon';
