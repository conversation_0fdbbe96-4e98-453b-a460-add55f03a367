-- Add Cashier Role Migration Script
-- This script adds the CASHIER role to the system and sets up the necessary permissions

-- 1. Ensure the role column in company_administrators accepts the CASHIER value
-- (No schema change needed as the role column is already TEXT type)

-- 2. Create RLS policies for cashier access to members and transactions

-- Allow cashiers to view members from their company
CREATE POLICY cashier_view_members ON loyalty_members
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = loyalty_members.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- Allow cashiers to add new members to their company
CREATE POLICY cashier_insert_members ON loyalty_members
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = loyalty_members.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- Allow cashiers to update basic member information
CREATE POLICY cashier_update_members ON loyalty_members
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = loyalty_members.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- Allow cashiers to view transactions from their company
CREATE POLICY cashier_view_transactions ON points_transactions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = points_transactions.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- Allow cashiers to add new transactions to their company
CREATE POLICY cashier_insert_transactions ON points_transactions
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = points_transactions.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- Allow cashiers to view rewards from their company
CREATE POLICY cashier_view_rewards ON rewards
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = rewards.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- Allow cashiers to process reward redemptions
CREATE POLICY cashier_insert_redemptions ON reward_redemptions
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = reward_redemptions.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- Allow cashiers to view reward redemptions
CREATE POLICY cashier_view_redemptions ON reward_redemptions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM company_administrators
      WHERE administrator_id = auth.uid()
      AND company_id = reward_redemptions.company_id
      AND (role = 'OWNER' OR role = 'CASHIER')
    )
  );

-- 3. Create a function to add a cashier to a company
CREATE OR REPLACE FUNCTION add_cashier(
  p_company_id UUID,
  p_email TEXT,
  p_name TEXT,
  p_phone_number TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  v_admin_id UUID;
BEGIN
  -- First check if the administrator already exists
  SELECT id INTO v_admin_id FROM administrators WHERE email = p_email;
  
  -- If not, create a new administrator
  IF v_admin_id IS NULL THEN
    INSERT INTO administrators (email, name, phone_number, is_active)
    VALUES (p_email, p_name, p_phone_number, TRUE)
    RETURNING id INTO v_admin_id;
  END IF;
  
  -- Add the administrator as a cashier for the company
  INSERT INTO company_administrators (administrator_id, company_id, role, created_by)
  VALUES (v_admin_id, p_company_id, 'CASHIER', auth.uid())
  ON CONFLICT (administrator_id, company_id) DO UPDATE
  SET role = 'CASHIER';
  
  RETURN v_admin_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create a function to check if a user is a cashier
CREATE OR REPLACE FUNCTION is_cashier(p_company_id UUID) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM company_administrators
    WHERE administrator_id = auth.uid()
    AND company_id = p_company_id
    AND role = 'CASHIER'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. Create a function to get user role for a company
CREATE OR REPLACE FUNCTION get_user_role(p_company_id UUID) RETURNS TEXT AS $$
DECLARE
  v_role TEXT;
BEGIN
  SELECT role INTO v_role FROM company_administrators
  WHERE administrator_id = auth.uid()
  AND company_id = p_company_id;
  
  RETURN v_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
