-- First, insert the administrator record into the administrators table
-- This is required because company_administrators.administrator_id is a foreign key
INSERT INTO administrators (id, email, name, is_super_admin, is_active, created_at)
VALUES (
  '2905012d-9b46-4237-980e-f8eb278ba7ad', -- Administrator ID for "<EMAIL>"
  '<EMAIL>', -- Email
  'Eshetu Feleke', -- Name
  TRUE, -- Is super admin
  TRUE, -- Is active
  now() -- Created at
)
ON CONFLICT (id) DO NOTHING; -- Skip if already exists

-- Then, insert the company_administrators record
INSERT INTO company_administrators (id, company_id, administrator_id, role, created_at)
VALUES (
  gen_random_uuid(), -- Generate a random UUID for the id
  'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6', -- Company ID for "Addis Beauty Salon"
  '2905012d-9b46-4237-980e-f8eb278ba7ad', -- Administrator ID for "<EMAIL>"
  'ADMIN', -- Role must be uppercase to satisfy the check constraint (OWNER, ADMIN, MANAGER, REPOR<PERSON>, CASHIER)
  now() -- Current timestamp for created_at
)
ON CONFLICT (administrator_id, company_id, role) DO NOTHING; -- Skip if already exists
