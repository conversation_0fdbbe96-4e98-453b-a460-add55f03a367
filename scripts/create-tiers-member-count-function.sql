-- Create or replace the RPC function for getting tiers with member counts
CREATE OR REPLACE FUNCTION get_tiers_with_member_counts(company_id_param UUID)
RETURNS TABLE (
  id UUID,
  tier_name TEXT,
  minimum_points INTEGER,
  benefits_description TEXT,
  company_id UUID,
  member_count BIGINT,
  member_stats JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  WITH tier_ranges AS (
    SELECT
      t.id,
      t.tier_name,
      t.minimum_points,
      t.benefits_description,
      t.company_id,
      t.minimum_points AS range_start,
      LEAD(t.minimum_points, 1, 2147483647) OVER (ORDER BY t.minimum_points) AS range_end
    FROM tier_definitions t
    WHERE t.company_id = company_id_param
  ),
  member_tier_counts AS (
    SELECT
      tr.id,
      COUNT(m.id) AS member_count,
      JSONB_BUILD_OBJECT(
        'active', COUNT(m.id) FILTER (WHERE m.last_activity_date > CURRENT_DATE - INTERVAL '30 days'),
        'new', COUNT(m.id) FILTER (WHERE m.created_at > CURRENT_DATE - INTERVAL '30 days'),
        'inactive', COUNT(m.id) FILTER (WHERE m.last_activity_date <= CURRENT_DATE - INTERVAL '30 days' OR m.last_activity_date IS NULL)
      ) AS member_stats
    FROM tier_ranges tr
    LEFT JOIN loyalty_members m ON 
      m.company_id = company_id_param AND
      m.lifetime_points >= tr.range_start AND
      m.lifetime_points < tr.range_end
    GROUP BY tr.id
  )
  SELECT
    tr.id,
    tr.tier_name,
    tr.minimum_points,
    tr.benefits_description,
    tr.company_id,
    COALESCE(mtc.member_count, 0) AS member_count,
    COALESCE(mtc.member_stats, '{"active": 0, "new": 0, "inactive": 0}'::JSONB) AS member_stats
  FROM tier_ranges tr
  LEFT JOIN member_tier_counts mtc ON tr.id = mtc.id
  ORDER BY tr.minimum_points;
END;
$$;

COMMENT ON FUNCTION get_tiers_with_member_counts(UUID) IS 'Returns tier definitions with member counts and statistics for a given company';
