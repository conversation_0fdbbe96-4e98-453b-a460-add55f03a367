-- Cleanup script to remove all sample data and start fresh
-- This script deletes data in the correct order to respect foreign key constraints

-- First, disable triggers temporarily to avoid trigger-related issues
SET session_replication_role = 'replica';

-- 1. Delete from tables with foreign keys first (leaf nodes)
-- These tables reference other tables but aren't referenced by others

-- Delete reward redemptions
TRUNCATE reward_redemptions CASCADE;

-- Delete points transactions
TRUNCATE points_transactions CASCADE;

-- Delete receipts
TRUNCATE receipts CASCADE;

-- Delete member notifications
TRUNCATE member_notifications CASCADE;

-- Delete reward tier eligibility
TRUNCATE reward_tier_eligibility CASCADE;

-- 2. Delete from mid-level tables

-- Delete rewards
TRUNCATE rewards CASCADE;

-- Delete tier definitions
TRUNCATE tier_definitions CASCADE;

-- Delete program rules
TRUNCATE program_rules CASCADE;

-- Delete loyalty members
TRUNCATE loyalty_members CASCADE;

-- Delete company administrators
TRUNCATE company_administrators CASCADE;

-- 3. Delete from top-level tables

-- Delete companies
TRUNCATE companies CASCADE;

-- Delete administrators
TRUNCATE administrators CASCADE;

-- Delete audit log
TR<PERSON><PERSON><PERSON> audit_log CASCADE;

-- 4. Reset any sequences if needed
-- This ensures IDs start from 1 again for tables with serial/identity columns
-- Example: ALTER SEQUENCE table_name_id_seq RESTART WITH 1;

-- Re-enable triggers
SET session_replication_role = 'origin';

-- 5. Insert our administrator and company for development
-- First, insert the administrator
INSERT INTO administrators (id, email, name, is_super_admin, is_active, created_at)
VALUES (
  '2905012d-9b46-4237-980e-f8eb278ba7ad', -- Administrator ID for "<EMAIL>"
  '<EMAIL>', -- Email
  'Eshetu Feleke', -- Name
  TRUE, -- Is super admin
  TRUE, -- Is active
  now() -- Created at
);

-- Then, insert a company
INSERT INTO companies (
  id, 
  name, 
  administrator_id,
  logo_url,
  primary_color,
  points_expiration_days,
  points_earning_ratio,
  slug,
  is_active,
  created_at
)
VALUES (
  'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6', -- Company ID
  'Addis Beauty Salon', -- Company name
  '2905012d-9b46-4237-980e-f8eb278ba7ad', -- Administrator ID
  'https://picsum.photos/seed/e31cbdcd-a9f4-482d-ad65-d2be34dde3c6/200/200', -- Logo URL
  '#3B82F6', -- Primary color
  365, -- Points expiration days
  1.0, -- Points earning ratio
  'addis-beauty-salon', -- Slug
  TRUE, -- Is active
  now() -- Created at
);

-- Finally, insert the company administrator record
INSERT INTO company_administrators (id, company_id, administrator_id, role, created_at)
VALUES (
  gen_random_uuid(), -- Generate a random UUID for the id
  'e31cbdcd-a9f4-482d-ad65-d2be34dde3c6', -- Company ID
  '2905012d-9b46-4237-980e-f8eb278ba7ad', -- Administrator ID
  'ADMIN', -- Role must be uppercase
  now() -- Current timestamp for created_at
);
