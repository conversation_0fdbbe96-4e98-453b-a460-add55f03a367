-- ============================================================================
-- PHASE 4: BUSINESS INTELLIGENCE - ANALYTICS VIEWS & QUERIES
-- Date: August 12, 2025
-- Purpose: Create analytics views for customer preferences and business insights
-- ============================================================================

-- =====================================================
-- CUSTOMER ITEM PREFERENCES ANALYTICS
-- =====================================================

-- View: Customer's most purchased items with preferences
CREATE OR REPLACE VIEW customer_item_preferences AS
SELECT
    m.id as member_id,
    m.name as member_name,
    bi.item_name,
    bi.item_category,
    COUNT(ri.id) as purchase_count,
    SUM(ri.total_price) as total_spent,
    AVG(ri.unit_price) as avg_price_paid,
    MAX(r.purchase_date) as last_purchase_date,
    -- Preference score (frequency + recency + spend)
    (COUNT(ri.id) * 0.4 +
     EXTRACT(DAYS FROM (NOW() - MAX(r.purchase_date))) * -0.1 +
     SUM(ri.total_price) / NULLIF(AVG(bi.standard_price), 0) * 0.5) as preference_score
FROM loyalty_members m
JOIN receipts r ON r.member_id = m.id
JOIN receipt_items ri ON ri.receipt_id = r.id
JOIN business_items bi ON bi.id = ri.business_item_id
WHERE ri.business_item_id IS NOT NULL
GROUP BY m.id, m.name, bi.id, bi.item_name, bi.item_category
ORDER BY preference_score DESC;

-- View: Top items per customer (customer's favorites)
CREATE OR REPLACE VIEW customer_favorite_items AS
SELECT DISTINCT ON (member_id)
    member_id,
    member_name,
    item_name as favorite_item,
    item_category as favorite_category,
    purchase_count,
    total_spent,
    preference_score
FROM customer_item_preferences
ORDER BY member_id, preference_score DESC;

-- Function: Get customer item preferences with seasonal analysis
CREATE OR REPLACE FUNCTION get_customer_seasonal_preferences(
    p_member_id UUID,
    p_months_back INTEGER DEFAULT 12
)
RETURNS TABLE (
    item_name TEXT,
    item_category TEXT,
    spring_purchases INTEGER,
    summer_purchases INTEGER,
    fall_purchases INTEGER,
    winter_purchases INTEGER,
    total_purchases INTEGER,
    seasonal_preference TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi.item_name,
        bi.item_category,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END)::INTEGER as spring_purchases,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END)::INTEGER as summer_purchases,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END)::INTEGER as fall_purchases,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)::INTEGER as winter_purchases,
        COUNT(ri.id)::INTEGER as total_purchases,
        CASE
            WHEN COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END) =
                 GREATEST(
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)
                 ) THEN 'Spring'
            WHEN COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END) =
                 GREATEST(
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)
                 ) THEN 'Summer'
            WHEN COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END) =
                 GREATEST(
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END),
                     COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)
                 ) THEN 'Fall'
            ELSE 'Winter'
        END as seasonal_preference
    FROM receipts r
    JOIN receipt_items ri ON ri.receipt_id = r.id
    JOIN business_items bi ON bi.id = ri.business_item_id
    WHERE r.member_id = p_member_id
    AND r.purchase_date >= NOW() - (p_months_back || ' months')::INTERVAL
    AND ri.business_item_id IS NOT NULL
    GROUP BY bi.item_name, bi.item_category
    HAVING COUNT(ri.id) > 0
    ORDER BY COUNT(ri.id) DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- BUSINESS PERFORMANCE ANALYTICS
-- =====================================================

-- View: Item performance analytics per business
CREATE OR REPLACE VIEW business_item_performance AS
SELECT
    c.id as company_id,
    c.name as company_name,
    bi.id as business_item_id,
    bi.item_name,
    bi.item_category,
    bi.standard_price,
    COUNT(ri.id) as total_sales,
    SUM(ri.total_price) as total_revenue,
    AVG(ri.unit_price) as avg_selling_price,
    MAX(r.purchase_date) as last_sold_date,
    -- Performance metrics
    (SUM(ri.total_price) / NULLIF(COUNT(DISTINCT r.member_id), 0)) as revenue_per_customer,
    COUNT(DISTINCT r.member_id) as unique_customers,
    -- Price optimization insights
    CASE
        WHEN AVG(ri.unit_price) > bi.standard_price * 1.1 THEN 'Premium Pricing'
        WHEN AVG(ri.unit_price) < bi.standard_price * 0.9 THEN 'Discounted'
        ELSE 'Standard Pricing'
    END as pricing_strategy,
    -- Popularity score
    (COUNT(ri.id) * 0.6 + COUNT(DISTINCT r.member_id) * 0.4) as popularity_score
FROM companies c
JOIN business_items bi ON bi.company_id = c.id
LEFT JOIN receipt_items ri ON ri.business_item_id = bi.id
LEFT JOIN receipts r ON r.id = ri.receipt_id
WHERE bi.is_active = true
GROUP BY c.id, c.name, bi.id, bi.item_name, bi.item_category, bi.standard_price
ORDER BY popularity_score DESC;

-- View: Revenue per item category analysis
CREATE OR REPLACE VIEW category_revenue_analysis AS
SELECT
    c.id as company_id,
    c.name as company_name,
    bi.item_category,
    COUNT(DISTINCT bi.id) as items_in_category,
    COUNT(ri.id) as total_transactions,
    SUM(ri.total_price) as total_revenue,
    AVG(ri.unit_price) as avg_price,
    COUNT(DISTINCT r.member_id) as unique_customers,
    -- Category performance metrics
    ROUND((SUM(ri.total_price) / NULLIF(SUM(SUM(ri.total_price)) OVER (PARTITION BY c.id), 0) * 100), 2) as revenue_percentage,
    -- Growth indicator (comparing last 3 months vs previous 3 months)
    CASE
        WHEN COUNT(CASE WHEN r.purchase_date >= NOW() - INTERVAL '3 months' THEN 1 END) >
             COUNT(CASE WHEN r.purchase_date >= NOW() - INTERVAL '6 months'
                       AND r.purchase_date < NOW() - INTERVAL '3 months' THEN 1 END)
        THEN 'Growing'
        WHEN COUNT(CASE WHEN r.purchase_date >= NOW() - INTERVAL '3 months' THEN 1 END) <
             COUNT(CASE WHEN r.purchase_date >= NOW() - INTERVAL '6 months'
                       AND r.purchase_date < NOW() - INTERVAL '3 months' THEN 1 END)
        THEN 'Declining'
        ELSE 'Stable'
    END as trend
FROM companies c
JOIN business_items bi ON bi.company_id = c.id
LEFT JOIN receipt_items ri ON ri.business_item_id = bi.id
LEFT JOIN receipts r ON r.id = ri.receipt_id
WHERE bi.is_active = true
GROUP BY c.id, c.name, bi.item_category
HAVING COUNT(ri.id) > 0
ORDER BY total_revenue DESC;

-- Function: Service bundling analysis (items frequently bought together)
CREATE OR REPLACE FUNCTION get_service_bundling_patterns(
    p_company_id UUID,
    p_min_occurrences INTEGER DEFAULT 2
)
RETURNS TABLE (
    item1_name TEXT,
    item2_name TEXT,
    bundle_frequency INTEGER,
    avg_bundle_value NUMERIC,
    bundle_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi1.item_name as item1_name,
        bi2.item_name as item2_name,
        COUNT(*) as bundle_frequency,
        AVG(ri1.total_price + ri2.total_price) as avg_bundle_value,
        -- Bundle score considers frequency and value
        (COUNT(*)::NUMERIC * AVG(ri1.total_price + ri2.total_price) / 100) as bundle_score
    FROM receipt_items ri1
    JOIN receipt_items ri2 ON ri1.receipt_id = ri2.receipt_id AND ri1.id != ri2.id
    JOIN business_items bi1 ON bi1.id = ri1.business_item_id
    JOIN business_items bi2 ON bi2.id = ri2.business_item_id
    JOIN receipts r ON r.id = ri1.receipt_id
    WHERE bi1.company_id = p_company_id
    AND bi2.company_id = p_company_id
    AND bi1.id < bi2.id -- Avoid duplicates
    GROUP BY bi1.item_name, bi2.item_name
    HAVING COUNT(*) >= p_min_occurrences
    ORDER BY bundle_score DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TEMPLATE PERFORMANCE METRICS
-- =====================================================

-- View: Template extraction accuracy and performance
CREATE OR REPLACE VIEW template_performance_metrics AS
SELECT
    rt.id as template_id,
    rt.template_name,
    c.name as company_name,
    rt.confidence_threshold,
    rt.total_extractions,
    rt.successful_extractions,
    rt.avg_confidence_score,
    -- Performance calculations
    CASE
        WHEN rt.total_extractions > 0
        THEN ROUND((rt.successful_extractions::NUMERIC / rt.total_extractions * 100), 2)
        ELSE 0
    END as success_rate_percentage,
    -- Recent performance (last 30 days)
    COUNT(CASE WHEN r.created_at >= NOW() - INTERVAL '30 days' THEN 1 END) as recent_extractions,
    AVG(CASE WHEN r.created_at >= NOW() - INTERVAL '30 days' THEN r.extraction_confidence END) as recent_avg_confidence,
    -- Template effectiveness score
    CASE
        WHEN rt.total_extractions > 0 AND rt.avg_confidence_score IS NOT NULL
        THEN (rt.successful_extractions::NUMERIC / rt.total_extractions * 0.7 + rt.avg_confidence_score * 0.3)
        ELSE 0
    END as effectiveness_score
FROM receipt_templates rt
JOIN companies c ON c.id = rt.company_id
LEFT JOIN receipts r ON r.template_id = rt.id
WHERE rt.is_active = true
GROUP BY rt.id, rt.template_name, c.name, rt.confidence_threshold,
         rt.total_extractions, rt.successful_extractions, rt.avg_confidence_score
ORDER BY effectiveness_score DESC;

-- Function: Template error pattern analysis
CREATE OR REPLACE FUNCTION analyze_template_errors(
    p_template_id UUID,
    p_days_back INTEGER DEFAULT 30
)
RETURNS TABLE (
    error_type TEXT,
    error_count INTEGER,
    avg_confidence NUMERIC,
    common_issues TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        'Low Confidence'::TEXT as error_type,
        COUNT(*)::INTEGER as error_count,
        AVG(r.extraction_confidence) as avg_confidence,
        ARRAY_AGG(DISTINCT
            CASE
                WHEN r.extraction_confidence < 0.5 THEN 'Very Low Confidence'
                WHEN r.extraction_confidence < 0.7 THEN 'Low Confidence'
                ELSE 'Moderate Confidence'
            END
        ) as common_issues
    FROM receipts r
    WHERE r.template_id = p_template_id
    AND r.created_at >= NOW() - (p_days_back || ' days')::INTERVAL
    AND (r.extraction_confidence IS NULL OR r.extraction_confidence < 0.8)

    UNION ALL

    SELECT
        'Missing Items'::TEXT as error_type,
        COUNT(*)::INTEGER as error_count,
        AVG(r.extraction_confidence) as avg_confidence,
        ARRAY['No items extracted', 'Item matching failed']::TEXT[] as common_issues
    FROM receipts r
    LEFT JOIN receipt_items ri ON ri.receipt_id = r.id
    WHERE r.template_id = p_template_id
    AND r.created_at >= NOW() - (p_days_back || ' days')::INTERVAL
    AND ri.id IS NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ANALYTICS SUMMARY QUERIES
-- =====================================================

-- Query: Overall system analytics summary
CREATE OR REPLACE VIEW system_analytics_summary AS
SELECT
    COUNT(DISTINCT lm.id) as total_customers,
    COUNT(DISTINCT c.id) as total_businesses,
    COUNT(DISTINCT bi.id) as total_business_items,
    COUNT(DISTINCT rt.id) as total_templates,
    COUNT(r.id) as total_receipts_processed,
    COUNT(ri.id) as total_items_extracted,
    SUM(r.total_amount) as total_transaction_value,
    AVG(r.extraction_confidence) as avg_extraction_confidence,
    COUNT(CASE WHEN r.template_id IS NOT NULL THEN 1 END) as template_enhanced_receipts,
    ROUND(
        (COUNT(CASE WHEN r.template_id IS NOT NULL THEN 1 END)::NUMERIC /
         NULLIF(COUNT(r.id), 0) * 100), 2
    ) as template_usage_percentage
FROM loyalty_members lm
CROSS JOIN companies c
LEFT JOIN business_items bi ON bi.company_id = c.id
LEFT JOIN receipt_templates rt ON rt.company_id = c.id
LEFT JOIN receipts r ON r.member_id = lm.id OR r.company_id = c.id
LEFT JOIN receipt_items ri ON ri.receipt_id = r.id;

COMMENT ON VIEW system_analytics_summary IS 'High-level analytics summary for dashboard KPIs';
COMMENT ON VIEW customer_item_preferences IS 'Customer purchase patterns and item preferences with scoring';
COMMENT ON VIEW business_item_performance IS 'Business item sales performance and pricing insights';
COMMENT ON VIEW category_revenue_analysis IS 'Revenue analysis by item category with trends';
COMMENT ON VIEW template_performance_metrics IS 'Template extraction accuracy and effectiveness metrics';

-- Grant permissions for analytics access
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO authenticated;
-- GRANT SELECT ON ALL VIEWS IN SCHEMA public TO authenticated;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
