-- Complete SQL script to fix all function_search_path_mutable warnings
-- This script adds SET search_path = public to the function definitions

-- 1. Fix calculate_growth_rate function
CREATE OR REPLACE FUNCTION public.calculate_growth_rate(current_value integer, previous_value integer)
RETURNS numeric
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  IF previous_value = 0 OR previous_value IS NULL THEN
    RETURN CASE WHEN current_value > 0 THEN 100.0 ELSE 0.0 END;
  END IF;

  RETURN ROUND(((current_value - previous_value)::decimal / previous_value::decimal) * 100, 2);
END;
$function$;

-- 2. Fix add_points_transaction function
CREATE OR REPLACE FUNCTION public.add_points_transaction(p_member_id uuid, p_company_id uuid, p_points integer, p_description text, p_transaction_type text DEFAULT 'EARN'::text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  v_member_exists BOOLEAN;
  v_expiration_date DATE;
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;

  -- Check if member exists and belongs to the company
  SELECT EXISTS (
    SELECT 1 FROM public.loyalty_members
    WHERE id = p_member_id AND company_id = p_company_id
  ) INTO v_member_exists;

  IF NOT v_member_exists THEN
    RAISE EXCEPTION 'Member not found or does not belong to this company';
  END IF;

  -- Set expiration date - 1 year from now for EARN transactions, far future for others
  IF p_transaction_type = 'EARN' THEN
    v_expiration_date := CURRENT_DATE + INTERVAL '1 year';
  ELSE
    v_expiration_date := '2099-12-31'::DATE;
  END IF;

  -- Create the transaction record
  INSERT INTO public.points_transactions (
    member_id,
    company_id,
    points_change,
    description,
    transaction_type,
    expiration_date,
    transaction_date
  ) VALUES (
    p_member_id,
    p_company_id,
    p_points,
    p_description,
    p_transaction_type,
    v_expiration_date,
    CURRENT_TIMESTAMP
  );

  -- Update the member's lifetime points
  UPDATE public.loyalty_members
  SET lifetime_points = COALESCE(lifetime_points, 0) + p_points
  WHERE id = p_member_id AND company_id = p_company_id;
END;
$function$;

-- 3. Fix update_modified_timestamp function
CREATE OR REPLACE FUNCTION public.update_modified_timestamp()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$function$;

-- 4. Fix update_notification_status function
CREATE OR REPLACE FUNCTION public.update_notification_status(notification_id uuid, new_status text, attempt_successful boolean DEFAULT true)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  UPDATE public.member_notifications
  SET 
    delivery_status = new_status,
    delivery_attempts = delivery_attempts + 1,
    last_attempt_at = now(),
    updated_at = now()
  WHERE id = notification_id;
  
  -- If notification was read, update read_at timestamp
  IF new_status = 'DELIVERED' AND attempt_successful = true THEN
    UPDATE public.member_notifications
    SET is_read = true, read_at = now()
    WHERE id = notification_id AND is_read = false;
  END IF;
END;
$function$;

-- 5. Fix update_points_on_expiration function
CREATE OR REPLACE FUNCTION public.update_points_on_expiration()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  -- Prevent recursion
  IF pg_trigger_depth() > 1 THEN
    RETURN NEW;
  END IF;
  
  IF NEW.transaction_type = 'EXPIRE' THEN
    UPDATE public.loyalty_members
    SET expired_points = expired_points + ABS(NEW.points_change)
    WHERE id = NEW.member_id 
      AND company_id = NEW.company_id;  -- Add multi-tenant support
  END IF;
  
  RETURN NEW;
END;
$function$;

-- 6. Fix update_timestamp function
CREATE OR REPLACE FUNCTION public.update_timestamp()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  -- Set search path explicitly
  SET search_path TO public, pg_temp;
  
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$function$;

-- Note: You'll need to retrieve and fix the remaining functions in the same way.
-- For each function, add SECURITY DEFINER and SET search_path = public to the function definition.
