// <PERSON>ript to create the admin check function in Supabase
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase client with service role key
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createAdminCheckFunction() {
  try {
    console.log('Creating admin check function...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'create-admin-check-function.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      console.error('Error creating function:', error);
      
      // Try alternative approach - execute the function creation directly
      console.log('Trying alternative approach...');
      
      const { data: funcData, error: funcError } = await supabase
        .from('pg_proc')
        .select('*')
        .eq('proname', 'check_user_admin_status')
        .limit(1);
        
      if (funcError) {
        console.log('Function does not exist, creating it...');
        
        // Create the function using raw SQL execution
        const createFunctionSQL = `
CREATE OR REPLACE FUNCTION check_user_admin_status(user_id UUID)
RETURNS TABLE (
  admin_type TEXT,
  company_id UUID,
  company_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Check if user is company owner (administrator_id in companies table)
  RETURN QUERY
  SELECT 
    'owner'::TEXT as admin_type,
    c.id as company_id,
    c.name as company_name
  FROM companies c
  WHERE c.administrator_id = user_id
  LIMIT 1;
  
  -- If no results from companies table, check company_administrators table
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      ca.role::TEXT as admin_type,
      ca.company_id,
      c.name as company_name
    FROM company_administrators ca
    JOIN companies c ON c.id = ca.company_id
    WHERE ca.administrator_id = user_id 
      AND ca.role = 'OWNER'
    LIMIT 1;
  END IF;
  
  RETURN;
END;
$$;

-- Grant execution permissions
GRANT EXECUTE ON FUNCTION check_user_admin_status(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_user_admin_status(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION check_user_admin_status(UUID) TO anon;
        `;
        
        // This won't work directly, but let's try to create it manually
        console.log('Please run the following SQL in your Supabase SQL editor:');
        console.log(createFunctionSQL);
      } else {
        console.log('Function already exists');
      }
    } else {
      console.log('Function created successfully:', data);
    }
    
    // Test the function with a known user ID
    console.log('Testing function...');
    const testUserId = '0557e2e2-75dd-4b23-a6fb-ad5ac0211b00'; // From the logs
    
    const { data: testData, error: testError } = await supabase
      .rpc('check_user_admin_status', { user_id: testUserId });
      
    if (testError) {
      console.error('Error testing function:', testError);
    } else {
      console.log('Function test result:', testData);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

createAdminCheckFunction();
