-- Update the create_points_transaction function to include receipt_image_url parameter
CREATE OR REPLACE FUNCTION public.create_points_transaction(
  p_member_id uuid,
  p_company_id uuid,
  p_transaction_type text,
  p_points_change integer,
  p_description text,
  p_transaction_date timestamp with time zone,
  p_expiration_date date,
  p_total_amount numeric DEFAULT NULL::numeric,
  p_business_name text DEFAULT NULL::text,
  p_receipt_number text DEFAULT NULL::text,
  p_receipt_ocr_confidence numeric DEFAULT NULL::numeric,
  p_receipt_processing_status text DEFAULT NULL::text,
  p_receipt_image_url text DEFAULT NULL::text
)
RETURNS SETOF points_transactions
LANGUAGE sql
SECURITY DEFINER
AS $function$
    INSERT INTO points_transactions (
      member_id,
      company_id,
      transaction_type,
      points_change,
      description,
      transaction_date,
      expiration_date,
      total_amount,
      business_name,
      receipt_number,
      receipt_ocr_confidence,
      receipt_processing_status,
      receipt_image_url
    )
    VALUES (
      p_member_id,
      p_company_id,
      p_transaction_type,
      p_points_change,
      p_description,
      p_transaction_date,
      p_expiration_date,
      p_total_amount,
      p_business_name,
      p_receipt_number,
      p_receipt_ocr_confidence,
      p_receipt_processing_status,
      p_receipt_image_url
    )
    RETURNING *;
$function$;
