/**
 * Auth Flow Test Script
 * 
 * This script tests the authentication flow end-to-end.
 * It verifies:
 * 1. Login functionality
 * 2. Session persistence
 * 3. Protected route access
 * 4. Logout functionality
 * 
 * To run this test:
 * 1. Make sure you have a test user in your Supabase instance
 * 2. Set the TEST_EMAIL and TEST_PASSWORD environment variables
 * 3. Run: npx ts-node auth-flow-test.ts
 */

import { createClient } from '@supabase/supabase-js'
import dotenv from 'dotenv'
// We're keeping the test file with direct client initialization for isolation

// Load environment variables
dotenv.config({ path: '.env.local' })

// For tests, we'll continue using a direct client to avoid any singleton issues
// This ensures tests are isolated and don't interfere with the main application
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

// Note: In production code, we would use getSupabaseClient() instead

// Test credentials (should be set as environment variables)
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>'
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'password'

async function testAuthFlow() {
  console.log('🔑 Testing Authentication Flow')
  console.log('----------------------------')

  try {
    // Step 1: Sign in
    console.log('Step 1: Testing sign in...')
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD,
    })

    if (signInError) {
      throw new Error(`Sign in failed: ${signInError.message}`)
    }

    console.log('✅ Sign in successful')
    console.log(`Session expires at: ${signInData.session?.expires_at ? 
      new Date(signInData.session.expires_at * 1000).toLocaleString() : 'Unknown'}`)

    // Step 2: Get user profile
    console.log('\nStep 2: Testing session persistence...')
    const { data: userData, error: userError } = await supabase.auth.getUser()

    if (userError || !userData.user) {
      throw new Error(`Failed to get user: ${userError?.message || 'User not found'}`)
    }

    console.log('✅ Session persistence verified')
    console.log(`User ID: ${userData.user.id}`)
    console.log(`Email: ${userData.user.email}`)

    // Step 3: Test protected route access
    // This is a simplified test - in a real app, you'd use a headless browser like Puppeteer
    console.log('\nStep 3: Testing protected route access...')
    console.log('Note: This is a simplified test. For complete testing, use Cypress or Playwright.')
    
    // Get the access token
    const accessToken = signInData.session?.access_token
    
    if (!accessToken) {
      throw new Error('No access token found')
    }
    
    // Make a request to a protected route
    console.log('Attempting to access a protected route...')
    console.log('In a real test, you would verify that the middleware redirects unauthenticated users')
    console.log('and allows authenticated users to access protected routes.')
    
    // Step 4: Sign out
    console.log('\nStep 4: Testing sign out...')
    const { error: signOutError } = await supabase.auth.signOut()

    if (signOutError) {
      throw new Error(`Sign out failed: ${signOutError.message}`)
    }

    console.log('✅ Sign out successful')

    // Verify session is gone
    const { data: sessionData } = await supabase.auth.getSession()
    
    if (sessionData.session) {
      throw new Error('Session still exists after sign out')
    }
    
    console.log('✅ Session correctly removed after sign out')

    console.log('\n🎉 All auth flow tests passed!')
    
  } catch (error) {
    console.error('\n❌ Test failed:', error)
    process.exit(1)
  }
}

// Run the test
testAuthFlow()
