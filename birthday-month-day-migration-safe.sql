-- SAFE Migration: Update birthday field to store only month-day format
-- This migration includes proper error handling and verification steps

-- Step 0: Verify current state
DO $$
BEGIN
    -- Check if birthday_month_day column already exists
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'loyalty_members'
        AND column_name = 'birthday_month_day'
    ) THEN
        RAISE NOTICE 'Column birthday_month_day already exists, skipping creation';
    ELSE
        -- Step 1: Add new birthday_month_day column
        ALTER TABLE loyalty_members
        ADD COLUMN birthday_month_day VARCHAR(5) CHECK (birthday_month_day ~ '^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$');

        RAISE NOTICE 'Added birthday_month_day column';
    END IF;
END $$;

-- Step 2: Populate the new column with existing birthday data (month-day format)
UPDATE loyalty_members
SET birthday_month_day = TO_CHAR(birthday, 'MM-DD')
WHERE birthday IS NOT NULL
AND birthday_month_day IS NULL;

-- Step 3: Verify data migration
DO $$
DECLARE
    total_members INTEGER;
    migrated_members INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_members FROM loyalty_members WHERE birthday IS NOT NULL;
    SELECT COUNT(*) INTO migrated_members FROM loyalty_members WHERE birthday_month_day IS NOT NULL;

    RAISE NOTICE 'Total members with birthday: %, Migrated: %', total_members, migrated_members;

    IF total_members != migrated_members THEN
        RAISE EXCEPTION 'Birthday migration failed: Expected % migrated members, got %', total_members, migrated_members;
    END IF;
END $$;

-- Step 4: Update the birthday eligibility function to use the new format
CREATE OR REPLACE FUNCTION is_member_birthday_eligible(member_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  member_birthday_md VARCHAR(5);
  birthday_this_year DATE;
BEGIN
  -- Get the member's birthday in MM-DD format
  SELECT birthday_month_day INTO member_birthday_md
  FROM loyalty_members
  WHERE id = member_id;

  -- If no birthday data, return false
  IF member_birthday_md IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Create birthday date for current year
  birthday_this_year := TO_DATE(EXTRACT(YEAR FROM CURRENT_DATE)::TEXT || '-' || member_birthday_md, 'YYYY-MM-DD');

  -- Check if current date is within 7 days before or after birthday
  RETURN
    CURRENT_DATE BETWEEN
      (birthday_this_year - INTERVAL '7 DAYS')
    AND
      (birthday_this_year + INTERVAL '7 DAYS');
EXCEPTION
  WHEN OTHERS THEN
    -- Handle invalid date formats gracefully
    RETURN FALSE;
END;
$$;

-- Step 5: Safely drop and recreate the get_birthday_eligible_members function
DROP FUNCTION IF EXISTS get_birthday_eligible_members(UUID);

CREATE FUNCTION get_birthday_eligible_members(p_company_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  email TEXT,
  phone_number TEXT,
  birthday_month_day VARCHAR(5),
  telegram_chat_id TEXT,
  available_points INTEGER
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.name,
    m.email,
    m.phone_number,
    m.birthday_month_day,
    m.telegram_chat_id,
    mp.available_points
  FROM
    loyalty_members m
  JOIN
    member_points_live mp ON m.id = mp.id
  WHERE
    m.company_id = p_company_id
    AND m.telegram_chat_id IS NOT NULL
    AND m.birthday_month_day IS NOT NULL
    AND is_member_birthday_eligible(m.id) = TRUE;
END;
$$;

-- Step 6: Test the new functions
DO $$
DECLARE
    test_result BOOLEAN;
    test_count INTEGER;
BEGIN
    -- Test birthday eligibility function
    SELECT is_member_birthday_eligible(id) INTO test_result
    FROM loyalty_members
    WHERE birthday_month_day IS NOT NULL
    LIMIT 1;

    RAISE NOTICE 'Birthday eligibility function test completed';

    -- Test get_birthday_eligible_members function
    SELECT COUNT(*) INTO test_count
    FROM get_birthday_eligible_members('00000000-0000-0000-0000-000000000000');

    RAISE NOTICE 'get_birthday_eligible_members function test completed, returned % rows', test_count;
END $$;

-- Step 7: Verification queries
SELECT
    'Migration Verification' as step,
    COUNT(*) as total_members,
    COUNT(birthday) as members_with_birthday,
    COUNT(birthday_month_day) as members_with_month_day,
    COUNT(CASE WHEN birthday IS NOT NULL AND birthday_month_day IS NULL THEN 1 END) as migration_failures
FROM loyalty_members;

-- Final success message
RAISE NOTICE 'Birthday month-day migration completed successfully!';
