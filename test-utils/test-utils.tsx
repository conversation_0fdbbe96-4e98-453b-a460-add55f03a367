import React, { ReactElement } from 'react'
import { render, RenderOptions, act, waitFor } from '@testing-library/react'
import { ThemeProvider } from 'next-themes'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { server } from './msw-handlers'

// Create a more stable testing query client
export const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      gcTime: Infinity,
      staleTime: Infinity,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
    },
    mutations: {
      retry: false,
    },
  },
})

// Global setup for MSW
beforeAll(() => server.listen({ onUnhandledRequest: 'error' }))
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

// Mock matchMedia if not available (for next-themes)
if (typeof window !== 'undefined') {
  window.matchMedia = window.matchMedia || function () {
    return {
      matches: false,
      addListener: () => {},
      removeListener: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => true,
    }
  }
}

interface CustomRenderOptions extends Omit<RenderOptions, 'queries'> {
  withReactQuery?: boolean
  withTheme?: boolean
}

export function customRender(
  ui: ReactElement,
  {
    withReactQuery = true,
    withTheme = true,
    ...renderOptions
  }: CustomRenderOptions = {}
) {
  const queryClient = createTestQueryClient()
  
  function Wrapper({ children }: { children: React.ReactNode }) {
    // Apply wrappers based on options
    let wrapped = <>{children}</>

    if (withReactQuery) {
      wrapped = (
        <QueryClientProvider client={queryClient}>
          {wrapped}
        </QueryClientProvider>
      )
    }

    if (withTheme) {
      wrapped = (
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          {wrapped}
        </ThemeProvider>
      )
    }

    return wrapped
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

/**
 * Creates a render hook wrapper with QueryClientProvider
 * Use this with renderHook from RTL for consistent React Query hook testing
 */
export function createQueryWrapper(customQueryClient?: QueryClient) {
  const queryClient = customQueryClient || createTestQueryClient()
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

/**
 * Utility to forcibly set mock data for a query
 * This bypasses the actual API call and directly sets data for testing
 */
export function setQueryData(queryClient: QueryClient, queryKey: any[], mockData: any) {
  return queryClient.setQueryData(queryKey, mockData);
}

// Export additional testing utilities
export * from '@testing-library/react'
