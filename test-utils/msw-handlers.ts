import { rest } from 'msw'
import { setupServer } from 'msw/node'
import { testMembers, testRewards, testRedemptions, testReceipts } from './fixtures/index'

// Create handlers using MSW v1 rest API
export const handlers = [
  // Members API handlers
  rest.get('/api/members', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ data: testMembers })
    )
  }),
  
  rest.get('/api/members/:id', (req, res, ctx) => {
    const id = req.params.id
    const member = testMembers.find(m => m.id === id)
    
    if (member) {
      return res(ctx.status(200), ctx.json(member))
    }
    
    return res(ctx.status(404), ctx.json({ message: 'Member not found' }))
  }),
  
  rest.get('/api/members/find', (req, res, ctx) => {
    const email = req.url.searchParams.get('email')
    const phone = req.url.searchParams.get('phone')
    
    const member = testMembers.find(m => 
      (email && m.email === email) || (phone && m.phone === phone)
    )
    
    if (member) {
      return res(ctx.status(200), ctx.json({ data: member }))
    }
    
    return res(ctx.status(404), ctx.json({ message: 'Member not found' }))
  }),
  
  rest.post('/api/members', (req, res, ctx) => {
    // Cast request body to any to avoid TypeScript errors with spread
    const body = req.body as any
    const newMember = {
      id: '3',
      name: body.name,
      email: body.email,
      phone: body.phone,
      loyalty_id: body.loyalty_id,
      loyalty_tier: body.loyalty_tier,
      points: 0,
      company_id: '1',
      created_at: new Date().toISOString()
    }
    
    return res(ctx.status(201), ctx.json({ data: newMember }))
  }),
  
  // Rewards API handlers
  rest.get('/api/rewards', (req, res, ctx) => {
    // Return rewards with proper field names matching the Reward type in hooks
    const formattedRewards = testRewards.map(reward => ({
      id: reward.id,
      name: reward.title, // map title to name
      description: reward.description,
      points_cost: reward.points_required, // map points_required to points_cost
      active: reward.is_active, // map is_active to active
      image_url: `/images/rewards/${reward.id}.jpg`,
      company_id: reward.company_id,
      created_at: reward.created_at
    }))
    
    return res(
      ctx.status(200),
      ctx.json({ data: formattedRewards })
    )
  }),
  
  rest.get('/api/rewards/:id', (req, res, ctx) => {
    const id = req.params.id
    const reward = testRewards.find(r => r.id === id)
    
    if (reward) {
      // Format reward to match hook expectations
      const formattedReward = {
        id: reward.id,
        name: reward.title,
        description: reward.description,
        points_cost: reward.points_required,
        active: reward.is_active,
        image_url: `/images/rewards/${reward.id}.jpg`,
        company_id: reward.company_id,
        created_at: reward.created_at
      }
      
      return res(ctx.status(200), ctx.json(formattedReward))
    }
    
    return res(ctx.status(404), ctx.json({ message: 'Reward not found' }))
  }),
  
  rest.post('/api/rewards', (req, res, ctx) => {
    // Cast request body to any to avoid TypeScript errors with spread
    const body = req.body as any
    const newReward = {
      id: '3',
      name: body.name,
      description: body.description,
      points_cost: body.points_cost,
      active: body.active,
      image_url: body.image_url,
      company_id: body.company_id || '1',
      created_at: new Date().toISOString()
    }
    
    return res(ctx.status(201), ctx.json({ data: newReward }))
  }),
  
  // Redemptions API handlers
  rest.get('/api/redemptions', (req, res, ctx) => {
    const memberId = req.url.searchParams.get('memberId')
    
    if (memberId) {
      const filteredRedemptions = testRedemptions.filter(r => r.member_id === memberId)
      return res(ctx.status(200), ctx.json({ data: filteredRedemptions }))
    }
    
    return res(
      ctx.status(200),
      ctx.json({ data: testRedemptions })
    )
  }),
  
  rest.post('/api/redemptions', (req, res, ctx) => {
    // Cast request body to any to avoid TypeScript errors with spread
    const body = req.body as any
    const newRedemption = {
      id: '2',
      reward_id: body.reward_id,
      member_id: body.member_id,
      points_used: body.points_used,
      company_id: body.company_id || '1',
      status: 'redeemed',
      created_at: new Date().toISOString()
    }
    
    return res(ctx.status(201), ctx.json({ data: newRedemption }))
  }),
  
  // Receipts API handlers
  rest.get('/api/receipts', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({ data: testReceipts })
    )
  }),
  
  rest.post('/api/receipts', (req, res, ctx) => {
    // Cast request body to any to avoid TypeScript errors with spread
    const body = req.body as any
    const newReceipt = {
      id: '3',
      member_id: body.member_id,
      company_id: body.company_id || '1',
      amount: body.amount,
      receipt_number: body.receipt_number,
      receipt_date: body.receipt_date,
      receipt_items: body.receipt_items,
      points_earned: body.points_earned,
      created_at: new Date().toISOString()
    }
    
    return res(ctx.status(201), ctx.json({ data: newReceipt }))
  })
]

// Create MSW server
export const server = setupServer(...handlers)
