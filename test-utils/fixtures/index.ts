// Common test fixtures for use across tests

export type Member = {
  id: string
  loyalty_id: string
  telegram_chat_id: string | null
  name: string
  phone: string
  email: string | null
  tier: 'SILVER' | 'GOLD' | 'PLATINUM'
  available_points: number
  lifetime_points: number
  created_at: string
  updated_at: string
  company_id: string
}

export type Reward = {
  id: string
  company_id: string
  code: string
  title: string
  description: string
  type: 'PERCENTAGE' | 'FIXED'
  value: number
  points_required: number
  expires_at: string | null
  is_active: boolean
  created_at: string
  updated_at: string
}

export type Receipt = {
  id: string
  member_id: string
  company_id: string
  amount: number
  points_earned: number
  receipt_number: string
  created_at: string
}

export type Redemption = {
  id: string
  member_id: string
  reward_id: string
  company_id: string
  points_used: number
  created_at: string
}

export type TierDefinition = {
  id: string
  company_id: string
  tier: 'SILVER' | 'GOLD' | 'PLATINUM'
  minimum_points: number
  created_at: string
  updated_at: string
}

// Test Members
export const testMembers: Member[] = [
  {
    id: '1',
    company_id: '1',
    loyalty_id: 'LOY123',
    telegram_chat_id: '*********',
    name: 'Test User',
    phone: '+251912345678',
    email: '<EMAIL>',
    tier: 'SILVER',
    available_points: 500,
    lifetime_points: 1000,
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  },
  {
    id: '2',
    company_id: '1',
    loyalty_id: 'LOY456',
    telegram_chat_id: '*********',
    name: 'Another User',
    phone: '+251*********',
    email: '<EMAIL>',
    tier: 'GOLD',
    available_points: 1500,
    lifetime_points: 2500,
    created_at: '2025-01-02T00:00:00.000Z',
    updated_at: '2025-01-02T00:00:00.000Z',
  },
  {
    id: '3',
    company_id: '1',
    loyalty_id: 'LOY789',
    telegram_chat_id: null,
    name: 'Web User',
    phone: '+251923456789',
    email: '<EMAIL>',
    tier: 'PLATINUM',
    available_points: 3500,
    lifetime_points: 5000,
    created_at: '2025-01-03T00:00:00.000Z',
    updated_at: '2025-01-03T00:00:00.000Z',
  }
]

// Test Rewards
export const testRewards: Reward[] = [
  {
    id: '1',
    company_id: '1',
    code: 'AB12',
    title: 'Discount Reward',
    description: 'Get 10% off your next purchase',
    type: 'PERCENTAGE',
    value: 10,
    points_required: 100,
    expires_at: '2025-12-31T00:00:00.000Z',
    is_active: true,
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  },
  {
    id: '2',
    company_id: '1',
    code: 'CD34',
    title: 'Free Service',
    description: 'Get a free service',
    type: 'FIXED',
    value: 200,
    points_required: 500,
    expires_at: '2025-12-31T00:00:00.000Z',
    is_active: true,
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  },
  {
    id: '3',
    company_id: '1',
    code: 'EF56',
    title: 'Premium Service',
    description: 'Get a premium service',
    type: 'FIXED',
    value: 500,
    points_required: 1000,
    expires_at: null,
    is_active: true,
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  }
]

// Test Tier Definitions
export const testTierDefinitions: TierDefinition[] = [
  {
    id: '1',
    company_id: '1',
    tier: 'SILVER',
    minimum_points: 0,
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  },
  {
    id: '2',
    company_id: '1',
    tier: 'GOLD',
    minimum_points: 2000,
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  },
  {
    id: '3',
    company_id: '1',
    tier: 'PLATINUM',
    minimum_points: 5000,
    created_at: '2025-01-01T00:00:00.000Z',
    updated_at: '2025-01-01T00:00:00.000Z',
  }
]

// Test Receipts
export const testReceipts: Receipt[] = [
  {
    id: '1',
    member_id: '1',
    company_id: '1',
    amount: 500,
    points_earned: 500,
    receipt_number: 'R12345',
    created_at: '2025-01-05T00:00:00.000Z',
  },
  {
    id: '2',
    member_id: '1',
    company_id: '1',
    amount: 300,
    points_earned: 300,
    receipt_number: 'R12346',
    created_at: '2025-01-06T00:00:00.000Z',
  },
  {
    id: '3',
    member_id: '2',
    company_id: '1',
    amount: 1000,
    points_earned: 1000,
    receipt_number: 'R12347',
    created_at: '2025-01-07T00:00:00.000Z',
  }
]

// Test Redemptions
export const testRedemptions: Redemption[] = [
  {
    id: '1',
    member_id: '1',
    reward_id: '1',
    company_id: '1',
    points_used: 100,
    created_at: '2025-01-10T00:00:00.000Z',
  },
  {
    id: '2',
    member_id: '2',
    reward_id: '2',
    company_id: '1',
    points_used: 500,
    created_at: '2025-01-11T00:00:00.000Z',
  }
]
