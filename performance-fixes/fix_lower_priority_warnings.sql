-- Fix lower priority Supabase performance warnings
-- Addresses auth_rls_initplan warnings by replacing direct auth function calls with subselects

-- 6. Fix Auth RLS Initialization Plan in Other Tables

-- Update tenant_isolation_policy on company_administrators
ALTER POLICY tenant_isolation_policy ON public.company_administrators 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update tenant_isolation_policy on member_notifications
ALTER POLICY tenant_isolation_policy ON public.member_notifications 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update tenant_isolation_policy on program_rules
ALTER POLICY tenant_isolation_policy ON public.program_rules 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update tenant_isolation_policy on reward_tier_eligibility
ALTER POLICY tenant_isolation_policy ON public.reward_tier_eligibility 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update tenant_isolation_policy on tier_definitions
ALTER POLICY tenant_isolation_policy ON public.tier_definitions 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Verify changes
COMMENT ON POLICY tenant_isolation_policy ON public.company_administrators IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY tenant_isolation_policy ON public.member_notifications IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY tenant_isolation_policy ON public.program_rules IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY tenant_isolation_policy ON public.reward_tier_eligibility IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY tenant_isolation_policy ON public.tier_definitions IS 'Fixed auth_rls_initplan warning by using subselect';
