-- Fix for security_definer_view issue on public.member_summary
-- Drop the existing view
DROP VIEW IF EXISTS public.member_summary;

-- Recreate the view with explicit SECURITY INVOKER setting
CREATE OR REPLACE VIEW public.member_summary 
WITH (security_invoker = on) AS
SELECT 
  mp.id,
  mp.name,
  mp.loyalty_id,
  mp.available_points,
  mp.lifetime_points,
  mp.redeemed_points,
  mp.expired_points
FROM member_points mp;

-- Add proper comment to the view
COMMENT ON VIEW public.member_summary IS 'Summary view of loyalty members with company information (SECURITY INVOKER)';


-- Fix for rls_disabled_in_public issue on public.dashboard_metrics_history
-- Enable RLS on the table
ALTER TABLE public.dashboard_metrics_history ENABLE ROW LEVEL SECURITY;

-- Drop the existing policy if it exists
DROP POLICY IF EXISTS dashboard_metrics_company_access ON public.dashboard_metrics_history;

-- Create a policy that allows access only to users who belong to the company
CREATE POLICY dashboard_metrics_company_access
  ON public.dashboard_metrics_history
  FOR ALL
  USING (company_id = (auth.jwt() ->> 'company_id')::uuid OR 
         EXISTS (
           SELECT 1 FROM companies 
           WHERE companies.id = dashboard_metrics_history.company_id 
           AND companies.administrator_id = auth.uid()
         ));

-- Drop the existing service role policy if it exists
DROP POLICY IF EXISTS dashboard_metrics_service_access ON public.dashboard_metrics_history;

-- Create a policy for service role access (bypasses RLS)
CREATE POLICY dashboard_metrics_service_access
  ON public.dashboard_metrics_history
  FOR ALL
  USING (current_setting('role') = 'service_role');

COMMENT ON TABLE public.dashboard_metrics_history IS 'Historical metrics data with row-level security enabled';
