-- Fix medium priority Supabase performance warnings
-- Addresses auth_rls_initplan warnings by replacing direct auth function calls with subselects

-- 4. Fix Rewards and Redemptions Tables Policies

-- Update tenant_isolation_policy on rewards
ALTER POLICY tenant_isolation_policy ON public.rewards 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update Rewards are viewable by their company policy
ALTER POLICY "Rewards are viewable by their company" ON public.rewards 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

-- Update tenant_isolation_policy on reward_redemptions
ALTER POLICY tenant_isolation_policy ON public.reward_redemptions 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update Reward redemptions are viewable by their company policy
ALTER POLICY "Reward redemptions are viewable by their company" ON public.reward_redemptions 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

-- 5. Fix Dashboard Metrics History Policies

-- Update dashboard_metrics_company_access policy
ALTER POLICY dashboard_metrics_company_access ON public.dashboard_metrics_history 
USING (((company_id = ((SELECT (auth.jwt() ->> 'company_id'::text))::uuid)) OR 
       (EXISTS ( SELECT 1 FROM companies WHERE ((companies.id = dashboard_metrics_history.company_id) AND 
                                              ((SELECT auth.uid()) = companies.administrator_id))))));

-- Update dashboard_metrics_service_access policy
ALTER POLICY dashboard_metrics_service_access ON public.dashboard_metrics_history 
USING (((SELECT auth.role()) = 'service_role'::text));

-- Verify changes
COMMENT ON POLICY tenant_isolation_policy ON public.rewards IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY "Rewards are viewable by their company" ON public.rewards IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY tenant_isolation_policy ON public.reward_redemptions IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY "Reward redemptions are viewable by their company" ON public.reward_redemptions IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY dashboard_metrics_company_access ON public.dashboard_metrics_history IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY dashboard_metrics_service_access ON public.dashboard_metrics_history IS 'Fixed auth_rls_initplan warning by using subselect';
