-- Fix for remaining function_search_path_mutable warnings

-- Fix for get_member_engagement (second overload)
CREATE OR REPLACE FUNCTION public.get_member_engagement(p_start_date date, p_end_date date, p_tiers text[] DEFAULT NULL::text[])
RETURNS TABLE(date date, earned bigint, spent bigint, balance bigint)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN QUERY
  SELECT
    date_trunc('day', pt.created_at)::date AS date,
    SUM(CASE WHEN pt.amount > 0 THEN pt.amount ELSE 0 END)::BIGINT AS earned,
    SUM(CASE WHEN pt.amount < 0 THEN ABS(pt.amount) ELSE 0 END)::BIGINT AS spent,
    SUM(pt.amount)::BIGINT AS balance
  FROM points_transactions pt
  JOIN loyalty_members lm ON pt.member_id = lm.id
  WHERE pt.created_at BETWEEN p_start_date AND p_end_date
    AND (p_tiers IS NULL OR lm.loyalty_tier = ANY(p_tiers))
  GROUP BY 1
  ORDER BY 1;
END;
$function$;

-- Fix for get_location_distribution (no parameter version)
CREATE OR REPLACE FUNCTION public.get_location_distribution()
RETURNS TABLE(region text, count bigint, percentage numeric)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
DECLARE
  total_count BIGINT;
BEGIN
  -- Get total count
  SELECT COUNT(*) INTO total_count FROM loyalty_members;
  
  -- This is a stub - replace with your actual location data
  -- For now, just returning a dummy record
  RETURN QUERY
  SELECT
    'All Regions' AS region,
    total_count AS count,
    100.0 AS percentage;
END;
$function$;

-- Fix for get_member_growth
CREATE OR REPLACE FUNCTION public.get_member_growth(p_months integer DEFAULT 12)
RETURNS TABLE(month text, new_members bigint, total_members bigint, growth_rate numeric)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN QUERY
  WITH months AS (
    SELECT generate_series(
      date_trunc('month', CURRENT_DATE) - ((p_months - 1) || ' months')::interval,
      date_trunc('month', CURRENT_DATE),
      '1 month'::interval
    )::date AS month_start
  ),
  monthly_counts AS (
    SELECT
      date_trunc('month', created_at)::date AS join_month,
      COUNT(*) AS new_members
    FROM loyalty_members
    WHERE created_at >= date_trunc('month', CURRENT_DATE) - ((p_months - 1) || ' months')::interval
    GROUP BY 1
  )
  SELECT
    to_char(m.month_start, 'YYYY-MM') AS month,
    COALESCE(mc.new_members, 0)::bigint AS new_members,
    SUM(COALESCE(mc.new_members, 0)) OVER (ORDER BY m.month_start)::bigint AS total_members,
    CASE
      WHEN LAG(COALESCE(mc.new_members, 0)) OVER (ORDER BY m.month_start) = 0 THEN NULL
      ELSE ROUND(
        (COALESCE(mc.new_members, 0) - LAG(COALESCE(mc.new_members, 0)) OVER (ORDER BY m.month_start))::numeric /
        NULLIF(LAG(COALESCE(mc.new_members, 0)) OVER (ORDER BY m.month_start), 0) * 100,
        2
      )
    END AS growth_rate
  FROM months m
  LEFT JOIN monthly_counts mc ON m.month_start = mc.join_month
  ORDER BY m.month_start;
END;
$function$;

-- Fix for get_tier_distribution (no parameter version)
CREATE OR REPLACE FUNCTION public.get_tier_distribution()
RETURNS TABLE(tier text, count bigint, percentage numeric)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN QUERY
  WITH tier_counts AS (
    SELECT
      COALESCE(loyalty_tier, 'None') AS tier_name,
      COUNT(*) AS member_count
    FROM loyalty_members
    GROUP BY loyalty_tier
  ),
  total AS (
    SELECT SUM(member_count) AS total_members FROM tier_counts
  )
  SELECT
    tc.tier_name,
    tc.member_count::bigint,
    ROUND((tc.member_count::numeric / t.total_members) * 100, 2) AS percentage
  FROM tier_counts tc, total t
  ORDER BY tc.member_count DESC;
END;
$function$;

-- Fix for get_top_members (all overloads)
CREATE OR REPLACE FUNCTION public.get_top_members(p_limit integer DEFAULT 10)
RETURNS TABLE(id uuid, name text, email text, points integer, tier text)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $function$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.name,
    m.email,
    m.lifetime_points - COALESCE(m.redeemed_points, 0) - COALESCE(m.expired_points, 0) AS points,
    m.loyalty_tier
  FROM loyalty_members m
  ORDER BY points DESC
  LIMIT p_limit;
END;
$function$;

-- Fix for materialized_view_in_api warning
-- Revoke access from anon and authenticated roles
REVOKE SELECT ON public.dashboard_metrics FROM anon, authenticated;

-- Create a secure view that filters data based on user's company_id
CREATE OR REPLACE VIEW public.secure_dashboard_metrics AS
SELECT dm.*
FROM public.dashboard_metrics dm
WHERE dm.company_id = (auth.jwt() ->> 'company_id')::uuid;

-- Grant access to the secure view
GRANT SELECT ON public.secure_dashboard_metrics TO authenticated;
