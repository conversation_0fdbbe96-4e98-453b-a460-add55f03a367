-- <PERSON><PERSON>t to synchronize company_administrators table with companies table
-- This addresses the data consistency issue where companies have administrator_id values
-- but the company_administrators table is empty

-- First, let's check the current state
SELECT 'Before synchronization' as status;
SELECT COUNT(*) as companies_count FROM companies;
SELECT COUNT(*) as company_administrators_count FROM company_administrators;

-- Insert missing company administrators based on companies.administrator_id
INSERT INTO company_administrators (
    id,
    administrator_id,
    company_id,
    created_at,
    role
)
SELECT 
    gen_random_uuid() as id,
    administrator_id,
    id as company_id,
    NOW() as created_at,
    'OWNER' as role
FROM 
    companies c
WHERE 
    NOT EXISTS (
        SELECT 1 
        FROM company_administrators ca 
        WHERE ca.company_id = c.id AND ca.administrator_id = c.administrator_id
    );

-- Verify the results after synchronization
SELECT 'After synchronization' as status;
SELECT COUNT(*) as companies_count FROM companies;
SELECT COUNT(*) as company_administrators_count FROM company_administrators;

-- Show the synchronized data
SELECT 
    c.id as company_id,
    c.name as company_name,
    c.administrator_id,
    ca.id as company_admin_id,
    ca.role as admin_role,
    u.email as admin_email
FROM 
    companies c
LEFT JOIN 
    company_administrators ca ON c.id = ca.company_id AND c.administrator_id = ca.administrator_id
LEFT JOIN 
    auth.users u ON c.administrator_id = u.id
ORDER BY 
    c.name;

-- Note: This script assumes:
-- 1. The company_administrators table has the structure we observed
-- 2. 'owner' is an appropriate role for the administrator (adjust if needed)
-- 3. The gen_random_uuid() function is available (standard in PostgreSQL)
-- 4. You have appropriate permissions to access auth.users
