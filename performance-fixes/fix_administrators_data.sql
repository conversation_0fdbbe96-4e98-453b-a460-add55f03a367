-- Sc<PERSON>t to fix data consistency issues between administrators and company_administrators tables
-- This script addresses the foreign key constraint issue by:
-- 1. First populating the administrators table with entries from auth.users
-- 2. Then populating the company_administrators table

-- Check current state
SELECT 'Before synchronization' as status;
SELECT COUNT(*) as companies_count FROM companies;
SELECT COUNT(*) as administrators_count FROM administrators;
SELECT COUNT(*) as company_administrators_count FROM company_administrators;

-- Step 1: Populate the administrators table with entries from auth.users
-- that correspond to administrator_id values in the companies table
INSERT INTO administrators (
    id,
    email,
    name,
    created_at,
    is_active
)
SELECT 
    u.id,
    u.email,
    COALESCE(u.raw_user_meta_data->>'full_name', u.email) as name,
    u.created_at,
    true as is_active
FROM 
    auth.users u
JOIN 
    companies c ON u.id = c.administrator_id
WHERE 
    NOT EXISTS (
        SELECT 1 
        FROM administrators a 
        WHERE a.id = u.id
    );

-- Check intermediate state
SELECT 'After populating administrators' as status;
SELECT COUNT(*) as administrators_count FROM administrators;

-- Step 2: Now that administrators exist, populate the company_administrators table
INSERT INTO company_administrators (
    id,
    administrator_id,
    company_id,
    created_at,
    role
)
SELECT 
    gen_random_uuid() as id,
    administrator_id,
    id as company_id,
    NOW() as created_at,
    'OWNER' as role
FROM 
    companies c
WHERE 
    NOT EXISTS (
        SELECT 1 
        FROM company_administrators ca 
        WHERE ca.company_id = c.id AND ca.administrator_id = c.administrator_id
    );

-- Verify the final results
SELECT 'After full synchronization' as status;
SELECT COUNT(*) as companies_count FROM companies;
SELECT COUNT(*) as administrators_count FROM administrators;
SELECT COUNT(*) as company_administrators_count FROM company_administrators;

-- Show the synchronized data
SELECT 
    c.id as company_id,
    c.name as company_name,
    c.administrator_id,
    a.email as admin_email,
    ca.id as company_admin_id,
    ca.role as admin_role
FROM 
    companies c
LEFT JOIN 
    administrators a ON c.administrator_id = a.id
LEFT JOIN 
    company_administrators ca ON c.id = ca.company_id AND c.administrator_id = ca.administrator_id
ORDER BY 
    c.name;

-- Note: This script:
-- 1. First populates the administrators table with data from auth.users
-- 2. Then populates the company_administrators table
-- 3. Uses 'OWNER' as the role (one of the allowed values: 'OWNER', 'ADMIN', 'MANAGER', 'REPORTS', 'CASHIER')
-- 4. Avoids duplicate entries by checking if records already exist
