-- <PERSON><PERSON><PERSON> to verify performance improvements after applying RLS policy fixes
-- Run this before and after applying the fixes to compare performance

-- 1. Test Points Transactions Table Query Performance
EXPLAIN ANALYZE
SELECT * FROM points_transactions 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
LIMIT 100;

-- 2. Test Loyalty Members Table Query Performance
EXPLAIN ANALYZE
SELECT * FROM loyalty_members 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
LIMIT 100;

-- 3. Test Companies Table Query Performance
EXPLAIN ANALYZE
SELECT * FROM companies 
WHERE id = 'd10aed7e-3116-403c-a572-c16ab870d761';

-- 4. Test Rewards Table Query Performance
EXPLAIN ANALYZE
SELECT * FROM rewards 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
LIMIT 100;

-- 5. Test Reward Redemptions Table Query Performance
EXPLAIN ANALYZE
SELECT * FROM reward_redemptions 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
LIMIT 100;

-- 6. Test Dashboard Metrics History Table Query Performance
EXPLAIN ANALYZE
SELECT * FROM dashboard_metrics_history 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
LIMIT 100;

-- 7. Test Company Administrators Table Query Performance
EXPLAIN ANALYZE
SELECT * FROM company_administrators 
WHERE company_id = 'd10aed7e-3116-403c-a572-c16ab870d761'
LIMIT 100;

-- Note: Replace '00000000-0000-0000-0000-000000000001' with an actual company ID from your database
-- You should run this script before and after applying the fixes to compare the execution plans
-- Look for differences in:
--   1. Execution time
--   2. Number of InitPlan executions
--   3. Overall query planning and execution strategy

-- Expected improvements:
--   1. Reduced number of InitPlan executions (should be just once instead of per row)
--   2. Lower execution time
--   3. More efficient query plans
