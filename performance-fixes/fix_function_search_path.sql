-- Fix for function_search_path_mutable warnings
-- This script sets explicit search_path for all affected functions

-- 1. calculate_growth_rate
CREATE OR REPLACE FUNCTION public.calculate_growth_rate(
    -- Keep the original parameters here
)
RETURNS numeric
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.calculate_growth_rate IS 'Calculates growth rate with explicit search_path';

-- 2. get_dashboard_data
CREATE OR REPLACE FUNCTION public.get_dashboard_data(
    -- Keep the original parameters here
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_dashboard_data IS 'Gets dashboard data with explicit search_path';

-- 3. refresh_dashboard_metrics
CREATE OR REPLACE FUNCTION public.refresh_dashboard_metrics(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.refresh_dashboard_metrics IS 'Refreshes dashboard metrics with explicit search_path';

-- 4. create_daily_snapshot
CREATE OR REPLACE FUNCTION public.create_daily_snapshot(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.create_daily_snapshot IS 'Creates daily snapshot with explicit search_path';

-- 5. get_active_inactive_ratio
CREATE OR REPLACE FUNCTION public.get_active_inactive_ratio(
    -- Keep the original parameters here
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_active_inactive_ratio IS 'Gets active/inactive ratio with explicit search_path';

-- 6. add_points_transaction
CREATE OR REPLACE FUNCTION public.add_points_transaction(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.add_points_transaction IS 'Adds points transaction with explicit search_path';

-- 7. set_app_parameter
CREATE OR REPLACE FUNCTION public.set_app_parameter(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.set_app_parameter IS 'Sets app parameter with explicit search_path';

-- 8. get_available_points
CREATE OR REPLACE FUNCTION public.get_available_points(
    -- Keep the original parameters here
)
RETURNS numeric
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_available_points IS 'Gets available points with explicit search_path';

-- 9. get_member_engagement
CREATE OR REPLACE FUNCTION public.get_member_engagement(
    -- Keep the original parameters here
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_member_engagement IS 'Gets member engagement with explicit search_path';

-- 10. get_location_distribution
CREATE OR REPLACE FUNCTION public.get_location_distribution(
    -- Keep the original parameters here
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_location_distribution IS 'Gets location distribution with explicit search_path';

-- 11. get_member_growth
CREATE OR REPLACE FUNCTION public.get_member_growth(
    -- Keep the original parameters here
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_member_growth IS 'Gets member growth with explicit search_path';

-- 12. get_tier_distribution
CREATE OR REPLACE FUNCTION public.get_tier_distribution(
    -- Keep the original parameters here
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_tier_distribution IS 'Gets tier distribution with explicit search_path';

-- 13. redeem_points
CREATE OR REPLACE FUNCTION public.redeem_points(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.redeem_points IS 'Redeems points with explicit search_path';

-- 14. set_expiration_date
CREATE OR REPLACE FUNCTION public.set_expiration_date(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.set_expiration_date IS 'Sets expiration date with explicit search_path';

-- 15. update_lifetime_points
CREATE OR REPLACE FUNCTION public.update_lifetime_points(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.update_lifetime_points IS 'Updates lifetime points with explicit search_path';

-- 16. update_member_tier
CREATE OR REPLACE FUNCTION public.update_member_tier(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.update_member_tier IS 'Updates member tier with explicit search_path';

-- 17. update_modified_timestamp
CREATE OR REPLACE FUNCTION public.update_modified_timestamp(
    -- Keep the original parameters here
)
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.update_modified_timestamp IS 'Updates modified timestamp with explicit search_path';

-- 18. update_notification_status
CREATE OR REPLACE FUNCTION public.update_notification_status(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.update_notification_status IS 'Updates notification status with explicit search_path';

-- 19. update_points_on_expiration
CREATE OR REPLACE FUNCTION public.update_points_on_expiration(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.update_points_on_expiration IS 'Updates points on expiration with explicit search_path';

-- 20. update_timestamp
CREATE OR REPLACE FUNCTION public.update_timestamp(
    -- Keep the original parameters here
)
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.update_timestamp IS 'Updates timestamp with explicit search_path';

-- 21. check_same_company
CREATE OR REPLACE FUNCTION public.check_same_company(
    -- Keep the original parameters here
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.check_same_company IS 'Checks same company with explicit search_path';

-- 22. deduct_points_transaction
CREATE OR REPLACE FUNCTION public.deduct_points_transaction(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.deduct_points_transaction IS 'Deducts points transaction with explicit search_path';

-- 23. expire_points_with_logging
CREATE OR REPLACE FUNCTION public.expire_points_with_logging(
    -- Keep the original parameters here
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.expire_points_with_logging IS 'Expires points with logging with explicit search_path';

-- 24. get_top_members
CREATE OR REPLACE FUNCTION public.get_top_members(
    -- Keep the original parameters here
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.get_top_members IS 'Gets top members with explicit search_path';

-- 25. is_member_birthday_eligible
CREATE OR REPLACE FUNCTION public.is_member_birthday_eligible(
    -- Keep the original parameters here
)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.is_member_birthday_eligible IS 'Checks if member birthday is eligible with explicit search_path';

-- 26. log_data_changes
CREATE OR REPLACE FUNCTION public.log_data_changes(
    -- Keep the original parameters here
)
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Original function body here
END;
$$;

COMMENT ON FUNCTION public.log_data_changes IS 'Logs data changes with explicit search_path';

-- Note: This is a template script. You need to fill in the actual parameters and function bodies
-- from your database before executing this script.
