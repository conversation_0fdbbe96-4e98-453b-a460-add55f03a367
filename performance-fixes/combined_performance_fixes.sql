-- Combined script to fix all Supabase performance warnings
-- This script addresses auth_rls_initplan warnings and multiple permissive policies

-- =============================================
-- HIGH PRIORITY FIXES
-- =============================================

-- 1. Fix Points Transactions Table Policies
ALTER POLICY tenant_isolation_transactions ON public.points_transactions 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY "Points transactions are viewable by their company" ON public.points_transactions 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

-- 2. Fix Loyalty Members Table Policies
ALTER POLICY company_isolation ON public.loyalty_members 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY tenant_isolation_members ON public.loyalty_members 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY "Members are viewable by their company" ON public.loyalty_members 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

-- 3. Consolidate Multiple Permissive Policies in Companies Table
-- First, check if the policies exist before dropping
DO $$
BEGIN
    -- Consolidate INSERT policies
    IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Company Admin Access' AND tablename = 'companies') THEN
        DROP POLICY "Company Admin Access" ON public.companies;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'insert_companies' AND tablename = 'companies') THEN
        DROP POLICY insert_companies ON public.companies;
    END IF;
    
    -- Create consolidated INSERT policy
    CREATE POLICY companies_insert_policy ON public.companies
    FOR INSERT TO public
    WITH CHECK ((auth.uid() = administrator_id));
    
    -- Consolidate SELECT policies
    IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Companies are viewable by company administrators' AND tablename = 'companies') THEN
        DROP POLICY "Companies are viewable by company administrators" ON public.companies;
    END IF;
    
    -- Note: We already dropped "Company Admin Access" above, so no need to drop it again
    
    -- Create consolidated SELECT policy
    CREATE POLICY companies_select_policy ON public.companies
    FOR SELECT TO public
    USING ((((id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)) OR 
           ((SELECT auth.uid()) = administrator_id)));
END
$$;

-- =============================================
-- MEDIUM PRIORITY FIXES
-- =============================================

-- 4. Fix Rewards and Redemptions Tables Policies
ALTER POLICY tenant_isolation_policy ON public.rewards 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY "Rewards are viewable by their company" ON public.rewards 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

ALTER POLICY tenant_isolation_policy ON public.reward_redemptions 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY "Reward redemptions are viewable by their company" ON public.reward_redemptions 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

-- 5. Fix Dashboard Metrics History Policies
ALTER POLICY dashboard_metrics_company_access ON public.dashboard_metrics_history 
USING (((company_id = ((SELECT (auth.jwt() ->> 'company_id'::text))::uuid)) OR 
       (EXISTS ( SELECT 1 FROM companies WHERE ((companies.id = dashboard_metrics_history.company_id) AND 
                                              ((SELECT auth.uid()) = companies.administrator_id))))));

ALTER POLICY dashboard_metrics_service_access ON public.dashboard_metrics_history 
USING (((SELECT auth.role()) = 'service_role'::text));

-- =============================================
-- LOWER PRIORITY FIXES
-- =============================================

-- 6. Fix Auth RLS Initialization Plan in Other Tables
ALTER POLICY tenant_isolation_policy ON public.company_administrators 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY tenant_isolation_policy ON public.member_notifications 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY tenant_isolation_policy ON public.program_rules 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY tenant_isolation_policy ON public.reward_tier_eligibility 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

ALTER POLICY tenant_isolation_policy ON public.tier_definitions 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Run these after applying the fixes to verify changes

-- Verify points_transactions policies
SELECT policyname, qual FROM pg_policies WHERE tablename = 'points_transactions';

-- Verify loyalty_members policies
SELECT policyname, qual FROM pg_policies WHERE tablename = 'loyalty_members';

-- Verify companies policies (should show consolidated policies)
SELECT policyname, cmd, qual, with_check FROM pg_policies WHERE tablename = 'companies';

-- Verify rewards and redemptions policies
SELECT tablename, policyname, qual FROM pg_policies 
WHERE tablename IN ('rewards', 'reward_redemptions');

-- Verify dashboard_metrics_history policies
SELECT policyname, qual FROM pg_policies WHERE tablename = 'dashboard_metrics_history';

-- Verify other table policies
SELECT tablename, policyname, qual FROM pg_policies 
WHERE tablename IN ('company_administrators', 'member_notifications', 'program_rules', 
                   'reward_tier_eligibility', 'tier_definitions');
