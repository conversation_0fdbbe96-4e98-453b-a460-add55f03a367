[
    {
      "name": "security_definer_view",
      "title": "Security Definer View",
      "level": "ERROR",
      "facing": "EXTERNAL",
      "categories": [
        "SECURITY"
      ],
      "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user",
      "detail": "View \\`public.secure_dashboard_metrics\\` is defined with the SECURITY DEFINER property",
      "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view",
      "metadata": {
        "name": "secure_dashboard_metrics",
        "type": "view",
        "schema": "public"
      },
      "cache_key": "security_definer_view_public_secure_dashboard_metrics"
    }
  ]

  [
    {
      "name": "function_search_path_mutable",
      "title": "Function Search Path Mutable",
      "level": "WARN",
      "facing": "EXTERNAL",
      "categories": [
        "SECURITY"
      ],
      "description": "Detects functions where the search_path parameter is not set.",
      "detail": "Function \\`public.get_member_growth\\` has a role mutable search_path",
      "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable",
      "metadata": {
        "name": "get_member_growth",
        "type": "function",
        "schema": "public"
      },
      "cache_key": "function_search_path_mutable_public_get_member_growth_95e4f5646e9a5a059fd277e7858f2f90"
    },
    {
      "name": "function_search_path_mutable",
      "title": "Function Search Path Mutable",
      "level": "WARN",
      "facing": "EXTERNAL",
      "categories": [
        "SECURITY"
      ],
      "description": "Detects functions where the search_path parameter is not set.",
      "detail": "Function \\`public.get_top_members\\` has a role mutable search_path",
      "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable",
      "metadata": {
        "name": "get_top_members",
        "type": "function",
        "schema": "public"
      },
      "cache_key": "function_search_path_mutable_public_get_top_members_15085016a081d8cc7d10ceded6b0278d"
    },
    {
      "name": "auth_leaked_password_protection",
      "title": "Leaked Password Protection Disabled",
      "level": "WARN",
      "facing": "EXTERNAL",
      "categories": [
        "SECURITY"
      ],
      "description": "Leaked password protection is currently disabled.",
      "detail": "Supabase Auth prevents the use of compromised passwords by checking against HaveIBeenPwned.org. Enable this feature to enhance security.",
      "cache_key": "auth_leaked_password_protection",
      "remediation": "https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection",
      "metadata": {
        "type": "auth",
        "entity": "Auth"
      }
    }
  ]

  [
    {
      "name": "auth_rls_initplan",
      "title": "Auth RLS Initialization Plan",
      "level": "WARN",
      "facing": "EXTERNAL",
      "categories": [
        "PERFORMANCE"
      ],
      "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row",
      "detail": "Table \\`public.dashboard_metrics_history\\` has a row level security policy \\`dashboard_metrics_company_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.",
      "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan",
      "metadata": {
        "name": "dashboard_metrics_history",
        "type": "table",
        "schema": "public"
      },
      "cache_key": "auth_rls_init_plan_public_dashboard_metrics_history_dashboard_metrics_company_access"
    },
    {
      "name": "multiple_permissive_policies",
      "title": "Multiple Permissive Policies",
      "level": "WARN",
      "facing": "EXTERNAL",
      "categories": [
        "PERFORMANCE"
      ],
      "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.",
      "detail": "Table \\`public.companies\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{admin_company_access,companies_insert_policy}\\`",
      "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies",
      "metadata": {
        "name": "companies",
        "type": "table",
        "schema": "public"
      },
      "cache_key": "multiple_permissive_policies_public_companies_authenticated_INSERT"
    }
  ]