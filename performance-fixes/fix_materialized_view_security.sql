-- Fix for materialized_view_in_api warning on public.dashboard_metrics
-- This script restricts access to the materialized view and creates a secure view

-- 1. Revoke SELECT permissions from anon and authenticated roles on the materialized view
REVOKE SELECT ON public.dashboard_metrics FROM anon, authenticated;

-- 2. Create a secure view on top of the materialized view with security_invoker
CREATE OR REPLACE VIEW public.dashboard_metrics_secure
WITH (security_invoker = on) AS
SELECT * FROM public.dashboard_metrics
WHERE 
  -- Filter by company_id from JWT token
  company_id = (auth.jwt() ->> 'company_id')::uuid 
  OR 
  -- Allow company administrators to see their company data
  EXISTS (
    SELECT 1 FROM companies 
    WHERE companies.id = dashboard_metrics.company_id 
    AND companies.administrator_id = auth.uid()
  ) 
  OR 
  -- Allow service role to see all data
  current_setting('role') = 'service_role';

-- 3. Grant SELECT on the secure view to authenticated users
GRANT SELECT ON public.dashboard_metrics_secure TO authenticated;

-- 4. Add proper comment to the view
COMMENT ON VIEW public.dashboard_metrics_secure IS 'Secure view of dashboard metrics with row filtering built into the view definition';

-- 5. Update any application code to use dashboard_metrics_secure instead of dashboard_metrics
-- Note: This requires code changes in your application
