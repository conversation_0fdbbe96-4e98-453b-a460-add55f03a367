# Supabase Performance and Security Optimization Guide

This document outlines the necessary fixes for security warnings and performance suggestions identified by the Supabase Performance and Security Advisors.

## Priority 1: Security Warnings (MUST FIX)

### 1. Security Definer View Issue

**Problem:** The `public.secure_dashboard_metrics` view is defined with the SECURITY DEFINER property, which enforces Postgres permissions and RLS policies of the view creator rather than the querying user. This can be a security risk.

**Fix:**

```sql
-- Option 1: Recreate the view without SECURITY DEFINER (preferred approach)
CREATE OR REPLACE VIEW public.secure_dashboard_metrics 
AS
SELECT 
  dm.company_id,
  dm.company_name,
  dm.total_members,
  dm.active_members_30d,
  dm.total_lifetime_points,
  dm.total_redeemed_points,
  dm.total_expired_points,
  dm.total_available_points,
  dm.total_rewards,
  dm.active_rewards,
  dm.total_transactions,
  dm.earn_transactions,
  dm.redeem_transactions,
  dm.last_updated,
  dm.first_member_date,
  dm.last_transaction_date,
  dm.redemption_rate_percentage,
  dm.days_since_first_member
FROM dashboard_metrics dm
WHERE dm.company_id = ((auth.jwt() ->> 'company_id'::text)::uuid);

-- Option 2: If SECURITY DEFINER is actually needed, add explicit security barrier
-- CREATE OR REPLACE VIEW public.secure_dashboard_metrics 
-- WITH (security_barrier = true)
-- AS
-- SELECT 
--   [same columns as above]
-- FROM dashboard_metrics dm
-- WHERE dm.company_id = ((auth.jwt() ->> 'company_id'::text)::uuid);
```

### 2. Function Search Path Mutable Issues

**Problem:** The functions `get_member_growth` and `get_top_members` have mutable search paths, which can be a security risk for SQL injection.

**Fix for get_member_growth:**

```sql
-- Step 1: Drop the existing function
DROP FUNCTION IF EXISTS public.get_member_growth(uuid, integer);

-- Step 2: Recreate the function with SET search_path
CREATE OR REPLACE FUNCTION public.get_member_growth(
  p_company_id uuid,
  p_months integer
)
RETURNS json
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = 'public'
AS $function$
DECLARE
  result JSON;
BEGIN
  WITH monthly_data AS (
    SELECT
      DATE_TRUNC('month', created_at) AS month,
      COUNT(*) AS new_members
    FROM loyalty_members
    WHERE 
      company_id = p_company_id AND
      created_at >= CURRENT_DATE - p_months * INTERVAL '1 month'
    GROUP BY DATE_TRUNC('month', created_at)
    ORDER BY month
  ),
  growth_data AS (
    SELECT
      TO_CHAR(month, 'YYYY-MM') AS month_label,
      new_members,
      SUM(new_members) OVER (ORDER BY month) AS cumulative_members,
      LAG(new_members, 1, 0) OVER (ORDER BY month) AS previous_month,
      CASE 
        WHEN LAG(new_members, 1, 0) OVER (ORDER BY month) = 0 THEN NULL
        ELSE ROUND(((new_members - LAG(new_members, 1, 0) OVER (ORDER BY month)) * 100.0 / 
             NULLIF(LAG(new_members, 1, 0) OVER (ORDER BY month), 0))::numeric, 2)
      END AS growth_rate
    FROM monthly_data
  )
  SELECT json_agg(
    json_build_object(
      'month', month_label,
      'new_members', new_members,
      'cumulative_members', cumulative_members,
      'growth_rate', growth_rate
    )
  ) INTO result
  FROM growth_data;
  
  RETURN result;
END;
$function$;
```

**Fix for get_top_members:**

```sql
-- Step 1: Drop the existing function
DROP FUNCTION IF EXISTS public.get_top_members(uuid, text, integer);

-- Step 2: Recreate the function with SET search_path
CREATE OR REPLACE FUNCTION public.get_top_members(
  p_company_id uuid,
  p_metric text,
  p_limit integer
)
RETURNS json
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = 'public'
AS $function$
DECLARE
  result JSON;
BEGIN
  -- Validate metric parameter
  IF p_metric NOT IN ('lifetime_points', 'available_points', 'redeemed_points', 'redemption_count') THEN
    RAISE EXCEPTION 'Invalid metric parameter. Valid options are: lifetime_points, available_points, redeemed_points, redemption_count';
  END IF;
  
  -- Dynamic query based on metric
  IF p_metric = 'redemption_count' THEN
    WITH member_redemptions AS (
      SELECT 
        m.id,
        m.name,
        m.email,
        m.loyalty_tier,
        COUNT(pt.id) FILTER (WHERE pt.transaction_type = 'REDEEM') AS redemption_count
      FROM loyalty_members m
      LEFT JOIN points_transactions pt ON m.id = pt.member_id
      WHERE m.company_id = p_company_id
      GROUP BY m.id, m.name, m.email, m.loyalty_tier
      ORDER BY redemption_count DESC
      LIMIT p_limit
    )
    SELECT json_agg(
      json_build_object(
        'id', id,
        'name', name,
        'email', email,
        'loyalty_tier', loyalty_tier,
        'value', redemption_count,
        'metric', 'redemption_count'
      )
    ) INTO result
    FROM member_redemptions;
  ELSE
    -- For points-based metrics
    WITH member_points AS (
      SELECT 
        id,
        name,
        email,
        loyalty_tier,
        lifetime_points,
        available_points,
        redeemed_points
      FROM loyalty_members
      WHERE company_id = p_company_id
      ORDER BY 
        CASE 
          WHEN p_metric = 'lifetime_points' THEN lifetime_points
          WHEN p_metric = 'available_points' THEN available_points
          WHEN p_metric = 'redeemed_points' THEN redeemed_points
        END DESC
      LIMIT p_limit
    )
    SELECT json_agg(
      json_build_object(
        'id', id,
        'name', name,
        'email', email,
        'loyalty_tier', loyalty_tier,
        'value', 
          CASE 
            WHEN p_metric = 'lifetime_points' THEN lifetime_points
            WHEN p_metric = 'available_points' THEN available_points
            WHEN p_metric = 'redeemed_points' THEN redeemed_points
          END,
        'metric', p_metric
      )
    ) INTO result
    FROM member_points;
  END IF;
  
  RETURN result;
END;
$function$;
```

### 3. Leaked Password Protection

**Problem:** Supabase Auth's leaked password protection is currently disabled, which could allow users to use compromised passwords.

**Fix:**

This is a configuration change in the Supabase dashboard:

1. Go to the Supabase dashboard
2. Navigate to Authentication > Providers > Email
3. Enable "Protect against leaked passwords"
4. Save changes

Alternatively, you can enable this programmatically using the Supabase Management API:

```typescript
// Example using the Supabase Management API
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  'https://your-project-url.supabase.co',
  'your-service-role-key'
)

// Enable leaked password protection
const updateAuthConfig = async () => {
  const { data, error } = await supabase
    .from('auth.config')
    .update({ enable_leaked_password_protection: true })
    .eq('id', 1)
  
  if (error) console.error('Error updating auth config:', error)
  else console.log('Successfully enabled leaked password protection')
}

updateAuthConfig()
```

## Priority 2: Performance Warnings (SHOULD FIX)

### 1. Auth RLS Initialization Plan

**Problem:** The `dashboard_metrics_history` table has an RLS policy that re-evaluates `auth.<function>()` for each row, impacting performance.

**Fix:**

```sql
-- Find the current policy definition
SELECT pg_get_expr(polqual, polrelid) AS policy_definition
FROM pg_policy
WHERE polname = 'dashboard_metrics_company_access'
AND polrelid = 'public.dashboard_metrics_history'::regclass;

-- Update the policy to wrap auth functions in SELECT statements
ALTER POLICY dashboard_metrics_company_access ON public.dashboard_metrics_history
USING (company_id = (SELECT auth.uid())::uuid);
-- Note: Adjust the actual condition based on the current policy definition
```

### 2. Multiple Permissive Policies

**Problem:** The `companies` table has multiple permissive policies for role `authenticated` for action `INSERT`.

**Fix:**

```sql
-- Step 1: Check the existing policies
SELECT 
  policyname, 
  cmd, 
  roles, 
  qual, 
  with_check
FROM 
  pg_policies 
WHERE 
  tablename = 'companies' AND 
  policyname IN ('admin_company_access', 'companies_insert_policy');

-- Step 2: Create a consolidated policy
CREATE POLICY companies_consolidated_insert_policy ON public.companies
FOR INSERT TO authenticated
WITH CHECK (
  -- Combine conditions from both policies
  (/* condition from admin_company_access */) OR
  (/* condition from companies_insert_policy */)
);

-- Step 3: Drop the old policies
DROP POLICY admin_company_access ON public.companies;
DROP POLICY companies_insert_policy ON public.companies;
```

## Priority 3: Performance Suggestions (CONSIDER FIXING)

### 1. Unindexed Foreign Keys

**Problem:** Several tables have foreign keys without covering indexes, which can impact query performance.

**Fix:**

```sql
-- Create indexes for unindexed foreign keys
CREATE INDEX IF NOT EXISTS idx_company_administrators_created_by ON public.company_administrators(created_by);
CREATE INDEX IF NOT EXISTS idx_member_notifications_company_id ON public.member_notifications(company_id);
CREATE INDEX IF NOT EXISTS idx_points_transactions_receipt_id ON public.points_transactions(receipt_id);
CREATE INDEX IF NOT EXISTS idx_program_rules_company_id ON public.program_rules(company_id);
CREATE INDEX IF NOT EXISTS idx_reward_tier_eligibility_company_id ON public.reward_tier_eligibility(company_id);
CREATE INDEX IF NOT EXISTS idx_tier_definitions_company_id ON public.tier_definitions(company_id);
```

### 2. Unused Indexes

**Problem:** Several indexes are reported as unused, which can impact write performance and storage.

**Recommendation:**

Before removing unused indexes, consider:
1. How long the system has been running
2. Whether these indexes might be used in future queries or during specific operations
3. If they're needed for unique constraints or foreign keys

If you decide to remove them, here's how:

```sql
-- Example of removing unused indexes (be cautious!)
DROP INDEX IF EXISTS public.idx_receipts_member_company;
DROP INDEX IF EXISTS public.idx_receipts_purchase_date;
DROP INDEX IF EXISTS public.idx_member_notifications_created_at;
DROP INDEX IF EXISTS public.idx_member_notifications_delivery_status;
DROP INDEX IF EXISTS public.idx_member_notifications_notification_type;
DROP INDEX IF EXISTS public.idx_points_transactions_processing_status;
DROP INDEX IF EXISTS public.idx_points_transactions_ocr_confidence;
DROP INDEX IF EXISTS public.idx_points_transactions_ocr_data_gin;
DROP INDEX IF EXISTS public.idx_points_transactions_receipt_number;
DROP INDEX IF EXISTS public.idx_points_transactions_business_name;
DROP INDEX IF EXISTS public.idx_loyalty_members_profile_image;
DROP INDEX IF EXISTS public.idx_dashboard_metrics_history_company_id;
DROP INDEX IF EXISTS public.idx_companies_administrator_id;
DROP INDEX IF EXISTS public.idx_company_administrators_administrator_id;
DROP INDEX IF EXISTS public.idx_company_administrators_company_id;
DROP INDEX IF EXISTS public.idx_points_transactions_member_company;
DROP INDEX IF EXISTS public.idx_loyalty_members_company_id;
DROP INDEX IF EXISTS public.idx_active_rewards;
DROP INDEX IF EXISTS public.idx_loyalty_members_birthday_month_day;
DROP INDEX IF EXISTS public.idx_audit_log_changed_at;
DROP INDEX IF EXISTS public.idx_audit_log_table_record;
```

## Implementation Recommendations

1. **Always back up your database before applying changes**
2. **Test in a development environment first**
3. **Apply changes incrementally and test after each change**
4. **For unused indexes:**
   - Monitor query performance before and after removal
   - Consider keeping indexes that might be used in future features
   - Keep indexes on columns used in WHERE clauses or JOINs
5. **Re-run the Supabase Performance Advisor after applying fixes to verify resolution**

## Prioritization Guide

1. **Security issues (ERROR level):** Fix immediately
2. **Performance warnings (WARN level):** Fix in the short term
3. **Unindexed foreign keys (INFO level):** Add indexes for frequently queried tables
4. **Unused indexes (INFO level):** Remove only after careful consideration

Remember that some performance optimizations might have trade-offs. For example, adding indexes improves read performance but can slow down writes. Always test thoroughly after making changes.
