-- Fix high priority Supabase performance warnings
-- Addresses auth_rls_initplan warnings by replacing direct auth function calls with subselects

-- 1. Fix Points Transactions Table Policies
-- Update tenant_isolation_transactions policy
ALTER POLICY tenant_isolation_transactions ON public.points_transactions 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update Points transactions are viewable by their company policy
ALTER POLICY "Points transactions are viewable by their company" ON public.points_transactions 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

-- 2. Fix Loyalty Members Table Policies
-- Update company_isolation policy
ALTER POLICY company_isolation ON public.loyalty_members 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update tenant_isolation_members policy
ALTER POLICY tenant_isolation_members ON public.loyalty_members 
USING (((company_id)::text = (SELECT current_setting('app.current_company_id'::text))));

-- Update Members are viewable by their company policy
ALTER POLICY "Members are viewable by their company" ON public.loyalty_members 
USING (((company_id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)));

-- 3. Consolidate Multiple Permissive Policies in Companies Table
-- First, check if the policies exist before dropping
DO $$
BEGIN
    -- Consolidate INSERT policies
    IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Company Admin Access' AND tablename = 'companies') THEN
        DROP POLICY "Company Admin Access" ON public.companies;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'insert_companies' AND tablename = 'companies') THEN
        DROP POLICY insert_companies ON public.companies;
    END IF;
    
    -- Create consolidated INSERT policy
    CREATE POLICY companies_insert_policy ON public.companies
    FOR INSERT TO public
    WITH CHECK ((auth.uid() = administrator_id));
    
    -- Consolidate SELECT policies
    IF EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'Companies are viewable by company administrators' AND tablename = 'companies') THEN
        DROP POLICY "Companies are viewable by company administrators" ON public.companies;
    END IF;
    
    -- Note: We already dropped "Company Admin Access" above, so no need to drop it again
    
    -- Create consolidated SELECT policy
    CREATE POLICY companies_select_policy ON public.companies
    FOR SELECT TO public
    USING ((((id)::text = COALESCE((SELECT current_setting('app.current_company_id'::text, true)), ''::text)) OR 
           ((SELECT auth.uid()) = administrator_id)));
END
$$;

-- Verify changes
COMMENT ON POLICY tenant_isolation_transactions ON public.points_transactions IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY "Points transactions are viewable by their company" ON public.points_transactions IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY company_isolation ON public.loyalty_members IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY tenant_isolation_members ON public.loyalty_members IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY "Members are viewable by their company" ON public.loyalty_members IS 'Fixed auth_rls_initplan warning by using subselect';
COMMENT ON POLICY companies_insert_policy ON public.companies IS 'Consolidated INSERT policies for better performance';
COMMENT ON POLICY companies_select_policy ON public.companies IS 'Consolidated SELECT policies for better performance';
