-- URGENT: Fix infinite recursion in companies table policies
-- Run this IMMEDIATELY in Supabase SQL Editor to restore admin access

-- 1. Drop the problematic recursive policy
DROP POLICY IF EXISTS admin_company_access ON public.companies;

-- 2. Create a simple non-recursive policy for companies owned by the user
CREATE POLICY admin_company_access ON public.companies
FOR ALL TO authenticated
USING (administrator_id = auth.uid());

-- 3. Create a separate policy for company administrators (non-recursive)
DROP POLICY IF EXISTS administrator_company_access ON public.companies;
CREATE POLICY administrator_company_access ON public.companies
FOR ALL TO authenticated
USING (
  id IN (
    SELECT company_id FROM company_administrators WHERE administrator_id = auth.uid()
  )
);

-- 4. Fix company_administrators policy to be non-recursive
DROP POLICY IF EXISTS tenant_isolation_policy ON public.company_administrators;
CREATE POLICY tenant_isolation_policy ON public.company_administrators
FOR ALL TO authenticated
USING (administrator_id = auth.uid());

-- 5. Remove problematic analytics policies with app.current_company_id
DROP POLICY IF EXISTS admin_analytics_access ON public.analytics_cache;
DROP POLICY IF EXISTS admin_analytics_access ON public.analytics_queries;
DROP POLICY IF EXISTS company_isolation_policy ON public.analytics_cache;
DROP POLICY IF EXISTS company_isolation_policy ON public.analytics_queries;

-- 6. Create new analytics policies without configuration parameters
CREATE POLICY tenant_isolation_policy ON public.analytics_cache
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT company_id FROM company_administrators WHERE administrator_id = auth.uid()
  )
);

CREATE POLICY tenant_isolation_policy ON public.analytics_queries
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT company_id FROM company_administrators WHERE administrator_id = auth.uid()
  )
);

-- 7. Ensure RLS is enabled
ALTER TABLE public.analytics_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_queries ENABLE ROW LEVEL SECURITY;

-- 8. Verify no more infinite recursion
SELECT 'Policies updated successfully - infinite recursion should be resolved' as status;
