// Import the necessary types from testing-library
import '@testing-library/jest-dom';

// This file explicitly extends Jest's expect
declare global {
  namespace jest {
    // Define interfaces for type safe assertions
    interface Matchers<R> {
      // Custom assertions from @testing-library/jest-dom
      toBeInTheDocument(): R;
      toBeVisible(): R;
      toBeEmpty(): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeInvalid(): R;
      toBeRequired(): R;
      toBeChecked(): R;
      toBePartiallyChecked(): R;
      toHaveAttribute(attr: string, value?: any): R;
      toHaveClass(...classNames: string[]): R;
      toHaveStyle(css: string | object): R;
      toHaveTextContent(text: string | RegExp, options?: { normalizeWhitespace: boolean }): R;
      toHaveValue(value?: string | string[] | number | null): R;
      toHaveFocus(): R;
      toHaveFormValues(expectedValues: Record<string, any>): R;
      
      // Standard Jest assertions
      toBeTruthy(): R;
      toBeFalsy(): R;
      toBeNull(): R;
      toBeDefined(): R;
      toBeUndefined(): R;
      toBeNaN(): R;
      toBe(expected: any): R;
      toEqual(expected: any): R;
      toBeGreaterThan(expected: number): R;
      toBeGreaterThanOrEqual(expected: number): R;
      toBeLessThan(expected: number): R;
      toBeLessThanOrEqual(expected: number): R;
      toBeCloseTo(expected: number, precision?: number): R;
      toMatch(expected: string | RegExp): R;
      toMatchObject(expected: object | object[]): R;
      toContain(expected: any): R;
      toContainEqual(expected: any): R;
      toHaveLength(expected: number): R;
      toHaveProperty(propertyPath: string | string[], value?: any): R;
      toThrow(expected?: string | Error | RegExp): R;
      toThrowError(expected?: string | Error | RegExp): R;
    }
  }
}

// Ensures this is treated as a module
export {};
