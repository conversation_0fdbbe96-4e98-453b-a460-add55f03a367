// This file contains typescript type definitions for Jest matchers that are not included
// in the default @types/jest package but are used in our test suite.

import '@testing-library/jest-dom';
import { expect } from '@jest/globals';

// Extended type for Jest's expect function to fix TypeScript errors
declare global {
  namespace jest {
    interface Matchers<R> {
      // Basic Jest matchers
      toBe(expected: any): R;
      toEqual(expected: any): R;
      toStrictEqual(expected: any): R;
      toBeDefined(): R;
      toBeUndefined(): R;
      toBeNull(): R;
      toBeTruthy(): R;
      toBeFalsy(): R;
      toBeGreaterThan(expected: number): R;
      toBeGreaterThanOrEqual(expected: number): R;
      toBeLessThan(expected: number): R;
      toBeLessThanOrEqual(expected: number): R;
      toContain(expected: any): R;
      toContainEqual(expected: any): R;
      toHaveLength(expected: number): R;
      toHaveProperty(key: string, value?: any): R;
      toMatch(expected: string | RegExp): R;
      toMatchObject(expected: object): R;
      toThrow(expected?: string | Error | RegExp): R;
      
      // Testing Library DOM matchers
      toBeInTheDocument(): R;
      toBeVisible(): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeRequired(): R;
      toBeEmpty(): R;
      toBeChecked(): R;
      toBePartiallyChecked(): R;
      toHaveAttribute(attr: string, value?: any): R;
      toHaveTextContent(text: string | RegExp): R;
      toHaveValue(value: string | string[] | number): R;
      toHaveStyle(css: string): R;
      toHaveClass(...classNames: string[]): R;
      toHaveFocus(): R;
      toHaveFormValues(expectedValues: Record<string, any>): R;
      
      // Negated versions for completeness
      not: {
        toBe(expected: any): R;
        toEqual(expected: any): R;
        toStrictEqual(expected: any): R;
        toBeDefined(): R;
        toBeUndefined(): R;
        toBeNull(): R;
        toBeTruthy(): R;
        toBeFalsy(): R;
        toBeGreaterThan(expected: number): R;
        toBeGreaterThanOrEqual(expected: number): R;
        toBeLessThan(expected: number): R;
        toBeLessThanOrEqual(expected: number): R;
        toContain(expected: any): R;
        toContainEqual(expected: any): R;
        toHaveLength(expected: number): R;
        toHaveProperty(key: string, value?: any): R;
        toMatch(expected: string | RegExp): R;
        toMatchObject(expected: object): R;
        toThrow(expected?: string | Error | RegExp): R;
        
        // Testing Library DOM matchers
        toBeInTheDocument(): R;
        toBeVisible(): R;
        toBeDisabled(): R;
        toBeEnabled(): R;
        toBeRequired(): R;
        toBeEmpty(): R;
        toBeChecked(): R;
        toBePartiallyChecked(): R;
        toHaveAttribute(attr: string, value?: any): R;
        toHaveTextContent(text: string | RegExp): R;
        toHaveValue(value: string | string[] | number): R;
        toHaveStyle(css: string): R;
        toHaveClass(...classNames: string[]): R;
        toHaveFocus(): R;
        toHaveFormValues(expectedValues: Record<string, any>): R;
      };
    }
  }
}

// Add React Testing Library types if not already included
declare module '@testing-library/react' {
  // Add any missing types here
  export interface RenderResult {
    // Add any missing properties here
  }
}

// Extend the vitest types if you're using vitest
declare module 'vitest' {
  interface Assertion<T = any> extends CustomMatchers<T> {}
  interface AsymmetricMatchersContaining extends CustomMatchers {}
}

export {}
