-- Telegram Bot Database Schema Extensions
-- Run this to add Telegram integration fields

-- Add missing Telegram-related fields to loyalty_members
ALTER TABLE loyalty_members
ADD COLUMN IF NOT EXISTS telegram_username TEXT,
ADD COLUMN IF NOT EXISTS linking_token TEXT,
ADD COLUMN IF NOT EXISTS linked_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"points_earned": true, "tier_upgrade": true, "reward_expiry": true, "special_offers": true}',
ADD COLUMN IF NOT EXISTS ai_preferences JSONB DEFAULT '{"language": "en", "communication_style": "friendly"}';

-- Create conversation history table
CREATE TABLE IF NOT EXISTS telegram_conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES loyalty_members(id),
  chat_id TEXT NOT NULL,
  message_type TEXT NOT NULL, -- 'user' or 'bot'
  message_text TEXT NOT NULL,
  username TEXT, -- telegram username for analytics
  intent TEXT, -- detected intent for analytics
  response_time_ms INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notification log table
CREATE TABLE IF NOT EXISTS telegram_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  member_id UUID REFERENCES loyalty_members(id),
  chat_id TEXT NOT NULL,
  notification_type TEXT NOT NULL,
  title TEXT,
  message TEXT NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  delivery_status TEXT DEFAULT 'sent', -- 'sent', 'delivered', 'failed'
  related_transaction_id UUID REFERENCES points_transactions(id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_loyalty_members_telegram_chat_id ON loyalty_members(telegram_chat_id);
CREATE INDEX IF NOT EXISTS idx_loyalty_members_linking_token ON loyalty_members(linking_token);
CREATE INDEX IF NOT EXISTS idx_telegram_conversations_member_id ON telegram_conversations(member_id);
CREATE INDEX IF NOT EXISTS idx_telegram_conversations_chat_id ON telegram_conversations(chat_id);
CREATE INDEX IF NOT EXISTS idx_telegram_notifications_member_id ON telegram_notifications(member_id);
CREATE INDEX IF NOT EXISTS idx_telegram_notifications_chat_id ON telegram_notifications(chat_id);

-- Add unique constraint on telegram_chat_id to prevent duplicate linkings (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'unique_telegram_chat_id'
        AND conrelid = 'loyalty_members'::regclass
    ) THEN
        ALTER TABLE loyalty_members ADD CONSTRAINT unique_telegram_chat_id UNIQUE (telegram_chat_id);
    END IF;
END $$;
