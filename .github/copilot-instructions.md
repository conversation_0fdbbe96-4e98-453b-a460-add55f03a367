# GitHub Copilot Repository Custom Instructions

These instructions guide GitHub Copilot to align suggestions with our project conventions and task tracking.

## 📋 Task Management
- Always refer to `TASKS.md` before starting or after completing any coding task to check status and update progress.
- Use `PRD.md` as the authoritative spec for feature requirements and user stories.

## ⚛️ Tech Stack & Architecture
- Adhere to our Next.js 15 + TypeScript conventions, including App Router, API Routes, and Edge Functions.
- Generate UI components using `shadcn` (not shadcn-ui) + Tailwind CSS patterns.
  - Always use `npx shadcn@latest add [component]` (not shadcn-ui)
- For server data fetching, use TanStack Query; for global UI state, consider Zustand.
- All backend logic should run in Next.js API routes invoking the Supabase client.
- Follow code quality rules defined in ESLint and Prettier configurations.

## 🔐 Supabase Authentication Standards (CRITICAL)

### Core Auth Principles
**⚠️ MANDATORY: Always follow these auth patterns to maintain app consistency and prevent infinite loading issues.**

### 1. **Consolidated Auth Hook System** ✅
- **Primary**: `useAuth()` - React Query-based hook for most components
- **Login/Logout**: `useSimpleAuth()` - Direct Supabase calls for auth pages to prevent loading issues
- **Protected Routes**: `useRequireAuth()` - Auto-redirects if not authenticated
- **Logout**: `signOut()` function with proper cleanup and redirect

### 2. **Client vs Server Side Auth** ✅
```typescript
// ✅ CLIENT SIDE - Use this in components/hooks
import { createClient } from '@/lib/supabase/client'
const supabase = createClient()

// ✅ SERVER SIDE - Use this in API routes/server components
import { createClient } from '@/lib/supabase/server'
const supabase = await createClient()
```

### 3. **Deprecated Patterns - NEVER USE** ❌
```typescript
// ❌ NEVER use these - they cause infinite loading and conflicts
import { useEnhancedAuth } from '@/hooks/use-enhanced-auth'
import { useConsolidatedAuth } from '@/hooks/use-consolidated-auth'
import { useAuthQuery } from '@/hooks/use-auth-query'
import { getSupabaseClient } from '@/lib/supabase' // OLD PATTERN

// ❌ NEVER manually manage localStorage for auth
localStorage.setItem('loyal_app_user_id', user.id)
localStorage.removeItem('loyal_app_user_id')
```

### 4. **Auth Component Patterns** ✅
```typescript
### 4. **Component-Level Auth Usage** ✅

**For Login/Logout Pages (Critical):**
```typescript
import { useSimpleAuth } from '@/hooks/use-simple-auth'

export default function LoginPage() {
  const { user, isLoading, isAuthenticated } = useSimpleAuth()

  if (isLoading) return <div>Loading...</div>
  if (isAuthenticated) router.replace('/dashboard')

  return <LoginForm />
}
```

**For Protected Pages:**
```typescript
import { useRequireAuth } from '@/hooks/use-auth'

export default function DashboardPage() {
  const { user, isLoading } = useRequireAuth()

  if (isLoading) return <div>Loading...</div>

  return <div>Welcome {user?.email}</div>
}
```

**For General Components:**
```typescript
import { useAuth } from '@/hooks/use-auth'

export default function Header() {
  const { user, isAuthenticated } = useAuth()

  return (
    <nav>
      {isAuthenticated ? (
        <>
          <span>{user?.email}</span>
          <button onClick={signOut}>Logout</button>
        </>
      ) : (
        <Link href="/login">Login</Link>
      )}
    </nav>
  )
}
```
```

### 6. **⚡ React Query Performance Standards** ✅

**Auth Query Optimization:**
- Stale Time: 5+ minutes for auth data (rarely changes)
- Refetch Control: Respect global `refetchOnMount: false` and `refetchOnWindowFocus: false`
- Cache Management: 10+ minute garbage collection time for auth data
- Event Filtering: Only invalidate auth cache on meaningful events (SIGNED_IN, SIGNED_OUT, TOKEN_REFRESHED)

**Performance Anti-Patterns (AVOID):**
```typescript
// ❌ BAD: Overriding global refetch settings causing cascades
useQuery({
  queryKey: ['auth'],
  refetchOnMount: true,     // Causes 20+ simultaneous requests
  refetchOnWindowFocus: true, // Causes focus storm
  staleTime: 0,             // Immediate staleness
})

// ❌ BAD: Multiple auth hooks in same component
const auth1 = useAuth()
const auth2 = useRequireAuth()
```

**Optimized Patterns (USE):**
```typescript
// ✅ GOOD: Optimized auth configuration
useQuery({
  queryKey: ['auth'],
  staleTime: 5 * 60 * 1000,    // 5 minutes
  gcTime: 10 * 60 * 1000,      // 10 minutes
  refetchOnMount: false,        // Respect global config
  refetchOnWindowFocus: false,  // Respect global config
  retry: 1,                     // Limit retries
})
```
- Follows official Supabase SSR guidelines

### 6. **API Route Auth** ✅
```typescript
// ✅ API Route Pattern
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  const supabase = await createClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return Response.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // Your API logic here
}
```

### 7. **Auth State Management Rules** ✅
- **Single auth listener**: Only one `onAuthStateChange` listener per app
- **React Query caching**: 5-minute stale time for auth data
- **No manual localStorage**: Let Supabase handle session persistence
- **Proper loading states**: Always handle `isLoading` properly

### 8. **Common Auth Issues to Avoid** ⚠️
- **Infinite Loading**: Usually caused by improper redirect logic or multiple auth hooks
- **Console Warnings**: "Multiple GoTrueClient instances" means multiple auth hooks are active
- **Session Loss**: Incorrect cookie handling or manual localStorage interference
- **Build Errors**: Using client-side auth utilities on server-side

## 🗄️ Database Operations
- For database queries and structure information, use the postgres MCP tool or direct SQL queries on Supabase.
- Always use the `supabase` client from appropriate location (`lib/supabase/client.ts` or `lib/supabase/server.ts`).
- Protect admin pages and API routes with Supabase Auth via Next.js middleware.

## 📁 Project Structure Standards
- **Components**: Use proper TypeScript types and export patterns
- **API Routes**: Follow RESTful conventions and proper error handling
- **Hooks**: Keep them focused and reusable
- **Utils**: Separate client and server utilities clearly

## 🔄 Migration & Updates
- When updating auth-related code, refer to `AUTH_MIGRATION_GUIDE.md`
- Follow implementation patterns from `SUPABASE_AUTH_FIX_TRACKER.md`
- Check `AUTH_IMPLEMENTATION_SUCCESS.md` for verified patterns

## 🚨 Critical Reminders
1. **NEVER** create new auth hooks - use the consolidated `useAuth()` system
2. **ALWAYS** check if you're on client or server side before choosing Supabase client
3. **NEVER** manually manage auth sessions - let Supabase handle it
4. **ALWAYS** handle loading states properly to prevent infinite loading
5. **TEST** auth flows after any auth-related changes

## 🎯 Code Quality
- Use TypeScript strict mode and proper type definitions
- Follow established patterns for error handling and loading states
- Ensure components are properly wrapped in error boundaries
- Use consistent naming conventions across the codebase
