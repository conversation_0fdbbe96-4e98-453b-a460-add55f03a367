-- Create a function to insert points transactions that bypasses RLS
-- This function will run with elevated privileges

CREATE OR REPLACE FUNCTION create_points_transaction(
  p_member_id UUID,
  p_company_id UUID,
  p_transaction_type TEXT,
  p_points_change INTEGER,
  p_description TEXT,
  p_transaction_date TIMESTAMPTZ,
  p_expiration_date DATE,
  p_total_amount NUMERIC DEFAULT NULL,
  p_business_name TEXT DEFAULT NULL,
  p_receipt_number TEXT DEFAULT NULL,
  p_receipt_ocr_confidence NUMERIC DEFAULT NULL,
  p_receipt_processing_status TEXT DEFAULT NULL
)
RETURNS SETOF points_transactions
LANGUAGE sql
SECURITY DEFINER
AS $$
    INSERT INTO points_transactions (
      member_id,
      company_id,
      transaction_type,
      points_change,
      description,
      transaction_date,
      expiration_date,
      total_amount,
      business_name,
      receipt_number,
      receipt_ocr_confidence,
      receipt_processing_status
    )
    VALUES (
      p_member_id,
      p_company_id,
      p_transaction_type,
      p_points_change,
      p_description,
      p_transaction_date,
      p_expiration_date,
      p_total_amount,
      p_business_name,
      p_receipt_number,
      p_receipt_ocr_confidence,
      p_receipt_processing_status
    )
    RETURNING *;
$$;

-- Grant execution permissions
GRANT EXECUTE ON FUNCTION create_points_transaction(UUID, UUID, TEXT, INTEGER, TEXT, TIMESTAMPTZ, DATE, NUMERIC, TEXT, TEXT, NUMERIC, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_points_transaction(UUID, UUID, TEXT, INTEGER, TEXT, TIMESTAMPTZ, DATE, NUMERIC, TEXT, TEXT, NUMERIC, TEXT) TO service_role;
