# Testing Plan for Loyal

This document outlines the comprehensive testing strategy for the Loyal loyalty program platform.

## Testing Goals

1. **Ensure Functionality**: Verify all features work as described in the PRD
2. **Maintain Stability**: Prevent regressions in core features
3. **Validate Business Logic**: Confirm points, tiers, and rewards calculations are correct
4. **Ensure Performance**: Verify the application meets performance requirements
5. **Test Integrations**: Validate all external system integrations work properly

## Recent Progress (April 2025)

We've made significant improvements to the testing infrastructure:

1. **Modernized Test Environment**
   - Updated MSW handlers to use the correct API structure
   - Fixed TypeScript compatibility issues in test assertions
   - Added proper polyfills for Next.js and React 19
   - Improved test utilities for React Query hooks

2. **Improved Test Stability**
   - Created robust waitForHookToResolve utility for async testing
   - Added act() wrapping for React state updates
   - Suppressed irrelevant React warnings
   - Fixed test fixtures to match current data models

3. **Enhanced Test Coverage**
   - Added tests for members, rewards, and redemptions hooks
   - Improved dashboard component testing
   - Added tests for API handlers

## Current Status

Most tests are now passing, with a few remaining issues:

1. **Passing Tests**
   - API handler tests
   - Utility function tests
   - Basic component rendering tests

2. **Remaining Issues**
   - Some React Query hook tests still have act() warnings
   - Complex component tests need more robust async handling
   - E2E tests need to be implemented

## Testing Layers

### 1. Unit Tests (Jest + RTL)

Unit tests will focus on isolated pieces of functionality with minimal dependencies.

#### Priority Areas:
- **Utils & Helpers** (✅ completed)
  - Points calculation
  - Tier determination
  - Date/time handling
  - Data transformation functions
  
- **Hook Testing** (🔄 in progress)
  - Custom hooks for authentication
  - Data fetching hooks (React Query)
  - Form validation hooks
  
- **Component Testing** (🔄 in progress)
  - UI components (forms, tables, cards)
  - Interactive elements
  - Conditional rendering logic

### 2. Integration Tests (Jest + MSW)

Integration tests will verify that various parts of the application work together correctly.

#### Priority Areas:
- **API Handler Tests** (✅ completed)
  - `/api/members` endpoints
  - `/api/receipts` endpoints
  - `/api/rewards` endpoints
  - `/api/redemptions` endpoints
  
- **Data Flow Tests** (🔄 in progress)
  - Member creation → points assignment
  - Points calculation → tier updates
  - Reward redemption → points deduction
  
- **Authentication Flows** (⏳ planned)
  - Login process
  - Session handling
  - Protected routes

### 3. End-to-End Tests (Cypress)

E2E tests will verify complete user flows and critical paths through the application.

#### Priority Areas:
- **Admin Flows** (⏳ planned)
  - Login and session management
  - Member management (search, view, edit)
  - Reward creation and management
  - Dashboard visualization and filtering
  
- **Points & Transactions** (⏳ planned)
  - Receipt entry → points calculation
  - Points history display
  - Points expiry visualization
  
- **Reward Redemption** (⏳ planned)
  - Code validation
  - Points deduction
  - Redemption history

### 4. API Contract Tests

Ensure API endpoints follow expected contract and handle errors appropriately.

#### Priority Areas:
- **Schema Validation** (🔄 in progress)
  - Input validation
  - Response structure consistency
  - Error handling patterns
  
- **Authorization Tests** (⏳ planned)
  - Proper RLS enforcement
  - Tenant isolation
  - Permission checks

### 5. Performance Tests

Verify the application meets performance requirements.

#### Priority Areas:
- **API Efficiency** (⏳ planned)
  - Response times for core endpoints
  - Database query performance
  - Caching effectiveness
  
- **UI Performance** (⏳ planned)
  - Loading and rendering times
  - Animation smoothness
  - Memory usage patterns

## Next Steps (Immediate)

1. **Fix Remaining Test Issues**
   - Resolve React Query act() warnings by:
     - Using a more robust testing approach for async hooks
     - Considering a custom renderHook utility specific to React Query
     - Using a different assertion pattern that doesn't rely on loading states

2. **Expand Component Test Coverage**
   - Add tests for all major UI components
   - Focus on interactive elements and conditional rendering
   - Use mock data consistently across tests

3. **Implement E2E Tests**
   - Set up Cypress for end-to-end testing
   - Implement tests for critical user flows
   - Test error states and recovery

## Testing Implementation Plan

### Phase 1: Foundations (✅ completed)

1. **Audit Existing Tests**
   - Review current test coverage
   - Identify gaps in testing
   - Update existing tests to match current implementation

2. **Setup Testing Infrastructure**
   - Configure MSW for API mocking
   - Set up testing database for integration tests
   - Create test utilities and fixtures

3. **Unit Test Core Utils**
   - Implement additional unit tests for utility functions
   - Ensure coverage of edge cases
   - Add tests for date/time handling

### Phase 2: API & Data Flow (🔄 in progress)

1. **API Handler Tests**
   - Implement tests for all API routes
   - Test validation logic
   - Test error handling

2. **Integration Tests**
   - Test database triggers
   - Test data transformations
   - Test authentication flows

3. **React Query Hook Tests**
   - Test custom data fetching hooks
   - Test caching and invalidation
   - Test error states

### Phase 3: UI Components & E2E (⏳ planned)

1. **Component Tests**
   - Test form components
   - Test interactive elements
   - Test data display components

2. **E2E Tests**
   - Implement critical path tests
   - Test user flows end-to-end
   - Test error recovery

### Phase 4: Performance & Optimization (⏳ planned)

1. **API Performance Tests**
   - Test response times
   - Test under load
   - Identify bottlenecks

2. **UI Performance Tests**
   - Test rendering performance
   - Test animation smoothness
   - Test memory usage

## Test Organization

Tests will be organized to match the structure of the codebase:

```
/__tests__/              # Jest tests
  /components/           # Component tests
  /hooks/                # Hook tests
  /lib/                  # Library and utility tests
  /pages/                # Page component tests
  /api/                  # API handler tests

/cypress/                # Cypress tests
  /e2e/                  # End-to-end tests
  /integration/          # Integration tests
  /fixtures/             # Test data

/test-utils/             # Test utilities and helpers
  /mocks/                # Mock data and services
  /fixtures/             # Test fixtures
  /helpers/              # Test helper functions
```

## Test Data Management

1. **Fixtures**
   - Create realistic test data fixtures
   - Maintain consistency across tests
   - Version control test data

2. **Mocking Strategy**
   - Use MSW for API mocking
   - Mock external dependencies
   - Create realistic mock responses

## Recommended Testing Approaches

Based on our recent experience, here are some recommended approaches for testing React Query hooks:

1. **Focus on final state, not intermediate states**
   - Test for data presence or error state, not loading states
   - Use waitForHookToResolve utility to wait for hooks to resolve

2. **Use act() for all state changes**
   - Wrap hook mutations in act()
   - Be aware of React Query's internal state changes

3. **Consider skipping problematic tests**
   - If a test is consistently failing due to React/RTL limitations, consider skipping it
   - Focus on testing the most critical functionality first

4. **Use MSW for API mocking**
   - Create realistic API responses
   - Test error states and edge cases
   - Ensure consistency between API contracts and frontend expectations
