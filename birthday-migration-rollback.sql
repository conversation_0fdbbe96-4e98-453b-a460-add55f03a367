-- <PERSON><PERSON><PERSON><PERSON><PERSON>K SCRIPT: Birthday Month-Day Migration
-- Use this script to rollback the birthday changes if needed

-- Step 1: Restore original is_member_birthday_eligible function
CREATE OR REPLACE FUNCTION is_member_birthday_eligible(member_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  member_birthday DATE;
BEGIN
  SELECT birthday INTO member_birthday
  FROM loyalty_members
  WHERE id = member_id;

  RETURN
    CURRENT_DATE BETWEEN
      (member_birthday + (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM member_birthday)) * INTERVAL '1 YEAR')
      - INTERVAL '7 DAYS'
    AND
      (member_birthday + (EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM member_birthday)) * INTERVAL '1 YEAR')
      + INTERVAL '7 DAYS';
END;
$$;

-- Step 2: Restore original get_birthday_eligible_members function
DROP FUNCTION IF EXISTS get_birthday_eligible_members(UUID);

CREATE FUNCTION get_birthday_eligible_members(p_company_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  email TEXT,
  phone_number TEXT,
  birthday DATE,
  telegram_chat_id TEXT,
  available_points INTEGER
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.name,
    m.email,
    m.phone_number,
    m.birthday,
    m.telegram_chat_id,
    mp.available_points
  FROM
    loyalty_members m
  JOIN
    member_points_live mp ON m.id = mp.id
  WHERE
    m.company_id = p_company_id
    AND m.telegram_chat_id IS NOT NULL
    AND is_member_birthday_eligible(m.id) = TRUE;
END;
$$;

-- Step 3: Optionally remove the birthday_month_day column (CAREFUL!)
-- ALTER TABLE loyalty_members DROP COLUMN IF EXISTS birthday_month_day;

-- Verification
SELECT 'Rollback completed' as status;
