# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# cypress
cypress/screenshots/
cypress/videos/
cypress/downloads/

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# temporary files and backups
*.backup.*
*.tmp
*.temp
debug-*.js
debug-*.ts
cookies.txt

# test artifacts and debugging files
test-*.sh
*.test.js
*.test.ts

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# VS Code settings and MCP config (local dev environment)
.vscode/
.mcp.json

# Development scripts (temporary/diagnostic)
scripts/mcp-*.js
scripts/mcp-*.mjs
scripts/mcp-*.sql
scripts/test-*.js
scripts/test-*.mjs
scripts/test-*.sql
scripts/test-*.ts
scripts/*-mcp.sql
scripts/db-query.*
scripts/analyze-*.sql
scripts/apply-*.sh
scripts/run-*.sh
scripts/query-*.sh
scripts/verify-*.mjs
scripts/get-user-*.mjs
scripts/quick-query.*
scripts/user-count.sql
scripts/count-users*.sql
scripts/global-*.sql
scripts/enable-rls*.sql
scripts/improved-*.sql
scripts/minimal-*.sql
scripts/add-*-column.sql
scripts/auth-reactivation-*.md
scripts/test-*.sh
scripts/toggle-*.sh

# Documentation that may contain sensitive info or be temporary
docs/MCP-*.md
docs/LOYAL-MCP-*.md
docs/SUPABASE-MCP-*.md
docs/SECURITY-FIX-*.md
docs/fixed-settings.json

# Analysis and working documents
*ANALYSIS*.md
*PERFORMANCE*.md
*PERFORMANCE*.sql
*IMPLEMENTATION*.md

# Database scripts (should be run in Supabase, not committed)
fix-*.sql
verify-*.sql
verify-*.sh
database-*.sql
improved-*.sql

