-- Migration: Update birthday field to store only month-day format
-- This migration adds a new column for month-day birthday format and updates related functions

-- Step 1: Add new birthday_month_day column
ALTER TABLE loyalty_members
ADD COLUMN birthday_month_day VARCHAR(5) CHECK (birthday_month_day ~ '^(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$');

-- Step 2: Populate the new column with existing birthday data (month-day format)
UPDATE loyalty_members
SET birthday_month_day = TO_CHAR(birthday, 'MM-DD')
WHERE birthday IS NOT NULL;

-- Step 3: Update the birthday eligibility function to use the new format
CREATE OR REPLACE FUNCTION is_member_birthday_eligible(member_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
  member_birthday_md VARCHAR(5);
  birthday_this_year DATE;
BEGIN
  -- Get the member's birthday in MM-DD format
  SELECT birthday_month_day INTO member_birthday_md
  FROM loyalty_members
  WHERE id = member_id;

  -- If no birthday data, return false
  IF member_birthday_md IS NULL THEN
    RETURN FALSE;
  END IF;

  -- Create birthday date for current year
  birthday_this_year := TO_DATE(EXTRACT(YEAR FROM CURRENT_DATE)::TEXT || '-' || member_birthday_md, 'YYYY-MM-DD');

  -- Check if current date is within 7 days before or after birthday
  RETURN
    CURRENT_DATE BETWEEN
      (birthday_this_year - INTERVAL '7 DAYS')
    AND
      (birthday_this_year + INTERVAL '7 DAYS');
EXCEPTION
  WHEN OTHERS THEN
    -- Handle invalid date formats gracefully
    RETURN FALSE;
END;
$$;

-- Step 4: Drop and recreate the get_birthday_eligible_members function to change return type
DROP FUNCTION IF EXISTS get_birthday_eligible_members(UUID);

CREATE FUNCTION get_birthday_eligible_members(p_company_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  email TEXT,
  phone_number TEXT,
  birthday_month_day VARCHAR(5),
  telegram_chat_id TEXT,
  available_points INTEGER
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.name,
    m.email,
    m.phone_number,
    m.birthday_month_day,
    m.telegram_chat_id,
    mp.available_points
  FROM
    loyalty_members m
  JOIN
    member_points_live mp ON m.id = mp.id
  WHERE
    m.company_id = p_company_id
    AND m.telegram_chat_id IS NOT NULL
    AND m.birthday_month_day IS NOT NULL
    AND is_member_birthday_eligible(m.id) = TRUE;
END;
$$;

-- Step 5: Verification - Test the updated functions
SELECT
    'Migration Verification' as step,
    COUNT(*) as total_members,
    COUNT(birthday) as members_with_birthday,
    COUNT(birthday_month_day) as members_with_month_day
FROM loyalty_members;

-- Step 6: After verifying everything works, we can make birthday_month_day NOT NULL
-- and drop the old birthday column (commented out for safety)
-- ALTER TABLE loyalty_members ALTER COLUMN birthday_month_day SET NOT NULL;
-- ALTER TABLE loyalty_members DROP COLUMN birthday;

-- For now, we'll keep both columns for backward compatibility
