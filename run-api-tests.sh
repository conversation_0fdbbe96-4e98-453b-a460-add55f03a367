#!/bin/zsh

# This script runs the API tests with proper permissions

echo "Running test-debug-api.sh..."
curl -X GET "http://localhost:3000/api/debug/lookup-member?id=6d300a19-4106-4ea5-8f0a-773fb59cb73c" | json_pp
echo ""
echo "=========================================="
echo ""

echo "Testing debug endpoint with schema info..."
curl -X GET "http://localhost:3000/api/debug/lookup-member?schema=true" | json_pp
echo ""
echo "=========================================="
echo ""

echo "Running test-ai-validation.sh..."
curl -X POST http://localhost:3000/api/redemptions/validate-ai \
  -H "Content-Type: application/json" \
  -d '{
    "memberId": "6d300a19-4106-4ea5-8f0a-773fb59cb73c",
    "rewardId": "b401c003-ddfd-4854-a98d-5d71d9119986",
    "receiptTotal": 100.50
  }' | json_pp
