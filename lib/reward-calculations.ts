/**
 * Utility functions for reward calculations
 * Uses subtotal (before tax) for percentage-based rewards as per business requirements
 */

export interface RewardCalculationInput {
  total_amount: number
  subtotal?: number
  reward_value: number
  reward_value_type: 'PERCENTAGE' | 'FIXED' | 'DOUBLE_POINTS'
}

export interface RewardCalculationResult {
  discount_amount: number
  amount_due: number
  calculation_base: 'subtotal' | 'total'
}

/**
 * Calculate reward discount and amount due
 * Uses subtotal for percentage calculations when available
 */
export function calculateRewardDiscount(input: RewardCalculationInput): RewardCalculationResult {
  const { total_amount, subtotal, reward_value, reward_value_type } = input
  
  // Use subtotal for percentage calculations if available, otherwise fall back to total
  const calculationBase = subtotal && subtotal > 0 ? subtotal : total_amount
  const baseType = subtotal && subtotal > 0 ? 'subtotal' : 'total'
  
  let discount_amount = 0
  
  switch (reward_value_type) {
    case 'PERCENTAGE':
      // Apply percentage discount to subtotal (before tax)
      discount_amount = calculationBase * (reward_value / 100)
      break
      
    case 'FIXED':
      // Fixed amount discount
      discount_amount = reward_value
      break
      
    case 'DOUBLE_POINTS':
      // Double points don't affect the amount due
      discount_amount = 0
      break
      
    default:
      discount_amount = 0
  }
  
  // Calculate final amount due (never go below 0)
  const amount_due = Math.max(0, total_amount - discount_amount)
  
  return {
    discount_amount,
    amount_due,
    calculation_base: baseType
  }
}

/**
 * Calculate points earned from a transaction
 * Uses subtotal for points calculation when available
 */
export function calculatePointsEarned(
  total_amount: number, 
  subtotal: number | undefined, 
  points_earning_ratio: number
): number {
  const amountForPoints = subtotal && subtotal > 0 ? subtotal : total_amount
  return Math.floor(amountForPoints * points_earning_ratio)
}

/**
 * Check if a reward is expired
 */
export function isRewardExpired(expiration_date: string): boolean {
  return new Date(expiration_date) <= new Date()
}

/**
 * Check if a reward expires soon (within 7 days)
 */
export function isRewardExpiringSoon(expiration_date: string): boolean {
  const expirationDate = new Date(expiration_date)
  const sevenDaysFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  return expirationDate <= sevenDaysFromNow
}

/**
 * Get reward status label
 */
export function getRewardStatusLabel(expiration_date: string): {
  status: 'active' | 'expires_soon' | 'expired'
  label: string
  variant: 'default' | 'warning' | 'destructive'
} {
  if (isRewardExpired(expiration_date)) {
    return {
      status: 'expired',
      label: 'Expired',
      variant: 'destructive'
    }
  }
  
  if (isRewardExpiringSoon(expiration_date)) {
    return {
      status: 'expires_soon',
      label: 'Expires Soon',
      variant: 'warning'
    }
  }
  
  return {
    status: 'active',
    label: 'Active',
    variant: 'default'
  }
}
