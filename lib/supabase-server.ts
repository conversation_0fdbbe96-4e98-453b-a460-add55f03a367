import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { type CookieOptions } from '@supabase/ssr'
import { RequestCookies } from 'next/dist/server/web/spec-extension/cookies'

/**
 * Creates a Supabase client for use in API routes and server components
 *
 * This implementation is compatible with Next.js App Router and handles
 * cookie access in a way that avoids TypeScript errors.
 */
export function createServerSupabaseClient() {
  // Create a cookie handler object that works with Next.js App Router
  const cookieStore = {
    async get(name: string) {
      try {
        // Await cookies() as required by Next.js 13+ dynamic API routes
        const cookieList = await cookies() as unknown as RequestCookies
        return cookieList.get(name)?.value
      } catch (error) {
        console.error('Error accessing cookies:', error)
        return undefined
      }
    },
    set(name: string, value: string, options: CookieOptions) {
      // In Next.js App Router API routes, we can't directly set cookies
      // This should be handled by middleware instead
      console.log(`Cookie set operation: ${name}=${value}`, options)
      // No-op in API routes - middleware should handle this
    },
    remove(name: string, options: CookieOptions) {
      // In Next.js App Router API routes, we can't directly remove cookies
      // This should be handled by middleware instead
      console.log(`Cookie remove operation: ${name}`, options)
      // No-op in API routes - middleware should handle this
    },
  }

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    { cookies: cookieStore }
  )
}
