/**
 * API Usage Monitoring Utility
 * 
 * This utility helps track and monitor API calls to prevent excessive usage
 * and optimize performance. It works by intercepting fetch requests and
 * logging their frequency, timing, and response sizes.
 */

// Types for API monitoring
declare global {
  interface Window {
    __API_MONITORING_INITIALIZED__?: boolean;
  }
}

type ApiCall = {
  url: string;
  method: string;
  duration: number;
  timestamp: number;
  status: number;
  responseSize: number;
  error?: string;
};

type ApiUsageSummary = {
  totalCalls: number;
  uniqueEndpoints: number;
  totalResponseSize: number;
  averageDuration: number;
  callsByEndpoint: Record<string, number>;
  slowestEndpoints: Array<{endpoint: string, duration: number}>;
};

// In-memory storage for API calls (will be reset on page refresh)
const apiCalls: ApiCall[] = [];

// Maximum number of calls to keep in memory
const MAX_CALLS_HISTORY = 100;

/**
 * Intercept and track a fetch request
 */
export async function trackedFetch(
  input: RequestInfo | URL, 
  init?: RequestInit
): Promise<Response> {
  const url = typeof input === 'string' ? input : input.toString();
  const method = init?.method || 'GET';
  const startTime = performance.now();
  
  try {
    const response = await fetch(input, init);
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Clone the response to read its body without consuming it
    const clonedResponse = response.clone();
    const text = await clonedResponse.text();
    const responseSize = text.length;
    
    // Record the API call
    const apiCall: ApiCall = {
      url,
      method,
      timestamp: Date.now(),
      duration,
      status: response.status,
      responseSize,
    };
    
    // Add to history, removing oldest if needed
    apiCalls.push(apiCall);
    if (apiCalls.length > MAX_CALLS_HISTORY) {
      apiCalls.shift();
    }
    
    // Log slow requests (over 500ms)
    if (duration > 500) {
      console.warn(`Slow API call to ${url}: ${duration.toFixed(2)}ms`);
    }
    
    return response;
  } catch (error) {
    console.error(`API call to ${url} failed:`, error);
    throw error;
  }
}

/**
 * Get a summary of recent API usage
 */
export function getApiUsageSummary(): ApiUsageSummary {
  if (apiCalls.length === 0) {
    return {
      totalCalls: 0,
      uniqueEndpoints: 0,
      totalResponseSize: 0,
      averageDuration: 0,
      callsByEndpoint: {},
      slowestEndpoints: [],
    };
  }
  
  // Count calls by endpoint
  const callsByEndpoint: Record<string, number> = {};
  const durationsByEndpoint: Record<string, number[]> = {};
  
  apiCalls.forEach(call => {
    // Extract endpoint path from URL
    const url = new URL(call.url, window.location.origin);
    const endpoint = `${call.method} ${url.pathname}`;
    
    // Count calls
    callsByEndpoint[endpoint] = (callsByEndpoint[endpoint] || 0) + 1;
    
    // Track durations
    if (!durationsByEndpoint[endpoint]) {
      durationsByEndpoint[endpoint] = [];
    }
    durationsByEndpoint[endpoint].push(call.duration);
  });
  
  // Calculate average duration across all calls
  const totalDuration = apiCalls.reduce((sum, call) => sum + call.duration, 0);
  const averageDuration = totalDuration / apiCalls.length;
  
  // Calculate total response size
  const totalResponseSize = apiCalls.reduce((sum, call) => sum + call.responseSize, 0);
  
  // Find slowest endpoints (by average duration)
  const slowestEndpoints = Object.entries(durationsByEndpoint)
    .map(([endpoint, durations]) => ({
      endpoint,
      duration: durations.reduce((sum, d) => sum + d, 0) / durations.length,
    }))
    .sort((a, b) => b.duration - a.duration)
    .slice(0, 5);
  
  return {
    totalCalls: apiCalls.length,
    uniqueEndpoints: Object.keys(callsByEndpoint).length,
    totalResponseSize,
    averageDuration,
    callsByEndpoint,
    slowestEndpoints,
  };
}

/**
 * Get recent API calls with filtering options
 */
export function getRecentApiCalls(options?: {
  endpoint?: string;
  minDuration?: number;
  limit?: number;
}): ApiCall[] {
  let filteredCalls = [...apiCalls];
  
  if (options?.endpoint) {
    filteredCalls = filteredCalls.filter(call => call.url.includes(options.endpoint || ''));
  }
  
  if (options?.minDuration !== undefined) {
    filteredCalls = filteredCalls.filter(call => call.duration >= (options.minDuration || 0));
  }
  
  // Sort by most recent first
  filteredCalls.sort((a, b) => b.timestamp - a.timestamp);
  
  if (options?.limit) {
    filteredCalls = filteredCalls.slice(0, options.limit);
  }
  
  return filteredCalls;
}

/**
 * Check if an endpoint is being called excessively
 * Returns true if the endpoint has been called more than maxCalls times in the last timeWindowMs
 */
export function isEndpointOverused(
  endpoint: string, 
  maxCalls: number = 5,
  timeWindowMs: number = 10000
): boolean {
  const now = Date.now();
  const recentCalls = apiCalls.filter(
    call => call.url.includes(endpoint) && (now - call.timestamp) < timeWindowMs
  );
  
  return recentCalls.length > maxCalls;
}

/**
 * Initialize API monitoring 
 */
export function initApiMonitoring(options?: {
  enablePerformanceTracking?: boolean;
  slowRequestThreshold?: number;
}) {
  if (typeof window === 'undefined') return;

  // Only initialize once
  if (window.__API_MONITORING_INITIALIZED__) return;
  window.__API_MONITORING_INITIALIZED__ = true;

  // Track slow requests
  const slowThreshold = options?.slowRequestThreshold ?? 1000; // Default 1 second

  // Store original fetch
  const originalFetch = window.fetch;

  // Override fetch with our instrumented version
  window.fetch = async (input, init) => {
    const url = typeof input === 'string' 
      ? input 
      : input instanceof Request ? input.url : input.toString();
      
    const method = init?.method || 
      (typeof input === 'string' ? 'GET' : 
       input instanceof Request ? input.method : 'GET');
    
    // Only track API calls
    if (!url.includes('/api/')) {
      return originalFetch(input, init);
    }

    const start = performance.now();
    const startTime = new Date();
    
    try {
      const response = await originalFetch(input, init);
      
      // Clone the response so we can access body content
      const clonedResponse = response.clone();
      const duration = performance.now() - start;
      let responseSize = 0;
      
      try {
        const text = await clonedResponse.text();
        responseSize = text.length;
        
        // Add to history, removing oldest if needed
        apiCalls.push({
          url,
          method,
          duration,
          timestamp: startTime.getTime(),
          status: response.status,
          responseSize
        });
        
        if (apiCalls.length > MAX_CALLS_HISTORY) {
          apiCalls.shift();
        }
        
        // Log slow requests if tracking is enabled
        if (options?.enablePerformanceTracking && duration > slowThreshold) {
          console.warn(`[API Monitoring] Slow request (${Math.round(duration)}ms): ${method} ${url}`);
        }
      } catch (e) {
        // Ignore body reading errors
        console.debug('[API Monitoring] Error reading response body', e);
      }
      
      return response;
    } catch (e) {
      const duration = performance.now() - start;
      
      // Add to history, removing oldest if needed
      apiCalls.push({
        url,
        method,
        duration,
        timestamp: startTime.getTime(),
        status: 0,
        responseSize: 0,
        error: e instanceof Error ? e.message : String(e)
      });
      
      if (apiCalls.length > MAX_CALLS_HISTORY) {
        apiCalls.shift();
      }
      
      throw e;
    }
  };

  console.info('[API Monitoring] Initialized');
}
