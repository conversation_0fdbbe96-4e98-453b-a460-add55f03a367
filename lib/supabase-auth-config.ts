/**
 * Supabase authentication configuration
 * This file centralizes all authentication-related settings
 */

import { getBaseUrl } from '@/lib/utils/environment';

/**
 * Get the site URL for Supabase authentication
 * This handles both development and production environments
 */
export function getSiteUrl(): string {
  return getBaseUrl();
}

/**
 * Get the redirect URL for Supabase authentication callbacks
 */
export function getRedirectUrl(path: string = '/auth/callback'): string {
  return `${getSiteUrl()}${path.startsWith('/') ? path : `/${path}`}`;
}

/**
 * Get authentication configuration for Supabase
 */
export function getAuthConfig() {
  return {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Add any additional auth configuration here
    // For example, you might want to specify which providers to use
    // or set other auth-related options
  };
}

/**
 * List of all valid redirect URLs for your application
 * This is useful for configuring Supabase Auth settings
 */
export const validRedirectUrls = [
  // Local development
  'http://localhost:3000/auth/callback',
  'http://localhost:3000/dashboard',
  'http://localhost:3000/login',
  
  // Production (Vercel)
  'https://loyal-et.vercel.app/auth/callback',
  'https://loyal-et.vercel.app/dashboard',
  'https://loyal-et.vercel.app/login',
];
