import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { getServiceRoleClient } from './supabase'

/**
 * Get server-side session and Supabase client
 * Uses the standardized Supabase SSR approach for cookie handling
 */
export async function getServerSession() {
  try {
    // Create a Supabase client for server-side use with modern cookie handling
    // In Next.js 15+, cookies() returns a Promise that resolves to ReadonlyRequestCookies
    const cookieStore = await cookies()
    console.log('Cookie store initialized')
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          // Implementation for Next.js App Router cookies
          getAll: () => {
            try {
              // Get all cookies from the store
              const allCookies = Array.from(cookieStore.getAll())
              console.log('Cookies retrieved:', allCookies.length)
              // Map to the format expected by Supabase
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              return allCookies.map((cookie: any) => ({
                name: cookie.name,
                value: cookie.value,
              }))
            } catch (e) {
              console.error('Error accessing cookies:', e)
              return []
            }
          },
          // Implementation that does nothing since we can't set cookies in a Server Component
          // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
          setAll: (cookiesToSet: any) => {
            // This is a read-only context, cookies can't be set here
            // In a real middleware or route handler, you would set cookies on the response
            return
          }
        }
      }
    )

    const { data: { session } } = await supabase.auth.getSession()
    return { session, supabase }
  } catch (error) {
    console.error('Error in getServerSession:', error)
    return { session: null, supabase: null }
  }
}

/**
 * Get user role for a company
 * Uses service role client to bypass RLS
 */
export async function getUserRole(userId: string, companyId: string) {
  if (!userId || !companyId) {
    return null
  }
  
  try {
    // Use the service client for privileged operations
    const serviceClient = getServiceRoleClient()
    
    // First check company_administrators table for role
    const { data: adminData, error: adminError } = await serviceClient
      .from('company_administrators')
      .select('role')
      .eq('administrator_id', userId)
      .eq('company_id', companyId)
      .maybeSingle()

    if (adminError) {
      console.error('Error checking company_administrators:', adminError)
    }
    
    if (adminData?.role) {
      return adminData.role
    }

    // Fallback to companies table
    const { data: companyData, error: companyError } = await serviceClient
      .from('companies')
      .select('id')
      .eq('administrator_id', userId)
      .eq('id', companyId)
      .maybeSingle()

    if (companyError) {
      console.error('Error checking companies:', companyError)
    }
    
    if (companyData) {
      return 'OWNER'
    }
    
    return null
  } catch (error) {
    console.error('Unexpected error getting user role:', error)
    return null
  }
}
