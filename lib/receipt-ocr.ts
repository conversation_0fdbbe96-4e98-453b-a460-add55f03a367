import { generateObject } from 'ai';
import { geminiFlash, geminiFallback } from '@/lib/ai-config';
import { z } from 'zod';

// Schema for receipt data extraction
const ReceiptSchema = z.object({
  business_name: z.string().describe("Name of the business/company"),
  financial_system_number: z.string().describe("FS No or receipt number"),
  total_amount: z.number().describe("Total amount paid"),
  subtotal: z.number().optional().describe("Subtotal before tax"),
  tax_amount: z.number().optional().describe("Tax amount"),
  service_description: z.string().describe("Service or item description"),
  payment_method: z.string().describe("Payment method (CASH, CARD, etc.)"),
  business_tin: z.string().optional().describe("Business TIN number"),
  receipt_date: z.string().describe("Date on receipt (DD/MM/YYYY format)"),
  business_location: z.string().optional().describe("Business address or location"),
  confidence: z.number().min(0).max(1).describe("Confidence level of extraction")
});

export type ReceiptData = z.infer<typeof ReceiptSchema>;

/**
 * Extracts structured data from a receipt image using Gemini models with fallback
 */
export async function extractReceiptData(imageBuffer: Buffer): Promise<ReceiptData> {
  const extractionPrompt = [
    {
      role: 'user' as const,
      content: [
        {
          type: 'text' as const,
          text: `Extract the following information from this receipt image.
                 Be very careful with numbers and dates.
                 If any field is unclear or missing, mark confidence as lower.
                 Extract all financial amounts as numbers (no currency symbols).
                 For dates, use DD/MM/YYYY format.
                 Return a confidence score between 0 and 1 based on image clarity and data extraction certainty.`
        },
        {
          type: 'image' as const,
          image: imageBuffer,
        },
      ],
    },
  ];

  // Try primary model first (Gemini 2.0 Flash)
  try {
    console.log('Attempting OCR with Gemini 2.0 Flash...');
    const { object } = await generateObject({
      model: geminiFlash,
      schema: ReceiptSchema,
      messages: extractionPrompt,
    });

    console.log('OCR successful with Gemini 2.0 Flash');
    return object;
  } catch (primaryError) {
    console.log('Primary model failed, trying fallback...', primaryError);

    // Check if it's an overload error, try fallback model
    if (primaryError instanceof Error &&
        (primaryError.message.includes('overloaded') ||
         primaryError.message.includes('503') ||
         primaryError.message.includes('UNAVAILABLE'))) {

      try {
        console.log('Attempting OCR with Gemini 2.5 Flash Preview fallback...');
        const { object } = await generateObject({
          model: geminiFallback,
          schema: ReceiptSchema,
          messages: extractionPrompt,
        });

        console.log('OCR successful with fallback model');
        return object;
      } catch (fallbackError) {
        console.error('Fallback model also failed:', fallbackError);

        // If both models fail, return a low-confidence fallback result
        console.log('Both models failed, returning fallback result');
        return {
          total_amount: 0,
          business_name: '',
          receipt_date: '',
          service_description: '',
          financial_system_number: '',
          payment_method: '',
          confidence: 0.1
        };
      }
    }

    // For non-overload errors, throw the original error
    console.error('Receipt OCR extraction error:', primaryError);
    throw new Error('Failed to extract receipt data');
  }
}

/**
 * Convert extracted receipt data to transaction form format
 */
export function convertReceiptToTransactionData(receiptData: ReceiptData) {
  return {
    description: receiptData.service_description || 'Transaction from receipt',
    total_amount: receiptData.total_amount,
    business_name: receiptData.business_name,
    financial_system_number: receiptData.financial_system_number,
    receipt_date: receiptData.receipt_date,
    // Calculate suggested points based on amount (customize this logic)
    suggested_points: Math.floor(receiptData.total_amount * 0.1), // 10% points ratio
  };
}
