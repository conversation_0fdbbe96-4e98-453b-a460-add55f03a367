'use client'

import { useEffect } from 'react'

/**
 * Removes known browser extension attributes from the <body>
 * to prevent hydration mismatches.
 */
export function RemoveExtensionAttributes() {
  useEffect(() => {
    const attributesToRemove = [
      'cz-shortcut-listen',
      'monica-id',
      'monica-version',
      'data-grammarly',
      'data-lt-installed',
    ];
    attributesToRemove.forEach(attr => {
      if (document.body.hasAttribute(attr)) {
        document.body.removeAttribute(attr);
      }
    });
  }, []);

  return null;
}
