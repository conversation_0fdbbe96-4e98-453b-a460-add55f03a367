import { generateObject } from 'ai';
import { geminiFlash, geminiFallback } from '@/lib/ai-config';
import { z } from 'zod';
import { getServiceRoleClient } from '@/lib/supabase';

// Type definitions
export interface ReceiptTemplate {
  id: string;
  company_id: string;
  template_name: string;
  template_image_url: string;
  template_metadata: Record<string, unknown>;
  ai_prompt_context: string;
  validation_rules?: Record<string, unknown>;
  confidence_threshold?: number;
  is_active?: boolean;
}

export interface BusinessItem {
  id: string;
  business_item_id?: string;
  company_id: string;
  item_name: string;
  item_category?: string;
  standard_price?: number;
  keywords?: string[];
  is_active: boolean;
}

export interface MatchedItem {
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  matched_business_item?: BusinessItem;
  confidence_score?: number;
  matching_reason?: string;
}

// Enhanced schema for receipt data extraction with item-level detail
const EnhancedReceiptSchema = z.object({
  business_name: z.string().describe("Name of the business/company"),
  financial_system_number: z.string().describe("FS No or receipt number"),
  total_amount: z.number().describe("Total amount paid"),
  subtotal: z.number().optional().describe("Subtotal before tax"),
  tax_amount: z.number().optional().describe("Tax amount"),
  payment_method: z.string().describe("Payment method (CASH, CARD, etc.)"),
  business_tin: z.string().optional().describe("Business TIN number"),
  receipt_date: z.string().describe("Date on receipt (DD/MM/YYYY format)"),
  business_location: z.string().optional().describe("Business address or location"),

  // Enhanced item-level extraction
  items: z.array(z.object({
    description: z.string().describe("Item or service description"),
    quantity: z.number().default(1).describe("Quantity of item"),
    unit_price: z.number().describe("Price per unit"),
    total_price: z.number().describe("Total price for this item")
  })).describe("List of items/services with individual pricing"),

  // System metadata
  pos_system: z.string().optional().describe("POS system type (MarakiPOS, DATECS, etc.)"),
  confidence: z.number().min(0).max(1).describe("Confidence level of extraction"),
  processing_notes: z.string().optional().describe("Any issues or notes during processing")
});

export type EnhancedReceiptData = z.infer<typeof EnhancedReceiptSchema>;

// Ethiopian receipt validation functions
interface EthiopianValidationResult {
  isValid: boolean;
  issues: string[];
  confidence: number;
}

/**
 * Validate Ethiopian receipt compliance
 */
function validateEthiopianReceipt(receiptData: EnhancedReceiptData): EthiopianValidationResult {
  const issues: string[] = [];
  let confidence = 1.0;

  // Validate FS number format (8 digits)
  if (receiptData.financial_system_number) {
    const fsNumber = receiptData.financial_system_number.replace(/\D/g, '');
    if (fsNumber.length !== 8) {
      issues.push(`FS number should be 8 digits, got ${fsNumber.length}`);
      confidence -= 0.2;
    }
  } else {
    issues.push('Missing FS number');
    confidence -= 0.3;
  }

  // Validate VAT calculation (15% for Ethiopian businesses)
  if (receiptData.subtotal && receiptData.tax_amount) {
    const expectedVat = receiptData.subtotal * 0.15;
    const vatDifference = Math.abs(receiptData.tax_amount - expectedVat);
    if (vatDifference > 0.50) { // Allow small rounding differences
      issues.push(`VAT amount ${receiptData.tax_amount} doesn't match 15% of subtotal ${receiptData.subtotal}`);
      confidence -= 0.15;
    }
  }

  // Validate total calculation
  if (receiptData.subtotal && receiptData.tax_amount && receiptData.total_amount) {
    const calculatedTotal = receiptData.subtotal + receiptData.tax_amount;
    const totalDifference = Math.abs(receiptData.total_amount - calculatedTotal);
    if (totalDifference > 0.10) { // Allow small rounding
      issues.push(`Total amount ${receiptData.total_amount} doesn't match subtotal + tax`);
      confidence -= 0.2;
    }
  }

  // Validate TIN number format (if present)
  if (receiptData.business_tin) {
    const tin = receiptData.business_tin.replace(/\D/g, '');
    if (tin.length < 10 || tin.length > 15) {
      issues.push('TIN number format appears incorrect');
      confidence -= 0.1;
    }
  }

  // Validate date format (DD/MM/YYYY)
  if (receiptData.receipt_date) {
    const datePattern = /^\d{2}\/\d{2}\/\d{4}$/;
    if (!datePattern.test(receiptData.receipt_date)) {
      issues.push('Receipt date not in DD/MM/YYYY format');
      confidence -= 0.1;
    }
  }

  return {
    isValid: issues.length === 0,
    issues,
    confidence: Math.max(0, confidence)
  };
}

/**
 * Detect POS system from receipt patterns
 */
function detectPOSSystem(receiptData: EnhancedReceiptData): string | undefined {
  const allText = JSON.stringify(receiptData).toLowerCase();

  // MarakiPOS patterns (common in beauty salons)
  if (allText.includes('maraki') || allText.includes('beauty') || allText.includes('salon')) {
    return 'MarakiPOS';
  }

  // DATECS patterns (common in restaurants/cafes)
  if (allText.includes('datecs') || allText.includes('restaurant') || allText.includes('cafe')) {
    return 'DATECS';
  }

  // Generic patterns
  if (allText.includes('pos') || allText.includes('terminal')) {
    return 'Generic POS';
  }

  return undefined;
}

/**

// Template-aware receipt processing
export interface ReceiptTemplate {
  id: string;
  company_id: string;
  template_name: string;
  ai_prompt_context: string;
  validation_rules: Record<string, unknown>;
  confidence_threshold: number;
  is_active: boolean;
}

/**
 * Enhanced receipt extraction with template support and business item matching
 */
export async function extractReceiptDataEnhanced(
  imageBuffer: Buffer,
  companyId?: string,
  templateId?: string
): Promise<EnhancedReceiptData & { matched_items?: MatchedItem[] }> {
  const supabase = getServiceRoleClient();

  // Get template and business context if available
  let template: ReceiptTemplate | null = null;
  let businessItems: BusinessItem[] = [];

  if (companyId) {
    // Fetch active template for this company
    if (templateId) {
      const { data: templateData } = await supabase
        .from('receipt_templates')
        .select('*')
        .eq('id', templateId)
        .eq('is_active', true)
        .single();
      template = templateData;
    } else {
      // Get the most successful template for this company
      const { data: templateData } = await supabase
        .from('receipt_templates')
        .select('*')
        .eq('company_id', companyId)
        .eq('is_active', true)
        .order('successful_extractions', { ascending: false })
        .limit(1)
        .single();
      template = templateData;
    }

    // Fetch business items for matching
    const { data: items } = await supabase
      .from('business_items')
      .select('*')
      .eq('company_id', companyId)
      .eq('is_active', true);
    businessItems = items || [];
  }

  // Build enhanced prompt with template context
  const enhancedPrompt = buildTemplateAwarePrompt(template, businessItems);

  const extractionMessages = [
    {
      role: 'user' as const,
      content: [
        {
          type: 'text' as const,
          text: enhancedPrompt
        },
        {
          type: 'image' as const,
          image: imageBuffer,
        },
      ],
    },
  ];

  // Extract with template-aware processing
  let extractedData: EnhancedReceiptData;

  try {
    console.log(`Attempting enhanced OCR with ${template ? 'template-aware' : 'standard'} processing...`);
    const { object } = await generateObject({
      model: geminiFlash,
      schema: EnhancedReceiptSchema,
      messages: extractionMessages,
    });

    extractedData = object;
    console.log('Enhanced OCR successful with Gemini 2.0 Flash');
  } catch (primaryError) {
    console.log('Primary model failed, trying fallback...', primaryError);

    try {
      const { object } = await generateObject({
        model: geminiFallback,
        schema: EnhancedReceiptSchema,
        messages: extractionMessages,
      });

      extractedData = object;
      console.log('Enhanced OCR successful with fallback model');
    } catch (fallbackError) {
      console.error('Both models failed:', fallbackError);
      throw new Error('Failed to extract enhanced receipt data');
    }
  }

  // Validate Ethiopian receipt compliance
  const validation = validateEthiopianReceipt(extractedData);
  if (!validation.isValid) {
    console.warn('Ethiopian receipt validation issues:', validation.issues);
  }

  // Detect and set POS system
  if (!extractedData.pos_system) {
    extractedData.pos_system = detectPOSSystem(extractedData);
  }

  // Adjust confidence based on validation results
  if (extractedData.confidence && validation.confidence < extractedData.confidence) {
    extractedData.confidence = validation.confidence;
    console.log(`Adjusted confidence to ${validation.confidence} due to validation issues`);
  }

  // Match extracted items with business catalog
  const matchedItems = await matchExtractedItems(
    extractedData.items,
    businessItems,
    companyId
  );

  // Update template performance if used
  if (template) {
    await updateTemplatePerformance(template.id, extractedData.confidence);
  }

  return {
    ...extractedData,
    matched_items: matchedItems
  };
}

/**
 * Build template-aware AI prompt with business-specific context
 */
function buildTemplateAwarePrompt(
  template: ReceiptTemplate | null,
  businessItems: BusinessItem[]
): string {
  let prompt = `Extract detailed information from this receipt image, including individual items and their prices.

CRITICAL REQUIREMENTS:
1. Extract EVERY item/service with individual quantities and prices
2. Be extremely accurate with financial amounts
3. Use DD/MM/YYYY format for dates
4. Identify the POS system if visible (MarakiPOS, DATECS, etc.)
5. Return confidence score based on clarity and extraction certainty`;

  if (template?.ai_prompt_context) {
    prompt += `\n\nBUSINESS-SPECIFIC CONTEXT:\n${template.ai_prompt_context}`;
  }

  if (businessItems.length > 0) {
    const commonItems = businessItems
      .slice(0, 10) // Limit to avoid token overflow
      .map(item => `- ${item.item_name} (${item.standard_price} ETB)`)
      .join('\n');

    prompt += `\n\nCOMMON ITEMS FOR THIS BUSINESS:\n${commonItems}\n
When extracting items, try to match descriptions to these known business items.`;
  }

  // Ethiopian receipt-specific guidance
  prompt += `\n\nETHIOPIAN RECEIPT COMPLIANCE:
- FS numbers are typically 8-digit format
- VAT is usually 15%
- Common POS systems: MarakiPOS, DATECS
- TIN format validation if present`;

  return prompt;
}

/**
 * Match extracted items against business catalog using fuzzy matching
 */
async function matchExtractedItems(
  extractedItems: Array<{ description: string; quantity: number; unit_price: number; total_price: number }>,
  businessItems: BusinessItem[],
  companyId?: string
) {
  if (!companyId || businessItems.length === 0) {
    return extractedItems.map(item => ({
      ...item,
      matched_business_item: null,
      confidence_score: 0,
      matching_reason: 'no_catalog'
    }));
  }

  const supabase = getServiceRoleClient();
  const matchedItems = [];

  for (const item of extractedItems) {
    try {
      // Use the database matching function
      const { data: matches } = await supabase
        .rpc('match_business_item', {
          p_company_id: companyId,
          p_description: item.description,
          p_price: item.unit_price
        });

      const bestMatch = matches && matches.length > 0 ? matches[0] : null;

      matchedItems.push({
        ...item,
        matched_business_item: bestMatch,
        confidence_score: bestMatch?.confidence_score || 0,
        matching_reason: bestMatch?.matching_reason || 'no_match'
      });
    } catch (error) {
      console.error('Error matching item:', error);
      matchedItems.push({
        ...item,
        matched_business_item: null,
        confidence_score: 0,
        matching_reason: 'error'
      });
    }
  }

  return matchedItems;
}

/**
 * Update template performance metrics
 */
async function updateTemplatePerformance(
  templateId: string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _confidence: number
) {
  const supabase = getServiceRoleClient();

  // Simple update - could be enhanced with proper RPC functions later
  await supabase
    .from('receipt_templates')
    .update({
      updated_at: new Date().toISOString()
    })
    .eq('id', templateId);
}

/**
 * Save processed receipt with enhanced item-level data
 */
export async function saveEnhancedReceiptData(
  receiptData: EnhancedReceiptData,
  matchedItems: MatchedItem[],
  receiptId: string,
  templateId?: string
) {
  const supabase = getServiceRoleClient();

  // Update receipt with enhanced metadata
  await supabase
    .from('receipts')
    .update({
      template_id: templateId,
      extraction_confidence: receiptData.confidence,
      pos_system: receiptData.pos_system
    })
    .eq('id', receiptId);

  // Save individual receipt items
  for (const item of matchedItems) {
    const receiptItem = {
      receipt_id: receiptId,
      business_item_id: item.matched_business_item?.business_item_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price,
      extracted_description: item.description,
      confidence_score: item.confidence_score
    };

    await supabase
      .from('receipt_items')
      .insert(receiptItem)
      .select()
      .single();

    // Update business item statistics if matched
    if (item.matched_business_item?.business_item_id) {
      await supabase.rpc('update_business_item_stats', {
        p_business_item_id: item.matched_business_item.business_item_id,
        p_sale_price: item.unit_price,
        p_quantity: item.quantity
      });
    }
  }
}

/**
 * Backward compatibility function - converts enhanced data to legacy format
 */
export function convertToLegacyFormat(enhancedData: EnhancedReceiptData) {
  return {
    business_name: enhancedData.business_name,
    financial_system_number: enhancedData.financial_system_number,
    total_amount: enhancedData.total_amount,
    subtotal: enhancedData.subtotal,
    tax_amount: enhancedData.tax_amount,
    service_description: enhancedData.items.map(item => item.description).join(', '),
    payment_method: enhancedData.payment_method,
    business_tin: enhancedData.business_tin,
    receipt_date: enhancedData.receipt_date,
    business_location: enhancedData.business_location,
    confidence: enhancedData.confidence
  };
}
