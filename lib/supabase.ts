import { createClient as createSupaba<PERSON><PERSON>lient } from '@supabase/supabase-js'

// Re-export the client creation functions for easy access
export { createClient as createBrowserClient } from './supabase/client'
// NOTE: Server and middleware clients are not re-exported here to avoid importing next/headers in client code
// Import them directly from './supabase/server' and './supabase/middleware' when needed

// Legacy exports for backward compatibility during migration
import { createClient as browserClient } from './supabase/client'

let cachedClient: ReturnType<typeof browserClient> | null = null

/**
 * Get a Supabase client for browser usage
 * Uses proper SSR-compatible approach with automatic cookie handling
 */
export function getSupabaseClient() {
  if (typeof window === 'undefined') {
    // Server-side: Don't cache server clients
    throw new Error('getSupabaseClient should only be called on the client side. Use createServerClient for server-side usage.')
  }

  // Client-side: Use singleton pattern
  if (!cachedClient) {
    cachedClient = browserClient()
  }

  return cachedClient
}

/**
 * Legacy browser client getter - alias for getSup<PERSON><PERSON><PERSON>lient
 * @deprecated Use getSupabaseClient() instead
 */
export function getSupabaseBrowserClient() {
  return getSupabaseClient()
}

/**
 * Create a service role client that bypasses RLS
 * Should only be used in API routes or server-side code
 */
export function getServiceRoleClient() {
  if (typeof window !== 'undefined') {
    throw new Error('Service role client should never be used on the client side')
  }

  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// Deprecated - use createBrowserClient instead
export const getSupabase = getSupabaseClient
export const refreshSupabaseClient = () => {
  cachedClient = null
  return getSupabaseClient()
}
