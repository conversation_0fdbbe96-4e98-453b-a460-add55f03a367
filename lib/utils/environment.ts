/**
 * Environment utility functions for handling URLs and environment-specific logic
 */

/**
 * Get the base URL of the application based on the current environment
 * This handles both development and production (Vercel) environments
 */
export function getBaseUrl(): string {
  // First check for explicitly set APP_URL
  if (process.env.NEXT_PUBLIC_APP_URL) {
    return process.env.NEXT_PUBLIC_APP_URL;
  }
  
  // Check for Vercel-specific environment variables
  if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    return `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`;
  }
  
  // Fallback for local development
  return 'http://localhost:3000';
}

/**
 * Get the full URL by combining the base URL with a path
 */
export function getUrl(path: string): string {
  return `${getBaseUrl()}${path.startsWith('/') ? path : `/${path}`}`;
}
