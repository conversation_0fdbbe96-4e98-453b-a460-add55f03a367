import { generateObject, generateText } from 'ai';
import { geminiFlash, geminiFallback } from '@/lib/ai-config';
import { z } from 'zod';

// Check if AI is available
const IS_AI_AVAILABLE = !!process.env.GOOGLE_GENERATIVE_AI_API_KEY;

// Log warning if AI is not available
if (!IS_AI_AVAILABLE) {
  console.warn('Google Generative AI API Key not found. AI validation will use fallback logic.');
}

// Database entity types
export interface Member {
  id: string;
  name: string;
  loyalty_tier: 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM';
  available_points: number;
  lifetime_points: number;
  redeemed_points: number;
  expired_points: number;
}

export interface Reward {
  id: string;
  title: string;
  description?: string;
  points_required: number;
  reward_value_type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SERVICE' | 'POINTS_BONUS' | 'PRODUCT_GIFT' | 'DOUBLE_POINTS';
  reward_value: number;
  is_active: boolean;
  expiration_date?: string | null;
}

export interface ReceiptData {
  total_amount: number;
  business_name?: string;
  receipt_date?: string;
  service_description?: string;
  financial_system_number?: string;
}

// Types for AI validation
export interface MemberQualification {
  memberId: string;
  currentTier: 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM';
  availablePoints: number;
  lifetimePoints: number;
  qualifiedRewards: RewardEligibility[];
  recommendations: RewardRecommendation[];
}

export interface RewardEligibility {
  rewardId: string;
  eligible: boolean;
  reasons: string[];
  missingPoints?: number;
  tierRequirement?: string;
  confidence: number;
}

export interface RewardRecommendation {
  rewardId: string;
  rewardTitle: string;
  score: number;
  reasoning: string;
  savings: number;
  urgency: 'low' | 'medium' | 'high';
}

export interface TransactionOutcome {
  originalAmount: number;
  discountAmount: number;
  finalAmount: number;
  pointsUsed: number;
  pointsRemaining: number;
  savingsPercentage: number;
  additionalBenefits: string[];
  nextTierProgress?: TierProgress;
  confidence: number;
}

export interface TierProgress {
  currentTier: string;
  nextTier: string;
  pointsToNext: number;
  benefitsUnlocked: string[];
}

export interface ReceiptAnalysis {
  receiptTotal: number;
  merchantName: string;
  transactionDate: string;
  applicableRewards: RewardMatch[];
  recommendedReward: RewardMatch | null;
  calculatedOutcome: TransactionOutcome;
  confidence: number;
}

export interface RewardMatch {
  rewardId: string;
  rewardType: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SERVICE' | 'POINTS_BONUS' | 'PRODUCT_GIFT' | 'DOUBLE_POINTS';
  applicabilityScore: number;
  estimatedSavings: number;
  finalAmount: number;
  explanation: string;
}

// AI Configuration
const aiConfig = {
  model: geminiFlash, // Use Gemini 2.0 Flash as primary
  temperature: 0.1, // Low temperature for consistent validation
  maxTokens: 1000,
};

/**
 * Helper function to generate objects with model fallback
 */
async function generateObjectWithFallback<T>(options: {
  schema: z.ZodSchema<T>;
  prompt: string;
  temperature?: number;
  maxTokens?: number;
}): Promise<{ object: T }> {
  const { schema, prompt, temperature = 0.1, maxTokens = 1000 } = options;

  // Try primary model first (Gemini 2.0 Flash)
  try {
    console.log('Attempting AI generation with Gemini 2.0 Flash...');
    const result = await generateObject({
      model: geminiFlash,
      schema,
      prompt,
      temperature,
      maxTokens,
    });
    console.log('AI generation successful with primary model');
    return result;
  } catch (primaryError) {
    console.log('Primary model failed, trying fallback...', primaryError);

    // Check if it's an overload error, try fallback model
    if (primaryError instanceof Error &&
        (primaryError.message.includes('overloaded') ||
         primaryError.message.includes('503') ||
         primaryError.message.includes('UNAVAILABLE'))) {

      try {
        console.log('Attempting AI generation with Gemini 2.5 Flash Preview fallback...');
        const result = await generateObject({
          model: geminiFallback,
          schema,
          prompt,
          temperature,
          maxTokens,
        });
        console.log('AI generation successful with fallback model');
        return result;
      } catch (fallbackError) {
        console.error('Both AI models failed:', fallbackError);
        throw primaryError; // Throw original error for proper handling by calling code
      }
    }

    // For non-overload errors, throw the original error
    console.error('AI generation error:', primaryError);
    throw primaryError;
  }
}

/**
 * Helper function to generate text with model fallback
 */
async function generateTextWithFallback(options: {
  prompt: string;
  temperature?: number;
  maxTokens?: number;
}): Promise<{ text: string }> {
  const { prompt, temperature = 0.1, maxTokens = 1000 } = options;

  // Try primary model first (Gemini 2.0 Flash)
  try {
    console.log('Attempting text generation with Gemini 2.0 Flash...');
    const result = await generateText({
      model: geminiFlash,
      prompt,
      temperature,
      maxTokens,
    });
    console.log('Text generation successful with primary model');
    return result;
  } catch (primaryError) {
    console.log('Primary model failed, trying fallback...', primaryError);

    // Check if it's an overload error, try fallback model
    if (primaryError instanceof Error &&
        (primaryError.message.includes('overloaded') ||
         primaryError.message.includes('503') ||
         primaryError.message.includes('UNAVAILABLE'))) {

      try {
        console.log('Attempting text generation with Gemini 2.5 Flash Preview fallback...');
        const result = await generateText({
          model: geminiFallback,
          prompt,
          temperature,
          maxTokens,
        });
        console.log('Text generation successful with fallback model');
        return result;
      } catch (fallbackError) {
        console.error('Both AI models failed:', fallbackError);
        throw primaryError; // Throw original error for proper handling by calling code
      }
    }

    // For non-overload errors, throw the original error
    console.error('Text generation error:', primaryError);
    throw primaryError;
  }
}

// Validation schemas for AI responses
const rewardEligibilitySchema = z.object({
  eligible: z.boolean(),
  reasons: z.array(z.string()),
  missingPoints: z.number().optional(),
  tierRequirement: z.string().optional(),
  confidence: z.number().min(0).max(1),
});

const transactionOutcomeSchema = z.object({
  originalAmount: z.number(),
  discountAmount: z.number(),
  finalAmount: z.number(),
  pointsUsed: z.number(),
  pointsRemaining: z.number(),
  savingsPercentage: z.number(),
  additionalBenefits: z.array(z.string()),
  confidence: z.number().min(0).max(1),
});

const rewardRecommendationSchema = z.object({
  recommendations: z.array(z.object({
    rewardId: z.string(),
    rewardTitle: z.string(),
    score: z.number().min(0).max(100), // Changed from max(1) to max(100) to allow actual savings amounts
    reasoning: z.string(),
    savings: z.number(),
    urgency: z.enum(['low', 'medium', 'high']),
  })),
});

/**
 * AI-powered validation service for loyalty program redemptions
 */
export class AIValidationService {
  /**
   * Validate if a member is eligible for a specific reward
   */
  async validateMemberEligibility(
    member: Member,
    reward: Reward,
    receiptTotal?: number
  ): Promise<RewardEligibility> {
    // Use fallback logic if AI is not available
    if (!IS_AI_AVAILABLE) {
      return this.fallbackEligibilityCheck(member, reward);
    }
    const prompt = `
Analyze if this loyalty program member can redeem the specified reward:

MEMBER INFO:
- Name: ${member.name}
- Tier: ${member.loyalty_tier || 'BRONZE'}
- Available Points: ${member.available_points}
- Lifetime Points: ${member.lifetime_points}

REWARD INFO:
- Title: ${reward.title}
- Points Required: ${reward.points_required}
- Type: ${reward.reward_value_type}
- Value: ${reward.reward_value}
- Active: ${reward.is_active}
- Expires: ${reward.expiration_date ? new Date(reward.expiration_date).toLocaleDateString() : 'No expiration'}

${receiptTotal ? `TRANSACTION: Receipt total is ${receiptTotal} Birr` : ''}

VALIDATION RULES:
1. Member must have sufficient available points
2. Reward must be active and not expired
3. Consider tier restrictions (if any)
4. Check if reward type is applicable to transaction

Provide eligibility analysis with clear reasoning.
`;

    try {
      const { object } = await generateObjectWithFallback({
        schema: rewardEligibilitySchema,
        prompt,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
      });

      return {
        rewardId: reward.id,
        eligible: object.eligible,
        reasons: object.reasons,
        missingPoints: object.missingPoints,
        tierRequirement: object.tierRequirement,
        confidence: object.confidence,
      };
    } catch (error) {
      console.error('AI validation failed:', error);
      // Fallback to rule-based validation
      return this.fallbackValidation(member, reward);
    }
  }

  /**
   * Calculate reward outcome using AI for complex scenarios
   */
  async calculateRewardOutcome(
    member: Member,
    reward: Reward,
    receiptTotal: number
  ): Promise<TransactionOutcome> {
    // Use fallback calculation if AI is not available
    if (!IS_AI_AVAILABLE) {
      return this.fallbackCalculation(member, reward, receiptTotal);
    }
    const prompt = `
Calculate the exact outcome of applying this reward to the transaction:

MEMBER:
- Available Points: ${member.available_points}
- Current Tier: ${member.loyalty_tier || 'BRONZE'}
- Lifetime Points: ${member.lifetime_points}

REWARD:
- Title: ${reward.title}
- Type: ${reward.reward_value_type}
- Value: ${reward.reward_value}
- Points Cost: ${reward.points_required}

TRANSACTION:
- Receipt Total: ${receiptTotal} Birr

TIER THRESHOLDS:
- SILVER: 1000 points
- GOLD: 5000 points
- PLATINUM: 10000 points

CALCULATION REQUIREMENTS:
1. For PERCENTAGE rewards: discount = (receiptTotal * reward.reward_value) / 100
2. For FIXED_AMOUNT rewards: discount = reward.reward_value (max: receiptTotal)
3. Final amount = receiptTotal - discount (minimum: 0)
4. Points remaining = available_points - points_required
5. Calculate savings percentage
6. Check tier progression after points deduction

Provide exact calculations with high precision.
`;

    try {
      const { object } = await generateObjectWithFallback({
        schema: transactionOutcomeSchema,
        prompt,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
      });

      // Calculate tier progression
      const nextTierProgress = this.calculateTierProgress(
        member.lifetime_points
      );

      return {
        ...object,
        nextTierProgress,
      };
    } catch (error) {
      console.error('AI calculation failed:', error);
      // Fallback to manual calculation
      return this.fallbackCalculation(member, reward, receiptTotal);
    }
  }

  /**
   * Generate intelligent reward recommendations
   */
  async recommendAlternatives(
    member: Member,
    availableRewards: Reward[],
    receiptTotal?: number,
    excludeRewardId?: string
  ): Promise<RewardRecommendation[]> {
    // Use fallback recommendations if AI is not available
    if (!IS_AI_AVAILABLE) {
      return this.fallbackRecommendations(member, availableRewards, receiptTotal, excludeRewardId);
    }
    const eligibleRewards = availableRewards.filter(
      r => r.id !== excludeRewardId &&
           r.is_active &&
           member.available_points >= r.points_required
    );

    if (eligibleRewards.length === 0) {
      return [];
    }

    const prompt = `
Recommend the best rewards for this member based on their profile and transaction:

MEMBER PROFILE:
- Tier: ${member.loyalty_tier || 'BRONZE'}
- Available Points: ${member.available_points}
- Lifetime Points: ${member.lifetime_points}

${receiptTotal ? `TRANSACTION: ${receiptTotal} Birr receipt` : 'NO CURRENT TRANSACTION'}

AVAILABLE REWARDS:
${eligibleRewards.map(r => `
- ${r.title}: ${r.points_required} points, ${r.reward_value_type} ${r.reward_value}
  ID: ${r.id}
`).join('')}

RECOMMENDATION CRITERIA:
1. Maximum value/savings for the member
2. Point efficiency (best savings per point)
3. Tier progression consideration
4. Transaction context relevance
5. Urgency based on point balance and reward expiry

Rank rewards by overall benefit and provide reasoning.
`;

    try {
      const { object } = await generateObjectWithFallback({
        schema: rewardRecommendationSchema,
        prompt,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
      });

      return object.recommendations;
    } catch (error) {
      console.error('AI recommendation failed:', error);
      // Fallback to simple recommendations
      return this.fallbackRecommendations(member, eligibleRewards, receiptTotal);
    }
  }

  /**
   * Analyze receipt for intelligent reward matching
   */
  async analyzeReceipt(
    receiptData: ReceiptData,
    member: Member,
    availableRewards: Reward[]
  ): Promise<ReceiptAnalysis> {
    // Use fallback receipt analysis if AI is not available
    if (!IS_AI_AVAILABLE) {
      return this.fallbackReceiptAnalysis(receiptData, member, availableRewards);
    }
    const prompt = `
Analyze this receipt and recommend the best rewards for the member:

RECEIPT DATA:
- Business: ${receiptData.business_name || 'Unknown'}
- Total Amount: ${receiptData.total_amount} Birr
- Date: ${receiptData.receipt_date || 'Unknown'}
- Service: ${receiptData.service_description || 'General purchase'}

MEMBER:
- Tier: ${member.loyalty_tier || 'BRONZE'}
- Available Points: ${member.available_points}

AVAILABLE REWARDS:
${availableRewards.map(r => `
- ${r.title}: ${r.points_required} points
  Type: ${r.reward_value_type}, Value: ${r.reward_value}
  Description: ${r.description || 'No description'}
`).join('')}

ANALYSIS REQUIREMENTS:
1. Identify rewards most applicable to this transaction type
2. Calculate potential savings for each applicable reward
3. Consider merchant category relevance
4. Factor in member's point balance efficiency
5. Recommend optimal reward choice

Provide detailed analysis with confidence scoring.
`;

    try {
      const response = await generateTextWithFallback({
        prompt,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
      });

      // Parse the AI response and structure it
      const recommendedReward = this.parseRecommendationFromText(response.text, availableRewards, receiptData, member);

      // Calculate outcome for recommended reward
      let calculatedOutcome: TransactionOutcome;
      if (recommendedReward) {
        const reward = availableRewards.find(r => r.id === recommendedReward.rewardId);
        if (reward) {
          calculatedOutcome = await this.calculateRewardOutcome(member, reward, receiptData.total_amount);
        } else {
          calculatedOutcome = this.getDefaultOutcome(receiptData, member);
        }
      } else {
        calculatedOutcome = this.getDefaultOutcome(receiptData, member);
      }

      return {
        receiptTotal: receiptData.total_amount,
        merchantName: receiptData.business_name || 'Unknown',
        transactionDate: receiptData.receipt_date || new Date().toISOString(),
        applicableRewards: [], // Would be populated by parsing AI response
        recommendedReward,
        calculatedOutcome,
        confidence: 0.8, // Would be calculated based on AI response
      };
    } catch (error) {
      console.error('AI receipt analysis failed:', error);
      return this.fallbackReceiptAnalysis(receiptData, member, availableRewards);
    }
  }

  /**
   * Generate a descriptive transaction description based on reward and receipt data
   */
  async generateTransactionDescription(
    reward: Reward,
    receiptData?: ReceiptData,
    member?: Member
  ): Promise<string> {
    // Use fallback logic if AI is not available
    if (!IS_AI_AVAILABLE) {
      return this.fallbackTransactionDescription(reward, receiptData);
    }

    const prompt = `
Generate a concise, professional transaction description for a loyalty program redemption.

REWARD INFORMATION:
- Reward Name: ${reward.title}
- Reward Type: ${reward.reward_value_type}
- Reward Value: ${reward.reward_value}${reward.reward_value_type === 'PERCENTAGE' ? '%' : ' Birr'}
- Description: ${reward.description || 'No additional details'}

${receiptData ? `
RECEIPT INFORMATION:
- Business: ${receiptData.business_name || 'Unknown business'}
- Total Amount: ${receiptData.total_amount} Birr
- Service Description: ${receiptData.service_description || 'General service'}
- Date: ${receiptData.receipt_date || 'Today'}
` : ''}

${member ? `
MEMBER INFORMATION:
- Member: ${member.name}
- Tier: ${member.loyalty_tier}
` : ''}

REQUIREMENTS:
1. Keep it concise (under 80 characters)
2. Be specific about what the reward provides
3. Include the service/business context if available
4. Make it professional and clear
5. Use active voice

EXAMPLES:
- "Redeemed 10% discount on haircut service at ATLAS HOTEL"
- "Applied 50 Birr discount on spa treatment"
- "Used free coffee reward at Coffee Shop"
- "Redeemed 15% off dining at Restaurant Name"

Generate ONLY the description text, no quotes or additional formatting.
`;

    try {
      const result = await generateTextWithFallback({
        prompt,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
      });

      // Clean up the result and ensure it's under 100 characters
      const description = result.text.trim()
        .replace(/^["']|["']$/g, '') // Remove surrounding quotes
        .substring(0, 100); // Limit to 100 characters

      return description || this.fallbackTransactionDescription(reward, receiptData);
    } catch (error) {
      console.error('Error generating transaction description:', error);
      return this.fallbackTransactionDescription(reward, receiptData);
    }
  }

  /**
   * Fallback method for generating transaction descriptions when AI is unavailable
   */
  private fallbackTransactionDescription(reward: Reward, receiptData?: ReceiptData): string {
    const businessPart = receiptData?.business_name ? ` at ${receiptData.business_name}` : '';
    const servicePart = receiptData?.service_description ? ` on ${receiptData.service_description.toLowerCase()}` : '';

    switch (reward.reward_value_type) {
      case 'PERCENTAGE':
        return `Redeemed ${reward.reward_value}% discount${servicePart}${businessPart}`;
      case 'FIXED_AMOUNT':
        return `Applied ${reward.reward_value} Birr discount${servicePart}${businessPart}`;
      case 'FREE_SERVICE':
        return `Used ${reward.title.toLowerCase()}${businessPart}`;
      case 'PRODUCT_GIFT':
        return `Redeemed ${reward.title.toLowerCase()}${businessPart}`;
      default:
        return `Redeemed ${reward.title}${businessPart}`;
    }
  }

  /**
   * Fallback validation using rule-based logic
   */
  private fallbackValidation(member: Member, reward: Reward): RewardEligibility {
    const reasons: string[] = [];
    let eligible = true;

    // Check points
    if (member.available_points < reward.points_required) {
      eligible = false;
      reasons.push(`Insufficient points: need ${reward.points_required}, have ${member.available_points}`);
    }

    // Check if active
    if (!reward.is_active) {
      eligible = false;
      reasons.push('Reward is not currently active');
    }

    // Check expiry
    if (reward.expiration_date && new Date(reward.expiration_date) < new Date()) {
      eligible = false;
      reasons.push('Reward has expired');
    }

    if (eligible) {
      reasons.push('All eligibility requirements met');
    }

    return {
      rewardId: reward.id,
      eligible,
      reasons,
      missingPoints: eligible ? undefined : Math.max(0, reward.points_required - member.available_points),
      confidence: 0.9, // High confidence for rule-based validation
    };
  }

  /**
   * Calculate reward outcome using manual logic
   */
  private fallbackCalculation(member: Member, reward: Reward, receiptTotal: number): TransactionOutcome {
    let discountAmount = 0;
    let finalAmount = receiptTotal;

    // Calculate based on reward type
    switch (reward.reward_value_type) {
      case 'PERCENTAGE':
        discountAmount = receiptTotal * (reward.reward_value / 100);
        finalAmount = receiptTotal - discountAmount;
        break;
      case 'FIXED_AMOUNT':
        discountAmount = Math.min(reward.reward_value, receiptTotal);
        finalAmount = receiptTotal - discountAmount;
        break;
      case 'FREE_SERVICE':
        discountAmount = receiptTotal;
        finalAmount = 0;
        break;
      case 'POINTS_BONUS':
        // No direct discount, might add bonus points
        discountAmount = 0;
        finalAmount = receiptTotal;
        break;
      case 'PRODUCT_GIFT':
        // Assume product value is the reward value
        discountAmount = reward.reward_value;
        finalAmount = receiptTotal;
        break;
      case 'DOUBLE_POINTS':
        // Double points applies to future transactions, no immediate discount
        discountAmount = 0;
        finalAmount = receiptTotal;
        break;
    }

    // Calculate remaining points
    const pointsRemaining = member.available_points - reward.points_required;

    // Determine savings percentage
    const savingsPercentage = (discountAmount / receiptTotal) * 100;

    console.log('Fallback calculation debug:', {
      receiptTotal,
      discountAmount,
      savingsPercentage,
      rewardType: reward.reward_value_type,
      rewardValue: reward.reward_value
    });

    // Create outcome object
    return {
      originalAmount: receiptTotal,
      discountAmount,
      finalAmount,
      pointsUsed: reward.points_required,
      pointsRemaining,
      savingsPercentage,
      additionalBenefits: [],
      confidence: 1.0 // High confidence in rule-based calculation
    };
  }

  /**
   * Generate fallback eligibility check without AI
   */
  private fallbackEligibilityCheck(member: Member, reward: Reward): RewardEligibility {
    // Basic eligibility logic
    const hasEnoughPoints = member.available_points >= reward.points_required;
    const tierRequirement = this.getTierRequirement(member.loyalty_tier, reward);
    const eligible = hasEnoughPoints && !tierRequirement && reward.is_active;

    // Generate reasons
    const reasons = [];
    if (!hasEnoughPoints) {
      reasons.push(`Member has only ${member.available_points} points but needs ${reward.points_required} points.`);
    } else {
      reasons.push(`Member has sufficient points (${member.available_points} available).`);
    }

    if (tierRequirement) {
      reasons.push(`This reward requires ${tierRequirement} tier but member is ${member.loyalty_tier}.`);
    } else {
      reasons.push(`Member's tier ${member.loyalty_tier} qualifies for this reward.`);
    }

    if (!reward.is_active) {
      reasons.push('This reward is currently inactive.');
    }

    if (reward.expiration_date) {
      const expiryDate = new Date(reward.expiration_date);
      const now = new Date();
      if (expiryDate < now) {
        reasons.push(`This reward expired on ${expiryDate.toLocaleDateString()}.`);
        return {
          rewardId: reward.id,
          eligible: false,
          reasons,
          missingPoints: hasEnoughPoints ? undefined : reward.points_required - member.available_points,
          tierRequirement: tierRequirement,
          confidence: 1.0, // High confidence in rule-based decision
        };
      } else {
        reasons.push(`This reward is valid until ${expiryDate.toLocaleDateString()}.`);
      }
    }

    return {
      rewardId: reward.id,
      eligible,
      reasons,
      missingPoints: hasEnoughPoints ? undefined : reward.points_required - member.available_points,
      tierRequirement,
      confidence: 1.0, // High confidence in rule-based decision
    };
  }

  /**
   * Helper method to check tier requirements
   */
  private getTierRequirement(memberTier: string, reward: Reward): string | undefined {
    // Simple tier hierarchy for fallback
    const tiers = ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM'];

    // Extract tier requirement from reward description or custom logic
    // This is a placeholder - in a real implementation, you would have a proper field for tier requirement
    const tierRequirementMatch = reward.description?.match(/requires (BRONZE|SILVER|GOLD|PLATINUM) tier/i);
    if (tierRequirementMatch) {
      const requiredTier = tierRequirementMatch[1].toUpperCase();
      const memberTierIndex = tiers.indexOf(memberTier.toUpperCase());
      const requiredTierIndex = tiers.indexOf(requiredTier);

      if (memberTierIndex < requiredTierIndex) {
        return requiredTier;
      }
    }

    return undefined;
  }

  /**
   * Calculate tier progression
   */
  private calculateTierProgress(lifetimePoints: number): TierProgress | undefined {
    const tiers = [
      { name: 'BRONZE', min: 0 },
      { name: 'SILVER', min: 1000 },
      { name: 'GOLD', min: 5000 },
      { name: 'PLATINUM', min: 10000 },
    ];

    const currentTier = tiers.reverse().find(t => lifetimePoints >= t.min) || tiers[0];
    const nextTier = tiers.find(t => t.min > lifetimePoints);

    if (!nextTier) return undefined;

    return {
      currentTier: currentTier.name,
      nextTier: nextTier.name,
      pointsToNext: nextTier.min - lifetimePoints,
      benefitsUnlocked: [], // Would be populated based on tier benefits
    };
  }

  /**
   * Fallback recommendations using simple logic
   */
  private fallbackRecommendations(
    member: Member,
    eligibleRewards: Reward[],
    receiptTotal?: number,
    excludeRewardId?: string
  ): RewardRecommendation[] {
    // Filter out excluded reward if specified
    const filteredRewards = excludeRewardId ?
      eligibleRewards.filter(r => r.id !== excludeRewardId) :
      eligibleRewards;
    return filteredRewards
      .filter(r => member.available_points >= r.points_required)
      .sort((a, b) => {
        // Sort by point efficiency (value per point)
        const aEfficiency = this.calculateEfficiency(a, receiptTotal);
        const bEfficiency = this.calculateEfficiency(b, receiptTotal);
        return bEfficiency - aEfficiency;
      })
      .slice(0, 3)
      .map((reward, index) => ({
        rewardId: reward.id,
        rewardTitle: reward.title,
        score: 1 - (index * 0.2), // Decreasing scores
        reasoning: `Efficient use of ${reward.points_required} points`,
        savings: this.calculateSavings(reward, receiptTotal || 0),
        urgency: index === 0 ? 'high' : index === 1 ? 'medium' : 'low',
      }));
  }

  private calculateEfficiency(reward: Reward, receiptTotal?: number): number {
    if (!receiptTotal) return reward.reward_value / reward.points_required;

    const savings = this.calculateSavings(reward, receiptTotal);
    return savings / reward.points_required;
  }

  private calculateSavings(reward: Reward, receiptTotal: number): number {
    if (reward.reward_value_type === 'PERCENTAGE') {
      return (receiptTotal * reward.reward_value) / 100;
    } else if (reward.reward_value_type === 'FIXED_AMOUNT') {
      return Math.min(reward.reward_value, receiptTotal);
    }
    return 0;
  }

  private parseRecommendationFromText(text: string, availableRewards: Reward[], receiptData: ReceiptData, member: Member): RewardMatch | null {
    // Simple implementation - in production, you'd want more sophisticated parsing
    const bestReward = availableRewards.find(r => member.available_points >= r.points_required);
    if (!bestReward) return null;

    const savings = this.calculateSavings(bestReward, receiptData.total_amount);
    return {
      rewardId: bestReward.id,
      rewardType: bestReward.reward_value_type,
      applicabilityScore: 0.8,
      estimatedSavings: savings,
      finalAmount: receiptData.total_amount - savings,
      explanation: 'AI recommended based on transaction analysis',
    };
  }

  private getDefaultOutcome(receiptData: ReceiptData, member: Member): TransactionOutcome {
    return {
      originalAmount: receiptData.total_amount,
      discountAmount: 0,
      finalAmount: receiptData.total_amount,
      pointsUsed: 0,
      pointsRemaining: member.available_points,
      savingsPercentage: 0,
      additionalBenefits: [],
      confidence: 0.6,
    };
  }

  /**
   * Fallback receipt analysis without AI
   */
  private fallbackReceiptAnalysis(
    receiptData: ReceiptData,
    member: Member,
    availableRewards: Reward[]
  ): ReceiptAnalysis {
    // Get best matching rewards based on simple criteria
    const eligibleRewards = availableRewards.filter(
      r => r.is_active && member.available_points >= r.points_required
    );

    // Sort for best match
    const bestMatches = eligibleRewards
      .sort((a, b) => {
        // Prefer percentage discounts for higher amounts
        if (a.reward_value_type === 'PERCENTAGE' && b.reward_value_type !== 'PERCENTAGE') {
          return -1;
        }
        if (a.reward_value_type !== 'PERCENTAGE' && b.reward_value_type === 'PERCENTAGE') {
          return 1;
        }

        // Otherwise sort by value
        return this.calculateSavings(b, receiptData.total_amount) -
               this.calculateSavings(a, receiptData.total_amount);
      })
      .slice(0, 3);

    const insights = [];

    // Add basic insights based on receipt
    insights.push(`Receipt total: ${receiptData.total_amount} Birr`);

    if (receiptData.business_name) {
      insights.push(`Business: ${receiptData.business_name}`);
    }

    if (receiptData.receipt_date) {
      insights.push(`Date: ${receiptData.receipt_date}`);
    }

    const topReward = bestMatches[0];
    if (topReward) {
      const savings = this.calculateSavings(topReward, receiptData.total_amount);
      insights.push(`Best match: ${topReward.title} saving approximately ${savings} Birr`);
    }

    // Create receipt analysis object
    const topMatch = bestMatches[0];

    // Calculate outcome for top match if available
    let calculatedOutcome: TransactionOutcome = {
      originalAmount: receiptData.total_amount,
      discountAmount: 0,
      finalAmount: receiptData.total_amount,
      pointsUsed: 0,
      pointsRemaining: member.available_points,
      savingsPercentage: 0,
      additionalBenefits: [],
      confidence: 0.8
    };

    if (topMatch) {
      calculatedOutcome = this.fallbackCalculation(member, topMatch, receiptData.total_amount);
    }

    return {
      receiptTotal: receiptData.total_amount,
      merchantName: receiptData.business_name || 'Unknown',
      transactionDate: receiptData.receipt_date || new Date().toISOString(),
      applicableRewards: bestMatches.map(reward => ({
        rewardId: reward.id,
        rewardType: reward.reward_value_type,
        applicabilityScore: 0.85,
        estimatedSavings: this.calculateSavings(reward, receiptData.total_amount),
        finalAmount: receiptData.total_amount - this.calculateSavings(reward, receiptData.total_amount),
        explanation: `${reward.title} can be applied to this receipt.`
      })),
      recommendedReward: topMatch ? {
        rewardId: topMatch.id,
        rewardType: topMatch.reward_value_type,
        applicabilityScore: 0.95,
        estimatedSavings: this.calculateSavings(topMatch, receiptData.total_amount),
        finalAmount: receiptData.total_amount - this.calculateSavings(topMatch, receiptData.total_amount),
        explanation: `${topMatch.title} is the best match for this transaction.`
      } : null,
      calculatedOutcome,
      confidence: 0.8
    };
  }
}

// Export singleton instance
export const aiValidationService = new AIValidationService();
