import { sendTelegramMessage } from './telegram'
import { getServiceRoleClient } from './supabase'

export async function sendPointsNotification(
  chatId: string,
  pointsEarned: number,
  businessName: string,
  memberId: string
) {
  const message = `🎉 <b>Points Earned!</b>\n\n` +
    `You earned <b>${pointsEarned} points</b> at ${businessName}!\n` +
    `Keep shopping to earn more rewards! 💎`

  const success = await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })

  // Log notification in database
  if (success) {
    const supabase = getServiceRoleClient()
    await supabase
      .from('telegram_notifications')
      .insert({
        member_id: memberId,
        chat_id: chatId,
        notification_type: 'points_earned',
        title: 'Points Earned',
        message: `Earned ${pointsEarned} points at ${businessName}`,
        delivery_status: 'delivered'
      })
  }

  return success
}

export async function sendTierUpgradeNotification(
  chatId: string,
  newTier: string,
  businessName: string,
  memberId: string
) {
  const message = `🎊 <b>Tier Upgrade!</b>\n\n` +
    `Congratulations! You've been upgraded to <b>${newTier}</b> tier!\n` +
    `Enjoy enhanced benefits and exclusive rewards! ✨`

  const success = await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })

  // Log notification in database
  if (success) {
    const supabase = getServiceRoleClient()
    await supabase
      .from('telegram_notifications')
      .insert({
        member_id: memberId,
        chat_id: chatId,
        notification_type: 'tier_upgrade',
        title: 'Tier Upgrade',
        message: `Upgraded to ${newTier} tier`,
        delivery_status: 'delivered'
      })
  }

  return success
}

export async function sendRewardRedemptionNotification(
  chatId: string,
  rewardTitle: string,
  pointsUsed: number,
  memberId: string
) {
  const message = `🎁 <b>Reward Redeemed!</b>\n\n` +
    `You've successfully redeemed: <b>${rewardTitle}</b>\n` +
    `Points used: ${pointsUsed} points\n\n` +
    `Enjoy your reward! 🎉`

  const success = await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })

  // Log notification in database
  if (success) {
    const supabase = getServiceRoleClient()
    await supabase
      .from('telegram_notifications')
      .insert({
        member_id: memberId,
        chat_id: chatId,
        notification_type: 'reward_redeemed',
        title: 'Reward Redeemed',
        message: `Redeemed ${rewardTitle} for ${pointsUsed} points`,
        delivery_status: 'delivered'
      })
  }

  return success
}

export async function sendSpecialOfferNotification(
  chatId: string,
  offerTitle: string,
  offerDescription: string,
  memberId: string
) {
  const message = `🔥 <b>Special Offer!</b>\n\n` +
    `<b>${offerTitle}</b>\n\n` +
    `${offerDescription}\n\n` +
    `Don't miss out! Offer valid for limited time. ⏰`

  const success = await sendTelegramMessage(chatId, message, { parse_mode: 'HTML' })

  // Log notification in database
  if (success) {
    const supabase = getServiceRoleClient()
    await supabase
      .from('telegram_notifications')
      .insert({
        member_id: memberId,
        chat_id: chatId,
        notification_type: 'special_offer',
        title: offerTitle,
        message: offerDescription,
        delivery_status: 'delivered'
      })
  }

  return success
}
