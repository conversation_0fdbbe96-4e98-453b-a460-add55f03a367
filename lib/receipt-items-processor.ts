/**
 * Receipt Items Processor
 *
 * This module handles the creation of receipt_items when receipts are processed.
 * It matches receipt data to business_items and creates the necessary linkages
 * for business analytics to work properly.
 */

import { getServiceRoleClient } from '@/lib/supabase'

interface ReceiptItemData {
  receipt_id: string
  business_item_id: string | null
  quantity: number
  unit_price: number
  total_price: number
  extracted_description: string
  confidence_score: number
  manual_override: boolean
}

interface BusinessItem {
  id: string
  item_name: string
  item_category: string
  standard_price: number
  ai_recognition_patterns: string[]
  common_variations: string[]
}

interface ParsedReceiptItem {
  description: string
  quantity: number
  unit_price: number
  total_price: number
}

/**
 * Process receipt and create receipt_items entries
 */
export async function processReceiptItems(
  receiptId: string,
  companyId: string,
  serviceDescription: string,
  totalAmount: number,
  subtotal: number
): Promise<{ success: boolean; items_created: number; error?: string }> {
  try {
    const supabase = getServiceRoleClient()

    // Get business items for this company
    const { data: businessItems, error: businessItemsError } = await supabase
      .from('business_items')
      .select('*')
      .eq('company_id', companyId)
      .eq('is_active', true)

    if (businessItemsError) {
      console.error('Error fetching business items:', businessItemsError)
      return { success: false, items_created: 0, error: 'Failed to fetch business items' }
    }

    if (!businessItems || businessItems.length === 0) {
      console.log('No business items found for company, creating generic receipt item')
      return await createGenericReceiptItem(receiptId, serviceDescription, totalAmount, subtotal)
    }

    // Parse the service description to extract items
    const parsedItems = parseServiceDescription(serviceDescription, totalAmount, subtotal)

    // Match parsed items to business items
    const receiptItems: ReceiptItemData[] = []

    for (const parsedItem of parsedItems) {
      const matchedBusinessItem = findBestMatch(parsedItem, businessItems)

      const receiptItem: ReceiptItemData = {
        receipt_id: receiptId,
        business_item_id: matchedBusinessItem?.id || null,
        quantity: parsedItem.quantity,
        unit_price: parsedItem.unit_price,
        total_price: parsedItem.total_price,
        extracted_description: parsedItem.description,
        confidence_score: matchedBusinessItem ? calculateConfidenceScore(parsedItem, matchedBusinessItem) : 0.3,
        manual_override: false
      }

      receiptItems.push(receiptItem)
    }

    // Insert receipt items
    const { error: insertError } = await supabase
      .from('receipt_items')
      .insert(receiptItems)

    if (insertError) {
      console.error('Error inserting receipt items:', insertError)
      return { success: false, items_created: 0, error: 'Failed to insert receipt items' }
    }

    // Update business item statistics for matched items
    for (const item of receiptItems) {
      if (item.business_item_id) {
        await updateBusinessItemStats(item.business_item_id, item.unit_price, item.quantity)
      }
    }

    console.log(`Successfully created ${receiptItems.length} receipt items for receipt ${receiptId}`)
    return { success: true, items_created: receiptItems.length }

  } catch (error) {
    console.error('Error processing receipt items:', error)
    return {
      success: false,
      items_created: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Create a generic receipt item when no business items exist
 */
async function createGenericReceiptItem(
  receiptId: string,
  serviceDescription: string,
  totalAmount: number,
  subtotal: number
): Promise<{ success: boolean; items_created: number; error?: string }> {
  const supabase = getServiceRoleClient()

  const genericItem: ReceiptItemData = {
    receipt_id: receiptId,
    business_item_id: null,
    quantity: 1,
    unit_price: subtotal,
    total_price: totalAmount,
    extracted_description: serviceDescription || 'General Service',
    confidence_score: 0.5,
    manual_override: false
  }

  const { error } = await supabase
    .from('receipt_items')
    .insert([genericItem])

  if (error) {
    console.error('Error inserting generic receipt item:', error)
    return { success: false, items_created: 0, error: 'Failed to insert generic receipt item' }
  }

  return { success: true, items_created: 1 }
}

/**
 * Parse service description to extract individual items
 */
function parseServiceDescription(
  serviceDescription: string,
  totalAmount: number,
  subtotal: number
): ParsedReceiptItem[] {
  if (!serviceDescription || serviceDescription.trim() === '') {
    return [{
      description: 'General Service',
      quantity: 1,
      unit_price: subtotal,
      total_price: totalAmount
    }]
  }

  // Simple parsing - look for common patterns
  const description = serviceDescription.trim()

  // Check if it looks like a list of items (comma-separated)
  if (description.includes(',') && description.split(',').length > 1) {
    const items = description.split(',').map(item => item.trim()).filter(item => item.length > 0)
    const pricePerItem = subtotal / items.length

    return items.map(item => ({
      description: item,
      quantity: 1,
      unit_price: pricePerItem,
      total_price: pricePerItem
    }))
  }

  // Check if it looks like quantity x item format
  const quantityMatch = description.match(/(\d+)\s*x?\s*(.+)/i)
  if (quantityMatch) {
    const quantity = parseInt(quantityMatch[1])
    const itemDescription = quantityMatch[2].trim()
    const unitPrice = subtotal / quantity

    return [{
      description: itemDescription,
      quantity: quantity,
      unit_price: unitPrice,
      total_price: subtotal
    }]
  }

  // Default: single item
  return [{
    description: description,
    quantity: 1,
    unit_price: subtotal,
    total_price: totalAmount
  }]
}

/**
 * Find the best matching business item for a parsed item
 */
function findBestMatch(
  parsedItem: ParsedReceiptItem,
  businessItems: BusinessItem[]
): BusinessItem | null {
  let bestMatch: BusinessItem | null = null
  let bestScore = 0

  for (const businessItem of businessItems) {
    const score = calculateMatchScore(parsedItem, businessItem)
    if (score > bestScore && score > 0.6) { // Minimum confidence threshold
      bestScore = score
      bestMatch = businessItem
    }
  }

  return bestMatch
}

/**
 * Calculate match score between parsed item and business item
 */
function calculateMatchScore(parsedItem: ParsedReceiptItem, businessItem: BusinessItem): number {
  const description = parsedItem.description.toLowerCase()
  const itemName = businessItem.item_name.toLowerCase()

  // Exact match
  if (description === itemName) {
    return 1.0
  }

  // Check if parsed item contains business item name
  if (description.includes(itemName) || itemName.includes(description)) {
    return 0.8
  }

  // Check AI recognition patterns
  if (businessItem.ai_recognition_patterns) {
    for (const pattern of businessItem.ai_recognition_patterns) {
      if (description.includes(pattern.toLowerCase())) {
        return 0.9
      }
    }
  }

  // Check common variations
  if (businessItem.common_variations) {
    for (const variation of businessItem.common_variations) {
      if (description.includes(variation.toLowerCase()) || variation.toLowerCase().includes(description)) {
        return 0.85
      }
    }
  }

  // Simple word similarity
  const descriptionWords = description.split(' ')
  const itemWords = itemName.split(' ')
  let matchingWords = 0

  for (const word of descriptionWords) {
    if (itemWords.some(itemWord => itemWord.includes(word) || word.includes(itemWord))) {
      matchingWords++
    }
  }

  const wordScore = matchingWords / Math.max(descriptionWords.length, itemWords.length)
  return wordScore > 0.5 ? wordScore * 0.7 : 0 // Lower confidence for word-based matching
}

/**
 * Calculate confidence score for a matched item
 */
function calculateConfidenceScore(parsedItem: ParsedReceiptItem, businessItem: BusinessItem): number {
  const baseScore = calculateMatchScore(parsedItem, businessItem)

  // Adjust based on price similarity if available
  if (businessItem.standard_price && businessItem.standard_price > 0) {
    const priceDifference = Math.abs(parsedItem.unit_price - businessItem.standard_price) / businessItem.standard_price
    const priceScore = Math.max(0, 1 - priceDifference) // Lower score for larger price differences
    return (baseScore + priceScore) / 2
  }

  return baseScore
}

/**
 * Update business item statistics after a sale
 */
async function updateBusinessItemStats(
  businessItemId: string,
  salePrice: number,
  quantity: number
): Promise<void> {
  const supabase = getServiceRoleClient()

  // Try to use RPC function if it exists, otherwise update manually
  try {
    const { error: rpcError } = await supabase.rpc('update_business_item_stats', {
      p_business_item_id: businessItemId,
      p_sale_price: salePrice,
      p_quantity: quantity
    })

    if (!rpcError) {
      return // RPC function worked
    }

    console.log('RPC function not available, updating manually')
  } catch (error) {
    console.log('RPC function not available, updating manually:', error)
  }

  // Manual update if RPC doesn't exist
  const { data: currentItem, error: fetchError } = await supabase
    .from('business_items')
    .select('total_sales_count, total_revenue, avg_selling_price')
    .eq('id', businessItemId)
    .single()

  if (fetchError || !currentItem) {
    console.error('Error fetching current business item stats:', fetchError)
    return
  }

  const newSalesCount = (currentItem.total_sales_count || 0) + quantity
  const newRevenue = (currentItem.total_revenue || 0) + (salePrice * quantity)
  const newAvgPrice = newRevenue / newSalesCount

  await supabase
    .from('business_items')
    .update({
      total_sales_count: newSalesCount,
      total_revenue: newRevenue,
      avg_selling_price: newAvgPrice,
      last_sold_date: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('id', businessItemId)
}

/**
 * Retroactively process existing receipts that don't have receipt_items
 */
export async function processExistingReceipts(companyId?: string): Promise<{
  success: boolean
  receipts_processed: number
  items_created: number
  errors: string[]
}> {
  const supabase = getServiceRoleClient()
  const errors: string[] = []
  let receiptsProcessed = 0
  let totalItemsCreated = 0

  try {
    // Get receipts that don't have receipt_items
    let receiptsQuery = supabase
      .from('receipts')
      .select(`
        id,
        company_id,
        service_description,
        total_amount,
        subtotal
      `)
      .not('id', 'in',
        supabase
          .from('receipt_items')
          .select('receipt_id')
      )

    if (companyId) {
      receiptsQuery = receiptsQuery.eq('company_id', companyId)
    }

    const { data: receiptsWithoutItems, error: receiptsError } = await receiptsQuery

    if (receiptsError) {
      errors.push(`Error fetching receipts: ${receiptsError.message}`)
      return { success: false, receipts_processed: 0, items_created: 0, errors }
    }

    if (!receiptsWithoutItems || receiptsWithoutItems.length === 0) {
      return { success: true, receipts_processed: 0, items_created: 0, errors: ['No receipts need processing'] }
    }

    // Process each receipt
    for (const receipt of receiptsWithoutItems) {
      if (companyId && receipt.company_id !== companyId) {
        continue
      }

      const result = await processReceiptItems(
        receipt.id,
        receipt.company_id,
        receipt.service_description || '',
        receipt.total_amount,
        receipt.subtotal
      )

      if (result.success) {
        receiptsProcessed++
        totalItemsCreated += result.items_created
      } else {
        errors.push(`Receipt ${receipt.id}: ${result.error}`)
      }
    }

    return {
      success: true,
      receipts_processed: receiptsProcessed,
      items_created: totalItemsCreated,
      errors
    }

  } catch (error) {
    errors.push(error instanceof Error ? error.message : 'Unknown error')
    return { success: false, receipts_processed: receiptsProcessed, items_created: totalItemsCreated, errors }
  }
}

/**
 * Process receipt items from enhanced OCR data
 */
export async function processReceiptItemsFromOCR(
  receiptId: string,
  companyId: string,
  ocrDataJson: string,
  totalAmount: number
): Promise<{ success: boolean; items_created: number; error?: string }> {
  try {
    const supabase = getServiceRoleClient()

    // Parse the OCR data
    let ocrData
    try {
      ocrData = JSON.parse(ocrDataJson)
    } catch (parseError) {
      console.error('Error parsing OCR data:', parseError)
      return { success: false, items_created: 0, error: 'Invalid OCR data format' }
    }

    // Get business items for this company
    const { data: businessItems, error: businessItemsError } = await supabase
      .from('business_items')
      .select('*')
      .eq('company_id', companyId)
      .eq('is_active', true)

    if (businessItemsError) {
      console.error('Error fetching business items:', businessItemsError)
      return { success: false, items_created: 0, error: 'Failed to fetch business items' }
    }

    const receiptItems: ReceiptItemData[] = []

    // Check if OCR data has items array
    if (ocrData.items && Array.isArray(ocrData.items)) {
      console.log(`[OCR Processor] Found ${ocrData.items.length} items in OCR data`)

      for (const item of ocrData.items) {
        const description = item.description || 'Unknown Item'
        
        // Fix: Properly handle quantity vs price confusion
        let quantity = item.quantity || 1
        let unitPrice = item.unit_price || 0
        let totalPrice = item.total_price || 0
        
        // If quantity looks like a price (too high), it's likely the OCR confused quantity with price
        if (quantity > 100 && totalPrice > 0) {
          console.log(`[OCR Processor] Detected price confusion: quantity=${quantity}, total_price=${totalPrice}`)
          // The "quantity" is actually the total price, so fix it
          quantity = 1
          unitPrice = totalPrice
        } else if (quantity < 1) {
          console.log(`[OCR Processor] Invalid quantity ${quantity} for item ${description}, defaulting to 1`)
          quantity = 1
        }
        
        // Ensure we have valid prices
        if (unitPrice === 0 && totalPrice > 0) {
          unitPrice = totalPrice / quantity
        } else if (totalPrice === 0 && unitPrice > 0) {
          totalPrice = unitPrice * quantity
        }

        // Find or create matching business item
        let businessItemId = null

        if (businessItems && businessItems.length > 0) {
          const matchedItem = findBestMatchByName(description, businessItems)
          if (matchedItem) {
            businessItemId = matchedItem.id
          } else {
            // Create a new business item for this OCR item
            const newBusinessItem = await createBusinessItemFromOCR(
              companyId,
              description,
              unitPrice,
              supabase
            )
            if (newBusinessItem) {
              businessItemId = newBusinessItem.id
            }
          }
        }

        const receiptItem: ReceiptItemData = {
          receipt_id: receiptId,
          business_item_id: businessItemId,
          quantity: quantity,
          unit_price: unitPrice,
          total_price: totalPrice,
          extracted_description: description,
          confidence_score: ocrData.confidence || 0.8,
          manual_override: false
        }

        receiptItems.push(receiptItem)
      }
    } else {
      // If no items array, create a single item from the business name
      const description = ocrData.business_name || 'General Service'

      // Find or create matching business item
      let businessItemId = null

      if (businessItems && businessItems.length > 0) {
        const matchedItem = findBestMatchByName(description, businessItems)
        if (matchedItem) {
          businessItemId = matchedItem.id
        } else {
          // Create a new business item
          const newBusinessItem = await createBusinessItemFromOCR(
            companyId,
            description,
            totalAmount,
            supabase
          )
          if (newBusinessItem) {
            businessItemId = newBusinessItem.id
          }
        }
      }

      const receiptItem: ReceiptItemData = {
        receipt_id: receiptId,
        business_item_id: businessItemId,
        quantity: 1,
        unit_price: totalAmount,
        total_price: totalAmount,
        extracted_description: description,
        confidence_score: ocrData.confidence || 0.8,
        manual_override: false
      }

      receiptItems.push(receiptItem)
    }

    if (receiptItems.length === 0) {
      return { success: false, items_created: 0, error: 'No valid items found in OCR data' }
    }

    // Insert receipt items
    const { error: insertError } = await supabase
      .from('receipt_items')
      .insert(receiptItems)

    if (insertError) {
      console.error('Error inserting receipt items from OCR:', insertError)
      return { success: false, items_created: 0, error: 'Failed to insert receipt items' }
    }

    console.log(`[OCR Processor] Successfully created ${receiptItems.length} receipt items`)
    return { success: true, items_created: receiptItems.length }

  } catch (error) {
    console.error('Error processing receipt items from OCR:', error)
    return {
      success: false,
      items_created: 0,
      error: error instanceof Error ? error.message : 'Unknown error during OCR processing'
    }
  }
}

/**
 * Find best match by simple name comparison
 */
function findBestMatchByName(itemName: string, businessItems: BusinessItem[]): BusinessItem | null {
  const normalizedItemName = itemName.toLowerCase().trim()

  // Try exact match first
  for (const businessItem of businessItems) {
    if (businessItem.item_name.toLowerCase().trim() === normalizedItemName) {
      return businessItem
    }
  }

  // Try partial match
  for (const businessItem of businessItems) {
    const businessItemName = businessItem.item_name.toLowerCase().trim()
    if (normalizedItemName.includes(businessItemName) || businessItemName.includes(normalizedItemName)) {
      return businessItem
    }
  }

  return null
}

/**
 * Create a new business item from OCR data
 */
async function createBusinessItemFromOCR(
  companyId: string,
  itemName: string,
  price: number,
  supabase: ReturnType<typeof getServiceRoleClient>
): Promise<{ id: string } | null> {
  try {
    const { data, error } = await supabase
      .from('business_items')
      .insert({
        id: crypto.randomUUID(),
        company_id: companyId,
        item_name: itemName,
        item_category: 'General',
        standard_price: price,
        total_sales_count: 1,
        total_revenue: price,
        avg_selling_price: price,
        last_sold_date: new Date().toISOString(),
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single()

    if (error) {
      console.error('Error creating business item from OCR:', error)
      return null
    }

    console.log(`[OCR Processor] Created new business item: ${itemName}`)
    return data

  } catch (error) {
    console.error('Exception creating business item from OCR:', error)
    return null
  }
}
