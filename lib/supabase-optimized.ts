import { createBrowserClient } from '@supabase/ssr'
import type { SupabaseClient } from '@supabase/supabase-js'

// Singleton instance
let supabaseInstance: SupabaseClient | null = null
let currentCompanyId: string | null = null

/**
 * Create a new Supabase client with optional company-specific headers
 */
function createNewSupabaseClient(companyId?: string): SupabaseClient {
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
      },
      global: {
        headers: companyId ? { 'X-Company-ID': companyId } : {},
      },
    }
  )

  // Store company ID for future reference
  if (companyId) {
    currentCompanyId = companyId
  }

  return supabase
}

/**
 * Get the singleton Supabase client instance
 * Creates a new instance if none exists or if company context changed
 */
export function getSupabaseClient(companyId?: string): SupabaseClient {
  // Create new instance if:
  // 1. No instance exists
  // 2. Company ID changed and we need to update headers
  const needsNewInstance = !supabaseInstance || (companyId && companyId !== currentCompanyId)

  if (needsNewInstance) {
    // Clean up existing instance if it exists
    if (supabaseInstance) {
      // Note: Supabase doesn't have a formal cleanup method, but we can clear the reference
      supabaseInstance = null
    }

    supabaseInstance = createNewSupabaseClient(companyId)
    console.log('Created new Supabase client instance', { companyId })
  }

  // TypeScript assertion since we ensure instance exists above
  return supabaseInstance!
}

/**
 * Refresh the Supabase client with new company context
 * Useful when user switches companies or logs in/out
 */
export function refreshSupabaseClient(companyId?: string): SupabaseClient {
  // Force create new instance
  supabaseInstance = null
  currentCompanyId = null

  return getSupabaseClient(companyId)
}

/**
 * Get current company ID associated with the client
 */
export function getCurrentCompanyId(): string | null {
  return currentCompanyId
}

/**
 * Clear the singleton instance (useful for testing or logout)
 */
export function clearSupabaseClient(): void {
  supabaseInstance = null
  currentCompanyId = null
}

// Default export for backward compatibility
export default getSupabaseClient
