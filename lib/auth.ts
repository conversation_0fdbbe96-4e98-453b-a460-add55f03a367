import { SupabaseClient } from '@supabase/supabase-js';
import { getServiceRoleClient } from './supabase';

/**
 * User role types for the system
 */
export type UserRole = 'OWNER' | 'CASHIER' | 'VIEWER' | null;

/**
 * Get the company ID from the current user's session
 * @param supabase Supabase client instance
 * @returns The company ID or null if not found
 */
export async function getCompanyIdFromSession(customClient?: SupabaseClient) {
  const supabase = customClient || getServiceRoleClient();
  try {
    // Get the user from the session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('Error getting user from session:', userError);
      return null;
    }

    // First try to get from company_administrators table
    const { data: adminData } = await supabase
      .from('company_administrators')
      .select('company_id')
      .eq('administrator_id', user.id)
      .maybeSingle();

    if (adminData?.company_id) {
      return adminData.company_id;
    }

    // Fallback to companies table (current implementation)
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('id')
      .eq('administrator_id', user.id)
      .maybeSingle();

    if (companyError || !companyData) {
      console.error('Error getting company:', companyError);
      return null;
    }

    return companyData.id;
  } catch (error) {
    console.error('Unexpected error in getCompanyIdFromSession:', error);
    return null;
  }
}

/**
 * Get the current user's ID from the session
 * @param supabase Supabase client instance
 * @returns The user ID or null if not found
 */
export async function getUserIdFromSession(customClient?: SupabaseClient) {
  const supabase = customClient || getServiceRoleClient();
  try {
    // First try to get session data which is faster and more reliable
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (!sessionError && sessionData.session?.user?.id) {
      return sessionData.session.user.id;
    }

    // Fallback to getUser() only if session approach fails
    if (sessionError) {
      console.log('Session error in getUserIdFromSession, falling back to getUser:', sessionError.message);
    }

    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      console.error('Error getting user from session:', error);
      return null;
    }

    return user.id;
  } catch (error) {
    console.error('Unexpected error in getUserIdFromSession:', error);
    return null;
  }
}

/**
 * Get the current user's role for the given company
 * @param supabase Supabase client instance
 * @param companyId Company ID to check
 * @returns The user's role or null if not found
 */
export async function getUserRole(supabase: SupabaseClient, companyId: string): Promise<UserRole> {
  try {
    const userId = await getUserIdFromSession(supabase);

    if (!userId) {
      console.log('No user ID found in getUserRole check');
      return null;
    }

    // Check company_administrators table for role
    const { data: adminData, error: adminError } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('administrator_id', userId)
      .eq('company_id', companyId)
      .maybeSingle();

    if (adminError) {
      console.log('No match in company_administrators table:', adminError.message);
    }

    if (adminData?.role) {
      console.log(`User has role ${adminData.role} via company_administrators table`);
      return adminData.role as UserRole;
    }

    // Fallback to companies table - if user is the company administrator, they're an OWNER
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('id')
      .eq('administrator_id', userId)
      .eq('id', companyId)
      .single();

    if (companyError) {
      console.log('No match in companies table:', companyError.message);
    }

    if (companyData) {
      console.log('User is OWNER via companies table');
      return 'OWNER';
    }

    console.log('User has no role for this company');
    return null;
  } catch (error) {
    console.error('Unexpected error in getUserRole:', error);
    return null;
  }
}

/**
 * Check if the current user is an administrator for the given company
 * @param supabase Supabase client instance
 * @param companyId Company ID to check
 * @returns Boolean indicating if the user is an administrator
 */
export async function isCompanyAdmin(supabase: SupabaseClient, companyId: string) {
  try {
    const role = await getUserRole(supabase, companyId);
    return role === 'OWNER';
  } catch (error) {
    console.error('Unexpected error in isCompanyAdmin:', error);
    return false;
  }
}

/**
 * Check if the current user is a cashier for the given company
 * @param supabase Supabase client instance
 * @param companyId Company ID to check
 * @returns Boolean indicating if the user is a cashier
 */
export async function isCompanyCashier(supabase: SupabaseClient, companyId: string) {
  try {
    const role = await getUserRole(supabase, companyId);
    return role === 'CASHIER';
  } catch (error) {
    console.error('Unexpected error in isCompanyCashier:', error);
    return false;
  }
}

/**
 * Check if the current user has any role (admin or cashier) for the given company
 * @param supabase Supabase client instance
 * @param companyId Company ID to check
 * @returns Boolean indicating if the user has any role
 */
export async function hasCompanyRole(supabase: SupabaseClient, companyId: string) {
  try {
    const role = await getUserRole(supabase, companyId);
    return role !== null;
  } catch (error) {
    console.error('Unexpected error in hasCompanyRole:', error);
    return false;
  }
}

/**
 * Check if a user with the given email is an administrator for the given company
 * This is useful for ensuring specific admins (like <EMAIL> for Arada)
 * have proper access
 *
 * @param supabase Supabase client instance
 * @param email Email address to check
 * @param companyId Company ID to check
 * @returns Boolean indicating if the user is an administrator
 */
export async function isCompanyAdminByEmail(supabase: SupabaseClient, email: string, companyId: string) {
  try {
    if (!email || !companyId) {
      return false;
    }

    console.log(`Checking admin privileges for email ${email} on company ${companyId}`);

    // First get the user ID from the email
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single();

    if (userError || !userData) {
      console.log(`No user found with email ${email}:`, userError?.message);
      return false;
    }

    const userId = userData.id;

    // Check company_administrators table
    const { data: adminData, error: adminError } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('administrator_id', userId)
      .eq('company_id', companyId)
      .maybeSingle();

    if (adminError) {
      console.log(`No match in company_administrators table for ${email}:`, adminError.message);
    }

    if (adminData?.role === 'OWNER') {
      console.log(`User ${email} is admin via company_administrators table`);
      return true;
    }

    // Fallback to companies table for OWNER role
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select('administrator_id')
      .eq('id', companyId)
      .maybeSingle();

    if (companyError) {
      console.log(`No match in companies table for ${email}:`, companyError.message);
    }

    if (companyData?.administrator_id === userId) {
      console.log(`User ${email} is admin via companies table`);
      return true;
    }

    console.log(`User ${email} is NOT an admin for company ${companyId}`);
    return false;
  } catch (error) {
    console.error(`Unexpected error in isCompanyAdminByEmail for ${email}:`, error);
    return false;
  }
}
