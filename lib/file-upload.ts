import { createClient } from '@/lib/supabase-browser';

/**
 * Upload an image file to Supabase storage
 * @param file - The file to upload
 * @param bucket - The storage bucket name (default: 'fufis')
 * @param folder - Optional folder path within the bucket
 * @returns Promise with the public URL of the uploaded file
 */
export async function uploadImage(
  file: File,
  bucket = 'fufis',
  folder = 'receipts'
): Promise<{ url: string; path: string }> {
  // Use client-side supabase for storage operations
  const supabase = createClient();

  // Validate file type
  if (!file.type.startsWith('image/')) {
    throw new Error('File must be an image');
  }

  // Validate file size (max 5MB)
  const maxSize = 5 * 1024 * 1024; // 5MB in bytes
  if (file.size > maxSize) {
    throw new Error('File size must be less than 5MB');
  }

  // Generate unique filename
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const fileExtension = file.name.split('.').pop() || 'jpg';
  const fileName = `${timestamp}_${randomString}.${fileExtension}`;
  const filePath = folder ? `${folder}/${fileName}` : fileName;

  // Upload file to storage
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(filePath, file, {
      cacheControl: '3600',
      upsert: false
    });

  if (error) {
    throw new Error(`Upload failed: ${error.message}`);
  }

  // Get public URL
  const { data: publicUrlData } = supabase.storage
    .from(bucket)
    .getPublicUrl(filePath);

  if (!publicUrlData?.publicUrl) {
    throw new Error('Failed to get public URL for uploaded file');
  }

  return {
    url: publicUrlData.publicUrl,
    path: data.path
  };
}

/**
 * Upload a profile image file to Supabase storage
 * @param file - The file to upload
 * @returns Promise with the public URL of the uploaded file
 */
export async function uploadProfileImage(file: File): Promise<{ url: string; path: string }> {
  return uploadImage(file, 'fufis', 'profile-images');
}

/**
 * Delete an image from Supabase storage
 * @param filePath - The file path to delete
 * @param bucket - The storage bucket name (default: 'fufis')
 */
export async function deleteImage(filePath: string, bucket = 'fufis'): Promise<void> {
  const supabase = createClient();

  const { error } = await supabase.storage
    .from(bucket)
    .remove([filePath]);

  if (error) {
    throw new Error(`Delete failed: ${error.message}`);
  }
}
