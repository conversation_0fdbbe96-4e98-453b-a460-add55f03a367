/**
 * Utility functions for standardized error handling in API calls
 */

import { toast } from 'sonner';

/**
 * Standard error handler for Supabase queries
 * @param error The error object from Supabase
 * @param context Additional context about where the error occurred
 * @param silent If true, will not show a toast notification
 * @returns The error message
 */
export const handleSupabaseError = (
  error: unknown, 
  context: string = 'Operation', 
  silent: boolean = false
): string => {
  let errorMessage = 'An unknown error occurred';
  
  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === 'object' && error !== null) {
    // Handle Supabase error object
    const supabaseError = error as { message?: string, details?: string, hint?: string, code?: string };
    errorMessage = supabaseError.message || 
                  supabaseError.details || 
                  supabaseError.hint || 
                  `Error code: ${supabaseError.code}` || 
                  errorMessage;
  } else if (typeof error === 'string') {
    errorMessage = error;
  }
  
  // Log the error for debugging
  console.error(`${context} error:`, error);
  
  // Show toast notification if not silent
  if (!silent) {
    toast.error(`${context} failed`, {
      description: errorMessage,
    });
  }
  
  return errorMessage;
};

/**
 * Wrapper for Supabase queries to standardize error handling
 * @param queryFn The Supabase query function to execute
 * @param fallbackValue The value to return if the query fails
 * @param context Additional context about the query
 * @param silent If true, will not show a toast notification
 * @returns The query result or fallback value
 */
export async function safeQuery<T>(
  queryFn: () => Promise<{ data: T | null, error: unknown }>,
  fallbackValue: T,
  context: string = 'Query',
  silent: boolean = false
): Promise<T> {
  try {
    const { data, error } = await queryFn();
    
    if (error) {
      handleSupabaseError(error, context, silent);
      return fallbackValue;
    }
    
    return data || fallbackValue;
  } catch (error) {
    handleSupabaseError(error, context, silent);
    return fallbackValue;
  }
}
