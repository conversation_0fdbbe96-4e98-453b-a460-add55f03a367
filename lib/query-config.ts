/**
 * Centralized React Query configuration
 */

export const CACHE_TIMES = {
  // Static data that rarely changes
  STATIC: 1000 * 60 * 15,    // 15 minutes (tiers, company settings)

  // Normal data that changes occasionally
  NORMAL: 1000 * 60 * 5,     // 5 minutes (members, rewards)

  // Dynamic data that changes frequently
  DYNAMIC: 1000 * 60 * 1,    // 1 minute (transactions, redemptions)

  // Real-time data
  REALTIME: 1000 * 30,       // 30 seconds (dashboard metrics)
} as const;

export const QUERY_KEYS = {
  // Company-related queries
  company: (companyId: string) => ['company', companyId] as const,
  companyAdmin: (userId: string) => ['company', 'admin', userId] as const,

  // Member-related queries
  members: (companyId: string, limit?: number) => ['members', companyId, ...(limit ? [limit] : [])] as const,
  member: (companyId: string, memberId: string) => ['member', companyId, memberId] as const,
  memberStats: (memberId: string) => ['member', 'stats', memberId] as const,
  memberRedemptions: (memberId: string, companyId: string) => ['member', 'redemptions', memberId, companyId] as const,

  // Transaction-related queries
  transactions: (companyId: string, limit?: number, dateRange?: { from?: Date; to?: Date }) => ['transactions', companyId, ...(limit ? [limit] : []), ...(dateRange ? [JSON.stringify(dateRange)] : [])] as const,
  transaction: (transactionId: string) => ['transaction', transactionId] as const,
  memberTransactions: (memberId: string) => ['transactions', 'member', memberId] as const,

  // Reward-related queries
  rewards: (companyId: string) => ['rewards', companyId] as const,
  reward: (rewardId: string) => ['reward', rewardId] as const,
  redemptions: (companyId: string, limit?: number) => ['redemptions', companyId, ...(limit ? [limit] : [])] as const,
  redemption: (redemptionId: string) => ['redemption', redemptionId] as const,

  // Tier-related queries
  tiers: (companyId: string) => ['tiers', companyId] as const,
  tier: (tierId: string) => ['tier', tierId] as const,

  // Analytics and reports
  analytics: (companyId: string) => ['analytics', companyId] as const,
  topMembers: (companyId: string) => ['top-members', companyId] as const,

  // Marketing campaigns
  campaigns: (companyId: string) => ['campaigns', companyId] as const,
  campaign: (campaignId: string) => ['campaign', campaignId] as const,

  // Onboarding
  onboardingStatus: (userId: string) => ['onboarding-status', userId] as const,

  // Staff
  staff: (companyId: string) => ['staff', companyId] as const,

  // Dashboard metrics
  pointsData: (companyId: string, timeRange?: string) => ['pointsData', companyId, ...(timeRange ? [timeRange] : [])] as const,
  activeMembers: (companyId: string, days?: number) => ['activeMembers', companyId, ...(days ? [days] : [])] as const,
  membersCount: (companyId: string) => ['membersCount', companyId] as const,

  // Telegram Integration
  telegramStatus: (memberId: string, companyId?: string) => ['telegramStatus', memberId, ...(companyId ? [companyId] : [])] as const,
  telegramMembers: (companyId: string) => ['membersWithTelegram', companyId] as const,
  telegramNotifications: (companyId: string, limit?: number) => ['telegramNotifications', companyId, ...(limit ? [limit] : [])] as const,
} as const;

export const COMMON_QUERY_OPTIONS = {
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  retry: (failureCount: number, error: unknown) => {
    // Don't retry on 4xx errors
    const errorStatus = (error as { status?: number })?.status;
    if (errorStatus && errorStatus >= 400 && errorStatus < 500) return false;
    return failureCount < 2;
  },
} as const;
