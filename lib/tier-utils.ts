/**
 * Tier calculation utilities for loyalty program
 */

export interface TierDefinition {
  id: string
  tier_name: string
  minimum_points: number
  benefits_description?: string
  company_id: string
}

/**
 * Calculate the current tier for a member based on their lifetime points
 * @param lifetimePoints - Total lifetime points earned by the member
 * @param tiers - Array of tier definitions sorted by minimum_points ascending
 * @returns The tier name or 'Basic' as default
 */
export function calculateMemberTier(
  lifetimePoints: number,
  tiers: TierDefinition[]
): string {
  if (!tiers || tiers.length === 0) {
    return 'Basic'
  }

  // Sort tiers by minimum_points in descending order to find the highest qualifying tier
  const sortedTiers = [...tiers].sort((a, b) => b.minimum_points - a.minimum_points)

  // Find the highest tier the member qualifies for
  for (const tier of sortedTiers) {
    if (lifetimePoints >= tier.minimum_points) {
      return tier.tier_name
    }
  }

  // If no tier qualifies, return the lowest tier or 'Basic'
  const lowestTier = tiers.find(t => t.minimum_points === Math.min(...tiers.map(tier => tier.minimum_points)))
  return lowestTier?.tier_name || 'Basic'
}

/**
 * Get tier color class for UI display
 * @param tierName - Name of the tier
 * @returns CSS class string for styling
 */
export function getTierColorClass(tierName: string): string {
  const tierLower = tierName.toLowerCase()

  if (tierLower.includes('platinum') || tierLower.includes('diamond')) {
    return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
  }
  if (tierLower.includes('gold')) {
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
  }
  if (tierLower.includes('silver')) {
    return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
  }
  if (tierLower.includes('bronze')) {
    return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'
  }

  // Default for Basic or other tiers
  return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
}

/**
 * Get progress to next tier
 * @param lifetimePoints - Current lifetime points
 * @param tiers - Array of tier definitions
 * @returns Object with current tier, next tier, and progress information
 */
export function getTierProgress(
  lifetimePoints: number,
  tiers: TierDefinition[]
): {
  currentTier: string
  nextTier: string | null
  pointsToNext: number | null
  progressPercentage: number
} {
  if (!tiers || tiers.length === 0) {
    return {
      currentTier: 'Basic',
      nextTier: null,
      pointsToNext: null,
      progressPercentage: 0
    }
  }

  const sortedTiers = [...tiers].sort((a, b) => a.minimum_points - b.minimum_points)
  const currentTier = calculateMemberTier(lifetimePoints, tiers)

  // Find current tier definition
  const currentTierDef = sortedTiers.find(t => t.tier_name === currentTier)
  const currentTierIndex = currentTierDef ? sortedTiers.indexOf(currentTierDef) : -1

  // Find next tier
  const nextTierDef = currentTierIndex >= 0 && currentTierIndex < sortedTiers.length - 1
    ? sortedTiers[currentTierIndex + 1]
    : null

  if (!nextTierDef) {
    return {
      currentTier,
      nextTier: null,
      pointsToNext: null,
      progressPercentage: 100
    }
  }

  const pointsToNext = nextTierDef.minimum_points - lifetimePoints
  const currentTierPoints = currentTierDef?.minimum_points || 0
  const nextTierPoints = nextTierDef.minimum_points
  const progressPercentage = Math.min(100, Math.max(0,
    ((lifetimePoints - currentTierPoints) / (nextTierPoints - currentTierPoints)) * 100
  ))

  return {
    currentTier,
    nextTier: nextTierDef.tier_name,
    pointsToNext,
    progressPercentage
  }
}
