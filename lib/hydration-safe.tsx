/**
 * Hydration-safe utilities for Next.js applications
 * 
 * These utilities help prevent hydration mismatches and flickering
 * by safely handling client-side only rendering and browser extensions.
 */

'use client'

import React, { useState, useEffect } from 'react'

/**
 * A wrapper component that safely handles client-side only rendering
 * without causing hydration mismatches or flickering.
 */
export function ClientOnly<T extends React.ReactNode>({ 
  children, 
  fallback 
}: { 
  children: T | (() => T), 
  fallback?: React.ReactNode 
}) {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  // On the first render, don't render the children at all to avoid hydration mismatch
  // Instead, render the fallback or nothing
  if (!hasMounted) {
    return <>{fallback}</> || null
  }

  // Once mounted on the client, render the children
  return <>{typeof children === 'function' ? (children as () => T)() : children}</>
}

/**
 * A hook that safely handles client-side only code execution
 * and returns whether the component has mounted.
 */
export function useHasMounted() {
  const [hasMounted, setHasMounted] = useState(false)
  
  useEffect(() => {
    setHasMounted(true)
  }, [])
  
  return hasMounted
}

/**
 * A hook that safely handles date formatting to prevent hydration mismatches
 * @param date The date to format
 * @param formatFn The function to use for formatting
 * @param fallback The fallback value to use before client-side rendering
 */
export function useSafeDate<T>(
  date: Date | string | number | null | undefined,
  formatFn: (date: Date | string | number) => T,
  fallback: T
): T {
  const hasMounted = useHasMounted()
  
  if (!hasMounted || !date) {
    return fallback
  }
  
  try {
    return formatFn(date)
  } catch (error) {
    console.error('Error formatting date:', error)
    return fallback
  }
}
