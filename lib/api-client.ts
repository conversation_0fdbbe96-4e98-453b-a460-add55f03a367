/**
 * Authenticated fetch utility for API calls
 * Automatically includes credentials for authentication
 */
export async function authenticatedFetch(url: string, options?: RequestInit): Promise<Response> {
  return fetch(url, {
    ...options,
    credentials: "include",
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
}

/**
 * Authenticated fetch with automatic JSON parsing and error handling
 */
export async function authenticatedFetchJson<T = unknown>(url: string, options?: RequestInit): Promise<T> {
  const response = await authenticatedFetch(url, options);

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Request failed: ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ''}`);
  }

  return response.json();
}
