/**
 * Beautiful gradient color schemes for reward cards
 * Each reward gets a consistent color based on its title/ID
 */

export const REWARD_COLOR_SCHEMES = [
  {
    name: 'Sunset',
    gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)'
  },
  {
    name: 'Ocean',
    gradient: 'linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%)'
  },
  {
    name: 'Forest',
    gradient: 'linear-gradient(135deg, #66bb6a 0%, #43a047 100%)'
  },
  {
    name: 'Purple Dream',
    gradient: 'linear-gradient(135deg, #ab47bc 0%, #8e24aa 100%)'
  },
  {
    name: 'Golden Hour',
    gradient: 'linear-gradient(135deg, #ffb74d 0%, #ff9800 100%)'
  },
  {
    name: 'Rose Garden',
    gradient: 'linear-gradient(135deg, #f06292 0%, #e91e63 100%)'
  },
  {
    name: 'Mint Fresh',
    gradient: 'linear-gradient(135deg, #26a69a 0%, #00897b 100%)'
  },
  {
    name: 'Cosmic Blue',
    gradient: 'linear-gradient(135deg, #5c6bc0 0%, #3f51b5 100%)'
  }
];

/**
 * Get a consistent color scheme for a reward based on its ID or title
 */
export function getRewardColorScheme(rewardId: string): (typeof REWARD_COLOR_SCHEMES)[0] {
  // Create a simple hash from the reward ID to ensure consistency
  let hash = 0;
  for (let i = 0; i < rewardId.length; i++) {
    const char = rewardId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Use absolute value and modulo to get a valid index
  const index = Math.abs(hash) % REWARD_COLOR_SCHEMES.length;
  return REWARD_COLOR_SCHEMES[index];
}

/**
 * Generate a default reward cover component
 */
export function generateDefaultRewardCover(rewardId: string) {
  const colorScheme = getRewardColorScheme(rewardId);

  return {
    gradient: colorScheme.gradient,
    name: colorScheme.name
  };
}
